$window = (Get-Process -Id $PID).MainWindowHandle
if ($window -ne 0) {
    # Set the window size (width x height)
    $width = 1200
    $height = 300
    # Set the window position (bottom-left corner)
    $left = 0
    $top = 750
    Add-Type @"
    using System;
    using System.Runtime.InteropServices;
    public class Win32 {
        [DllImport("user32.dll", SetLastError = true)]
        public static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);
        public static readonly IntPtr HWND_TOP = IntPtr.Zero;
        public const uint SWP_NOSIZE = 0x0001;
        public const uint SWP_NOMOVE = 0x0002;
        public const uint SWP_SHOWWINDOW = 0x0040;
    }
"@
    [Win32]::SetWindowPos($window, [Win32]::HWND_TOP, $left, $top, $width, $height, [Win32]::SWP_SHOWWINDOW)
}

if (-Not (Test-Path "py-luhao\Scripts\Activate.ps1")) {
    python -m venv py-luhao
}

# Activate the virtual environment
& "py-luhao\Scripts\Activate.ps1"


if (Test-Path "requirements.txt") {
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
}

pip list

$env:DEVICE_ID = 203
$env:PYTHONDONTWRITEBYTECODE = 1
python main.py
