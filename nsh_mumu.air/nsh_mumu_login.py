# -*- encoding=utf8 -*-
__author__ = "kkzhw016"

import traceback
from time import sleep

# -*- encoding=utf8 -*-
__author__ = "labugao"

# auto_setup(__file__)

# if 'auto_setup' in globals():
#     auto_setup(__file__)
# else:
#     from airtest.core.api import *
#     from airtest.cli.parser import cli_setup

#     if not cli_setup():
#         auto_setup(__file__, logdir=False, devices=[
#             "Android:///?cap_method=javacap&ori_method=adbori",
#     ])
import os
import sys

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

from airtest.core.api import *
from airtest.aircv import *
from paddleocr import PaddleOCR
import requests
import json
from airtest.aircv.cal_confidence import cal_ccoeff_confidence  
from airtest.core.settings import Settings as ST
import oss2  
import os  
import numpy as np
from PIL import Image, ImageFilter, ImageChops
import shutil  
import logging

import psutil

import sys_tool
from PIL import ImageGrab
import configs
import logger_config
import image_util
from server_client import PortalClient
from luhao_models import EventStatus, TaskEvent

#####################################################
# auto_setup(__file__, devices=["Android://127.0.0.1:5037/127.0.0.1:50000?ori_method=adbori"])

# auto_setup(__file__)
# connect_device("Android://127.0.0.1:5037/127.0.0.1:50000?ori_method=adbori")
auto_setup(__file__)
# connect_device("Windows:///?title_re=逆水寒手游桌面版.*")

#########################################################################
import datetime  
import random  
import string  
import re
import pyautogui
#############################################
#############################################################################全局变量
projectPath = "D:\\kkzhw\\airtest_log\\"
snapImgPath = ""
logger = None
logger2 = None
# 设置全局的截图精度为90
ST.SNAPSHOT_QUALITY = 99
# 获取当前设备的屏幕宽度和高度  
width = 1280
height = 756

center_x = 1280//2
center_y = 756//2
product_meta = []
luhao_task = None
luhao_failed = False
pic_url_arrays = []
tianniran_ocr_txt_arrays = set()
ocr_file = 1 #ocr 是否保存识别区域图片 1保存图片 0不保存
touch_fast = True
start_time = datetime.datetime.now()
end_time = datetime.datetime.now()
is_local = False
save_original_pic = False
is_debug = False
logger_queue=None
login_other_count = 0
android = None
tianniran_count = 0
ziran_pic_count = 0
waiguan_set = []
waiguan_taozhuang_set = []
is_fashi_end = False
waiguan_fashi_set = []
waiguan_ziran_fashi_set = []
waiguan_wuqi_set = []
waiguan_zuoqi_set = []
waiguan_xiangrui_set = []
waiguan_qingong_set = []
waiguan_ziran_set = []
waiguan_ts_jineng_set = []
neigong_lingyun_set = []
has_mumu = False
###############################################################################


##########################################
ocr = PaddleOCR(use_angle_cls=False, lang='ch',
                det_model_dir="models\\det\\ch_PP-OCRv4_det_infer",
                rec_model_dir="models\\rec\\ch_PP-OCRv4_rec_infer",
                cls_model_dir="models\\cls\\ch_ppocr_mobile_v2.0_cls_infer"
                )


portal_client = PortalClient()
###########################################################################

def remove_unwanted_chars(s):
    s = re.sub(r'\[[^\]]*\]', '', s) 
    return re.sub(r'[^·\u4e00-\u9fff]', '', s)
  
####################################################
def contains_digit(s):  
    return bool(re.search(r'\d', s)) 
######################################################################
def clear_folder(folder_path):
    # 文件夹路径  
    new_folder_name = f'{folder_path}_' + str(int(time.time()))
    if os.path.exists(folder_path):  
        shutil.move(folder_path, new_folder_name)  
        print(f"文件夹 '{folder_path}' 已重命名为 '{new_folder_name}'")
    os.makedirs(folder_path, exist_ok=True)
    print(f"新的空文件夹 '{folder_path}' 已创建")
##############################################################
def clear_folder2(folder_path):
    if os.path.exists(folder_path):
       return
    os.makedirs(folder_path, exist_ok=True)
    print(f"新的空文件夹 '{folder_path}' 已创建")
######################################################################
  
def generate_oss_path():  
    # 获取当前日期并格式化为 yyyyMMdd  
    today = datetime.datetime.now().strftime("%Y%m%d")
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=6))  
    # 获取当前毫秒时间戳  
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
    # 拼接路径  
    oss_path = f"{today}/"  
    return oss_path
###################################################################
def read_config(filename):  
    config = {}  
    with open(filename, 'r', encoding='utf-8') as file:  
        for line in file:  
            # 去除每行两端的空白字符，并检查是否为空行或注释行（假设以#开头的行为注释）  
            line = line.strip()  
            if not line or line.startswith('#'):  
                continue  
            # 使用等号分割键和值  
            key, value = line.split('=', 1)  
            # 去除值两端的引号（如果有的话）  
            value = value.strip('"').strip("'")  
            config[key] = value  
    return config  
########################################################################

def generate_oss_fileName():  
    # 生成六位随机字符数字串  
    # 注意：这里使用了string.ascii_letters + string.digits来确保生成的是字母和数字的混合  
    # 如果只想要数字，可以去掉string.ascii_letters部分  
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=7))  
      
    # 获取当前毫秒时间戳  
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
      
    # 拼接路径  
    oss_fileName = f"{random_str}_{current_millis}.jpg"  
      
    return oss_fileName
############################################################################


################################################
def resize_image_by_width(input_image_path, output_image_path, target_width):  
    # 打开原始图片  
    with Image.open(input_image_path) as img:  
        # 获取原始图片的宽度和高度  
        original_width, original_height = img.size  
          
        # 计算缩放比例  
        ratio = original_width / float(target_width)  
          
        # 根据缩放比例计算新的高度  
        new_height = int(original_height / ratio)  
          
        # 缩放图片  
        resized_img = img.resize((target_width, new_height), Image.LANCZOS)  
          
        # 保存缩放后的图片  
        resized_img.save(output_image_path)  
##############################################################################


#########################################################################

def upload_kk_img_to_oss(folder_path):
    for i in range(3):
        try:
            image_path_set = set()
            global pic_url_arrays
            pic_url_arrays = []

            bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称
            endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint
            access_key_id = configs.OSS_ACCESS_KEY_ID
            access_key_secret = configs.OSS_ACCESS_KEY_SECRET
            kkLogger_log(f"开始上传文件夹{folder_path} 内容")

            auth = oss2.Auth(access_key_id, access_key_secret)
            bucket = oss2.Bucket(auth, endpoint, bucket_name)
            total_count = count_images(folder_path)
            up_count  = 0
            for filename in os.listdir(folder_path):
                local_file_path = os.path.join(folder_path, filename)
                if filename.endswith('jpg') and "luhao_" not in filename:
                    up_count = up_count + 1
                    kkLogger_log(f"上传图片：{up_count}/{total_count}")
                    oss_file_path = "mall/images2/"+ generate_oss_path()

                    oss_fileName = oss_file_path+generate_oss_fileName()
                    with open(local_file_path, 'rb') as fileobj:
                        bucket.put_object(oss_fileName, fileobj)
                        image_path_set.add(oss_fileName)

                        parts = filename.split('_')
                        kkLogger_log(f"oss 文件:{oss_fileName}")
                        # new_json_object = {"name": parts[0], "value": oss_fileName}
                        if "头图_天赏外观" in filename:
                            new_json_object = {"name": "头图", "value": oss_fileName}
                            pic_url_arrays.append(new_json_object)

                            # new_json_object = {"name": parts[1], "value": oss_fileName}
                            # pic_url_arrays.append(new_json_object)
                        else:
                            new_json_object = {"name": parts[1], "value": oss_fileName}
                            pic_url_arrays.append(new_json_object)

            break
        except Exception as e:
            kkLogger_log(f"upload_kk_img_to_oss error：{traceback.format_exc()},第{i}次")
            sleep(2)
            continue
###############################################################################
def upload_one_img_to_oss(filename):
    try:
        bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称
        endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint
        access_key_id = 'LTAI5t8j4SZCrnBiFoEzXm7J'  # 替换为你的AccessKey ID
        access_key_secret = '******************************'  # 替换为你的AccessKey
        # print(f"开始上传文件夹{snapImgPath} 内容")

        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)

        local_file_path = os.path.join(snapImgPath, filename)
        oss_file_path = "mall/images2/"+ generate_oss_path()
        oss_fileName = oss_file_path+generate_oss_fileName()
        with open(local_file_path, 'rb') as fileobj:
            bucket.put_object(oss_fileName, fileobj)

        # print(f"oss_filename:{oss_fileName}")
        return oss_fileName
    except Exception as e:
        kkLogger_log(f"upload_one_img_to_oss error：{traceback.format_exc()}")

    #############################################################
def count_images(folder_path):  
    jpg_count = 0  
    png_count = 0  
    # 遍历指定文件夹及其所有子文件夹  
    for root, dirs, files in os.walk(folder_path):  
        for file in files:  
            # 检查文件扩展名  
            if file.lower().endswith('.jpg'):  
                jpg_count += 1  
#             elif file.lower().endswith('.png'):  
#                 png_count += 1  
    # 返回总数  
    total_images = jpg_count + png_count  
    return total_images  
###################################################################


############################################################################
#根据sn查询账号密码
def requestGameAccountInfo(productSn):  
    kk_url = 'https://api2.kkzhw.com/mall-portal/import/product/getProductAccountInfo?productSn='+productSn
    response = requests.get(kk_url)
    # 检查请求是否成功  
    if response.status_code == 200:  
        # 打印返回的内容  
        kkLogger_log(response.text)
        responseObj = json.loads(response.text)
        product_dict = responseObj.get('data')
        return product_dict
    
########################################################################
#根据分类ID获取待录号productSn
def requestCategoryProductSn(categoryId):  
    kk_url = f'http://api2.kkzhw.com/mall-portal/import/product/getTheProductAccountInfo?categoryId={categoryId}'
    response = requests.get(kk_url)
    # 检查请求是否成功  
    if response.status_code == 200:  
        # 打印返回的内容  
#         print(response.text)
        responseObj = json.loads(response.text)
        productSn = responseObj.get('data')
        return productSn
    else:
        return ""
###########################################################################
def requestGameProductCategoryMeta(categoryId): 
    kk_url = f'https://api2.kkzhw.com/mall-portal/openapi/record/product/meta?categoryId={categoryId}'
    response = requests.get(kk_url)
    # 检查请求是否成功
    if response.status_code == 200:  
        # 打印返回的内容
        responseObj = json.loads(response.text)
        product_meta = responseObj.get('data')
        return product_meta
###################################################################根据sn查询账号密码


##OCR TOUCH#########################################ffffffffff##########
def ocr_touch(target_text) :
      # 截屏当前画面
    pic_path=f"{snapImgPath}\\now.png"
    snapshot(pic_path)
    
     # 使用PaddleOCR识别图片文字
    ocr_result = ocr.ocr(pic_path, cls=False)
    
    # 遍历识别结果，找到目标文字的坐标
    target_coords = None
    for line in ocr_result:
        for word_info in line:
            #获取识别结果的文字信息
            textinfo = word_info[1][0]
            kkLogger_log(textinfo)
            if target_text in textinfo:
                # 获取文字的坐标（中心点）
                x1, y1 = word_info[0][0]
                x2, y2 = word_info[0][2]
                target_coords = ((x1 + x2) / 2, (y1 + y2) / 2)
                break
        if target_coords:
            break

    # 使用Airtest..坐标
    if target_coords:
        touch(target_coords)
    else:
        kkLogger_log(f"ocr_touch 未找到目标文字：{target_text}")
#####################################################################


##ocr text####################################################################
def ocr_text_all():
    for i in range(5):
        try:
            sleep(1)
            pic_path=f"{snapImgPath}\\now.png"
            snapshot(pic_path)
            ocr_result = ocr.ocr(pic_path, cls=False)
            target_coords = None
            for line in ocr_result:
                for word_info in line:
                    textinfo = word_info[1][0]
            return ocr_result
        except Exception as e:
            kkLogger_log(f"ocr_text_all 失败：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
############################################################

def find_all_subImg(temp, start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            find_result =  temp.match_all_in(local_screen)

            kkLogger_log(f"find_all_subImg：{find_result}")

            if find_result:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_all_subImg_{current_millis}.png')
            return find_result
        except Exception as e:
            kkLogger_log(f"find_all_subImg error：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
###################################################
def find_subImg(temp, start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            # sleep(0.5)
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            find_result = temp.match_in(local_screen)

            # kkLogger_log(f"find_subImg：{find_result}")

            if ocr_file == 1:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\plog_find_subImg_{current_millis}.png')
            return find_result
        except Exception as e:
            kkLogger_log(f"find_subImg 失败：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
########################################################
def find_subImg_inscreen(temp,local_screen):

    for i in range(5):
        try:
            # sleep(0.5)
            find_result = temp.match_in(local_screen)
            kkLogger_log(f"find_subImg_inscreen：{find_result}")
            if find_result and ocr_file == 1:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_subImg_inscreen_{current_millis}.png')
            return find_result
        except Exception as e:
            kkLogger_log(f"find_subImg_inscreen 失败：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
#########################################################
def find_first_subImg(temp, start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            sleep(0.5)
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            f_result = temp.match_all_in(local_screen)
            kkLogger_log(f"find_first_subImg：{f_result}")

            if f_result:
                #保存为图片
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_first_subImg_{current_millis}.png')

                print(f"f_result:{f_result}")  
                min_y = f_result[0]['result'][1]  # 假设f_result的第一个元素的第二个值是y坐标  
                min_y_item = f_result[0]['result']  

                # 遍历f_result找到y坐标最小的匹配项  
                for item in f_result:  
                    # 假设item是一个包含(x, y, w, h)的元组  
                    current_y = item['result'][1]
                    if current_y < min_y:  
                        min_y = current_y
                        min_y_item = item['result']
                print("Y坐标最小的匹配项:", min_y_item)  
                print("Y坐标:", min_y)  
                return min_y_item
            else:
                return None
        except Exception as e:
            kkLogger_log(f"find_first_subImg 失败：{traceback.format_exc()}")
            sleep(2)
            continue  
    return None
#######################################################################
def ocr_text_inverted(start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()
            if screen is None:
                sleep(0.5)
                screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            cropped_gray_screen_rgb = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            cropped_screen_rgb = cv2.cvtColor(cropped_gray_screen_rgb, cv2.COLOR_BGR2RGB)
            inverted_cropped_screen = 255 - cropped_screen_rgb
            ocr_result = ocr.ocr(inverted_cropped_screen, cls=False)
            if ocr_file == 1:
                pil_img = cv2_2_pil(inverted_cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_now_ocr_{current_millis}.png")

            return ocr_result
        except Exception as e:
            kkLogger_log(f"ocr_text error：{traceback.format_exc()}")
            sleep(2)
            continue
    return None

##############################################################
def local_ocr_text(start_x,start_y,end_x,end_y):
    for i in range(2):
        try:
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_util.capture_screen( f"{snapImgPath}\\local_ocr_text_{current_millis}.png",region=(start_x, start_y, end_x, end_y))
            ocr_result = ocr.ocr(f"{snapImgPath}\\local_ocr_text_{current_millis}.png",cls=False)
            if ocr_result is None:
                return None
            return ocr_result
        except Exception as e:
            kkLogger_log(f"ocr_text error：{traceback.format_exc()}")
            sleep(2)
            continue
    return None


##############################################################
def ocr_text(start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()
            if screen is None:
                sleep(0.5)
                screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            gray_cropped_screen = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            ocr_result = ocr.ocr(gray_cropped_screen, cls=False)

            if ocr_file == 1:
                pil_img = cv2_2_pil(gray_cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_now_ocr_{current_millis}.png")

            return ocr_result
        except Exception as e:
            kkLogger_log(f"ocr_text error：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
###############################################################################
def ocr_text_all(target_text,start_x,start_y,end_x,end_y) :
    for i in range(5):
        try:
            if touch_fast is False:
                sleep(0.5)
              # 截屏当前画面
            pic_path=f"{snapImgPath}\\now_orc_all.png"
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            pil_img = cv2_2_pil(cropped_screen)
            pil_img.save(f"{snapImgPath}\\now_orc_all.png")

             # 使用PaddleOCR识别图片文字
            ocr_result = ocr.ocr(pic_path, cls=False)
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            target_coords_list = []
            for line in ocr_result:
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"ocr_text_all 识别到{textinfo}")
                    # if target_text in textinfo:
                    if any(t.strip() in textinfo for t in target_text.split(',')):
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        target_coords_list.append(target_coords)
            kkLogger_log(f"识别到{target_text},数量：{len(target_coords_list)}")
            return target_coords_list
        except Exception as e:
            kkLogger_log(f"ocr_text_all error：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
################################################################################
def ocr_text_target_coords(target_text,start_x,start_y,end_x,end_y,is_wait=True) :
    if touch_fast is False and is_wait is True:
        sleep(0.2)
    for i in range(2):
        try:  
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_target_coords_{target_text}_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=False)
            if ocr_result is None:
                return None
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"识别到文字：{textinfo}")
                    # if target_text in textinfo:
                    if any(t.strip() in textinfo for t in target_text.split(',')):
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        break
                if target_coords:
                    break

            # 使用Airtest..坐标
            if target_coords:
                return target_coords
            else:
                kkLogger_log(f"ocr_text_target_coords 未找到目标文字：{target_text}")
                return target_coords
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_target_coords error：{traceback.format_exc()}")
            continue
            
    return None
#########################################################################
def calculate_similarity(image1, image2):
    diff = ImageChops.difference(image1, image2)
    diff = diff.convert('L')
    mean_diff = diff.getextrema()[1] / 255.0
    return mean_diff

def long_snapshot(mode_name,start_x,start_y,end_x,end_y,scroll_height,waite_time=1.5,same=0.8):
    window_width = end_x - start_x
    window_height = end_y - start_y

    try:
        images = []
        for i in range(50):
            screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            images.append(screenshot)
            pyautogui.scroll(-scroll_height)
            sleep(waite_time)
            new_screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            similarity = calculate_similarity(new_screenshot, screenshot)
            if (1-similarity) > same:
                break
        total_width = window_width
        total_height = sum(img.height for img in images)
        long_image = Image.new('RGB', (total_width, total_height))

        y_offset = 0
        for img in images:
            long_image.paste(img, (0, y_offset))
            y_offset += img.height

        long_image.save(f'{snapImgPath}\\{mode_name}_其他物品.jpg')
    except Exception as e:
        kkLogger_log(f"long_snapshot error：{traceback.format_exc()}")
############################################################################################
def long_snapshot_ziran(mode_name,start_x,start_y,end_x,end_y,scroll_height,waite_time=1.5,same=0.8):
    kktouch3(352,473,1,"空白")
    pyautogui.scroll(1000)
    sleep(5)
    window_width = end_x - start_x
    window_height = end_y - start_y

    try:
        images = []
        screenshot = pyautogui.screenshot(region=(175, 174, window_width, 715-window_height-174))
        images.append(screenshot)
        for i in range(50):
            screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            images.append(screenshot)

            pyautogui.scroll(-scroll_height)
            sleep(waite_time)
            new_screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            similarity = calculate_similarity(new_screenshot, screenshot)

            print(1-similarity)
            kktouch3(480,523,0.3,"空白")
            if (1-similarity) > same or wait_for_ocr_txt("更多",2,521, 35,750,75):
            # if (1-similarity) > same:
                break
            else:
                kktouch3(480,221,0.1,"")

        sleep(4)
        kktouch3(236, 523, 0.3, "空白")
        if not wait_for_ocr_txt("更多",2,521, 35,750,75):
            screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            images.append(screenshot)

        total_width = window_width
        total_height = sum(img.height for img in images)
        long_image = Image.new('RGB', (total_width, total_height))

        y_offset = 0
        print(len(images))
        for i, img in enumerate(images):
            long_image.paste(img, (0, y_offset))
            y_offset += img.height

        long_image.save(f'{snapImgPath}\\{mode_name}_其他物品.jpg')
    except Exception as e:
        kkLogger_log(f"long_snapshot error：{traceback.format_exc()}")
#########################################################################
############################################################################################
def long_snapshot_qunxia(mode_name,start_x,start_y,end_x,end_y,scroll_height,waite_time=1.5,same=0.8):
    window_width = end_x - start_x
    window_height = end_y - start_y

    try:
        images = []
        screenshot = pyautogui.screenshot(region=(41, 162, window_width, 708-window_height-95))
        images.append(screenshot)
        for i in range(50):
            screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            images.append(screenshot)

            pyautogui.scroll(-scroll_height)
            sleep(waite_time)
            new_screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            similarity = calculate_similarity(new_screenshot, screenshot)

            print(1-similarity)
            if (1-similarity) > same:
                break

        total_width = window_width
        total_height = sum(img.height for img in images)
        long_image = Image.new('RGB', (total_width, total_height))

        y_offset = 0
        print(len(images))
        for i, img in enumerate(images):
            # if len(images) == 3 and i == len(images) - 2:
            #     img = img.crop((0, 0, img.width, img.height-130))
            # elif len(images) > 5 and i == len(images) - 2:
            #     img = img.crop((0, 0, img.width, img.height - 82))
            # if len(images) > 3 and i == len(images) - 2:
            #     img = img.crop((0, 40, img.width, img.height))
            # elif len(images) == 3 and i == len(images) - 2:
            #     img = img.crop((0, 130, img.width, img.height))
            long_image.paste(img, (0, y_offset))
            y_offset += img.height

        long_image.save(f'{snapImgPath}\\{mode_name}_其他物品.jpg')
    except Exception as e:
        kkLogger_log(f"long_snapshot error：{traceback.format_exc()}")
###########################################################################
def long_snapshot2(mode_name,start_x,start_y,end_x,end_y,scroll_height,waite_time=1.5,same=0.7):
    window_width = end_x - start_x  # 窗口宽度
    window_height = end_y - start_y  # 窗口高度

    try:
        # 存储截图列表
        images = []
        for i in range(50):
            # 截图
            screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            images.append(screenshot)

            # 滚动窗口
            pyautogui.scroll(-scroll_height)  # 向下滚动
            sleep(waite_time)  # 等待滚动完成

            # 再次截图，检查是否有新的内容出现
            new_screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))

            # 计算两次截图的相似度
            similarity = calculate_similarity(new_screenshot, screenshot)
            # print(f"===================={1-similarity}")
            if (1 - similarity) > same:
                break

        # 拼接图片
        total_width = window_width
        total_height = sum(img.height for img in images)
        long_image = Image.new('RGB', (total_width, total_height))

        y_offset = 0
        for img in images:
            long_image.paste(img, (0, y_offset))
            y_offset += img.height

        # 保存长截图
        long_image.save(f'{snapImgPath}\\{mode_name}_其他物品.jpg')
    except Exception as e:
        kkLogger_log(f"long_snapshot error：{traceback.format_exc()}")
###############################################################
def ocr_text_target_coords_arrays(target_text,start_x,start_y,end_x,end_y,hit_txts=None) :
    if touch_fast is False:
        sleep(0.5)
    target_coords_arrays = []
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            gray_cropped_screen = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            if ocr_file == 1:
                pil_img = cv2_2_pil(gray_cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_target_coords_arrays_{target_text}_{current_millis}.png")

            ocr_result = ocr.ocr(gray_cropped_screen, cls=False)
            if ocr_result is None:
                return target_coords_arrays
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"o_t_t_c_a识别到文字：{textinfo}","info")
                    # if target_text in textinfo:
                    if any(t.strip() in textinfo for t in target_text.split(',')):
                        if hit_txts is not None:
                            hit_txts.append(textinfo.strip())
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        target_coords_arrays.append(target_coords)

            # 使用Airtest..坐标
            if len(target_coords_arrays) > 0:
                return target_coords_arrays
            else:
                kkLogger_log(f"ocr_text_target_coords_arrays 未找到目标文字：{target_text}")
                return target_coords_arrays
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_target_coords_arrays error：{traceback.format_exc()}")
            continue
            
    return target_coords_arrays

########################################################################
###############################################################################
def ocr_text_target_coords_arrays_inverted(target_text, start_x, start_y, end_x, end_y, hit_txts=None):
    if touch_fast is False:
        sleep(0.5)
    target_coords_arrays = []
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            cropped_screen_rgb = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            inverted_cropped_screen = 255 - cropped_screen_rgb

            if ocr_file == 1:
                pil_img = cv2_2_pil(inverted_cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_target_coords_arrays_{target_text}_{current_millis}.png")

            ocr_result = ocr.ocr(inverted_cropped_screen, cls=False)
            if ocr_result is None:
                return target_coords_arrays
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    # 获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"o_t_t_c_a识别到文字：{textinfo}", "info")
                    # if target_text in textinfo:
                    if any(t.strip() in textinfo for t in target_text.split(',')):
                        if hit_txts is not None:
                            hit_txts.append(textinfo.strip())
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x + (x1 + x2) / 2, start_y + (y1 + y2) / 2)
                        target_coords_arrays.append(target_coords)

            # 使用Airtest..坐标
            if len(target_coords_arrays) > 0:
                return target_coords_arrays
            else:
                kkLogger_log(f"ocr_text_target_coords_arrays 未找到目标文字：{target_text}")
                return target_coords_arrays
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_target_coords_arrays error：{traceback.format_exc()}")
            continue

    return target_coords_arrays
##ocr text#################################################################
def local_ocr_text_has_txt(start_x, start_y, end_x, end_y):
    if touch_fast is False:
        sleep(0.5)

    for i in range(2):
        try:
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_util.capture_screen(
                f"{snapImgPath}\\local_ocr_text_has_txt_{current_millis}.png",
                region=(start_x, start_y, end_x, end_y))

            ocr_result = ocr.ocr(f"{snapImgPath}\\local_ocr_text_has_txt_{current_millis}.png",
                                 cls=False)

            if ocr_result is None:
                return False

            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    # 获取识别结果的文字信息
                    txt = word_info[1][0]
                    if txt is not None and len(txt) > 0:
                        return True

            return False
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_number error：{traceback.format_exc()}")
            continue

    return False


################################################################################
def ocr_text_has_txt_inverted(start_x, start_y, end_x, end_y):
    if touch_fast is False:
        sleep(0.5)

    for i in range(2):
        try:
            screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            cropped_screen_rgb = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            inverted_cropped_screen = 255 - cropped_screen_rgb

            if ocr_file == 1:
                pil_img = cv2_2_pil(inverted_cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_has_txt_{current_millis}.png")

            ocr_result = ocr.ocr(inverted_cropped_screen, cls=False)
            if ocr_result is None or ocr_result is [None]:
                return False
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    txt = word_info[1][0]
                    if txt is not None and len(txt) > 0:
                        return True
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_txt error：{traceback.format_exc()}")
            continue

    return False
###########################################################################################

def ocr_text_has_txt(start_x, start_y, end_x, end_y):
    if touch_fast is False:
        sleep(0.5)

    for i in range(2):
        try:
            screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            # gray_cropped_screen = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_has_txt_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=False)
            # print(f"==============={ocr_result}")
            if ocr_result is None or ocr_result is [None]:
                return False
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    txt = word_info[1][0]
                    if txt is not None and len(txt) > 0:
                        return True
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_txt error：{traceback.format_exc()}")
            continue

    return False
#################################################
def local_ocr_text_has_number(start_x, start_y, end_x, end_y):
    if touch_fast is False:
        sleep(0.5)

    for i in range(2):
        try:
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_util.capture_screen(
                f"{snapImgPath}\\local_ocr_text_has_number_{current_millis}.png",
                region=(start_x, start_y, end_x, end_y))

            ocr_result = ocr.ocr(f"{snapImgPath}\\local_ocr_text_has_number_{current_millis}.png",
                                 cls=False)

            if ocr_result is None:
                return False

            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    # 获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    if bool(re.search(r'\d', textinfo)):
                        kkLogger_log(f"ocr_text_has_number查找到数字：{textinfo}")
                        return True

            return False
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_number error：{traceback.format_exc()}")
            continue

    return False

############################################
def ocr_text_has_number(start_x,start_y,end_x,end_y) :
    if touch_fast is False:
        sleep(0.5)
    
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_has_number_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=False)
            if ocr_result is None:
                return False
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    if bool(re.search(r'\d', textinfo)):
                        kkLogger_log(f"ocr_text_has_number查找到数字：{textinfo}")
                        return True

            return False
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_number error：{traceback.format_exc()}")
            continue
            
    return False
####################################################
##全屏截图方法，指定区域打码，去掉UUID
def save_cropped_screen_with_blur2(modename,start_x,start_y,end_x,end_y, x1, y1, x2, y2):
    sleep(1)
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    for i in range(5):
        try:
            kkLogger_log(f"全屏截图，部分打码：{modename}")
            blur_region = (x1-start_x, y1-start_y, x2-start_x, y2-start_y)
            screen = G.DEVICE.snapshot()

            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            pil_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(pil_img)

            region_to_blur = pil_img.crop(blur_region)

            region_to_blur = region_to_blur.filter(ImageFilter.GaussianBlur(radius=20))

            pil_img.paste(region_to_blur, blur_region)

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg', 'JPEG')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_screen_with_mulblur error：{traceback.format_exc()}")
            continue

###################################################################
##全屏截图方法，指定区域打码，去掉UUID
def save_cropped_screen_with_blur(modename, x1, y1, x2, y2):  
    sleep(1)
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    for i in range(5):
        try:  
            kkLogger_log(f"全屏截图，部分打码：{modename}")
            blur_region = (x1, y1, x2, y2)
            screen = G.DEVICE.snapshot()  
            
            cropped_screen = aircv.crop_image(screen, (1, 40, width, height))

            pil_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(pil_img)

            region_to_blur = pil_img.crop(blur_region)

            region_to_blur = region_to_blur.filter(ImageFilter.GaussianBlur(radius=20))

            pil_img.paste(region_to_blur, blur_region)

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg', 'JPEG')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_screen_with_mulblur error：{traceback.format_exc()}")
            continue
#########################################################
def kk_keyevent(key,times=1,sleep_time=0.8,msg=None):
    check_nshm_exits()
    for i in range(times):
        keyevent(key)
        kkLogger_log(f"kk_keyevent：{key},msg:{msg}")
        sleep(sleep_time)
################################################################
def kk_scroll(coords,wheel_dist,sleep_time= 0.9,msg=None):
    device().mouse.scroll(coords=coords, wheel_dist=wheel_dist)
    sleep(sleep_time)
    if msg is not None:
        kkLogger_log(f"kk_scroll wheel_dist：{coords},wheel_dist:{wheel_dist},msg:{msg}")
##################################################
is_gonggao_checked = False
is_zhuangyuan_gonggao_checked = False
is_game_update_checked = False
def check_and_try_game_home(attempts=4,step=None):
    kkLogger_log(f"check_and_try_game_home: {attempts}")
    kktouch3(924,689,1,"空白")
    kktouch3(924,689,1,"空白")
    #两次归零
    kk_keyevent("{ESC}", 2, 0.1, "回主页")
    if is_local is False and step is None:
        check_nshm_exits()

        check_yueka_jiazeng(1)
        check_huodong()
        check_game_update(1)
        check_game_alert_note()

    check_account_login_other()
    if attempts <= 0:
        kkLogger_log(f"尝试回到主页次数用完")
        return False

    #尝试唤出菜单
    kk_keyevent("{ESC}", 1, 0.5, "回主页2")
    for j in range(10):
        if local_ocr_text_target_coords("外", 1003, 87, 1231, 254) is not None:
            #关闭菜单
            kk_keyevent("{ESC}", 1, 0.5, "回主页2")
            return True
        else:
            kkLogger_log(f"尝试回到主页，第 {j}/10次")
            kk_keyevent("{ESC}", 1, 0.5, "回主页2")
            continue

    return False
###########################################################################
def save_cropped_screen_with_mulblur(modename, blur_regions):  
    sleep(1)
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    for i in range(5):
        try:  
            kkLogger_log(f"全屏截图，部分打码：{modename}")

            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (8, 39, width, height))
            
            pil_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(pil_img)

            # 遍历所有需要模糊的区域  
            for blur_region in blur_regions:  
                x1, y1, x2, y2 = blur_region  
                # 确保区域在图片范围内  
                x1, y1, x2, y2 = max(0, x1), max(0, y1), min(width, x2), min(height, y2)  
                # 裁剪出需要模糊的区域  
                region_to_blur = pil_img.crop((x1, y1, x2, y2))  
                # 应用高斯模糊  
                region_to_blur = region_to_blur.filter(ImageFilter.GaussianBlur(radius=20))  
                # 将模糊后的区域粘贴回原图  
                pil_img.paste(region_to_blur, (x1, y1))  

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg', 'JPEG')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_screen_with_mulblur error：{traceback.format_exc()}")
            continue
##全屏截图方法，去掉UUID
def save_cropped_screen(modename,file_endfix=".jpg"):
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    sleep(0.5)
    for i in range(2):
        try:  
            kkLogger_log(f"全屏截图：{modename}")

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            if width > 1290:
                image_util.capture_screen(f'{snapImgPath}\\{modename}_{current_millis}{file_endfix}',
                                          region=(7, 40, 1280, 720))
            else:
                image_util.capture_screen(f'{snapImgPath}\\{modename}_{current_millis}{file_endfix}',
                                          region=(7, 40, width, height))

            return f"{modename}_{current_millis}{file_endfix}"
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_screen error：{traceback.format_exc()},尺寸:{width},{height}")


    
###################################################################
def save_cropped_area_screen(modename,start_x,start_y,end_x,end_y,sleep_time=0.8,file_endfix=".jpg"):
    if sleep_time > 0:
        sleep(sleep_time)
    else:
        sleep(0.1)
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    for i in range(2):
        try:  
            kkLogger_log(f"区域截图：{modename}")
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_util.capture_screen(f'{snapImgPath}\\{modename}_{current_millis}{file_endfix}',
                region=(start_x, start_y, end_x, end_y))
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen error 截图失败：{traceback.format_exc()}")
            continue
#############################################################################################
def save_cropped_area_screen_dazao(modename,start_x,start_y,end_x,end_y):
    # sleep(1)
    for i in range(5):
        try:  
            kkLogger_log(f"区域截图：{modename}")
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            
            tile_img_2 = np.array(pil_img)
            if contains_purple_in_corner(tile_img_2,(55,29)):
                pil_img.save(f'{snapImgPath}\\{modename}_1_{current_millis}.jpg')
            elif contains_gold_in_corner(tile_img_2,(55,29)) and not contains_pink_in_corner(tile_img_2,(55,29)):
                pil_img.save(f'{snapImgPath}\\{modename}_0_{current_millis}.jpg')
            else:
                pil_img.save(f'{snapImgPath}\\{modename}_2_{current_millis}.jpg')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen error：{traceback.format_exc()}")
            continue
######################################################################################
def is_close_match(s1, s2):
    """
    检查两个字符串是否至多有一个字符不同。
    """
    if len(s1) != len(s2) or len(s1) < 3:
        return False

    diff_count = 0
    for c1, c2 in zip(s1, s2):
        if c1 != c2:
            diff_count += 1
            if diff_count > 1:
                return False

    return True

##########################################################
def find_close_match(input_str, string_array):
    """
    在字符串数组中查找与输入字符串至多有一个字符不同的字符串。
    """
    for target in string_array:
        if len(input_str) == len(target) and is_close_match(input_str, target):
            return target
    return None

######################################################################
tianniran_saved_hashes = set()  
def save_cropped_area_screen_tianniran(modename,start_x,start_y,end_x,end_y):
    sleep(1)
    for i in range(5):
        try:  
            kkLogger_log(f"save_cropped_area_screen_tianniran区域截图：{modename}")
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)
            
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen_tianniran error：{e}")
            continue
##############################################################
# def contains_color(start_x, start_y, end_x, end_y, color_hex):
#     try:
#         h_8bit, s_8bit, v_8bit = hex_to_8bit_hsv(color_hex)
#
#         screen = G.DEVICE.snapshot()
#         cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
#         hsv_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2HSV)
#
#         lower_bound = np.array([h_8bit, s_8bit, v_8bit], dtype=np.uint8) - 10  # 允许一些容差
#         upper_bound = np.array([h_8bit, s_8bit, v_8bit], dtype=np.uint8) + 10
#         # print(hsv_img)
#         # print("------------------")
#         # print(h_8bit, s_8bit, v_8bit)
#         # 应用颜色范围掩码
#         mask = cv2.inRange(hsv_img, lower_bound, upper_bound)
#         return np.any(mask != 0)
#     except Exception as e:
#         print(f"contains_color:{e}")
#         return False
#################################################################
def hex_to_8bit_hsv(hex_color):
    r, g, b = hex_to_rgb(hex_color)
    h, s, v = rgb_to_hsv(r, g, b)

    # 将H从0-360度映射到0-255
    h_8bit = int(h * 255 / 360)

    # 将S和V从0-1映射到0-255
    s_8bit = int(s * 255)
    v_8bit = int(v * 255)

    return h_8bit, s_8bit, v_8bit

def hex_to_rgb(hex_color):
    # 去掉#号，将16进制颜色转换为RGB
    hex_color = hex_color.lstrip('#')
    rgb = tuple(int(hex_color[i:i + 2], 16) for i in (0, 2, 4))
    return rgb


def rgb_to_hsv(r, g, b):
    # 标准的RGB到HSV转换
    r, g, b = r / 255.0, g / 255.0, b / 255.0
    cmax = max(r, g, b)
    cmin = min(r, g, b)
    delta = cmax - cmin

    if delta == 0:
        h = 0
    elif cmax == r:
        h = (60 * ((g - b) / delta) + 360) % 360
    elif cmax == g:
        h = (60 * ((b - r) / delta) + 120) % 360
    elif cmax == b:
        h = (60 * ((r - g) / delta) + 240) % 360

    s = 0 if cmax == 0 else delta / cmax
    v = cmax

    return h, s, v


def hex_to_8bit_hsv(hex_color):
    r, g, b = hex_to_rgb(hex_color)
    h, s, v = rgb_to_hsv(r, g, b)

    # 将H从0-360度映射到0-255
    h_8bit = int(h * 255 / 360)

    # 将S和V从0-1映射到0-255
    s_8bit = int(s * 255)
    v_8bit = int(v * 255)

    return h_8bit, s_8bit, v_8bit
######################################################################
def save_cropped_area_screen_and_ocr_add_to_get(modename,start_x,start_y,end_x,end_y,split_num=None,temp=None):
    check_game_alert_note()

    if touch_fast is False:
        sleep(0.5)
    else:
        sleep(0.1)
    has_lock = False
    is_end = False

    if (modename == "发式" and len(waiguan_fashi_set) > 1
            and ("踏歌" in waiguan_fashi_set or "俏也" in waiguan_fashi_set)):
        is_end = True
    if modename == "套装" and len(waiguan_taozhuang_set) > 1 and "自在裳" in waiguan_taozhuang_set:
        is_end = True


    for i in range(2):
        try:  
            kkLogger_log(f"区域截图：{modename}")

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_util.capture_screen(
                f"{snapImgPath}\\save_cropped_area_screen_and_ocr_add_to_get_{current_millis}.png",
                region=(start_x, start_y, end_x, end_y))
            pil_img = Image.open(f"{snapImgPath}\\save_cropped_area_screen_and_ocr_add_to_get_{current_millis}.png")

            if split_num is not None:
                # 获取图像的尺寸  
                img_width, img_height = pil_img.size  
                # 计算切割后的小图片尺寸（这里假设均匀切割）  
                tile_width = img_width // split_num[1]  
                tile_height = img_height // split_num[0]
                # 切割图像并保存  
                for row in range(split_num[0]):
                    for col in range(split_num[1]):
                        if is_end:
                            continue
                        left = col * tile_width
                        upper = row * tile_height  
                        right = (col + 1) * tile_width
                        lower = (row + 1) * tile_height
                        tile_img = pil_img.crop((left, upper, right, lower))

                        tile_img_ocr = pil_img.crop((left, lower-60, right, lower))
                        tile_img_ocr2 = pil_img.crop((left, lower-30, right, lower))

                        tile_img_2 = np.array(tile_img)
                        tile_img_2_ocr = np.array(tile_img_ocr)
                        tile_img_2_ocr2 = np.array(tile_img_ocr2)

                        if temp is not None and find_subImg_inscreen(temp,tile_img_2) is not None:
                            kkLogger_log(f"find_subImg_inscreen----- 发现锁")
                            continue
                        #是否无锁，是否有文字
                        result = (True,False)

                        try:
                            inverted_tile_img_2_ocr = 255 - tile_img_2_ocr
                            inverted_tile_img_2_ocr = np.clip(inverted_tile_img_2_ocr, 0, 255).astype(np.uint8)
                            result = ocr_screen_add_to_get(modename,inverted_tile_img_2_ocr)
                        except Exception as e:
                            kkLogger_log(f"ocr_screen_add_to_get2 error {traceback.format_exc()}")

                        #是否无锁
                        if result[0] is False:
                            has_lock = True
                            continue

                        try:
                            result = ocr_screen_add_to_get(modename, tile_img_2_ocr2,True)
                        except Exception as e:
                            kkLogger_log(f"ocr_screen_add_to_get3 error {traceback.format_exc()}")

                        if result[1] is False:
                            continue

                        current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
                        if contains_yellow_in_corner(tile_img_2,(15,15)):
                            # 保存切割后的小图片  
                            tile_img.save(f'{snapImgPath}\\{modename}_天赏外观_0_{current_millis}{row}{col}.jpg')
                        else:
                            tile_img.save(f'{snapImgPath}\\{modename}_普通外观_1_{current_millis}{row}{col}.jpg')

                        if modename == "祥瑞" and len(waiguan_xiangrui_set) > 1 and "空山闲" in waiguan_xiangrui_set:
                            is_end = True
                            break
                        if (modename == "发式" and len(waiguan_fashi_set) > 1
                                and ("踏歌" in waiguan_fashi_set or "俏也" in waiguan_fashi_set)):
                            is_end = True
                            break
                        if modename == "套装" and len(waiguan_taozhuang_set) > 1 and "自在裳" in waiguan_taozhuang_set:
                            is_end = True
                            break

            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen_and_ocr_add_to_get error：{e}")
            continue

    return has_lock
###############################################################################################################
def contains_pink_in_corner(tile_img_np, region_size):  
    try:  
        height, width, _ = tile_img_np.shape  

        x_end = region_size[0] 
        y_end = region_size[1]  
        corner_img = tile_img_np[22:y_end, 10:x_end]
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  

        lower_gold = np.array([233, 103, 94])  # 金黄色的下限（色调、饱和度、明度）  
        upper_gold = np.array([234, 108, 102])  # 金黄色的上限  

        mask = cv2.inRange(hsv_img, lower_gold, upper_gold)

        return np.any(mask != 0)  
    except Exception as e:  
        return False
########################################################
def contains_gold_in_corner(tile_img_np, region_size):  
    try:  
        height, width, _ = tile_img_np.shape  

        x_end = region_size[0] 
        y_end = region_size[1]  
        corner_img = tile_img_np[22:y_end, 10:x_end]
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  

        lower_gold = np.array([15, 100, 100])  # 金黄色的下限（色调、饱和度、明度）  
        upper_gold = np.array([45, 255, 255])  # 金黄色的上限  

        mask = cv2.inRange(hsv_img, lower_gold, upper_gold)

        return np.any(mask != 0)  
    except Exception as e:  
        return False
###########################################################
def contains_orange_in_corner(tile_img_np, region_size):
    try:
        # 获取图像的高度、宽度和通道数
        height, width, _ = tile_img_np.shape

        # 定义要检查的角落区域的尺寸
        x_end = region_size[0]
        y_end = region_size[1]
        # 从图像的指定角落提取子图像
        corner_img = tile_img_np[22:y_end, 10:x_end]

        # 将图像从RGB颜色空间转换为HSV颜色空间
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)

        # 定义橘黄色的HSV阈值范围
        # 这些值可能需要根据实际的图像情况进行调整
        lower_orange = np.array([10, 100, 100])  # 橘黄色的下限（色调、饱和度、明度）
        upper_orange = np.array([25, 255, 255])  # 橘黄色的上限

        # 创建一个掩码，用于标记橘黄色区域
        mask = cv2.inRange(hsv_img, lower_orange, upper_orange)

        # 检查掩码中是否存在非零像素，即是否包含橘黄色
        return np.any(mask != 0)
    except Exception as e:
        # 如果发生任何异常，返回False
        return False
###############################################################################
def contains_purple_in_corner(tile_img_np, region_size):  
    try:  
        height, width, _ = tile_img_np.shape  

        x_end = region_size[0]  # 左上角区域的右边界  
        y_end = region_size[1]  # 左上角区域的下边界  

        corner_img = tile_img_np[0:y_end, 0:x_end]  
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  

        lower_purple = np.array([120, 30, 150])  # 紫色下限（色调、饱和度、明度）  
        upper_purple = np.array([160, 200, 255])  # 紫色上限（注意：HSV色调是循环的，所以180接近0）  

        # 创建紫色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_purple, upper_purple)  

        return np.any(mask != 0)  
    except Exception as e:  
        return False

###############################################################################
def contains_yellow_in_corner(tile_img_np, region_size):   
    try:
        height, width, _ = tile_img_np.shape  
        # 计算右下角区域的左上角坐标  
        x_start = width-10 - region_size[0]
        y_start = height-10 - region_size[1]
        corner_img = tile_img_np[y_start:y_start+region_size[1], x_start:x_start+region_size[0]]  
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  
        # lower_yellow = np.array([15, 100, 100])
        lower_yellow = np.array([15, 100, 150])
        upper_yellow = np.array([35, 255, 255])

        # 创建黄色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_yellow, upper_yellow)  

        # 检查掩码中是否存在非零像素，以确定是否存在黄色  
        return np.any(mask != 0)  
    except Exception as e:
        return False
#################################################################
def contains_yellow(start_x,start_y,end_x,end_y):
    try:
        screen = G.DEVICE.snapshot()
        cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
        hsv_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2HSV)

        lower_yellow = np.array([15, 100, 100])  
        upper_yellow = np.array([45, 255, 255])


        # 创建黄色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_yellow, upper_yellow)  

        # 检查掩码中是否存在非零像素，以确定是否存在黄色  
        return np.any(mask != 0)  
    except Exception as e:
        print(e)
        return False
###############################################################################
#识别到的名称放入meta中
def add_to_get(ocr_text):
    return ocr_check_and_add(ocr_text)
    
#####################################################################
def add_to_json_array(json_array, name, value):
    new_json_object = {"name": name, "value": value}
    json_array.append(new_json_object)  
    return json_array
################################################

def kktouch(x, y, sleep_time):  
    touch((x,y))
    sleep(sleep_time)
######################################################

############################################################################
def kktouch_step(x, y, sleep_time,msg):
    if msg == "外观":
        kk_keyevent("{F12}")
        if ocr_text_target_coords("时", 198, 690, 358, 740) is not None:
            kkLogger_log(f"快捷键前往：{msg}，成功")
            return True
        else:
            kkLogger_log(f"快捷键前往：{msg}，失败，尝试..前往")
            return kktouch2(x, y, sleep_time, msg)
    if msg == "角色":
        return kktouch2(x, y, sleep_time, msg)
    if msg == "打造":
        kk_keyevent("{F10}")
        kktouch2(106,684,1,"快捷打造")
        sync_current_snapshot("开始打造。")
        if ocr_text_target_coords("打", 95, 42,187, 102) is not None:
            kkLogger_log(f"快捷键前往：{msg}，成功")
            return True
        else:
            kkLogger_log(f"快捷键前往：{msg}，失败，尝试..前往")
            return kktouch2(x, y, sleep_time, msg)
####################################################################
step_names = ["菜单","外观","角色","打造","武功","设置","开放世界","宠物","群侠","庄园"]
#################################################################################
def check_nshm_exits():
    if is_local:
        return True

    if wait_for_ocr_txt("手游",20,33,2,145,36):
        return True
    else:
        if process_exists("nshm.exe"):
            sleep(10)
            return True
        try:
            connect_device("windows:///?title_re=逆水寒手游桌面版")
            G.DEVICE.move((0, 0))
            return True
        except Exception as e:
            sync_current_snapshot("未识别到手游模拟器，开始重启逆水寒手游桌面版")
            step_restart_nshm(0)
            connect_device("Windows:///?title_re=逆水寒手游桌面版")
            sync_current_snapshot("未识别到手游模拟器，重启逆水寒手游桌面版完成")
            # connect_device("Windows:///?title_re=MuMu模拟器12")
            while True:
                portal_client.cancel_task_and_restart(luhao_task["id"])
                kkLogger_log("未识别到手游模拟器，重启逆水寒手游桌面版完成,等待任务重启指令")
                sleep(120)


####################################################
def kktouch3(x, y, sleep_time,msg):
    kkLogger_log(f"..:[{msg}]")
    if x < 381 and local_ocr_text_target_coords("你进行,邀请,育",19,319,381,395) is not None:
        sleep(15)
    touch((x, y))
    sleep(sleep_time)
#####################################################################################
def kktouch2(x, y, sleep_time,msg,attempts=4):
    if x < 381 and local_ocr_text_target_coords("你进行,邀请,育",19,319,381,395) is not None:
        sleep(15)

    current_step = luhao_task.get("stage","None")
    if current_step.startswith("step_") and current_step not in ["step_init","step_login"]:
        check_account_login_other()
        check_game_alert_note()

    if is_debug:
        save_cropped_area_screen(f"..：{msg}_{x}_{y}", x-30, y-30, x+30, y+30, 0.2,".png")

    global  touch_fast
    if attempts <= 0:
        return False

    if attempts <2:
        kkLogger_log(f".[{msg}],attempts:{attempts}，转换为普通模式")
        touch_fast = False

    if msg not in step_names and sleep_time < 1.1:
        sleep_time = 0.6

    if msg == "菜单":
        check_and_try_game_home(4,"菜单")
        kk_keyevent("{ESC}",1,2,"菜单")
        return True

    kkLogger_log(f"..[{msg}],attempts:{attempts}")
    touch((x,y))
    sleep(sleep_time)
    if msg == "菜单":
        coords = local_ocr_text_target_coords("外",1010, 129,1210, 305)
        sync_current_snapshot(f"菜单{attempts}")
        if coords is None:
            check_and_try_game_home()
            return kktouch2(1224+10, 32+37, 1, "菜单",attempts-1)
        return True
    if msg == "外观":
        sync_current_snapshot(f"外观{attempts}")
        coords = local_ocr_text_target_coords("时",298,700,408,738)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")

            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "角色":
        sync_current_snapshot(f"角色-背包{attempts}")
        coords = local_ocr_text_target_coords("角色",11, 92,143, 347)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 , 32 , 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "打造":
        sync_current_snapshot(f"打造{attempts}")
        coords = local_ocr_text_target_coords("打",95, 42,187, 102)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts - 1)
        return True
    if msg == "武功":
        sync_current_snapshot(f"武功{attempts}")
        coords = local_ocr_text_target_coords("功",12, 92,140, 499)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "设置":
        sync_current_snapshot(f"设置{attempts}")
        coords = local_ocr_text_target_coords("下",8, 196,160, 676)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "开放世界":
        sync_current_snapshot(f"开放世界{attempts}")
        return True
    if msg == "宠物":
        sleep(4)
        kktouch3(1077,128,1,"关闭更新弹框.")
        kktouch3(1077,128,1,"关闭更新弹框..")
        sync_current_snapshot(f"宠物{attempts}")
        coords = local_ocr_text_target_coords("宠物",105, 45,194, 98)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "群侠":
        sync_current_snapshot(f"群侠{attempts}")
        coords = local_ocr_text_target_coords("群",8, 84,158, 344)
        if coords is None:
            return False
        return True
    if msg == "庄园":
        sync_current_snapshot(f"庄园{attempts}")
        kktouch3(766,677,1,"取消引导")
        kktouch3(766,677,1,"取消引导")
        coords = local_ocr_text_target_coords("庄园",98, 45,183, 97)
        if coords is None:
            return False
        return True

    return True
#########################################################
def swipe_to_end(judge_area,swipe_start,swipe_end,judge_blank=False):
    if swipe_start[1] < swipe_end[1]:
        kk_scroll(swipe_start, 20,2,"swipe_to_end")
    else:
        kk_scroll(swipe_start, -20,2,"swipe_to_end")
###############################################################

#########################################################
def swipe_to_end_slow(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and (judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    for i in range(40):
        try:
            start_screen = G.DEVICE.snapshot()
            cropped_start_screen = aircv.crop_image(start_screen, judge_area)
            swipe(swipe_start, swipe_end, duration=2)
            sleep(3)
            end_screen = G.DEVICE.snapshot()
            cropped_end_screen = aircv.crop_image(end_screen, judge_area)
            confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
            kkLogger_log(f"{confidence},times:{i}")
            if confidence > 0.8:
                break
        except Exception as e:
            kkLogger_log(f"swipe_to_end_slow error：{e}")
            continue

#########################################################################
def swipe_and_judge_end(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and ocr_text(judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen,judge_area)

    swipe(swipe_start, swipe_end, steps=15, duration=3)
    sleep(2)

    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, judge_area)
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
    kkLogger_log(f"confidence：{confidence}")
    if confidence > 0.8:
        return True
    else:
        return False
##################################################################
def scroll_and_judge_end(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and ocr_text(judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen,judge_area)


    # swipe(swipe_start, swipe_end)
    # sleep(2)
    height = abs(swipe_end[1] - swipe_start[1])
    kkLogger_log(f"================height:{height}")
    scroll_count = height // (575 - 285)
    coords = ( (judge_area[2] + judge_area[0]) //2, (judge_area[3] + judge_area[1]) //2)
    kkLogger_log(f"================scroll_count:{scroll_count}，coords：{coords}")
    if swipe_end[1] > swipe_start[1]:
        kk_scroll(coords,scroll_count,2,"scroll_and_judge_end")
    else:
        kk_scroll(coords, -scroll_count, 2,"scroll_and_judge_end")

    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, judge_area)
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
    kkLogger_log(f"confidence：{confidence}")

    # sleep(3)
    if confidence > 0.8:
        return True
    else:
        return False

#####################################################
def swipe_and_judge_end_fast(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and ocr_text(judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen,judge_area)


    swipe(swipe_start, swipe_end)
    sleep(2)

    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, judge_area)
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
    kkLogger_log(f"confidence：{confidence}")
    if confidence > 0.8:
        return True
    else:
        return False
###########################################################
def init(log_queue=None, fast=False, save_original=False):
    if log_queue:
        global logger_queue
        logger_queue = log_queue
    global touch_fast
    touch_fast = fast

    global save_original_pic
    save_original_pic = save_original
########################################################################################
################################################
def init_local_nsh(l_task):
    global center_x
    global center_y
    global width
    global height
    global start_time
    global snapImgPath
    global projectPath
    global luhao_task
    global logger
    global product_meta
    global is_local
    global tianniran_count
    global ziran_pic_count
    global waiguan_set
    global waiguan_taozhuang_set
    global waiguan_fashi_set
    global waiguan_ziran_fashi_set
    global waiguan_wuqi_set
    global waiguan_zuoqi_set
    global waiguan_xiangrui_set
    global waiguan_qingong_set
    global waiguan_ts_jineng_set
    global neigong_lingyun_set



    waiguan_set = []
    waiguan_taozhuang_set = []
    waiguan_fashi_set = []
    waiguan_ziran_fashi_set = []
    waiguan_wuqi_set = []
    waiguan_zuoqi_set = []
    waiguan_xiangrui_set = []
    waiguan_qingong_set = []
    waiguan_ts_jineng_set = []
    neigong_lingyun_set = []

    tianniran_count = 0
    ziran_pic_count = 0

    is_local = True

    center_x = width // 2
    center_y = height // 2

    luhao_task = l_task
    productSn = luhao_task['productSn']


    nowTime = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    projectPath = "D:\\kkzhw\\airtest_log"
    snapImgPath = f"{projectPath}\\{productSn}_{nowTime}"

    clear_folder2(snapImgPath)

    logging.getLogger("airtest").setLevel(logging.INFO)
    logging.getLogger("ppocr").setLevel(logging.INFO)
    if logger is None:
        logger = logger_config.setup_logger(os.path.basename(__file__),
                                            log_file_name=f'{snapImgPath}\\{productSn}_runtime.log')


    for i in range(120):
        sleep(2)
        try:
            connect_device("windows:///?title_re=逆水寒手游桌面版")
            kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
            width, height = device().get_current_resolution()
            if width < 1280:
                print(f"设备错误或非初始界面，请调整，第{i}/{120}次")
                continue
            else:
                G.DEVICE.move((0, 0))
                break
        except Exception as e:
            print(e)
            continue

    # check_and_try_game_home(10,"init_local_nsh")
    # check_game_home(1000000)
    # if check_and_try_game_home() is False:
    #     for ii in range(100000):
    #         print(f"请调整至游戏主页，请调整，第{ii}次")

    print(f"设备屏幕宽：{width} 高：{height}")
    print(f"窗口标题 {G.DEVICE.get_title()}")
    G.DEVICE.move((0, 0))

    center_x = width // 2
    center_y = height // 2

    kkLogger_log(f"屏幕 宽：{width}，高：{height}")
    kkLogger_log(f"图片路径：{snapImgPath}")

    if logger is None:
        logger = logger_config.setup_logger(os.path.basename(__file__),
                                            log_file_name=f'{snapImgPath}\\{productSn}_runtime.log')
        kkLogger_log(f"屏幕 宽：{width}，高：{height}")
        kkLogger_log(f"图片路径：{snapImgPath}")

    product_meta = l_task['product_meta']

    # check_game_gonggao()
###################################################
def init_env():
    global center_x
    global snapImgPath
    global logger
    global logger2
    global width
    global height

    global center_x
    global center_y
    global product_meta
    global luhao_task
    global luhao_failed
    global pic_url_arrays
    global tianniran_ocr_txt_arrays
    global ocr_file
    global touch_fast
    global start_time
    global end_time
    global is_local
    global save_original_pic
    global is_debug
    global login_other_count
    global tianniran_count
    global ziran_pic_count

    global waiguan_set
    global waiguan_taozhuang_set
    global waiguan_fashi_set
    global waiguan_ziran_fashi_set
    global waiguan_wuqi_set
    global waiguan_zuoqi_set
    global waiguan_xiangrui_set
    global waiguan_qingong_set
    global waiguan_ts_jineng_set
    global has_mumu
    global neigong_lingyun_set

    waiguan_set = []
    waiguan_taozhuang_set = []
    waiguan_fashi_set = []
    waiguan_ziran_fashi_set = []
    waiguan_wuqi_set = []
    waiguan_zuoqi_set = []
    waiguan_xiangrui_set = []
    waiguan_qingong_set = []
    waiguan_ts_jineng_set = []
    neigong_lingyun_set = []



    snapImgPath = ""
    logger = None
    logger2 = None
    # 设置全局的截图精度为90
    ST.SNAPSHOT_QUALITY = 99
    # 获取当前设备的屏幕宽度和高度
    width = 1280
    height = 756
    login_other_count = 0

    center_x = 1280 // 2
    center_y = 756 // 2

    product_meta = []
    luhao_task = None
    luhao_failed = False
    pic_url_arrays = []
    tianniran_ocr_txt_arrays = set()
    ocr_file = 0  # ocr 是否保存识别区域图片 1保存图片 0不保存
    start_time = datetime.datetime.now()
    end_time = datetime.datetime.now()
    is_debug = False
    tianniran_count = 0
    ziran_pic_count = 0


    # try:
    #     connect_device(f"Android://127.0.0.1:5037/127.0.0.1:16384?cap_method=JAVACAP")
    #     start_app("com.netease.micro.nshm")
    #     print("启动云逆水寒成功")
    #     has_mumu = True
    #     return True
    # except Exception as e:
    #     kkLogger_log("error 连接mumu模拟器失败，判定设备仅能扫码上号")
    #     has_mumu = False

    has_mumu = False
    return True

#################################################################
def safe_step_gui_task(is_skip = 0):

    for i in range(3):
        try:
            task = portal_client.get_task_info(luhao_task["id"])
            kkLogger_log(f"获取到任务：{task}")
            task_id = task["id"]
            device_id = task["deviceId"]

            connect_device("windows:///?title_re=看看账号网逆水寒手游录号工具.*")

            kktouch2(116, 101, 1, "客服账号输入框")
            keyevent("^a")
            keyevent("{DELETE}")
            kktouch2(116, 101, 1, "客服账号输入框")
            text(f"DID:{device_id}")



            kktouch2(147, 171, 1, "编号输入框")
            keyevent("^a")
            keyevent("{DELETE}")
            kktouch2(147, 171, 1, "编号输入框")
            text(f"TID:{task_id}")
            kktouch2(291, 170, 2, "开始任务")
            connect_device("windows:///?title_re=逆水寒手游桌面版")
            break
        except Exception as e:
            kkLogger_log(f"safe_step_gui_task, error: {e}")
            sleep(5)
            continue

    for i in range(240): #最多40分钟
        try:
            task = portal_client.get_task_info(luhao_task["id"])
            if task is None:
                sleep(5)
                continue
            task_status = task.get("status", "None")

            global end_time
            end_time = datetime.datetime.now()
            total_time = end_time - start_time
            minutes = total_time.total_seconds() // 60

            kkLogger_log(f"当前任务状态：{task_status}，已执行：{minutes}分钟")
            if task_status == "COMPLETED":
                break
            elif task_status == "FAILED":
                break
            elif task_status == "CANCELLED":
                break
            else:
                sleep(10)
                continue
        except Exception as e:
            kkLogger_log(f"safe_step_gui_task, error: {e}")
            sleep(5)
            continue
#################################################################
def init_nsh(l_task):
    init_success = init_env()
    if not init_success:
        return get_return_taskEvent("step_init", EventStatus.FAILURE, "初始化失败")

    global center_x
    global center_y
    global width
    global height
    global start_time
    global snapImgPath
    global projectPath
    global luhao_task
    global logger
    global product_meta

    luhao_task = l_task
    productSn = luhao_task['productSn']

    start_time = datetime.datetime.now()
    for j in range(5):
        try:
            print("init_nsh 连接逆水寒手游桌面版")
            connect_device("windows:///?title_re=逆水寒手游桌面版")
            sleep(2)
            print("init_nsh 连接桌面")
            connect_device("windows:///")
            if local_ocr_text_target_coords("手游", 1, 1, 147, 35,False) is not None:
                #关闭弹窗
                touch((801,161))
                sleep(0.1)
                touch((1196,144))
                sleep(0.5)
                if wait_for_ocr_txt("选择", 5,499, 456, 783, 538):
                    connect_device("Windows:///?title_re=逆水寒手游桌面版")
                    G.DEVICE.move((0, 0))
                    width, height = device().get_current_resolution()
                    break
                else:
                    sync_login_fail("设备繁忙，请稍后再试。")
                    print("未找到【选择】文字，重启逆水寒客户端")
                    step_restart_nshm(0)
                    return get_return_taskEvent("step_init", EventStatus.CANCELLED, "设备初始化完成，退出当前任务。")
            else:
                raise Exception("逆水寒客户端不存在")
        except Exception as e:
            print(f"init_nsh error 设备窗口异常。{e}")
            sync_login_fail("设备繁忙，请稍后再试.")
            step_restart_nshm(0)
            return get_return_taskEvent("step_init", EventStatus.CANCELLED, "初始化完成，退出当前任务.")


    # try:
    #     connect_device("windows:///?title_re=逆水寒手游桌面版")
    #     print(f"设备屏幕宽：{width} 高：{height}")
    #     G.DEVICE.move((0,0))
    # except Exception as e:
    #     return get_return_taskEvent("step_init", EventStatus.MANUAL_REQUIRED, "初始化失败，需人工介入")



    center_x = width // 2
    center_y = height // 2

    nowTime = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    projectPath = "D:\\kkzhw\\airtest_log"
    snapImgPath = f"{projectPath}\\{productSn}_{nowTime}"

    clear_folder2(snapImgPath)

    logging.getLogger("airtest").setLevel(logging.INFO)
    logging.getLogger("ppocr").setLevel(logging.INFO)
    if logger is None:
        logger = logger_config.setup_logger(os.path.basename(__file__), log_file_name=f'{snapImgPath}\\{productSn}_runtime.log')
        kkLogger_log(f"屏幕 宽：{width}，高：{height}")
        kkLogger_log(f"图片路径：{snapImgPath}")


    product_meta = l_task['product_meta']

    kkLogger_log(f"touch_false:{touch_fast},ocr_file:{ocr_file}")

    return get_return_taskEvent("step_init", EventStatus.SUCCESS, "初始化成功")

########################################################################################
def kkLogger():
    global logger
    global logger2
    if logger is None:
        if logger2 is None:
            # logger2 = logger_config.setup_logger(os.path.basename(__file__), log_file_name=f'{projectPath}\\nsh_runtime.log')
            logger2 = logger_config.setup_logger(os.path.basename(__file__), log_file_name=f'{snapImgPath}\\nsh_runtime.log')

        return logger2
    else:
        return logger
########################################################################
def kkLogger_log(msg,level="info"):
    # print(msg)
    global logger_queue
    if ocr_file == 1 or ".." in msg or "error" in msg:
        try:
            if "info" == level:
                kkLogger().info(msg)
                if logger_queue:
                    logger_queue.put(msg)
            elif "error" == level:
                kkLogger().error(msg)
            elif "debug" == level:
                kkLogger().debug(msg)
            else:
                print(msg)
                if logger_queue:
                    logger_queue.put(msg)
        except Exception as e:
            print(f"kkLogger_log {msg} exception：:{e}")
######################################################################
def check_game_home(counts):
    check_and_try_game_home()
    for i in range(counts):
        sleep(2)
        if exists(Template(r"tpl1728385666525.png", record_pos=(0.471, -0.26), resolution=(1278, 720))):
            touch((1240,60))
            sleep(1)
            coords = local_ocr_text_target_coords("角色", 1198, 91, 1280, 236)
            if coords is not None:
                touch((1249,63))
                sleep(1)
                print(f"当前正在游戏主页")
                return True

            print(f"当前不在游戏登录成功角色主页，第{i}次")
            continue
        else:
            print(f"当前不在游戏登录成功角色主页，第{i}次")
            continue
    return False

#########################################################

def get_head_pic():
    pic = None
    for item in pic_url_arrays:
        if item['name'] == "头图":
            pic = item['value']
            break
    if pic is None:
        pic = pic_url_arrays[0]['value']
    return pic
#######################################################################


##################################################################
def ocr_add_to_get(start_x,start_y,end_x,end_y):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    add_to_get(get_ocr_txt)
                    kkLogger_log(f"ocr_add_to_get ocr_get：{get_ocr_txt}")
    else:
        kkLogger_log("ocr nothing")
###########################################################
def ocr_screen_add_to_get(mode_name,cropped_screen,is_record=False):
    try:
        has_text = False
        if cropped_screen is not None:
            ocr_result = ocr.ocr(cropped_screen, cls=False)
            if ocr_file == 1:
                current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
                cropped_screen = Image.fromarray(cropped_screen)
                cropped_screen.save(f"{snapImgPath}\\inverted_{current_millis}.png")

            if ocr_result:
                for line in ocr_result:
                    if line is None:
                        continue
                    for word_info in line:
                        if word_info is None:
                            continue
                        else:
                            get_ocr_txt_ = word_info[1][0]
                            has_text = True
                            if any(keyword in get_ocr_txt_ for keyword in configs.lock_keywords) or contains_digit(get_ocr_txt_):
                                kkLogger_log(f"ocr_screen_add_to_get 识别到非解锁字符 ：{get_ocr_txt_}")
                                return (False,has_text)

                            if len(get_ocr_txt_) > 0 and "穿戴" not in get_ocr_txt_ and "衣品" not in get_ocr_txt_ and "变身" not in get_ocr_txt_:
                                if len(get_ocr_txt_)>4 and ( get_ocr_txt_.endswith("台") or get_ocr_txt_.endswith("3") ):
                                    get_ocr_txt_ = get_ocr_txt_[:-1]
                                if "进阶" in get_ocr_txt_ and not get_ocr_txt_.endswith("进阶"):
                                    get_ocr_txt_ = get_ocr_txt_[:-1]

                                if len(get_ocr_txt_) > 1 and (get_ocr_txt_[-1] == "貌" or get_ocr_txt_[-1] == "兜"):
                                    get_ocr_txt_ = get_ocr_txt_[:-1] + '猊'
                                elif len(get_ocr_txt_) > 1 and get_ocr_txt_[-1] == "攀":
                                    get_ocr_txt_ = get_ocr_txt_[:-1] + '辇'

                                get_ocr_txt_ = remove_unwanted_chars(get_ocr_txt_)

                                if mode_name == "发式" and len(waiguan_fashi_set) > 1 and ("踏歌" in waiguan_fashi_set or "俏也" in waiguan_fashi_set):
                                    return (True,has_text)
                                if mode_name == "套装" and len(waiguan_taozhuang_set) > 1 and "自在裳" in waiguan_taozhuang_set:
                                    return (True,has_text)

                                if "武功-轻功" == mode_name:
                                    get_ocr_txt_ = get_ocr_txt_.replace("·", "")
                                    get_ocr_txt_ = get_ocr_txt_.replace("起", "")
                                    ocr_check_and_add(get_ocr_txt_)
                                else:
                                    ocr_check_and_add(get_ocr_txt_)

                                if is_record:
                                    if "套装" == mode_name:
                                        waiguan_taozhuang_set.append(f"{portal_client.get_error_text('逆水寒手游').get(get_ocr_txt_, get_ocr_txt_)}")
                                    elif "发式" == mode_name:
                                        waiguan_fashi_set.append(f"{portal_client.get_error_text('逆水寒手游').get(get_ocr_txt_, get_ocr_txt_)}")
                                    elif "武器" == mode_name:
                                        waiguan_wuqi_set.append(f"{portal_client.get_error_text('逆水寒手游').get(get_ocr_txt_, get_ocr_txt_)}")
                                    elif "坐骑" == mode_name:
                                        waiguan_zuoqi_set.append(f"{portal_client.get_error_text('逆水寒手游').get(get_ocr_txt_, get_ocr_txt_)}")
                                    elif "祥瑞" == mode_name:
                                        real_xiangrui = portal_client.get_error_text('逆水寒手游').get(get_ocr_txt_, get_ocr_txt_)
                                        if real_xiangrui not in waiguan_xiangrui_set:
                                            waiguan_xiangrui_set.append(real_xiangrui)
                                        elif real_xiangrui in waiguan_xiangrui_set and len(waiguan_xiangrui_set) > 6:
                                            return (True, False)
                                        else:
                                            waiguan_xiangrui_set.append(real_xiangrui)
                                    elif "武功-轻功" == mode_name:
                                        waiguan_qingong_set.append(f"{portal_client.get_error_text('逆水寒手游').get(get_ocr_txt_, get_ocr_txt_)}")

                            # kkLogger_log(f"ocr_screen_add_to_get ：{get_ocr_txt}")
                return (True,has_text)
            else:
                kkLogger_log("ocr_screen_add_to_get ocr nothing")
                return (False,has_text)
        else:
            kkLogger_log("ocr_screen_add_to_get cropped_screen is none")
            return (False,has_text)
    except Exception as e:
        kkLogger_log(f"ocr_screen_add_to_get error：{e}")
        return (False,has_text)


##################################################################
def ocr_add_to_get_attr(start_x,start_y,end_x,end_y,attr_names):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    for a_name in attr_names:
                        ocr_check_and_add_attr(get_ocr_txt,a_name)
                    kkLogger_log(f"ocr_add_to_get_attr ocr_get：{get_ocr_txt}")
    else:
        kkLogger_log("ocr nothing")

################################################################################
def ocr_text_inverted_denoised(start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()
            if screen is None:
                sleep(0.5)
                screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            cropped_gray_screen_rgb = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            cropped_screen_rgb = cv2.cvtColor(cropped_gray_screen_rgb, cv2.COLOR_BGR2RGB)
            inverted_cropped_screen = 255 - cropped_screen_rgb

            denoised_image = cv2.medianBlur(inverted_cropped_screen, 3)

            ocr_result = ocr.ocr(denoised_image, cls=False)
            if ocr_file == 1:
                pil_img = cv2_2_pil(denoised_image)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_now_ocr_{current_millis}.png")

            return ocr_result
        except Exception as e:
            kkLogger_log(f"ocr_text error：{traceback.format_exc()}")
            sleep(2)
            continue
    return None

########################################################
def ocr_add_to_get_and_count_tianniran_number(start_x,start_y,end_x,end_y):
    global tianniran_count
    # ocr_result = ocr_text_inverted_denoised(start_x,start_y,end_x,end_y)
    ocr_result = ocr_text_inverted(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    get_ocr_txt = get_ocr_txt.strip()

                    # print(f"-------------------------------------------------->{get_ocr_txt}")

                    if contains_digit(get_ocr_txt):
                        try:
                            match = re.search(r'\d+', get_ocr_txt)
                            value_number = match.group(0) if match else ""
                            kkLogger_log(f"get_ocr_number：{value_number}")
                            tianniran_count = int(value_number)//2
                            # if tianniran_count > 0:
                            #     ocr_check_and_set("天霓染", str(tianniran_count))
                            kkLogger_log(f"ocr_add_to_get_and_count_tianniran_number 天霓染数量 ：{tianniran_count}")
                            break
                        except Exception as e:
                            kkLogger_log(f"ocr_add_to_get_and_count_tianniran_number ：{e}")
                    else:
                        kkLogger_log(f"ocr_add_to_get_and_count_tianniran_number [{get_ocr_txt}]，未识别到自染数量")

    else:
        kkLogger_log("ocr_add_to_get_and_count_tianniran ocr nothing")
    return tianniran_count
##################################################################################
def ocr_add_to_get_and_count_tianniran(start_x,start_y,end_x,end_y,skip_word):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]

                    get_ocr_txt = get_ocr_txt.strip()
                    if(len(get_ocr_txt) == 1):
                        continue
                    if skip_word not in get_ocr_txt and "点" not in get_ocr_txt and "案" not in get_ocr_txt:
                        tianniran_ocr_txt_arrays.add(get_ocr_txt)
                    kkLogger_log(f"ocr_add_to_get_and_count_tianniran ocr_get：{get_ocr_txt}")
    else:
        kkLogger_log("ocr_add_to_get_and_count_tianniran ocr nothing")
    kkLogger_log(f"tianniran_ocr_txt_arrays ：{tianniran_ocr_txt_arrays}")
#################################################################################



#################################################################################
def ocr_check_and_add(ocr_txt):
    global product_meta
    ocr_txt = ocr_txt.strip()
    ocr_txt = remove_unwanted_chars(ocr_txt)

    if len(ocr_txt) == 1 or len(ocr_txt) == 0:
        return
    ocr_txt2 = portal_client.get_error_text('逆水寒手游').get(ocr_txt, ocr_txt)

    ocr_txt2 = portal_client.get_black_attr_value("逆水寒手游").get(ocr_txt2,ocr_txt2)
    hit_item_name_value = None
    for item in product_meta:
        if item["type"] not in [1,2]:
            continue
        if item["inputList"] and item["name"] not in configs.SKIP_ATTRI_NAME:
            input_arrays = item["inputList"].split(',')
            values = item.get("values", [])
            for input_value in input_arrays:
                input_value_2 = remove_unwanted_chars(input_value)
                if ocr_txt2 == input_value_2:
                    hit_item_name_value = item["name"]+":"+ocr_txt2
                    if item["name"] == "灵韵内功":
                        neigong_lingyun_set.append(ocr_txt2)
                    if input_value not in values:
                        values.append(input_value)

            item["values"] = values
    return hit_item_name_value
######################################################################
def ocr_check_and_add2(ocr_txt):
    global product_meta
    ocr_txt = ocr_txt.strip()
    ocr_txt = remove_unwanted_chars(ocr_txt)

    if len(ocr_txt) == 1 or len(ocr_txt) == 0:
        return
    # ocr_txt2 = configs.ERROR_TEXT.get(ocr_txt, ocr_txt)
    ocr_txt2 = portal_client.get_error_text('逆水寒手游').get(ocr_txt, ocr_txt)

    # ocr_txt2 = configs.black_attr_value.get(ocr_txt2,ocr_txt2)
    ocr_txt2 = portal_client.get_black_attr_value("逆水寒手游").get(ocr_txt2,ocr_txt2)
    hit_item_name_value = None
    for item in product_meta:
        if item["type"] not in [1,2]:
            continue
        if item["inputList"] and item["name"] not in configs.SKIP_ATTRI_NAME:
            input_arrays = item["inputList"].split(',')
            values = item.get("values", [])
            for input_value in input_arrays:
                input_value_2 = remove_unwanted_chars(input_value)
                if ocr_txt2 == input_value_2 or is_close_match(ocr_txt2,input_value):
                    hit_item_name_value = item["name"]+":"+ocr_txt2
                    if input_value not in values:
                        values.append(input_value)

            item["values"] = values
    return hit_item_name_value
#######################################################################

################################################################################
def ocr_check_and_add_attr(ocr_txt,attr_name):
    global product_meta
    kkLogger_log(f"ocr_check_and_add_attr ocr_txt:{ocr_txt}")
    ocr_txt = ocr_txt.strip()
    ocr_txt = remove_unwanted_chars(ocr_txt)

    if len(ocr_txt) == 1 or len(ocr_txt) == 0:
        return
    # ocr_txt2 = configs.ERROR_TEXT.get(ocr_txt, ocr_txt)
    ocr_txt2 = portal_client.get_error_text('逆水寒手游').get(ocr_txt, ocr_txt)

    # ocr_txt2 = configs.black_attr_value.get(ocr_txt2,ocr_txt2)
    ocr_txt2 = portal_client.get_black_attr_value("逆水寒手游").get(ocr_txt2,ocr_txt2)

    kkLogger_log(f"ocr_check_and_add ocr_txt2:{ocr_txt}")
    for item in product_meta:
        if item["type"] not in [1,2]:
            continue
        if item["inputList"] and item["name"] == attr_name:
            input_arrays = item["inputList"].split(',')
            values = item.get("values", [])
            for input_value in input_arrays:
                input_value_2 = remove_unwanted_chars(input_value)
                if ocr_txt2 == input_value_2:
                    if input_value not in values:
                        values.append(input_value)
                        kkLogger_log(f"{input_value} append in {values}")
                    else:
                        kkLogger_log(f"{input_value} already in {values}")

            item["values"] = values  # 更新字典中的values键
    return ocr_txt2
#############################################################
def category_meta_item(item_name):
    for item in product_meta:
        if item["name"] == item_name:
            return item
        else:
            return None
##############################################################################
def get_ocr_number(start_x,start_y,end_x,end_y):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    if contains_digit(get_ocr_txt):
                        match = re.search(r'\d+', get_ocr_txt)
                        value_number = match.group(0) if match else ""
                        kkLogger_log(f"get_ocr_number：{value_number}")
                        return value_number
    else:
        kkLogger_log(f"未获取到数字")

    return ""

#########################################################
def get_attr_ocr_number(attr_name,start_x,start_y,end_x,end_y):
    has_yipin = False
    try:
        # ocr_result = ocr_text(start_x,start_y,end_x,end_y)
        ocr_result = local_ocr_text(start_x,start_y,end_x,end_y)
        if ocr_result:
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    if word_info is None:
                        continue
                    else:
                        get_ocr_txt = word_info[1][0]
                        if get_ocr_txt and get_ocr_txt != "" and contains_digit(get_ocr_txt):
                            ocr_check_and_set_number(attr_name,get_ocr_txt)
                            kkLogger_log(f"..get_attr_ocr_number {attr_name}：{get_ocr_txt}")
                            has_yipin = True
                            break
        else:
            kkLogger_log(f"未获取到{attr_name}")
    except Exception as e:
        print(f"sync_login_success：{e}")
    return has_yipin
#####################################################################
def get_wenyu_attr_ocr_number(attr_name,start_x,start_y,end_x,end_y):
    has_wenyun = 0
    try:
        ocr_result = ocr_text(start_x,start_y,end_x,end_y)
        if ocr_result:
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    if word_info is None:
                        continue
                    else:
                        get_ocr_txt = word_info[1][0]
                        print(f"识别到纹玉：{get_ocr_txt}")
                        # if contains_digit(get_ocr_txt) and "/" in get_ocr_txt:
                        if contains_digit(get_ocr_txt):
                            parts = get_ocr_txt.split('/')
                            print(f"设置限定纹玉额度：{parts[0]}")
                            if parts[0].isdigit():
                                # ocr_check_and_set_number(attr_name,parts[0])
                                kkLogger_log(f"get_wenyu_attr_ocr_number {attr_name}：{get_ocr_txt}")

                                sync_current_snapshot(f"识别到限定纹玉：{parts[0]}", None, True)
                                has_wenyun = int(parts[0])
        else:
            kkLogger_log(f"未获取到{attr_name}")
    except Exception as e:
        print(f"sync_login_success：{e}")
    return has_wenyun

########################################################################
def ocr_check_and_set(attrName,ocr_txt):
    global product_meta
    for item in product_meta:
        if item["name"] == attrName:
            values = item.get("values", [])
            values.append(ocr_txt)
            item["values"] = values
            kkLogger_log(f"ocr_check_and_set: {item}")
            return ocr_txt
######################################################
def ocr_check_and_set_number(attrName,ocr_txt):
    global product_meta
    match = re.search(r'\d+', ocr_txt)
    value_number = match.group(0) if match else ""
    for item in product_meta:
        if item["name"] == attrName:
            if attrName == "充值金额" and (value_number == "0" or value_number == "1000" or value_number == "7"):
                return ocr_txt

            values = [value_number]
            item["values"] = values
            if attrName == "充值金额" and value_number != "" and value_number != "0" and value_number != "1000" and value_number != "7":
                ocr_check_and_set("充值称号",get_chongzhi_chenghao(value_number))
                print(f"..充值称号================：{get_chongzhi_chenghao(value_number)}")
            kkLogger_log(f"..ocr_check_and_set_number {item}")
            return ocr_txt
################################################################
def get_chongzhi_chenghao(num):
    sorted_chongzhi_chenghao = sorted(configs.chongzhi_chenghao, key=lambda x: x[1], reverse=True)
    for item in sorted_chongzhi_chenghao:
        if int(num) > item[1]:
            return item[0]
    return ""
################################################################
def get_luhao_snapshot():
    try:
        step_login_pic = save_cropped_screen("luhao_snapshot",".png")
        oss_file = upload_one_img_to_oss(step_login_pic)
        the_state_img = configs.image_server_url+oss_file
        return the_state_img
    except Exception as e:
        print(f"get_luhao_snapshot error {e}")

#######################################################
# def check_game_window(check_login=False):
#     connect_device("windows:///?title_re=逆水寒手游桌面版")
#
#     width2, height2 = device().get_current_resolution()
#     if width2 > 1280:
#         #判断是否存在扫码弹窗
#         coords = local_ocr_text_target_coords("扫码登录", 472, 212, 804, 552,False)
#         if coords is not None:
#             kktouch2(802 , 163 , 1, "关闭登录弹窗")
#             return True
#         # 判断是否存在扫码超时弹窗
#         coords = local_ocr_text_target_coords("登录失败", 472, 212, 804, 552,False)
#         if coords is not None:
#             kktouch2(802 , 163 , 1, "关闭登录弹窗")
#             return True
#         # 判断是否上一个游戏登录状态
#         if check_login and check_and_try_game_home(1,"init"):
#             kk_keyevent("{ESC}", 1, 1, "回主页")
#             if local_ocr_text_target_coords("角色", 1191, 30, 1275, 300,False) is not None:
#                 step_logout(0)
#                 connect_device("windows:///?title_re=逆水寒手游桌面版")
#                 kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
#             return True
#     else:
#         sleep(2)
#         connect_device("windows:///")
#         kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
#         coords = local_ocr_text_target_coords("扫码登录",472,212,804,552,False)
#         if coords is not None:
#             kktouch2(802,163,1,"关闭登录弹窗")
#             return True
#         else:
#             step_restart_nshm(0)
#         return True
###########################################################################################################
def check_mumu_game_ready():
    for i in range(5):
        # sleep(1)
        target = ocr_text_target_coords("详细", 399, 456, 910, 634,False)
        if target is not None:
            kktouch3(467, 579, 1, "同意")
            return True
        #登录状态则退出
        target = ocr_text_target_coords("退出", 872, 20, 1270, 214,False)
        if target is not None:
            kktouch3(target[0], target[1]-10, 1.2, "右上退出")
            kktouch3(746,461-33,1.1,"弹窗退出")

        target = ocr_text_target_coords("其他账号登录", 330, 417, 920, 626,False)
        if target is not None:
            kktouch3(target[0],target[1],1.2,"其他账号登录")

        for j in range(2):
            target = ocr_text_target_coords("详细", 399, 456, 910, 634,False)
            if target is not None:
                kktouch3(467,579,1,"同意")
                return True
            else:
                kktouch3(940,225-36,1.2,"关闭登录弹窗")
                kktouch3(940, 225 - 36, 1.2, "关闭登录弹窗")
                continue

        stop_app("com.netease.micro.nshm")
        sleep(10)
        start_app("com.netease.micro.nshm")
        sleep(10)

    return False

######################################################################################################3
def show_login_qrcode():
    for i in range(600):
        connect_device("windows:///?title_re=逆水寒手游桌面版")
        if device().get_current_resolution()[0] < 1280:
            kkLogger_log(f"游戏非初始界面:{device().get_current_resolution()}")
            connect_device("windows:///")
            kktouch3(1113, 100, 1.2, "关闭大神弹窗")
            kktouch3(802, 202, 1.2, "关闭二维码弹窗")
            sleep(2)
            continue
        coords = local_ocr_text_target_coords("选择",468, 422,858, 574,False)
        if coords is not None :
            kktouch3(1240,100,1,"右上账号")
            # kktouch3(647,508,1,"使用其他账号登录")
            kktouch3(690,546,1,"使用其他账号登录")
            return True
        else:
            # sync_current_snapshot("游戏非初始界面，未识别到：选择服务器")
            kkLogger_log(f"游戏非初始界面，未识别到选择服务器")
            sleep(1)
            continue
    return False
######################################################
def sync_login_success(msg="登录成功"):
    for i in range(3):
        try:
            end_time = datetime.datetime.now()
            total_time = end_time - start_time
            msg = f"{msg},用时：{total_time}"

            global luhao_task
            luhao_task["snapshot"] = ""
            luhao_task["stage"] = "step_login"
            luhao_task["msg"] = msg
            propertyBag = {
                "state": "login_success",
                "state_img": ""
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            break
        except Exception as e:
            print(f"sync_login_success：{e}")
            sleep(1)
            continue
################################################################
def sync_task_uid(uid):
    try:
        global luhao_failed
        global luhao_task
        luhao_task["msg"] = "同步UID"
        luhao_task["status"] = 'IN_PROGRESS'
        luhao_task["uid"] = uid
        portal_client.sync_task_info(luhao_task)
    except Exception as e:
        print(f"sync_task_uid：{e}")
####################################
def sync_login_fail(msg="登录失败"):
    for i in range(2):
        try:
            global luhao_task
            luhao_task["snapshot"] = get_luhao_snapshot()
            luhao_task["stage"] = "step_login"
            # luhao_task["status"] = "CANCELLED"
            luhao_task["status"] = "IN_PROGRESS"
            luhao_task["msg"] = msg
            propertyBag = {
                "state": "login_fail",
                "state_img": get_luhao_snapshot()
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            break
        except Exception as e:
            print(f"sync_login_fail：{e}")
            sleep(1)
            continue
def sync_task_cancel(msg="任务取消"):
    for i in range(2):
        try:
            global luhao_task
            # luhao_task["snapshot"] = get_luhao_snapshot()
            luhao_task["stage"] = "step_login"
            luhao_task["status"] = "CANCELLED"
            luhao_task["msg"] = msg
            propertyBag = {
                "state": "login_fail",
                "state_img": get_luhao_snapshot()
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            break
        except Exception as e:
            print(f"sync_login_fail：{e}")
            sleep(1)
            continue
#####################################################
def sync_current_snapshot(msg="录号同步",now_status=None,is_snapshot=True):
    try:
        kkLogger_log(msg)

        global luhao_failed
        global luhao_task
        luhao_task["msg"] = msg
        if is_snapshot:
            luhao_task["snapshot"] = get_luhao_snapshot()
        if now_status is not None:
            luhao_task["status"] = now_status
        else:
            luhao_task["status"] = 'IN_PROGRESS'
        portal_client.sync_task_info(luhao_task)

        if "FAILED" == now_status:
            luhao_failed = True

    except Exception as e:
        print(f"sync_current_snapshot：{e}")
###########################################################################
def up_qrcode_pic():

    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
    image_util.capture_screen(f"{snapImgPath}\\luhao_step_{current_millis}.png",region=(476, 239, 818, 614))
    oss_file = upload_one_img_to_oss(f"{snapImgPath}\\luhao_step_{current_millis}.png")
    the_state_img = configs.image_server_url + oss_file
    if local_ocr_text_target_coords("成功", 476, 180, 818, 614) is not None:
        propertyBag = {
            "state": "need_show_qrcode_done",
            "state_img": the_state_img
        }
        luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
        luhao_task["qrcode"] = the_state_img
        luhao_task["status"] = "IN_PROGRESS"
        portal_client.sync_task_info(luhao_task)
    else:
        propertyBag = {
            "state": "need_show_qrcode",
            "state_img": the_state_img
        }

        luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
        luhao_task["qrcode"] = the_state_img
        luhao_task["status"] = "PENDING"
        portal_client.sync_task_info(luhao_task)
#############################################################
def step_mumuclick_login():
    # set_current(0)
    connect_device("Android://127.0.0.1:5037/127.0.0.1:16384?cap_method=JAVACAP")
    width, height = device().get_current_resolution()
    kkLogger_log(f"step_mumuclick_login 设备屏幕宽：{width} 高：{height}")
    for i in range(5):
        target = ocr_text_target_coords("其他", 178, 75, 931, 709)
        if target:
            kktouch3(target[0], target[1] - 84, 1, "授权登录")
            kkLogger_log(f"step_mumuclick_login ..登录")
            return True
        else:
            kkLogger_log(f"未识别到 其他账号，继续等待")
            sleep(2)
            continue
    #不论是否识别到登录，都..这个位置
    kktouch3(651,460-37,1.5,"授权登录。")
    kktouch3(651, 460 - 37, 1.5, "授权登录。")
    return False

def start_login_mumu(l_task):
    if True:
        kkLogger_log("start step_login_mumu")
        luhao_task = l_task
        connect_device("Windows:///?title_re=MuMu模拟器12")
        # set_current(0)
        connect_device("Android://127.0.0.1:5037/127.0.0.1:16384?cap_method=JAVACAP")
        kktouch3(43,117,0.2,"适龄提示")
        kktouch3(1047,176-37,0.2,"关闭公告活动")

        account = luhao_task['gameAccount']
        account_pass = luhao_task['gamePassword']
        if account is None:
            return False

        account = account.replace(" ", "").replace("\t", "").replace("\n", "").replace("\r", "")

        if check_mumu_game_ready() is False:
            return False

        kktouch3(464,578-33,1,"同意条款")
        if '@' in account:
            kktouch3(653, 414, 1, "网易邮箱")
            if ocr_text_target_coords("输入",295,154,1116,674,False) is None:
                kktouch3(464,578-33,1,"同意条款")
                kktouch3(653, 414, 1, "网易邮箱")

            kktouch3(581, 259, 1, "账号输入框")
            kktouch3(614, 680, 1, "账号输入")
            text(account, enter=False)
            # kktouch2(1189, 680, 1, "下一步")

            kktouch3(546, 346, 1, "密码输入框")
            kktouch3(600, 672-33, 1, "密码输入")
            text(account_pass, enter=False)
            # kktouch2(1193, 675, 1, "下一步")
            kktouch3(642, 430, 3, "登录")
            # sleep(5)
            ################################################################
            kktouch3(540,483,1,"取消")
            target = ocr_text_target_coords("取消", 540-200, 483-200, 540+200, 483+200, False)
            if target is not None:
                kktouch3(target[0],target[1],1,"取消2")

            if ocr_text_target_coords("扫码", 872, 20, 1259, 106, False) is None:
                coords = ocr_text_target_coords("网络", 10, 10, 900, 200, False)
                if coords is not None:
                    kkLogger_log(f"需要二步验证")
                    kktouch3(40, 114 - 37, 1.5, "退出安全验证")
                    kktouch3(40, 114 - 37, 1.5, "退出安全验证")
                    kktouch3(369, 218 - 37, 2, "返回登录框")
                    sync_current_snapshot("需要二步验证", None, False)
                    return False
                coords = ocr_text_target_coords("二步", 10, 10, 900, 200,False)
                if coords is not None:
                    kkLogger_log(f"需要二步验证")
                    kktouch3(40, 114 - 37, 1.5, "退出安全验证")
                    kktouch3(40, 114 - 37, 1.5, "退出安全验证")
                    kktouch3(369, 218 - 37, 2, "返回登录框")
                    sync_current_snapshot("需要二步验证",None,False)
                    return False
                coords = ocr_text_target_coords("发送", 338, 531, 1034, 718,False)
                if coords is not None:
                    kkLogger_log(f"需要安全验证")
                    kktouch3(40, 114-37, 1.5, "退出安全验证")
                    kktouch3(40, 114-37, 1.5, "退出安全验证")
                    kktouch3(369, 218-37, 2, "返回登录框")
                    sync_current_snapshot("需要安全验证",None,False)
                    return False
                coords = ocr_text_target_coords("密码", 201, 98, 980, 604,False)
                if coords is not None:
                    kkLogger_log(f"密码错误，停留在登录界面")
                    kktouch3(341, 153, 2, "返回主登录界面")
                    # kktouch2(369, 214 - 33, 2, "退出登录框")
                    sync_current_snapshot("密码错误",None,False)
                    return False

            if is_mumulogin_success():
                return True
            else:
                sync_current_snapshot("is_mumulogin_success:False",None,False)
                return False

        else:
            kktouch3(642,381-36,2,"手机账号")
            if ocr_text_target_coords("输入",295,154,1116,674,False) is None:
                kktouch3(464,578-36,2,"同意条款")
                kktouch3(642,381-36,2,"手机账号")

            kktouch3(614,352-36,2,"请输入手机号码")
            kktouch3(577, 714-36, 2, "输入框")
            text(account)
            kktouch3(1145, 645 - 36, 2, "下一步")


            #判断短信是否用完
            if ocr_text_target_coords("发送",400,415,883,617,False) is not None:
                kktouch3(885,226-37,1.5,"关闭短信发送次数限制弹窗")
                kktouch3(941,223-37,3,"关闭登录弹窗")
                kktouch3(941, 226 - 37, 2, "关闭登录弹窗")
                kktouch3(640, 559 - 36, 2, "其他账号登录")

                kkLogger_log("短信次数达到上限，取消本次录号，开始恢复初始页面")
                sync_current_snapshot("短信验证码次数达到上限",None,False)
                return False

            sync_need_smscode()
            sleep(5)

            kktouch3(460, 395-36, 1, "验证码")
            kktouch3(577, 714-36, 1, "短信输入框")

            smscode = portal_client.get_sms_code(task_id=luhao_task['id'], timeout=90)

            if smscode is None:
                kkLogger_log("未获取到短信验证码，开始恢复初始页面")
                kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch3(640, 559 - 36, 1, "其他账号登录")
                return False

            if len(smscode) > 6:
                smscode = smscode[:6]

            if is_six_digit_code(smscode):
                text(smscode)
                sleep(3)
                kktouch3(980,291,1,"空白")
                # 判断验证码是否正确，不正确或者超时则要求重新输入
                if wait_for_ocr_txt("手机", 2, 380, 188, 927, 348):
                    sync_current_snapshot("验证码错误", None, False)
                    kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                    kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                    kktouch3(640, 559 - 36, 1, "其他账号登录")
                    return False
                if is_mumulogin_success():
                    kkLogger_log("短信登录成功")
                    return True
                else:
                    sync_current_snapshot("验证码验证异常", None, False)
                    kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                    kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                    kktouch3(640, 559 - 36, 1, "其他账号登录")
                    return False
            else:
                sync_current_snapshot("短信验证码异常，开始恢复初始页面", None, False)
                kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch3(640, 559 - 36, 1, "其他账号登录")
                return False

############################################################
def is_chinese_mobile_number(phone_number):
    pattern = re.compile(r'^1[3-9]\d{9}$')
    return pattern.match(phone_number) is not None
####################################
def is_six_digit_code(code):
    if len(code) == 6 and code.isdigit():
        return True
    return False
##########################################
def sync_need_smscode(is_snap=False):
    step_login_pic = save_cropped_screen("luhao_step")
    oss_file = upload_one_img_to_oss(step_login_pic)
    the_state_img = configs.image_server_url + oss_file
    propertyBag = {
        "state": "need_sms_code",
        "state_img": the_state_img
    }
    luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
    portal_client.sync_task_info(luhao_task)
############################################################
def step_mumu_login():
    is_mumu_login_success = start_login_mumu(luhao_task)

    if is_mumu_login_success is not True:
        # account = luhao_task.get('gameAccount', "None")
        kkLogger_log("云逆水寒登录失败，开始启动用户扫码登录")
        sync_current_snapshot("登录失败，开始启动用户扫码登录", None, True)
        return step_qrcode_login()
    else:
        sync_login_success("云逆水寒登录成功")
        kkLogger_log("云逆水寒登录成功并开启扫码")

    connect_device("Windows:///?title_re=逆水寒手游桌面版")
    (width2, height2) = device().get_current_resolution()
    kkLogger_log(f"尝试连接逆水寒手游桌面版:{(width2, height2)}")

    show_login_qrcode()

    is_scan = wait_qrcode_scan_desk(24)
    if not is_scan:
        kktouch3(802, 202, 1.1, "关闭扫码弹窗")
        sync_login_fail("等待扫码登录失败")
        return get_return_taskEvent("step_login", EventStatus.CANCELLED, "登录失败")

    step_mumuclick_login()

    is_ok = wait_qrcode_ok(20)
    if is_ok:
        for i in range(10):
            check_game_login_tiaokuan(2)
            check_game_gonggao()
            coords = local_ocr_text_target_coords("接受", 389, 555, 1000, 718,False)

            coords2 = local_ocr_text_target_coords("游戏,闯荡", 516,555,771,619,False)
            if coords is not None:
                #登录条款
                kktouch3(coords[0], coords[1], 1, "同意.")
                break
            elif coords2 is not None:
                break
            else:
                sleep(1)
                print(f"没有发现同意按钮 {i}")
                connect_device("windows:///?title_re=逆水寒手游桌面版")
                continue
    else:
        sync_login_fail("扫码超时，录号终止")
        kktouch3(802,202,1.1,"关闭扫码弹窗")
        return get_return_taskEvent("step_login", EventStatus.CANCELLED, "登录扫码超时")

    if is_login_success():
        return get_return_taskEvent("step_login", EventStatus.SUCCESS, "逆水寒模拟器登录成功")
    else:
        kktouch3(802, 202, 1.1, "关闭扫码弹窗")
        sync_login_fail("逆水寒模拟器登录失败，终止录号")
        return get_return_taskEvent("step_login", EventStatus.CANCELLED, "逆水寒模拟器登录失败")
#############################################################
def step_qrcode_login():
    connect_device("windows:///?title_re=逆水寒手游桌面版")
    if show_login_qrcode() is False:
        return get_return_taskEvent("step_login",EventStatus.MANUAL_REQUIRED,"登录界面错误")
    else:
        up_qrcode_pic()
        sleep(1)
        sync_current_snapshot("需要扫码")

        is_scan = wait_qrcode_scan_desk(24)
        if not is_scan:
            connect_device("Windows:///")
            kktouch3(802, 202, 1.1, "关闭扫码弹窗")
            sync_login_fail("用户扫码超时")
            if luhao_task.get("memberId", None):
                sn = luhao_task.get("productSn")
                portal_client.send_msg(luhao_task.get("memberId", None),
                                       f'亲爱的小主，您的商品编号【{sn}】,因未扫码并确认，录号失败！如需继续，请重新提交录号。')
            return get_return_taskEvent("step_login", EventStatus.CANCELLED, "登录失败")


        # is_ok = wait_qrcode_ok(300)
        is_ok = wait_qrcode_ok(20)#1分钟
        if is_ok:
            sync_login_success()
            for i in range(10):
                check_game_gonggao()
                coords = local_ocr_text_target_coords("接受",389, 555,1200, 648)
                if coords is not None:
                    kktouch3(coords[0], coords[1],1,"接受")
                    break

                coords2 = local_ocr_text_target_coords("游戏,闯荡", 516,555,771,619)
                if coords2 is not None:
                    break

                print(f"没有发现同意按钮，继续等待 {i}")
                sleep(1)
                continue
        else:
            sync_login_fail("用户扫码超时。")
            connect_device("Windows:///")
            kktouch3(802, 202, 1.1, "关闭扫码弹窗。")

            if luhao_task.get("memberId", None):
                sn = luhao_task.get("productSn")
                portal_client.send_msg(luhao_task.get("memberId", None),
                                       f'亲爱的小主，您的商品编号【{sn}】,因扫码后未确认，录号失败！如需继续，请重新提交录号。')

            return get_return_taskEvent("step_login",EventStatus.CANCELLED,"登录扫码超时")
    if is_login_success():

        if luhao_task.get("memberId", None):
            sn = luhao_task.get("productSn")
            portal_client.send_msg(luhao_task.get("memberId", None),
                                   f'亲爱的小主，您的商品编号【{sn}】，开始录号。【注意】30分钟内请勿上号，被顶号将导致录号失败！')

        return get_return_taskEvent("step_login",EventStatus.SUCCESS,"登录成功")
    else:
        sync_login_fail("用户扫码超时。。")
        connect_device("Windows:///")
        kktouch3(802, 202, 1.1, "关闭扫码弹窗。。")
        return get_return_taskEvent("step_login",EventStatus.CANCELLED,"登录扫码超时")

################################################################################################
def step_login(is_skip):
    if is_skip == 0:
        login_type = luhao_task.get("loginType", "None")
        account = luhao_task.get('gameAccount',"None")
        source = luhao_task.get('source',"WEB")

        # if "None" == account or len(account) < 4:
        #     sync_login_fail(f"账号异常:{account}")
        #     return get_return_taskEvent("step_login", EventStatus.CANCELLED, "账号异常")
        # elif is_chinese_mobile_number(account) is False or has_mumu is False or "WEB" == source or is_between_1_and_9():
        #     return step_qrcode_login()
        # else:
        #     return step_mumu_login()

        return step_qrcode_login()

#######################################################
def check_game_doubleCheck():
    if local_ocr_text_target_coords("手机号发送", 50, 60, 1000, 600, False) is not None:
        sync_login_fail("需要二次确认，取消录号，请联系客服人工录号")

        if luhao_task.get("memberId", None):
            sn = luhao_task.get("productSn")
            portal_client.send_msg(luhao_task.get("memberId", None),
                                   f'亲爱的小主，您的商品编号【{sn}】，因需要手机二次验证，录号终止，请联系客服人工录号')

        step_restart_nshm(0)
        connect_device("Windows:///?title_re=逆水寒手游桌面版")
        sync_current_snapshot("需要二次确认，取消录号，重启客户端完成")
        sync_task_cancel("需要二次验证，取消录号")
        # connect_device("Windows:///?title_re=MuMu模拟器12")


        while True:
            portal_client.cancel_task_and_restart(luhao_task["id"])
            kkLogger_log("需要二次确认，重启后，等待任务取消指令")
            sleep(120)
    elif local_ocr_text_target_coords("验证", 50, 60, 1000, 600, False) is not None:
        sync_login_fail("需要二次验证,中止录号，请联系客服人工录号")

        if luhao_task.get("memberId", None):
            sn = luhao_task.get("productSn")
            portal_client.send_msg(luhao_task.get("memberId", None),
                                   f'亲爱的小主，您的商品编号【{sn}】，因需要二次验证，录号终止，请联系客服人工录号')

        step_restart_nshm(0)
        connect_device("Windows:///?title_re=逆水寒手游桌面版")
        sync_current_snapshot("扫码后识别到需要二次验证，重启客户端完成")
        sync_task_cancel("需要二次验证，取消录号")



        # connect_device("Windows:///?title_re=MuMu模拟器12")
        while True:
            portal_client.cancel_task_and_restart(luhao_task["id"])
            kkLogger_log("二次验证重启后，等待任务取消指令")
            sleep(120)

###############################################################
def wait_qrcode_scan_desk(wait_times):
    for i in range(wait_times):
        try:
            connect_device("Windows:///")
            width,height = device().get_current_resolution()
            kkLogger_log(f"wait_qrcode_scan_desk 等待扫码，窗口尺寸: {width},{height}")

            check_game_gonggao()
            check_game_doubleCheck()

            if local_ocr_text_target_coords("扫码成功",389,223,853,567,False) is not None:
                kkLogger_log("扫码成功。。。。。")
                up_qrcode_pic()
                sync_login_success("扫码成功")
                return True
            # elif local_ocr_text_target_coords("更换",462,426,781,567,False) is not None:
            elif local_ocr_text_target_coords("更换",505,459,792,528,False) is not None:
                kkLogger_log("识别到主页更换角色，扫码成功")
                sync_login_success("更换角色，扫码成功")
                return True
            elif local_ocr_text_target_coords("接受",462,426,1200,700,False) is not None:
                kkLogger_log("识别到条款，扫码成功")
                sync_login_success("识别到条款，扫码成功")
                check_game_login_tiaokuan()
                return True
            else:
                kkLogger_log(f"wait_qrcode_scan_desk 等待扫码,第{i}/{wait_times}次")
                sleep(5)
                continue
        except Exception as e:
            kkLogger_log(f"wait_qrcode_scan error: {e}")
            sleep(5)
            continue

    kkLogger_log(f"未完成扫码,共计等待{wait_times}次")
    return False
#######################################################
def wait_qrcode_ok(wait_seconde):
    for i in range(wait_seconde):
        try:
            connect_device("Windows:///?title_re=逆水寒手游桌面版.*")
            kkLogger_log(f"wait_qrcode_ok 逆水寒手游桌面版：等待扫码，窗口尺寸: {device().get_current_resolution()}")
            check_game_login_face()
            check_game_login_tiaokuan(2)
            check_game_doubleCheck()
            if device().get_current_resolution()[0] >= 1280:
                sync_current_snapshot("扫码登录成功")
                return True
            else:
                sleep(3)
                continue
        except Exception as e:
            sleep(3)
            continue

    return False
###################################################################
def local_ocr_text_target_coords(target_text,start_x,start_y,end_x,end_y,is_wait=True):
    for i in range(2):
        try:
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_util.capture_screen(f"{snapImgPath}\\plog_local_ocr_text_target_coords_{target_text}_{current_millis}.png", region=(start_x, start_y, end_x, end_y))
            if touch_fast and is_wait is True:
                sleep(0.5)
            ocr_result = ocr.ocr(f"{snapImgPath}\\plog_local_ocr_text_target_coords_{target_text}_{current_millis}.png", cls=False)
            if ocr_result is None:
                return None
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    # if target_text in textinfo:
                    if any(t.strip() in textinfo for t in target_text.split(',')):
                        kkLogger_log(f"l_o_t_t_c：{textinfo},命中:{target_text}")
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        break
                    else:
                        kkLogger_log(f"l_o_t_t_c：{textinfo},未命中:{target_text}")
                if target_coords:
                    break

            # 使用Airtest..坐标
            if target_coords:
                return target_coords
            else:
                return target_coords
        except Exception as e:
            sleep(2)
            kkLogger_log(f"l_o_t_t_c error：{e}")
            continue

    return None
##############################
def is_login_success():
    for i in range(20):
        sleep(1)
        connect_device("Windows:///?title_re=逆水寒手游桌面版")
        if local_ocr_text_target_coords("游戏,闯荡",516,555,771,619,False) is not None:

            propertyBag = {
                "state":"login_success",
                "state_img":"the_state_img"
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            kkLogger_log("is_login_success 登录成功")
            return True
        else:
            check_game_login_tiaokuan()
            check_game_gonggao()
            check_yueka_jiazeng(1)
            check_game_update(1)

            kkLogger_log("未发现 进入游戏")
            continue

    propertyBag = {
        "state":"login_timeout",
        "state_img":"the_state_img"
    }
    luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
    portal_client.sync_task_info(luhao_task)
    print("登录超时")
    return False
###########################################################################################
def is_mumulogin_success():
    for i in range(30):
        # set_current(0)
        connect_device("Android://127.0.0.1:5037/127.0.0.1:16384?cap_method=JAVACAP")
        target = ocr_text_target_coords("取消", 540 - 200, 483 - 200, 540 + 200, 483 + 200, False)
        if target is not None:
            kktouch3(target[0], target[1], 1, "取消。")

        # kktouch3(479, 625, 1, "关闭实名弹窗")
        target = ocr_text_target_coords("关闭", 479 - 100, 625 - 100, 479 + 100, 625 + 50, False)
        if target is not None:
            kktouch3(target[0], target[1], 1, "关闭实名弹窗2")

        target = ocr_text_target_coords("扫码", 872, 20, 1259, 106,False)
        if target is not None:

            sync_login_success(f"云逆水寒登录成功")

            kktouch3(target[0], target[1] - 10, 2, "扫码")
            if local_ocr_text_target_coords("二",369,563,911,710,False) is None:
                kkLogger_log("没有识别到取景框")
                sleep(1)
                continue
            else:
                kkLogger_log("识别到取景框")

            kkLogger_log("连接桌面")
            connect_device("windows:///")
            if local_ocr_text_target_coords("二", 493, 302, 783, 511) is not None:
                kktouch3(635,460,1,"摄像头")
                # kktouch2(635, 392, 1, "桌面")
                return True
            else:
                kkLogger_log("未识别到摄像头选择弹框")
                continue
        else:
            kkLogger_log("未发现 扫码按钮")
            continue

    kkLogger_log("登录超时")
    return False
###########################################
#############################################################################
def scan_qrcode():
    # sleep(3)
    coords = ocr_text_target_coords("扫码", 872, 20, 1259, 106)
    if coords is not None:
        kktouch3(coords[0], coords[1]-10, 1, "扫码")
        sleep(1)
        for i in range(3):
            try:
                kkLogger_log("连接MuMu模拟器")
                connect_device("Windows:///?title_re=MuMu模拟器12")
                kkLogger_log("连接桌面")
                kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")

                dev2 = connect_device("windows:///")
                kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
                sleep(1)
                break
            except Exception as e:
                kkLogger_log(f"连接桌面异常：{e}")
                sleep(2)
                continue

        kktouch3(604, 467, 1, "摄像头")
        return True
    else:
        return False


####################################################################
def check_game_gonggao():
    try:
        connect_device("windows:///")

        global is_gonggao_checked
        global is_zhuangyuan_gonggao_checked
        if is_local:
            return
        # kktouch3(1199, 144, 2, "关闭公告.。")
        # kktouch3(1199, 149, 2, "关闭公告.。。")
        coords = local_ocr_text_target_coords("本期上新",10,10,280,422,False)
        if coords is not None and is_zhuangyuan_gonggao_checked is False:
            kktouch3(1089, 138, 2, "关闭庄园公告")
            # is_zhuangyuan_gonggao_checked = True

        coords = local_ocr_text_target_coords("月", 10, 10, 100, 653,False)
        if coords is not None and is_gonggao_checked is False:
            kktouch3(1199, 144, 2, "关闭公告.")
            kktouch3(1199, 149, 2, "关闭公告.")

        coords = local_ocr_text_target_coords("月", 10, 10, 100, 653, False)
        if coords is not None and is_gonggao_checked is False:
            kktouch3(1199, 144, 2, "关闭公告.")
            kktouch3(1199, 149, 2, "关闭公告.")

        coords = local_ocr_text_target_coords("月", 10, 10, 100, 653, False)
        if coords is not None and is_gonggao_checked is False:
            kktouch3(1199, 144, 2, "关闭公告.")
            kktouch3(1199, 149, 2, "关闭公告.")


        connect_device("windows:///?title_re=逆水寒手游桌面版")
        kkLogger_log(f"连接逆水寒手游桌面版,check_game_gonggao 尺寸:{device().get_current_resolution()}")
    except Exception as e:
        print(f"没有公告:{e}")

####################################
##########################################
def get_return_taskEvent(now_stage,now_status,now_msg=None):
    return TaskEvent(task_id=luhao_task['id'],
                             stage=now_stage,
                             status=now_status,
                             snapshot=get_luhao_snapshot(),
                             data=product_meta,
                             msg=now_msg)

#######################################
def luhao_health_check():
    for i in range(3):
        try:
            screen = G.DEVICE.snapshot()
            if touch_fast:
                sleep(0.5)
            pil_img = cv2_2_pil(screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))

            pil_img.save(f'{projectPath}\\device_health\\luhao_health_{current_millis}.jpg')

            filename = f'luhao_health_{current_millis}.jpg'
            snapImgPath = f'{projectPath}\\device_health'

            image_path_set = set()
            bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称
            endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint
            access_key_id = configs.OSS_ACCESS_KEY_ID
            access_key_secret = configs.OSS_ACCESS_KEY_SECRET

            auth = oss2.Auth(access_key_id, access_key_secret)
            bucket = oss2.Bucket(auth, endpoint, bucket_name)

            local_file_path = os.path.join(snapImgPath, filename)
            oss_file_path = "mall/images2/"+ generate_oss_path()
            oss_fileName = oss_file_path+generate_oss_fileName()
            with open(local_file_path, 'rb') as fileobj:
                bucket.put_object(oss_fileName, fileobj)

            print(f"oss_filename:{oss_fileName}")

            return True
        except Exception as e:
            continue

    return False

###########################################
def wait_for_ocr_txt(ocr_txt,timeout,start_x,start_y,end_x,end_y):
    for i in range(timeout):
        coords = local_ocr_text_target_coords(ocr_txt,start_x, start_y,end_x, end_y,False)
        if coords is not None:
            kkLogger_log(f"w_ft_识别到文字：{ocr_txt},时间：{i}")
            return coords
        else:
            if timeout > 1:
                sleep(1)
    kkLogger_log(f"没有找到文字：{ocr_txt},超时时间：{timeout}")
    return False

######################################################################
def check_huodong():
    if is_local:
        return
    kkLogger_log(f"检查是否有活动弹窗")
    coords = ocr_text_target_coords("参与",427, 300,1233, 734)
    if coords is not None:
        kktouch3(1150, 149,1.5,"关闭活动")

    target = wait_for_ocr_txt("不再弹出", 2, 100, 300, 1233, 600)
    if target:
        kktouch3(target[0],target[1],1,"不再弹出")


#######################################################################
def check_yueka_jiazeng(timeout=5):
    global is_jiazheng_checked
    global jiazheng_check_count

    if is_local:
        return

    coords = ocr_text_target_coords("赠送",1091, 644,1266, 736,False)
    if coords:
        kktouch3(coords[0],coords[1],2,"赠送")
        target= wait_for_ocr_txt("确定",2,100, 100,1266, 736)
        if target:
            kktouch3(target[0],target[1],1,"赠送")

    coords = ocr_text_target_coords("领取",627, 400,1233, 734,False)
    if coords:
        kktouch3(coords[0], coords[1],2,"领取")
        kktouch3(coords[0], coords[1],2,"领取")
        kktouch3(coords[0], coords[1], 2, "领取")
        kktouch3(coords[0], coords[1], 2, "领取")
        is_jiazheng_checked = True
        return

#######################################################################

#########################################################################
def step_qufu(is_skip):
    connect_device("windows:///?title_re=逆水寒手游桌面版")
    if is_skip == 0:
        wait_for_ocr_txt("游戏,闯荡",120, 516,555,771,619)
        for i in range(10):
            kktouch3(723,501,5,"更换角色")
            if wait_for_ocr_txt("最近", 2,22, 270, 209, 601):
                break
            else:
                continue

        if wait_for_ocr_txt("最近角色", 10, 22, 270, 209, 601) is False:
            sync_current_snapshot("未找到最近角色，开始重启客户端",None,True)

            if luhao_task.get("memberId", None):
                sn = luhao_task.get("productSn")
                portal_client.send_msg(luhao_task.get("memberId", None),
                                       f'亲爱的小主，您的商品编号【{sn}】，因未识别到默认角色，录号终止')


            step_restart_nshm(0)
            connect_device("windows:///?title_re=逆水寒手游桌面版")
            sync_login_fail("未找到最近角色，重启客户端完成")
            sync_task_cancel()
            while True:
                portal_client.cancel_task_and_restart(luhao_task["id"])
                kkLogger_log("未找到最近角色，重启客户端完成")
                sleep(120)

        kktouch3(99,320,1,"最近角色")

        if ocr_text_has_txt(275,264,576,348) is False:
            sync_current_snapshot("未发现第一个角色，开始重启客户端", None, True)

            if luhao_task.get("memberId", None):
                sn = luhao_task.get("productSn")
                portal_client.send_msg(luhao_task.get("memberId", None),
                                       f'亲爱的小主，您的商品编号【{sn}】，因未识别到默认角色，录号终止')

            step_restart_nshm(0)
            connect_device("windows:///?title_re=逆水寒手游桌面版")
            sync_login_fail("未发现第一个角色，重启客户端完成")
            sync_task_cancel()
            while True:
                portal_client.cancel_task_and_restart(luhao_task["id"])
                kkLogger_log("二次验证重启后，等待任务取消指令")
                sleep(120)


        sync_current_snapshot(f"选择录号角色，区服:【{get_qufu(0)}】")
        save_cropped_area_screen("区服_级别", 274, 307, 576, 349, 0)
        kktouch3(426, 310, 2, "第一个区服")
        # wait_for_ocr_txt("进入",120, 407,508,793,706)
        wait_for_ocr_txt("游戏,闯荡",120, 516,555,771,619)
        kktouch3(641, 595,2,"进入游戏")

        result = wait_for_ocr_txt("场景设置",30, 22,620,226,728)
        if result:
            # save_cropped_screen_with_mulblur("头图_面板属性",[(145, 76,385, 590),(858, 520,1179, 570),(894, 653,1095, 694)])
            kktouch3(170, 175,2,"第一个角色")
            kktouch3(1066, 660,2,"进入游戏")
            sleep(15)

        check_and_try_game_home(100)

        return get_return_taskEvent("step_qufu", EventStatus.SUCCESS,"角色选择完成")

###################################################################################################################################
#########################################################################################################################################
def safe_step_gameset(is_skip):
    return step_gameset(is_skip)
########################################################################
def check_game_login_face():
    kkLogger_log(f"大神人脸确认")
    sleep(1)
    coords = local_ocr_text_target_coords("人脸",50, 60, 1000, 700,False)
    if coords is not None:
        connect_device("windows:///")
        kkLogger_log(f"连接桌面,尺寸:{device().get_current_resolution()}")
        sync_current_snapshot("处理大神弹窗")
        kktouch3(1116, 99,2,"关闭大神弹窗")
        connect_device("windows:///?title_re=逆水寒手游桌面版")
        kkLogger_log(f"连接逆水寒手游桌面版,尺寸:{device().get_current_resolution()}")
#######################################################################
def check_game_login_tiaokuan(timeout=5):
    connect_device("windows:///")
    kkLogger_log(f"check_game_login_tiaokuan 连接桌面,尺寸:{device().get_current_resolution()}")

    # wait_for_ocr_txt("拒绝",timeout,288, 426,999, 648)
    coords = local_ocr_text_target_coords("接受",288, 426,1200, 648,False)
    if coords is not None:
        sync_current_snapshot("处理登录条款")
        kktouch3(coords[0], coords[1],3,"接受")

        ##########################################
        coords = local_ocr_text_target_coords("月", 10, 10, 100, 653, False)
        if coords is not None:
            kktouch3(1199, 144, 2, "关闭公告.")
            kktouch3(1199, 149, 2, "关闭公告.")

        ############################################

    connect_device("windows:///?title_re=逆水寒手游桌面版")
    kkLogger_log(f"连接逆水寒手游桌面版,尺寸:{device().get_current_resolution()}")
#######################################################################
def check_game_update(timeout=5):
    return
    # if is_local:
    #     return
    # kkLogger_log(f"check_game_update")
    # wait_for_ocr_txt("过旧",timeout,337, 206,909, 479)
    # coords = ocr_text_target_coords("确定",337, 206,909, 479)
    # if coords is not None:
    #     sync_current_snapshot("提示版本过旧，需要升级客户端","FAILED")
    #     kktouch2(coords[0], coords[1],5,"确定")
#######################################################################
def check_game_alert_note():
    if is_local:
        return
    kkLogger_log(f"检查是否有提示弹窗")
    cancel_target = local_ocr_text_target_coords("取消",239, 150,1200, 700,False)
    if cancel_target:
        kktouch2(cancel_target[0],cancel_target[1],1,"check_game_alert_note 取消")

#######################################################################

####################################################
def check_account_login_other():
    end_time = datetime.datetime.now()
    total_time = end_time - start_time
    minutes = total_time.total_seconds() // 60
    if minutes < 2:
        return False

    global login_other_count
    account_logout = False
    coords = local_ocr_text_target_coords("已在其他",439, 316,934, 467,False)
    if coords is not None:
        sync_current_snapshot("被顶号.")
        kktouch3(1162 + 10, 390 + 37, 5, "顶号：确定")
        account_logout = True

        # kktouch3(1162+10,390+37,5,"顶号：确定")
        # coords2 = local_ocr_text_target_coords("进入", 475, 432, 870, 640, False)
        # coords3 = local_ocr_text_target_coords("选择", 475, 432, 870, 640, False)
        # if login_other_count < 3 and coords2 is not None and coords3 is None:
        #     kktouch3(643,595, 15, "重新进入游戏")
        #     kktouch3(1225,662, 5, "重新进入游戏")
        #     sync_current_snapshot("被顶号后重新进入游戏")
        #     login_other_count = login_other_count + 1
        # else:
        #     sync_current_snapshot("被顶号","CANCELLED",True)
        #     account_logout = True
    elif local_ocr_text_target_coords("游戏,闯荡", 516,555,771,619, False) is not None:
        sync_current_snapshot("被顶号..")
        account_logout = True

        # if login_other_count < 3 and local_ocr_text_target_coords("选择", 475, 432, 870, 640, False) is None:
        #     kktouch3(643,595, 15, "重新进入游戏")
        #     kktouch3(1225, 662, 5, "重新进入游戏")
        #     sync_current_snapshot("被顶号后重新进入游戏")
        #     login_other_count = login_other_count + 1
        # else:
        #     sync_current_snapshot("被顶号", "CANCELLED", True)
        #     account_logout = True


    if account_logout:
        kktouch3(1243,98,3,"右上账号")
        kktouch3(590,394,3,"切换账号。")
        kktouch3(802 , 202 , 3, "切换账号。。")

        if luhao_task.get("memberId",None):
            sn = luhao_task.get("productSn")
            portal_client.send_msg(luhao_task.get("memberId",None), f'亲爱的小主，您的商品编号【{sn}】,因被顶号，终止录号。如需继续，请重新提交录号。')

        # connect_device("Windows:///?title_re=MuMu模拟器12")
        while True:
            portal_client.cancel_task_and_restart(luhao_task["id"])
            kkLogger_log("被顶号，等待任务取消指令")
            sleep(120)

    ##判断是否小芽
    coords = local_ocr_text_target_coords("移动火", 300, 300, 830, 372, False)
    if coords is not None:
        sync_current_snapshot("小芽号")
        swipe((803, 372), (727, 440), duration=1, steps=5)
        sleep(2)
        for j in range(30):
            kktouch3(727, 440, 1, "..")
            coords = local_ocr_text_target_coords("推门", 300, 300, 830, 372, False)
            if coords is not None:
                break
        swipe((226,144),(1009,518),duration=8)

    return account_logout

###################################################################################
def safe_step_fuben_in(is_skip):
    if is_skip == 0:
        try:
            kkLogger_log(f"开始进入副本")
            check_and_try_game_home(4)
            kk_keyevent("{ESC}",1,1,"菜单")
            kktouch3(1039,359,2,"副本")
            kktouch3(78,271,2,"历史赛年")

            kktouch3(298,415,3,"千机")

            kktouch3(1130,74,3,"秋岚画院")

            kktouch3(946,565,3,"单双人")

            kktouch3(1148,682,4,"开始挑战")

            kktouch3(1030,614,10,"就位")

            if check_and_try_game_home(4,"fuben"):
                sync_current_snapshot("进入副本成功")
                kktouch3(798, 485, 1, "关闭副本弹框")
                return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS,"进入副本完成")
            else:
                sync_current_snapshot("进入副本失败")
                kktouch3(798, 485, 1, "关闭副本弹框")
                return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS, "进入副本失败")
        except Exception as e:
            kkLogger_log(f"进入副本异常 {e}")
###########################################################################
# def safe_step_fuben_out(is_skip):
#     if is_skip == 0:
#         try:
#             kkLogger_log(f"开始退出副本")
#             check_and_try_game_home(4)
#             kktouch2(947,177,2,"退出副本")
#             if ocr_text_target_coords("确认",1043,343,1250,458,) is not None:
#                 kktouch2(1157,386,10,"确认")
#
#             sync_current_snapshot("退出副本")
#             if check_and_try_game_home(4):
#                 return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS, "退出副本完成")
#             else:
#                 return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS, "退出副本失败")
#         except Exception as e:
#             kkLogger_log(f"退出副本异常 {e}")
########################################################################
def step_gameset(step_gameset):
    if step_gameset == 0:
        connect_device("windows:///?title_re=逆水寒手游桌面版")
        check_yueka_jiazeng(1)
        check_game_update(1)

        sync_current_snapshot("游戏设置开始",None,False)
        for i in range(6):
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            target = local_ocr_text_target_coords("设置", 1214, 236, 1278, 621)
            if target:
                kktouch2(target[0], target[1],2,"设置")

                kktouch2(77+10, 445+37, 1, "视角")
                kktouch2(747+10, 126+37, 1, "2.5D")

                kktouch2(76, 359,1,"左侧画面")
                kktouch2(805, 319, 1, "流畅")



                kktouch2(70, 73,1,"左上退出游戏设定")
                kk_keyevent("{ESC}",2,0.5,"退出游戏设定")
                sync_current_snapshot("游戏设置完成")


                safe_step_fuben_in(0)
                return get_return_taskEvent("step_gameset", EventStatus.SUCCESS,"游戏设置完成回到主页")
            else:
                check_and_try_game_home(4)
                continue
        return get_return_taskEvent("step_gameset", EventStatus.SUCCESS,"游戏设置完成回到主页")
##################################################################################
def safe_step_juese(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游桌面版")
            return step_juese(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_juese error,{e}")
            check_and_try_game_home(4)

    return get_return_taskEvent("step_juese", EventStatus.FAILURE)

#####
def step_juese(is_skip):
    if is_skip == 0:

        kktouch2(1224 + 10, 32 + 37, 2, "菜单")
        kktouch3(1248, 275, 2, "背包")
        sync_current_snapshot("背包", None, True)
        kktouch3(1027, 56, 2, "纹玉")
        sync_current_snapshot("背包-纹玉", None, True)
        wenyu_count = 0
        if ocr_text_target_coords("兑换", 705, 40, 1250, 700):
            save_cropped_area_screen("纹玉兑换_面板属性", 705, 40, 1250, 700, 0.1)
            wenyu_count = get_wenyu_attr_ocr_number("限定纹玉额度", 927, 347, 1201, 403)

            kktouch3(909, 60, 2, "纹玉1-2")
            sync_current_snapshot("背包-纹玉1-2", None, True)
            target = ocr_text_target_coords("限", 1018, 230, 1259, 268)
            if ocr_text_target_coords("兑换", 705, 40, 1250, 700) and target:
                kktouch3(target[0], target[1], 1, "限定")
                save_cropped_area_screen("纹玉兑换_面板属性", 705, 40, 1250, 700, 0.1)
                wenyu_count = wenyu_count + get_wenyu_attr_ocr_number("限定纹玉额度", 1001, 448, 1130, 503)
        else:
            kktouch3(925, 56, 2, "纹玉2")
            sync_current_snapshot("背包-纹玉2", None, True)
            target = ocr_text_target_coords("限", 1018, 230, 1259, 268)
            if ocr_text_target_coords("兑换", 705, 40, 1250, 700) and target:
                kktouch3(target[0], target[1], 1, "限定")
                save_cropped_area_screen("纹玉兑换_面板属性", 705, 40, 1250, 700, 0.1)
                wenyu_count = get_wenyu_attr_ocr_number("限定纹玉额度", 1001, 448, 1130, 503)

        print(f"限定纹玉总额 {wenyu_count}")
        ocr_check_and_set_number("限定纹玉额度", str(wenyu_count))
        sync_current_snapshot(f"计算到限定纹玉额度总额为：{wenyu_count}", None, False)




        # kktouch2(1224 + 10, 32 + 37, 2, "菜单")
        # kktouch2(1250, 187, 1.5, "主页")
        # if ocr_text_target_coords("个人", 92, 40, 252, 113) is not None:
        #     sleep(5)
        #     save_cropped_screen_with_mulblur("主页_其他物品", [(17, 49, 317, 357)])
        #     kk_keyevent("{ESC}", 1, 1, "退出主页")


        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        # if kktouch_step(1247, 124, 2, "角色") is False:
        if kktouch2(1247, 124, 2, "角色") is False:
            return get_return_taskEvent("step_juese", EventStatus.SUCCESS, "未执行")
        kktouch2(68+10, 128+37,2,"左侧角色")

        uid = get_ocr_number(949,684,1068,729)
        sync_task_uid(uid)

        save_cropped_screen_with_mulblur("角色头图_面板属性",[(49, 41-40,449, 95),(866, 6,1261+10, 239+40),(884, 652,1101+10, 695+40),(515,6,788,186)])


        get_attr_ocr_number("评分",229, 103,441, 182)

        kktouch2(277+10, 257+37,1,"..角色-装备1")
        save_cropped_area_screen("角色-装备1_面板属性",347+10, 37+40,700+10, 634+40,0.1)

        kktouch2(323+10, 296+37,1,"..角色-装备2")
        save_cropped_area_screen("角色-装备2_面板属性",388+10, 32+40,742, 631+40,0.1)

        kktouch2(341+10, 425+37,1,"..角色-装备3")
        save_cropped_area_screen("角色-装备3_面板属性",436+10, 112+40,787, 712+40,0.1)

        kktouch2(284+10, 512+37,1,"..角色-装备4")
        save_cropped_area_screen("角色-装备4_面板属性",346+10, 115+40,698, 709+40,0.1)

        kktouch2(286+10, 598+37,1,"..角色-装备5")
        save_cropped_area_screen("角色-装备5_面板属性",346+10, 113+40,700, 710+40,0.1)

        kktouch2(256+10, 296+37,1,"..角色-装备6")
        save_cropped_area_screen("角色-装备6_面板属性",301+10, 34+40,655, 632+40,0.1)

        kktouch2(290+10, 343+37,1,"..角色-装备7")
        save_cropped_area_screen("角色-装备7_面板属性",346+10, 56+40,700, 658+40,0.1)

        kktouch2(254+10, 385+37,1,"..角色-装备8")
        save_cropped_area_screen("角色-装备8_面板属性",302+10, 98+40,656, 701+40,0.1)

        kktouch2(254+10, 467+37,1,"..角色-装备9")
        save_cropped_area_screen("角色-装备9_面板属性",301+10, 112+40,655, 710+40,0.1)

        kktouch2(256+10, 556+37,1,"..角色-装备10")
        save_cropped_area_screen("角色-装备10_面板属性",304+10, 112+40,656, 713+40,0.1)

        kktouch2(215+10, 344+37,1,"..角色-装备11")
        save_cropped_area_screen("角色-装备11_面板属性",259+10, 59+40,613, 655+40,0.1)

        kktouch2(212+10, 508+37,1,"..角色-装备12")
        save_cropped_area_screen("角色-装备12_面板属性",259+10, 113+40,616+10, 709+40,0.1)

        #############################################################
        kkLogger_log("======旅途截图开始")
        ############开始旅途
        kktouch3(75, 286, 2, "..左侧旅途")
        save_cropped_area_screen("纹玉兑换_面板属性", 864, 80, 1251, 687)
        # save_cropped_screen("旅途")
        kktouch3(67, 354, 2, "..左侧修行")
        save_cropped_screen("修行")

        #####################################################
        kktouch2(73+10, 194+37,2,"左侧背包")
        kktouch2(869, 716,2,"底部仓库")

        swipe_to_end((832, 175, 1170, 287),(1058, 191),(934, 523))
        save_cropped_area_screen("角色-背包_其他物品", 834, 124, 1181, 626, 0.2)

        long_snapshot("角色-背包_其他物品", 834, 629, 1181, 683, 24,1.5)

        kkLogger_log(f'背包截图结束,相关材料在{snapImgPath}')
        #############################################################
        kktouch3(869, 716,2,"底部仓库")
        kktouch3(52,169,1,"常规")
        findcangkuResult = find_all_subImg(Template(r"tpl1727872265286.png", threshold=0.4),191, 105,405, 160)
        cangkuCount = 0
        if findcangkuResult is None:
            cangkuCount = 0
        else:
            for line in findcangkuResult:
                kkLogger_log(line)
                if line['confidence'] > 0.4:
                    cangkuCount = cangkuCount +1
                    kktouch2(line['result'][0]+191,line['result'][1]+105,1,f"仓库第{cangkuCount}个包")
                    save_cropped_area_screen("仓库_其他物品", 105, 118, 450, 460)
            if cangkuCount >= 4:
                kktouch3(425,135,1,"最右边仓库")
                if (local_ocr_text_target_coords("是否", 408, 348, 871, 433)
                        or local_ocr_text_target_coords("取消", 77, 361, 169, 420)):
                    kktouch3(67,390,1,"取消")
                else:
                    save_cropped_area_screen("仓库_其他物品", 105, 118, 450, 460)
        print(f"=====仓库数量===={cangkuCount}")

        kk_keyevent("{ESC}",1,1,"退出仓库")

        return get_return_taskEvent("step_juese", EventStatus.SUCCESS,"角色信息完成")
    else:
        return get_return_taskEvent("step_juese", EventStatus.SUCCESS,"未执行")
###############################################################
def safe_step_jueji(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游桌面版")
            return step_jueji(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_jueji error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_jueji", EventStatus.FAILURE)
###################
def step_jueji(step_jueji):
    if step_jueji == 0:
        kkLogger_log("======武功-绝技截图开始")
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1065, 193,3,"武功") is False:
            return get_return_taskEvent("step_jueji", EventStatus.SUCCESS, "绝技未执行")
        kktouch2(64+10, 121+37,1,"左侧技能")
        kktouch2(68+10, 245+37,1,"绝技")
        save_cropped_screen("绝技_其他物品")

        if ocr_text_target_coords("升",877, 678,1056, 719) is not None:
            kktouch2(933,700,1,"绝技-升阶")
            sleep(2)
            # save_cropped_screen("绝技升阶_其他物品")
            jueji_pingtu()

        kk_keyevent("{ESC}",2,0.2)

        kkLogger_log("======武功-绝技截图完成")
        return get_return_taskEvent("step_jueji", EventStatus.SUCCESS,"绝技完成")
#############################################################################
def jueji_pingtu():
    try:
        images = []
        images.append(pyautogui.screenshot(region=(204, 14, 290 - 49, 278 - 182)))
        images.append(pyautogui.screenshot(region=(49, 183, 290 - 49, 278 - 182)))
        images.append(pyautogui.screenshot(region=(49, 283, 290 - 49, 278 - 182)))
        images.append(pyautogui.screenshot(region=(49, 383, 290 - 49, 278 - 182)))
        images.append(pyautogui.screenshot(region=(49, 483, 290 - 49, 278 - 182)))
        for i in range(10):
            screenshot = pyautogui.screenshot(region=(49, 575, 290 - 49, 278 - 182))
            images.append(screenshot)
            swipe((167, 666), (167, 540), steps=5, duration=1)
            sleep(1)
            new_screenshot = pyautogui.screenshot(region=(49, 575, 290 - 49, 278 - 182))
            similarity = calculate_similarity(new_screenshot, screenshot)
            print(1 - similarity)
            if (1 - similarity) > 0.2:
                break

        img_width, img_height = images[0].size

        images_per_row = 5
        total_rows = (len(images) + images_per_row - 1) // images_per_row
        final_width = img_width * images_per_row
        final_height = img_height * total_rows
        final_long_image = Image.new('RGB', (final_width, final_height),(125, 125, 125))
        for i, img in enumerate(images):
            row = i // images_per_row
            col = i % images_per_row
            x_offset = col * img_width
            y_offset = row * img_height
            final_long_image.paste(img, (x_offset, y_offset))
        final_long_image.save(f'{snapImgPath}\\绝技升阶_其他物品_xxxx.jpg')  # 保存拼接后的图像
    except Exception as e:
        print(e)
####################################################
def safe_step_neigong(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游桌面版")
            return step_neigong(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_neigong error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_neigong", EventStatus.FAILURE)
###################

def step_neigong(is_skip):
    if is_skip == 0:
        kkLogger_log("======内功截图开始")
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1065, 193,3,"武功") is False:
            return get_return_taskEvent("step_neigong", EventStatus.SUCCESS, "内功未执行")

        kktouch2(75+10, 123+37,1,"技能")
        kktouch2(75+10, 434+37,1,"内功")

        kktouch2(346+10, 658+37,1,"..详细属性加成")
        # if ocr_text_has_number(399, 47, 878, 662):
        if ocr_text_target_coords("加成",399, 47,878, 662) is not None:
            save_cropped_area_screen("内功详细属性加成_打造内功",399, 47,878, 715,0)
            kktouch2(270+10, 699+37,1,"取消详细属性加成")


        for j in range(3):
            kktouch2(1001 + 10, 653 + 37, 1.5, "全部内功")
            kktouch2(143+10, 107+37,1,"筛选")
            # kk_scroll((1008, 587), -5, 1, "词条搜索")
            # kktouch2(1054, 546,1.5,"词条")

            kktouch2(1059, 658,1,"词条")
            kktouch2(440, 219,1,"灵韵")
            kktouch2(1169, 701,1.2,"确定")
            save_cropped_screen("lingyunall_打造内功")
            # if ocr_text_target_coords("中",96,110,200,159) is not None:
            if True:
                start_x = 109
                start_y = 170
                end_x = 876
                end_y = 286
                loop_height = 127
                lingyun_count = 0
                for i in range(4):
                    find_lingyun_result = find_all_subImg(Template(r"tpl1726237406954.png", threshold=0.6), start_x,
                                                          start_y, end_x, end_y)
                    neigong_count = 0
                    if find_lingyun_result is None:
                        break
                    # elif ocr_text_has_txt(start_x, start_y, end_x, end_y) is False:
                    #     break
                    else:
                        kkLogger_log(f"第{i + 1}行，find_lingyun_result数量：{len(find_lingyun_result)}")
                        for line in find_lingyun_result:
                            neigong_count = neigong_count + 1
                            if line['confidence'] > 0.5:
                                x1 = line['result'][0]
                                y1 = line['result'][1]
                                kktouch2(x1 + start_x, y1 + start_y, 1, f"第{neigong_count}个灵韵内功")

                                # swipe((1055, 505), (1055, 450),duration=0.2,steps=2)
                                kk_scroll((1055, 505), -3, 1, "灵韵")


                                # find_lingyun_result = find_subImg(Template(r"tpl1726830336235.png"), 868, 407, 1263,663)

                                # screen = G.DEVICE.snapshot()
                                # cropped_screen = aircv.crop_image(screen, (868, 485, 1263, 663))
                                # pil_img = cv2_2_pil(cropped_screen)
                                # tile_img_2 = np.array(pil_img)

                                # if find_lingyun_result is not None or local_ocr_text_target_coords("灵韵",868, 407, 1263,663) is not None:
                                # if contains_orange_in_corner(tile_img_2, (1263 - 868, 663 - 485)):

                                if local_ocr_text_target_coords("等级",868, 407, 1263,663) is not None:
                                    save_cropped_area_screen("灵韵_打造内功_0", 857, 59, 1266, 669, 0)
                                    ocr_add_to_get(857, 59, 1129, 105)
                                    lingyun_count = lingyun_count + 1
                                    print(f"灵韵数量:{lingyun_count}")
                                else:
                                    save_cropped_area_screen("灵韵_打造内功_1", 857, 59, 1266, 669, 0)
                        start_y = start_y + loop_height
                        end_y = end_y + loop_height

                ocr_check_and_set("灵韵数量", str(lingyun_count))
                sync_current_snapshot(f"识别到灵韵数量：{lingyun_count}:{neigong_lingyun_set}",None,True)
                break

        kk_keyevent("{ESC}", 2, int(0.2))
    return get_return_taskEvent("step_neigong", EventStatus.SUCCESS,"内功完成")
####################################################
def safe_step_dazao(is_skip):
    for i in range(3):
        try:
            return step_dazao(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_dazao error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_dazao", EventStatus.FAILURE)
###################
def step_dazao(is_skip):
    if is_skip == 0:
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1172 , 329 , 1, "打造") is False:
            return get_return_taskEvent("step_dazao", EventStatus.SUCCESS, "无打造，未执行")
        kktouch2(77,160,1,"左侧强化")
        save_cropped_screen("打造强化_打造内功")

        kktouch3(71,224,2,"左侧打造")

        sync_current_snapshot("打造强化")

        loop = 80
        start_x = 221
        start_y = 167
        for i in range(6):

            kktouch2(start_x,start_y,0.5,f"第{i+1}个打造")

            kktouch3(1238, 336,2,"已有特技库")
            kktouch3(1263, 340,2,"已有特技库")

            if local_ocr_text_target_coords("打造", 92, 43, 194, 101) is not None:
                sync_current_snapshot(f"无法进入已有特技库 {i}",None,True)
                start_y = start_y + loop
                continue
            kktouch3(922,159,1,"第一个")
            kk_scroll((1032, 632), -10, 1.5, "打造滚动底部")
            kktouch2(872 + 10, 643 + 37, 1, "..最后一个")
            kk_scroll((1032, 632), 10, 1.5, "打造滚动顶部")

            target_arrays = ocr_text_target_coords_arrays("激活", 732, 98, 1266, 726)
            if target_arrays is not None:
                for item in target_arrays:
                    save_cropped_area_screen_dazao("打造属性_打造内功", item[0] - 384, item[1] - 40,(item[0] - 384) + 444, (item[1] - 40) + 81)
            else:
                target_arrays = ocr_text_target_coords_arrays("提升",732, 98,1266, 726)
                if target_arrays is not None:
                    for item in target_arrays:
                        save_cropped_area_screen_dazao("打造属性_打造内功",item[0]-121, item[1]-57,(item[0]-121)+444, (item[1]-57)+81)

            if target_arrays is not None:
                for i in range(5):
                    pyautogui.scroll(-21)
                    sleep(2)
                    # target_arrays_single = ocr_text_target_coords("激活", 622,601,1230,741)
                    target_arrays_single = wait_for_ocr_txt("激活", 2,622,601,1230,741)
                    if target_arrays_single:
                        save_cropped_area_screen_dazao("打造属性_打造内功", target_arrays_single[0] - 384, target_arrays_single[1] - 40,
                                                       (target_arrays_single[0] - 384) + 444, (target_arrays_single[1] - 40) + 81)
                    else:
                        break


            kktouch2(71+10, 40+37,1,"左上返回")
            start_y = start_y + loop

        # kktouch2(71+10, 36+37,1,"左上返回")
        kk_keyevent("{ESC}", 1, 0.2)
        kkLogger_log("打造结束")
    return get_return_taskEvent("step_dazao", EventStatus.SUCCESS,"打造完成")
###########################################################################################################

#####################################################################################################################
def safe_step_waiguan(is_skip):
    for i in range(2):
        try:
            return step_waiguan(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_waiguan error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_waiguan", EventStatus.FAILURE)
##############################################################################
def waiguan_substep_guancang1025(is_skip):
    if is_skip == 0:
        global tianniran_count
        global waiguan_ziran_fashi_set
        kktouch2(1094, 123, 2, "衣品")
        sync_current_snapshot("外观-衣品")
        save_cropped_area_screen("衣品", 835, 81, 1245, 651)
        has_yipin = get_attr_ocr_number("衣品", 1009, 84, 1254, 125)
        kk_keyevent("{ESC}", 1, 2, "退出衣品")


        kktouch2(1217, 124,5,"馆藏")
        target = wait_for_ocr_txt("跳过",3, 954, 54, 1273, 177)
        if target:
            sleep(5)
            kktouch3(target[0],target[1],2,"跳过")
            sleep(3)

        connect_device("Windows:///?title_re=逆水寒手游桌面版")
        save_cropped_screen(f"馆藏总等级_其他物品")
        sync_current_snapshot("馆藏")

        kktouch2(548,609,1.5,"时鉴赏")
        kktouch3(80, 176, 1, "")
        screenshot_1 = pyautogui.screenshot(region=(187, 133, 573 - 187,730 - 133))
        kktouch3(80, 237, 1, "")
        screenshot_2 = pyautogui.screenshot(region=(187, 133, 573 - 187,730 - 133))
        kktouch3(80, 305, 1, "")
        screenshot_3 = pyautogui.screenshot(region=(187, 133, 573 - 187,730 - 133))
        kktouch3(80, 375, 1, "")
        screenshot_4 = pyautogui.screenshot(region=(187, 133, 573 - 187,730 - 133))
        horizontal_screenshot_stitch('时鉴赏_其他物品',[screenshot_1, screenshot_2, screenshot_3, screenshot_4])
        
        kk_keyevent("{ESC}", 1, 1.5, "退出时鉴赏")

        kktouch2(335, 464,2,"天赏")
        kktouch2(89, 242, 2, "青丝馆霓")


        target = local_ocr_text_target_coords("暂无", 528, 558, 832, 618)
        if target is None:
            kktouch3(990, 68, 4, "自染分享")
            if wait_for_ocr_txt("角色",2,103,662,392,735):
                kktouch3(168,704,0.5,"角色")
                save_cropped_area_screen("头图_天赏外观", 116, 99, 1108, 654)
                kk_keyevent("{ESC}",1,1,"返回")
            else:
                save_cropped_area_screen("头图_天赏外观", 112, 95, 1116, 642)
                kktouch3(1219, 72, 1.5, "退出自染分享")


            kktouch2(334,140,1,"分类展示")
            save_cropped_screen("自染_天赏外观")

            ocr_add_to_get_and_count_tianniran_number(1091, 127, 1276, 167)
            sleep(1)
            if tianniran_count == 0:
                ocr_add_to_get_and_count_tianniran_number(1156, 109, 1272, 215)

            sync_current_snapshot(f"识别到自染数量:{tianniran_count}",None,True)

            if tianniran_count < 10:
                tianniran_count = 163

            if tianniran_count > 4:
                start_x = 178
                start_y = 167
                end_x = 301
                end_y = 370
                save_ziran_pic(start_x,start_y,end_x,end_y)
                not_3_count_num = 0 #不满三个自染的次数
                for i in range(30):
                    line1_coords = (178, 167, 301, 370)

                    swipe_and_judge_end((165, 310, 544, 372), (418, 571), (418, 335))
                    #第一行截图
                    zr_count = save_ziran_pic(line1_coords[0], line1_coords[1], line1_coords[2], line1_coords[3])
                    if zr_count < 3:
                        not_3_count_num = not_3_count_num + 1
                        if not_3_count_num > 2:
                            break

                    kktouch3(482, 675, 0.5, "空白")
                    check_area = (514,36,1000,241)
                    if wait_for_ocr_txt("更多", 2, check_area[0], check_area[1], check_area[2], check_area[3]):
                        break

                    kktouch3(224, 675, 0.5, "空白")
                    if wait_for_ocr_txt("更多", 2, check_area[0], check_area[1], check_area[2], check_area[3]):
                        break

                #识别第二行
                save_ziran_pic(177, 370, 301, 573)
                kk_scroll((341, 662), -10, 1, "自染滚动底部")
                # 识别倒数第二行
                save_ziran_pic(177, 308, 301, 510)
                #识别最后一行
                save_ziran_pic(177, 512, 301, 715)

                if tianniran_count == 163 and ziran_pic_count > 0:
                    ocr_check_and_set("天霓染", str(ziran_pic_count))
                elif 0 < tianniran_count < 100:
                    ocr_check_and_set("天霓染", str(tianniran_count))

                sync_current_snapshot(f"自染:{ziran_pic_count}:{waiguan_ziran_fashi_set}", None, True)
        else:
            # pass
            kktouch3(990, 68, 4, "自染分享")
            if wait_for_ocr_txt("角色", 2, 103, 662, 392, 735):
                kktouch3(168, 704, 0.5, "角色")
                save_cropped_area_screen("头图_天赏外观", 116, 99, 1108, 654)
                kk_keyevent("{ESC}", 1, 1, "返回")
            else:
                save_cropped_area_screen("头图_天赏外观", 112, 95, 1116, 642)
                kktouch3(1219, 72, 1.5, "退出自染分享")
            # save_cropped_screen_with_blur2("头图_天赏外观", 112, 95, 1116, 667, 112, 642, 1116, 667)
            # kktouch3(1219, 72, 1.5, "退出自染分享")

            save_cropped_screen("自染_天赏外观")

        kk_keyevent("{ESC}", 1, 1.5, "退出天赏")

        kktouch2(143, 689,1.2,"国色")
        save_cropped_screen(f"国色_其他物品")
        get_attr_ocr_number("国色值",843, 47,1130, 95)
        kk_keyevent("{ESC}", 1, 1.5, "退出国色")

        kkLogger_log("馆藏结束")

#################################################################################
def save_ziran_pic(start_x,start_y,end_x,end_y):
    global ziran_pic_count
    global tianniran_count
    global waiguan_ziran_fashi_set
    loop_x = 301 - 177 + 1
    count = 0
    for j in range(3):
        if ziran_pic_count < tianniran_count:
            if tianniran_count == 163 and local_ocr_text_has_txt(start_x,end_y-37,end_x,end_y) is False:
                kkLogger_log(f"..未发现自染头 {start_x},{end_y-37}")
                start_x = start_x + loop_x
                end_x = end_x + loop_x
                continue

            kktouch3(start_x + 50, start_y + 50, 0.5, "左侧自染头")
            pic_width = 149
            pic_height = 115

            hit_txt_arrays = []
            ocr_text_target_coords_arrays_inverted("点", start_x-20, start_y-20, start_x+50, start_y+50, hit_txt_arrays)
            if len(hit_txt_arrays) == 0:
                sleep(1)
                ocr_text_target_coords_arrays("点", start_x-20, start_y-20, start_x+50, start_y+50, hit_txt_arrays)

            if len(hit_txt_arrays) > 0 or local_ocr_text_has_txt(start_x,end_y-37,end_x,end_y) is not False:

                ziran_fashi = get_first_ocr_txt_local(start_x,end_y-40,end_x,end_y+5)
                if ziran_fashi is None:
                    ziran_fashi = get_first_ocr_txt_local(start_x,end_y-40,end_x,end_y+5)

                if ziran_fashi is None:
                    kkLogger_log("..自染识别到为[None]，跳过")
                    sync_current_snapshot(f"自染识别到为[None],{start_x, start_y, end_x, end_y}", None, True)

                    start_x = start_x + loop_x
                    end_x = end_x + loop_x
                    continue

                kkLogger_log(f"..识别到:[{ziran_fashi}]")
                if ziran_fashi is not None and ziran_fashi in waiguan_ziran_fashi_set and len(ziran_fashi) > 1:
                    start_x = start_x + loop_x
                    end_x = end_x + loop_x
                    kkLogger_log(f"..[{ziran_fashi}]，已存在，跳过")
                    continue

                if ziran_fashi is not None and ziran_fashi != "None":
                    waiguan_ziran_fashi_set.append(ziran_fashi)
                    save_cropped_area_screen(f"自染头_天赏外观", start_x, start_y, end_x, end_y, 0)

                item = "2点"
                if len(hit_txt_arrays) > 0:
                    item = hit_txt_arrays[0]

                if "6" in item:
                    ziran_pic_count = ziran_pic_count + 2
                    save_cropped_area_screen(f"自染方案_天赏外观", 823, 154-50, 823 + pic_width, 154-50 + pic_height, 0)
                    save_cropped_area_screen(f"自染方案_天赏外观", 717, 327-50, 717 + pic_width, 327-50 + pic_height, 0)
                    save_cropped_area_screen(f"自染方案_天赏外观", 862, 510-50, 862 + pic_width, 510-50 + pic_height, 0)
                elif "4" in item:
                    ziran_pic_count = ziran_pic_count + 1
                    save_cropped_area_screen(f"自染方案_天赏外观", 823, 154, 823 + pic_width, 154 + pic_height, 0)
                    save_cropped_area_screen(f"自染方案_天赏外观", 717, 327, 717 + pic_width, 327 + pic_height, 0)
                else:
                    save_cropped_area_screen(f"自染方案_天赏外观", 845, 242, 845 + pic_width, 242 + pic_height, 0)

                ziran_pic_count = ziran_pic_count + 1

                count = count + 1
            else:
                kkLogger_log(f"..未识别到:自染")

            start_x = start_x + loop_x
            end_x = end_x + loop_x

    print(f"发现{count}个自染")
    return count
##################################################################################
#######################################################################################################################
############################################################################
def waiguan_substep_shizhuang(is_skip):
    if is_skip == 0:
        # kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        # if kktouch2(1156, 193, 1, "外观") is False:
        #     return
        # connect_device("Windows:///?title_re=逆水寒手游桌面版")

        if local_ocr_text_target_coords("点",12,530,142,619) is not None:
            sync_current_snapshot("发现引导",None,True)
            kk_keyevent("{ESC}",1,1,"取消引导")
            check_game_alert_note()

        if local_ocr_text_target_coords("可以查看",787,78,1025,143) is not None:
            # sync_current_snapshot("发现引导",None,True)
            kk_keyevent("{ESC}",1,1,"取消引导装扮站")
            check_game_alert_note()


        kkLogger_log("开始时装")
        # kktouch3(258, 718,1,"底部时装")
        kktouch3(358,713,1,"底部时装")
        kkLogger_log("开始套装")
        kktouch3(71, 161,1,"套装")
        kktouch3(451, 653,2,"套装回到顶部")
        swipe_to_end((108, 217, 478, 615),(294, 233),(294, 537))

        kktouch3(405, 296,2,"右上第一个套装")

        sync_current_snapshot("外观-套装")

        split_start_x = 113
        split_start_y = 213
        split_end_x = 482
        split_end_y = 613
        for i in range(60):

            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),split_start_x, split_start_y,split_end_x, split_end_y)

            if find_lock_result is None:
                kkLogger_log(f"套装第{i}页")

                if i % 3 == 0:
                    kktouch3(370, 630,0.02,"右下套装")
                elif i % 3 == 1:
                    kktouch3(277, 630,0.02,"底中套装")
                else:
                    kktouch3(163, 630, 0.02, "底左套装")

                old_taozhuang_len = len(waiguan_taozhuang_set)
                if save_cropped_area_screen_and_ocr_add_to_get("套装",split_start_x, split_start_y,split_end_x, split_end_y,[2,3]):
                    sync_current_snapshot("套装结束。")
                    kktouch2(204, 76, 1, "套装重置")
                    kktouch2(1161, 389, 1, "确认套装重置")
                    break

                if i > 3 and old_taozhuang_len == len(waiguan_taozhuang_set):
                    sync_current_snapshot("套装结束【无新增】")
                    kktouch2(204, 76, 1, "套装重置")
                    kktouch2(1161, 389, 1, "确认套装重置")
                    break

                kktouch3(1072,716,0.1,"底部周边")
                kktouch3(357,715,1,"底部时装")
            else:
                kkLogger_log(f"套装识别到锁")
                sleep(2)
                save_cropped_area_screen_and_ocr_add_to_get("套装",split_start_x, split_start_y,split_end_x, split_end_y,[2,3],Template(r"tpl1727075488904.png", threshold=0.7))

                sync_current_snapshot("套装结束")

                kktouch2(204, 76,1,"重置")
                kktouch2(1161, 389,1,"确认重置")
                break
        # sync_current_snapshot(f"{waiguan_set}",None,False)
####################################################
def waiguan_substep_fashi(is_skip):
    if is_skip == 0:
        kkLogger_log("开始时装-发式")
        kktouch3(62+10, 406,1.2,"左侧发式")
        sync_current_snapshot("外观-发式")
        #滚动到顶部
        kktouch3(451, 653,2,"回到顶部")
        swipe_to_end((108, 217, 478, 615),(294, 233),(294, 537))
        kktouch3(451, 653,2,"回到顶部")
        kktouch3(405, 296,2,"右上第一个")

        split_start_x = 113
        split_start_y = 213
        split_end_x = 482
        split_end_y = 613
        for i in range(60):
            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),split_start_x, split_start_y,split_end_x, split_end_y)
            if find_lock_result is None:
                kkLogger_log(f"发式第{i}页")

                if i % 2 == 0:
                    kktouch3(277, 630,0.02,"底中")
                else:
                    kktouch3(367, 630,0.02,"底右")

                old_fashi_len = len(waiguan_fashi_set)
                if save_cropped_area_screen_and_ocr_add_to_get("发式",split_start_x, split_start_y,split_end_x, split_end_y,[2,3]):
                    sync_current_snapshot("发式结束.")

                    kktouch2(204, 76, 1, "重置")
                    kktouch2(1161, 389, 1, "确认重置")
                    break

                if i > 3 and old_fashi_len == len(waiguan_fashi_set):
                    sync_current_snapshot("发式结束[无新增]")
                    kktouch2(204, 76, 1, "重置")
                    kktouch2(1161, 389, 1, "确认重置")
                    break

                kktouch3(67+10, 125+37,0.5,"套装")
                kktouch3(56+10, 372+37,0.5,"发式")
            else:
                kkLogger_log(f"发式识别到锁")
                sleep(2)
                save_cropped_area_screen_and_ocr_add_to_get("发式",split_start_x, split_start_y,split_end_x, split_end_y,[2,3],Template(r"tpl1727075488904.png", threshold=0.7))

                sync_current_snapshot("发式结束")

                kktouch2(204, 76, 1, "重置")
                kktouch2(1161, 389, 1, "确认重置")
                break
        # sync_current_snapshot(f"{waiguan_set}", None, False)
####################################################
def waiguan_substep_wuqi(is_skip):
    if is_skip == 0:
        kktouch3(638,714,1,"底部武器")#..底部武器
        kktouch3(638,714,1,"底部武器")#..底部武器
        kktouch3(79,154,1,"分块")
        kktouch3(79,154,1,"分块")
        kktouch3(68,519,1,"套装")


        sync_current_snapshot("外观-武器")

        kkLogger_log("开始武器")
        kktouch3(430+10, 618+37,2,"武器回到顶部")
        swipe_to_end((110, 178,466, 604),(287, 200),(287, 500))
        for i in range(11):
            find_lock_result = find_subImg(Template(r"tpl1727007667521.png", threshold=0.7),108, 206,462, 624)

            if find_lock_result is None:
                kkLogger_log(f"武器第{i}页")
                save_cropped_area_screen_and_ocr_add_to_get("武器",114, 171,476, 600,[3,2],Template(r"tpl1727007667521.png", threshold=0.7))

                swipe_and_judge_end((110, 178, 466, 604), (287, 656), (287, 199))
                sleep(1)
            else:
                kkLogger_log(f"武器识别到锁")
                sleep(2)
                save_cropped_area_screen_and_ocr_add_to_get("武器",114, 171,476, 600,[3,2],Template(r"tpl1727007667521.png", threshold=0.7))

                sync_current_snapshot("武器结束")
                kktouch2(204, 76, 1, "重置")
                kktouch2(1161, 389, 1, "确认重置")
                break

####################################################
def waiguan_substep_wuqi_huanshen(is_skip):
    if is_skip == 0:
        kktouch3(73,232,1,"环身")

        sync_current_snapshot("外观-武器环身")

        kkLogger_log("开始武器环身")
        kktouch3(430+10, 618+37,2,"武器环身回到顶部")
        swipe_to_end((110, 178,466, 604),(287, 200),(287, 500))
        for i in range(11):
            find_lock_result = find_subImg(Template(r"tpl1727007667521.png", threshold=0.7),108, 206,462, 624)

            if find_lock_result is None:
                kkLogger_log(f"武器环身第{i}页")
                save_cropped_area_screen_and_ocr_add_to_get("武器",114, 171,476, 600,[3,2],Template(r"tpl1727007667521.png", threshold=0.7))

                swipe_and_judge_end((110, 178, 466, 604), (287, 656), (287, 199))
                sleep(1)
            else:
                kkLogger_log(f"武器环身识别到锁")
                sleep(2)
                save_cropped_area_screen_and_ocr_add_to_get("武器环身",114, 171,476, 600,[3,2],Template(r"tpl1727007667521.png", threshold=0.7))

                sync_current_snapshot("武器环身结束")
                kktouch2(204, 76, 1, "重置")
                kktouch2(1161, 389, 1, "确认重置")
                break
####################################################
def waiguan_substep_zhuoqi(is_skip):
    if is_skip == 0:
        kktouch3(784,714,1,"底部坐骑")#..底部坐骑
        kktouch3(784,714,1,"底部坐骑")#..底部坐骑
        kktouch3(79,156,1,"左侧坐骑")#..坐骑
        kktouch3(79,156,1,"左侧坐骑")#..坐骑

        sync_current_snapshot("外观-坐骑")
        kkLogger_log("开始坐骑")
        kktouch3(430+10, 616+37,2,"坐骑回到顶部")
        swipe_to_end((110, 215,470, 615),(230, 215+30),(230, 500))
        start_x = 114
        start_y = 210
        end_x = 484
        end_y = 611
        for i in range(40):

            find_lock_result = find_subImg(Template(r"tpl1726951381717.png", threshold=0.75),102, 258,467, 630)

            if find_lock_result is None:
                kkLogger_log(f"坐骑第{i}页")
                kktouch3(365, 637,0.1,"右下坐骑")
                old_zuoqi_len = len(waiguan_zuoqi_set)
                if save_cropped_area_screen_and_ocr_add_to_get("坐骑",start_x, start_y,end_x, end_y,[2,3]):
                    sync_current_snapshot("坐骑结束。")
                    kktouch2(204, 76, 1, "套装重置")
                    kktouch2(1161, 389, 1, "确认套装重置")
                    break
                if i > 3 and old_zuoqi_len == len(waiguan_zuoqi_set):
                    sync_current_snapshot("坐骑结束【无新增】")
                    kktouch2(204, 76, 1, "套装重置")
                    kktouch2(1161, 389, 1, "确认套装重置")
                    break

                kktouch3(63, 237,0.1,"祥瑞")
                kktouch3(67, 161,0.1,"坐骑")
            else:
                kkLogger_log(f"坐骑识别到锁")
                sleep(2)
                save_cropped_area_screen_and_ocr_add_to_get("坐骑",start_x, start_y,end_x, end_y,[2,3],Template(r"tpl1726951381717.png", threshold=0.7))

                sync_current_snapshot("坐骑结束")
                kktouch2(204, 76, 1, "重置")
                kktouch2(1161, 389, 1, "确认重置")
                break
        # sync_current_snapshot(f"{waiguan_set}", None, False)
########################################
def waiguan_substep_xiangrui(is_skip):
    if is_skip == 0:
        kktouch3(791,710, 1, "底部坐骑")  # ..底部坐骑
        kktouch3(791,710, 1, "底部坐骑")  # ..底部坐骑
        kktouch3(80,235,1,"左侧祥瑞")#..坐骑
        kktouch3(80,235,1,"左侧祥瑞")#..坐骑
        kkLogger_log("开始祥瑞")
        sync_current_snapshot("外观-祥瑞")
        kktouch3(431+10, 619+37,2,"祥瑞回到顶部")
        swipe_to_end((110, 215,470, 615),(230, 215+30),(230, 615))
        kktouch3(410+10, 290+37,2,"右上第一个祥瑞")

        start_x = 114
        start_y = 215
        end_x = 477
        end_y = 615
        lock_num = 0
        for i in range(27):
            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),102, 186,472, 598)

            if find_lock_result is None:
                kkLogger_log(f"祥瑞第{i}页")
                if i % 2 == 0:
                    kktouch3(308, 640, 1, "右中祥瑞")
                else:
                    kktouch3(182, 640,1,"底右")
                old_xiangrui_len = len(waiguan_xiangrui_set)
                save_cropped_area_screen_and_ocr_add_to_get("祥瑞",start_x, start_y,end_x, end_y,[2,3])

                if i > 3 and old_xiangrui_len == len(waiguan_xiangrui_set):
                    sync_current_snapshot("祥瑞结束【无新增】")
                    kktouch2(204, 76, 1, "重置")
                    kktouch2(1161, 389, 1, "确认重置")
                    break
                kktouch3(67, 161,0.1,"坐骑")
                kktouch3(63, 237,0.1,"祥瑞")
            else:
                lock_num = lock_num + 1
                kkLogger_log(f"祥瑞识别到锁")
                sleep(2)
                save_cropped_area_screen_and_ocr_add_to_get("祥瑞",start_x, start_y,end_x, end_y,[2,3],Template(r"tpl1727075488904.png", threshold=0.75))
                if lock_num < 2:
                    if i % 2 == 0:
                        kktouch3(308, 640, 1, "右中祥瑞")
                    else:
                        kktouch3(182, 640, 1, "底右")
                        
                    kktouch3(67, 161, 0.1, "坐骑")
                    kktouch3(63, 237, 0.1, "祥瑞")
                    continue
                else:
                    sync_current_snapshot("祥瑞结束")
                    kktouch2(204, 76, 1, "重置")
                    kktouch2(1161, 389, 1, "确认重置")
                    break
        print(waiguan_xiangrui_set)
        # sync_current_snapshot(f"{waiguan_set}", None, False)
#########################################################
def check_and_get_jueji(clickPoint, clickName,check_words,check_coords,save_coords,jueji_name):
    # kktouch3(clickPoint[0],clickPoint[1], 1, clickName)
    # kktouch3(1099, 137, 0.5, "未拥有")
    # if local_ocr_text_target_coords(check_words, check_coords[0], check_coords[1], check_coords[2], check_coords[3]) is None:
    #     kktouch3(1099, 137, 0.5, "取消未拥有1")
    #     save_cropped_area_screen(f"武功江湖{clickName}_天赏外观",save_coords[0], save_coords[1], save_coords[2], save_coords[3])
    #     kkLogger_log(f"识别到 {jueji_name}")
    #     add_to_get(jueji_name)
    #     waiguan_ts_jineng_set.append(jueji_name)
    # else:
    #     kktouch3(1099, 137, 0.5, "取消未拥有2")
    # kktouch3(clickPoint[0], clickPoint[1], 0.2, "")

    kktouch3(clickPoint[0], clickPoint[1], 0.5, clickName)
    if local_ocr_text_has_txt(save_coords[0], save_coords[1], save_coords[2],save_coords[3]) is False:
        kktouch3(1099, 137, 0.5, "取消未拥有。")
        save_cropped_area_screen(f"武功江湖{clickName}_天赏外观",save_coords[0], save_coords[1], save_coords[2], save_coords[3])
        kkLogger_log(f"识别到 {jueji_name}")
        add_to_get(jueji_name)
        waiguan_ts_jineng_set.append(jueji_name)
        kktouch3(1099, 137, 0.5, "未拥有。")
        kktouch3(clickPoint[0], clickPoint[1], 0.5, "")


def waiguan_substep_jueji(is_skip):
    if is_skip == 0:
        check_coords = (913, 612, 1015, 654)
        save_coords = (1069,177,1248,284)
        color_check_coords = (1074,121,1119,154)
        kktouch3(914,711,2,"底部武功")#..底部武功
        kktouch3(65,273,1,"左侧绝技")#..绝技
        sync_current_snapshot("外观-绝技")

        kktouch3(1099, 137, 1, "未拥有")
        if check_similar_color_in_region(color_check_coords[0],color_check_coords[1],color_check_coords[2],color_check_coords[3], (239, 223, 172)) is False:
            kktouch3(1099, 137, 0.5, "未拥有")

        check_and_get_jueji((201, 223),"冰火绝灭","石",check_coords,  save_coords,"明河曙天·长鲸天斗")

        check_and_get_jueji((297, 223),"残心三绝剑","石", check_coords,  save_coords,"天曦四象·朱雀")

        check_and_get_jueji((189, 341),"剑破乾坤","石",check_coords,  save_coords,"日曜八荒·轩辕剑")

        check_and_get_jueji((295, 347),"万剑绝","石",check_coords,  save_coords,"山海祠神·阳升羲和")

        check_and_get_jueji((293, 462),"花X凌波","穿,香,烟",save_coords,  save_coords,"绯烟香袅")

        check_and_get_jueji((187, 585),"流风千仞","石",check_coords,  save_coords,"琼华盈霄·灵泽天香")

        check_and_get_jueji((288, 585),"长歌献君","石",check_coords,  save_coords,"重蝶化梦")

        # 滚动窗口
        pyautogui.scroll(-20)  # 向下滚动
        sleep(3)

        check_and_get_jueji((195, 590),"狂发一怒","石",check_coords,  save_coords,"青丘雪")

        check_and_get_jueji((292, 590),"红莲焚夜","石",check_coords,  save_coords,"天曦四象·白虎")

        # 滚动窗口
        pyautogui.scroll(-20)  # 向下滚动
        sleep(3)

        check_and_get_jueji((195, 590),"繁花一梦","石",check_coords,  save_coords,"天曦四象·青龙")

        check_and_get_jueji((292, 590),"九天雷引","石",check_coords,  save_coords,"岁星行渡·千重焰")

        # 滚动窗口
        pyautogui.scroll(-20)  # 向下滚动
        sleep(3)

        check_and_get_jueji((195, 585),"法天象地","石",check_coords,  save_coords,"降魔破邪·金刚明王")

        check_and_get_jueji((292, 585),"相夷太剑","石",check_coords,  save_coords,"明河曙天·醉卧西海")

        # 滚动窗口
        pyautogui.scroll(-20)  # 向下滚动
        sleep(3)

        check_and_get_jueji((195, 585),"剑霄飞流","石",check_coords,  save_coords,"剑鸣天寰")

        check_and_get_jueji((292, 585),"蝶舞清梦","石",check_coords,  save_coords,"妖影寻灵·狐舞青丘")

        pyautogui.scroll(-20)  # 向下滚动
        sleep(3)

        check_and_get_jueji((292, 585),"剑魂冲销","石",check_coords,  save_coords,"丹青纵横·龙潭墨云")

        pyautogui.scroll(-20)  # 向下滚动
        sleep(3)


        check_and_get_jueji((191, 585),"万想鹰扬","石",check_coords,  save_coords,"百鬼问道·冥幽狼影")

        pyautogui.scroll(-20)  # 向下滚动
        sleep(3)

        check_and_get_jueji((292, 585),"烬海焚莲","石",check_coords,  save_coords,"九方嘉瑞·天瀑玄鳞")


        sync_current_snapshot(f"天赏技能：{waiguan_ts_jineng_set}", None, False)
        ##########################################################################
        try:
            kktouch3(65+10, 334+37,4,"左侧江湖")#..江湖
            sleep(5)

            kktouch3(283, 350, 1, "辉雪寒瑛")
            coords = local_ocr_text_target_coords("雪",117, 420,380, 663)
            if coords is not None:
                touch(coords)
                kkLogger_log(".. 辉雪寒瑛")
                sleep(1)

            if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
                kkLogger_log("识别到 辉雪寒瑛")
                save_cropped_area_screen("武功江湖辉雪寒瑛_天赏外观",save_coords[0], save_coords[1],save_coords[2], save_coords[3])
                add_to_get("山海祠神·赤凰重明")
                waiguan_ts_jineng_set.append("山海祠神·赤凰重明")


            kktouch3(184, 450, 1, "芳心妙愈")
            coords = local_ocr_text_target_coords("心",117, 420,380, 663)
            if coords is not None:
                touch(coords)
                kkLogger_log("..芳心妙愈")
                sleep(1)
            if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
                kkLogger_log("识别到 芳心妙愈")
                save_cropped_area_screen("武功江湖芳心妙愈_天赏外观",save_coords[0], save_coords[1],save_coords[2], save_coords[3])
                add_to_get("新世华霓·喵到病除")
                waiguan_ts_jineng_set.append("新世华霓·喵到病除")
        except Exception as e:
            kkLogger_log(f"----- error,{e}")

        if check_similar_color_in_region(color_check_coords[0],color_check_coords[1],color_check_coords[2],color_check_coords[3], (239, 223, 172)):
            kktouch3(1099, 137, 0.5, "未拥有")
    ###############################################################
        kktouch3(65 + 10, 425 + 37, 1, "左侧轻功")  # ..轻功
        kkLogger_log("开始轻功")

        swipe_to_end((876, 181, 1238, 511), (1055, 179), (1055, 492))
        pyautogui.scroll(-5)  # 向下滚动
        sleep(2)
        save_cropped_area_screen_and_ocr_add_to_get("武功-轻功", 880, 148, 1248, 594, [4, 2],
                                                    Template(r"tpl1727017209837.png", threshold=0.6))

        screenshot_aa = pyautogui.screenshot(region=(880, 148, 1248 - 880, 594 - 148))

        swipe_to_end((876, 181, 1238, 511), (1055, 492), (1055, 179))
        save_cropped_area_screen_and_ocr_add_to_get("武功-轻功", 879, 163, 1245, 612, [4, 2],
                                                    Template(r"tpl1727017209837.png", threshold=0.6))
        screenshot_bb = pyautogui.screenshot(region=(879, 163, 1245 - 879, 612 - 163))

        horizontal_screenshot_stitch("轻功_其他物品", [screenshot_aa, screenshot_bb])
####################################################################################################
def horizontal_screenshot_stitch(img_name, screenshots):
    if screenshots:
        total_width = sum(img.width for img in screenshots)
        max_height = max(img.height for img in screenshots)

        combined = Image.new('RGB', (total_width, max_height))
        x_offset = 0
        for img in screenshots:
            combined.paste(img, (x_offset, 0))
            x_offset += img.width

        # combined.save(output_path)
        current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
        combined.save(f'{snapImgPath}\\{img_name}_{current_millis}.jpg')
        return combined

################################################
def step_waiguan(is_skip):
    if is_skip == 0:
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1156 , 193 , 1, "外观") is False:
            return

        check_and_try_game_home()

        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1156, 193, 1, "外观") is False:
            return

        waiguan_substep_shizhuang(0)
        sleep(3)
        try:
            sync_current_snapshot(f"套装：{waiguan_taozhuang_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)

        check_account_login_other()
        waiguan_substep_fashi(0)
        sleep(3)
        try:
            sync_current_snapshot(f"发式：{waiguan_fashi_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)

        check_account_login_other()
        waiguan_substep_wuqi(0)
        sleep(1)
        waiguan_substep_wuqi_huanshen(0)
        sleep(3)
        try:
            sync_current_snapshot(f"武器：{waiguan_wuqi_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)

        check_account_login_other()
        waiguan_substep_zhuoqi(0)
        sleep(3)
        try:
            sync_current_snapshot(f"坐骑：{waiguan_zuoqi_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)

        check_account_login_other()
        waiguan_substep_xiangrui(0)
        sleep(3)
        try:
            sync_current_snapshot(f"祥瑞：{waiguan_xiangrui_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)

        check_account_login_other()
        waiguan_substep_jueji(0)
        sleep(3)
        try:
            sync_current_snapshot(f"轻功：{waiguan_qingong_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)


        check_and_try_game_home()
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1156, 193, 1, "外观") is False:
            return

        try:
            waiguan_substep_guancang1025(0)
        except Exception as e:
            sync_current_snapshot(f"waiguan_substep_guancang1025 error {e}",None,False)

        kk_keyevent("{ESC}", 1, 0.5, "左上退出外观")
    return get_return_taskEvent("step_waiguan", EventStatus.SUCCESS,"外观完成")
################################################

######################################################
#################################################
def safe_step_kaifang_shijie(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游桌面版")
            return step_kaifang_shijie(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_kaifangshijie error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_kaifang_shijie", EventStatus.FAILURE)
###################
def step_kaifang_shijie(step_kaifang_shijie):
    if step_kaifang_shijie == 0:
        kktouch2(1231+10, 33+37,2,"菜单")#..菜单
        if kktouch2(1161, 496,5,"开放世界") is False:
            return

        kktouch2(284, 621,1,"地名小三角")#..小三角

        swipe_to_end((220, 60, 465, 385),(302, 58),(302, 536))
        save_cropped_screen("开放世界")

        kk_keyevent("{ESC}", 1, 0.2, "退出开放世界")

        kkLogger_log("开放世界结束")
    return get_return_taskEvent("step_kaifang_shijie", EventStatus.SUCCESS)
##########################################################
def safe_step_qunxia(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游桌面版")
            return step_qunxia(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_qunxia error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_qunxia", EventStatus.FAILURE)
##########################################################
def step_qunxia(step_qunxia):
    if step_qunxia == 0:
        kktouch2(1231+10, 33+37,2,"菜单")#..菜单

        if kktouch2(894, 541,1,"群侠") is False:
            return

        kktouch2(64+10, 126+37,1,"左侧群侠")#..左侧群侠
        kktouch2(240+10, 353+37,1,"左侧第一个人")#..第一个人物
        kktouch2(279+10, 395+37,1,"左侧展开箭头")#..展开

        # save_cropped_screen("群侠")
        # pyautogui.scroll(-1000)
        # save_cropped_screen("群侠")
        screenshot_aa = pyautogui.screenshot(region=(33,124,716-33,689-124))
        pyautogui.scroll(-1000)
        screenshot_bb = pyautogui.screenshot(region=(33,124,716-33,689-124))

        horizontal_screenshot_stitch("群侠_其他物品", [screenshot_aa, screenshot_bb])

        kk_keyevent("{ESC}", 2, 0.2, "退出群侠")

        kkLogger_log("群侠结束")
    return get_return_taskEvent("step_qunxia", EventStatus.SUCCESS,"群侠完成")
##########################################################
#################################################
def safe_step_zhuangyuan(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游桌面版")
            return step_zhuangyuan(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_zhuangyuan error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_zhuangyuan", EventStatus.FAILURE)
###################
def step_zhuangyuan(step_zhuangyuan):
    if step_zhuangyuan == 0:
        kktouch2(1231+10, 33+37,1,"菜单")#..菜单
        if kktouch2(892, 181,2,"庄园") is False:
            return

        save_cropped_screen_with_blur("庄园",230, 80, 680, 504)
        # kktouch2(67+10, 38+37,1,"左上退出")
        kk_keyevent("{ESC}", 1, 0.2, "退出庄园")

        kkLogger_log("庄园结束")
    return get_return_taskEvent("step_zhuangyuan", EventStatus.SUCCESS,"庄园完成")
#######################################################################
#################################################
def safe_step_lingcong(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游桌面版")
            return step_lingcong(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_lingcong error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_lingcong", EventStatus.FAILURE)
#########################################################################
def step_lingcong(step_lingcong):
    if step_lingcong == 0:
        kktouch2(1231+10, 33+37,1,"菜单")
        if kktouch2(1246, 333,2,"宠物") is False:
            return get_return_taskEvent("step_lingcong", EventStatus.SUCCESS,"没有宠物")

        if wait_for_ocr_txt("跳过",2,1124-100,10,1124+100,63+100):
            kk_keyevent("{ESC}", 1, 1, "跳过教学")
            # kktouch2(1124,63,1.2,"跳过教学")
            kktouch3(796, 470, 1.2, "确定")
            # kk_keyevent("{ESC}", 1, 0.2, "退出灵宠")
        if wait_for_ocr_txt("上新",2,17,98,145,247):
            kktouch3(1085,125, 1.2, "x掉上新")

        if ocr_text_target_coords("跳过",1124-100,10,1124+100,63+100) is not None:
            # kktouch2(1124,63,1.2,"跳过教学")
            kk_keyevent("{ESC}", 1, 1, "跳过教学")
            kktouch2(796, 470, 1.2, "确定")

        kktouch3(1085, 125, 1.2, "x掉上新")
        kktouch3(696, 615, 0.5, "空白，跳过引导")
        kktouch3(1085, 125, 1.2, "x掉上新")
        kktouch3(696, 615, 0.5, "空白，跳过引导")
        kktouch3(696, 615, 0.5, "空白，跳过引导")
        kktouch3(696, 615, 0.5, "空白，跳过引导")
        kktouch3(451, 257, 2, "灵宠")
        kktouch3(696, 615, 0.5, "空白，跳过引导")
        kktouch3(696, 615, 0.5, "空白，跳过引导")
        kktouch3(696, 615, 0.5, "空白，跳过引导")

        # find_lingcong_result = find_subImg(Template(r"tpl1727025466427.png"),1136, 41,1269, 111)
        # if find_lingcong_result:
        if wait_for_ocr_txt("详情",2,90,39,248,96):
            # save_cropped_screen("灵宠_天赏外观")
            save_cropped_screen_with_mulblur("灵宠_天赏外观",
                                             [ (18,257-40,225,295-40),
                                               (18,387-40,233,431-40),
                                               (22,520-40,240,567-40),
                                               (16,648-40,244,691-40),
                                               (425, 121-40, 691, 173-40)])
            # loop = 136
            # start_x = 118
            # start_y = 195
            # for j in range(3):
            #     kktouch2(start_x,start_y,1.2,"第一个灵宠")
            #     if ocr_text_target_coords("天狼",600,80,1000,400) is not None:
            #         ocr_check_and_add_attr("灵宠·天狼星","天赏宠物")
            #     elif ocr_text_target_coords("天鹰",600,80,1000,400) is not None:
            #         ocr_check_and_add_attr("灵宠·天鹰座", "天赏宠物")
            #     elif ocr_text_target_coords("月",600,80,1000,400) is not None:
            #         ocr_check_and_add_attr("灵宠·心月狐", "天赏宠物")
            #     start_y = start_y + loop

        kk_keyevent("{ESC}", 1, 0.2, "退出灵宠")
        kkLogger_log("宠物结束")

    step_tianshangshi(0)
    step_chongzhi(0)
    step_shenqi(0)
    get_zhiye(0)
    if "踏歌" in waiguan_fashi_set:
        ocr_check_and_set("性别", "男")
    elif "俏也" in waiguan_fashi_set:
        ocr_check_and_set("性别", "女")
    #-------------------------------------------------------------------------------------
    try:
        check_and_try_game_home(4, "logout")
        kktouch2(1227 + 10, 35 + 37, 1, "菜单")
        kktouch3(1248, 466, 2, "右侧设置")
        kktouch3(73, 163, 2, "左侧系统")

        if local_ocr_text_target_coords("切换",224,293,442,345):
            kktouch3(585, 316, 2, "设置中切换账号")
            ocr_result = local_ocr_text_target_coords("@", 575, 286, 806, 344, False)
            if ocr_result is not None:
                ocr_check_and_set("账号类型", "邮箱账号")
                sync_current_snapshot("账号信息:邮箱账号", None, True)
                print("识别为 邮箱账号")
            elif ocr_text_has_number(575, 286, 806, 344):
                ocr_check_and_set("账号类型", "手机账号")
                print("识别为 手机账号")
                sync_current_snapshot("账号信息：手机账号", None, True)
            else:
                return get_return_taskEvent("step_lingcong", EventStatus.SUCCESS,"灵宠完成")

            kktouch3(802, 157, 2, "关闭切换账号")
            kk_keyevent("{ESC}", 1, 1, "回到主页")
        elif luhao_task.get("gameAccount",None):
            sync_current_snapshot("未识别到账号信息", None, True)
            account = luhao_task['gameAccount']
            if "@" in account:
                ocr_check_and_set("账号类型", "邮箱账号")
            else:
                ocr_check_and_set("账号类型", "手机账号")

    except Exception as e:
        print(e)
    #-------------------------------------------------------------------------------
    baili_chuanyin()

    return get_return_taskEvent("step_lingcong", EventStatus.SUCCESS,"灵宠完成")
##################################################################################
def baili_chuanyin():
    try:
        is_ok = False

        # product_info = luhao_task.get("product_info",None)
        # if product_info is None:
        #     return
        #
        # for item in product_info["productAttributeValueList"]:
        #     if item['value'] == "同意传音上架看看":
        #         is_ok = True
        #         break

        if not is_ok:
            return

        check_and_try_game_home()
        kktouch3(268,687,1,"进入")
        # if ocr_text_target_coords("传",534,557,592,683) is None:
        #     return
        kktouch3(563,619,1,"传音")
        kktouch3(563,619,1,"传音")

        if ocr_text_target_coords("百",772,271,955,335) is None:
            return
        sync_current_snapshot("传音开始", None, False)

        kktouch3(856,390,1,"百里传音")
        kktouch3(876,598,1,"空白")

        number = get_ocr_number(1017,275,1150,331)
        if "" != number and int(number) > 1:
            kktouch3(779,497,1,"喊话输入框")
            text(f"脱坑，已挂看看zh，{luhao_task['productSn']}，可刀")
            # text(f"脱坑上架看看zh领5R红包")
            # text(f"此耗以上看看，上架顶奢耗领88R")
            # text(f"脱入游，上看看，海量精品，费率超低！此号已上。")
            sync_current_snapshot("传音", None, True)
            kktouch3(1154, 596, 1, "发送喊话")
    except Exception as e:
        print(e)
        print("传音失败")

###################################
def get_product_meta_attr_inputList_array(attr_name):
    for item in product_meta:
        if attr_name == item["name"]:
            return item["inputList"].split(',')

    return []
#####################################
def get_product_meta_attr_value(attr_name):
    for item in product_meta:
        if attr_name == item["name"]:
            return item["value"]

    return None
##############################################################
def get_product_meta_qufu(ocr_txt):
    for item in product_meta:
        if "区服" == item["name"]:
            data = json.loads(item["inputList"])
            for it in data:
                parent_name = it["parent_name"]
                if ocr_txt in it["childList"]:
                    return f"{parent_name}|{ocr_txt}"

    return ""
#######################################
def get_zhiye(is_skip):
    try:
        if is_skip == 0:
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            kktouch3(1247, 410,10,"排行")
            sync_current_snapshot(f"排行", None, True)
            get_attr_ocr_number("角色等级", 585, 637, 682, 675)
            ocr_result_zhiye = ocr_text(455, 637, 636, 677)

            if ocr_result_zhiye:
                for line in ocr_result_zhiye:
                    if line is None:
                        continue
                    for word_info in line:
                        if word_info is None:
                            continue
                        else:
                            get_ocr_txt = word_info[1][0]
                            get_ocr_txt = get_ocr_txt.strip()

                            if len(get_ocr_txt) > 1 and get_ocr_txt in get_product_meta_attr_inputList_array("职业"):
                                print(f"识别到职业：{get_ocr_txt}")
                                ocr_check_and_set("职业", get_ocr_txt)
                                sync_current_snapshot(f"识别职业：【{get_ocr_txt}】", None, False)
                                break
                            else:
                                kkLogger_log(f"step_zhiye [{get_ocr_txt}]，未识别到职业")
            else:
                kkLogger_log(f"step_zhiye ，未识别到职业")
            #-------------------------------------------------------

    except Exception as e:
        kkLogger_log(f"step_zhiye ，error {e}")
#######################################################################
def get_first_ocr_txt(start_x,start_y,end_x,end_y):
    first_txt = "None"
    try:
        ocr_result = ocr_text(start_x, start_y, end_x, end_y)
        if ocr_result:
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    if word_info is None:
                        continue
                    else:
                        get_ocr_txt = word_info[1][0]
                        get_ocr_txt = get_ocr_txt.strip()
                        if len(get_ocr_txt) > 0:
                            first_txt = get_ocr_txt
                            # print(f"识别到：{first_txt}")
                            return first_txt
        else:
            print(f"未识别到文字")
            return first_txt
    except Exception as e:
        return first_txt
#################################################################
def get_first_ocr_txt_local(start_x,start_y,end_x,end_y):
    first_txt = None
    try:
        ocr_result = local_ocr_text(start_x, start_y, end_x, end_y)
        if ocr_result:
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    if word_info is None:
                        continue
                    else:
                        get_ocr_txt = word_info[1][0]
                        get_ocr_txt = get_ocr_txt.strip()
                        if len(get_ocr_txt) > 0:
                            first_txt = get_ocr_txt
                            return get_ocr_txt
        else:
            print(f"未识别到文字")
            return first_txt
    except Exception as e:
        return first_txt
##########################################################
def get_qufu(is_skip):
    qufu_tp = ""
    try:
        if is_skip == 0:
            ocr_result = ocr_text(398, 307, 563, 342)
            if ocr_result:
                for line in ocr_result:
                    if line is None:
                        continue
                    for word_info in line:
                        if word_info is None:
                            continue
                        else:
                            get_ocr_txt = word_info[1][0]
                            get_ocr_txt = get_ocr_txt.strip()
                            if len(get_ocr_txt) > 1:
                                print(f"识别到区服：{get_ocr_txt}")
                                qufu_tp = get_product_meta_qufu(get_ocr_txt)
                                if "|" in qufu_tp:
                                    ocr_check_and_set("区服", qufu_tp)
                                return qufu_tp
                            else:
                                kkLogger_log(f"get_qufu [{get_ocr_txt}]，未识别到区服")
            else:
                kkLogger_log(f"get_qufu ，未识别到区服")
                return qufu_tp
        return False
    except Exception as e:
        kkLogger_log(f"get_qufu ，error {e}")
        return qufu_tp
######################################################
def step_shenqi(is_skip):
    return get_return_taskEvent("step_shenqi", EventStatus.SUCCESS)
    # try:
    #     if is_skip == 0:
    #         kktouch2(1224 + 10, 32 + 37, 1, "菜单")
    #         kktouch2(1114, 331,0.5,"千机玄甲")
    #         sleep(1)
    #         kktouch2(618,661,1.2,"我知道啦")
    #         save_cropped_screen("神器")
    #
    #         kk_keyevent("{ESC}", 1, 0.2, "神器")
    #
    #     return get_return_taskEvent("step_shenqi", EventStatus.SUCCESS)
    # except Exception as e:
    #     kkLogger_log(f"step_shenqi 失败：{e}")
    #     kk_keyevent("{ESC}")
    #     return get_return_taskEvent("step_shenqi", EventStatus.FAILURE)
#######################################################################
def step_chongzhi(is_skip):
    try:
        if is_skip == 0:
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            kktouch2(1167, 722,3,"攻略")

            connect_device("windows:///?title_re=逆水寒手游桌面版")
            if wait_for_ocr_txt("回归",2,267,169,418,207):
                kktouch3(731,678, 2, "输入框")
                text("累计充值")
                kktouch3(1091,676, 3, "发送")
                coords = ocr_text_target_coords("元", 303,234,1099,504)
                if coords is not None:
                    get_attr_ocr_number("充值金额", 303,234,1099,504)

                sync_current_snapshot("充值金额", None, True)
                save_cropped_screen("累计充值2")
            else:
                kktouch3(800, 674,2,"输入框")
                text("累计充值")
                kktouch3(1090+10, 639+37,3,"发送")
                coords = ocr_text_target_coords("元",303,234,1099,504)
                if coords is not None:
                    get_attr_ocr_number("充值金额",303,234,1099,504)

                sync_current_snapshot("充值金额", None, True)
                save_cropped_screen("累计充值2")


            if wait_for_ocr_txt("回归", 2, 267, 169, 418, 207):
                kktouch3(731,678, 2, "输入框")
                text("赠礼额度")
                kktouch3(1091,676, 2, "发送")
                coords = ocr_text_target_coords("赠礼额度查询", 296, 269, 1219, 599)
                if coords is not None:
                    kktouch3(coords[0], coords[1], 3, "赠礼额度查询")
                    save_cropped_screen("赠礼额度")
                else:
                    kktouch3(625, 529, 3, "赠礼额度查询")
                    save_cropped_screen("赠礼额度2")
            else:
                kktouch2(800, 674,2,"输入框")
                text("赠礼额度")
                kktouch3(1090+10, 639+37,3,"发送")
                coords = ocr_text_target_coords("赠礼额度查询",296, 269,1219, 599)
                if coords is not None:
                    kktouch3(coords[0],coords[1],3,"赠礼额度查询")
                    save_cropped_screen("赠礼额度")
                else:
                    kktouch3(625, 529, 3, "赠礼额度查询")
                    save_cropped_screen("赠礼额度2")

            kk_keyevent("{ESC}", 1, 0.2, "退出小暖问答")
            kkLogger_log("充值查询结束")
        return get_return_taskEvent("step_chongzhi", EventStatus.SUCCESS)
    except Exception as e:
        kkLogger_log(f"step_chongzhi 失败：{e}")
        kktouch2(37+10, 32+37,2,"退出小暖问答")
        return get_return_taskEvent("step_chongzhi", EventStatus.FAILURE)
###################################################################################

##############################################################################
def sort_and_remove_number(arr):
    # 首先根据数字大小进行倒序排序
    sorted_arr = sorted(arr, key=lambda x: int(x.split(':')[-1]), reverse=True)
    # 然后遍历排序后的数组，移除冒号以及后面的数字
    cleaned_arr = [item.split(':')[0] for item in sorted_arr]
    return cleaned_arr
#######################################
def sort_by_number_desc(arr):
    # 使用lambda函数和sorted函数进行排序
    # lambda函数提取字符串中最后一个冒号后面的数字，并转换为整数
    # sorted函数的key参数使用上述lambda函数，reverse=True表示倒序
    sorted_arr = sorted(arr, key=lambda x: int(x.split(':')[-1]), reverse=True)
    return sorted_arr

#########################################################################
def step_tianshangshi(step_tianshangshi):
    try:
        if step_tianshangshi == 0:
            tss_value = 0
            tss_item = []
            for item in product_meta:
                if item["type"] not in [1,2]:
                    continue
                else:
                    item_values = item.get("values", [])
                    for item_value in item_values:
                        item_value_2 = remove_unwanted_chars(item_value)
                        # add_value = configs.TSS_VALUE.get(item_value_2, 0)
                        add_value = portal_client.get_tss_value().get(item_value_2, 0)
                        tss_value = tss_value + add_value

                        if add_value > 0:
                            tss_item.append(f"{item_value_2}:{add_value}")

            # kkLogger_log(f"已使用天赏石：{tss_value}")
            # ocr_check_and_set_number("已使用天赏石",str(tss_value))
            sync_current_snapshot(f"已使用天赏石：{sort_by_number_desc(tss_item)}：{tss_value}",None,False)

        return get_return_taskEvent("step_tianshangshi", EventStatus.SUCCESS)
    except Exception as e:
        kkLogger_log(f"step_tianshangshi 失败：{e}")
        return get_return_taskEvent("step_tianshangshi", EventStatus.FAILURE)
########################################################################
def step_img_upload(is_skip):
    if is_skip == 0:
        sleep(1)
        for i in range(3):
            try:
                kkLogger_log("开始处理图片")
                # 繁星账号198879保留原图包
                global save_original_pic
                if luhao_task['memberId'] in [198879]:
                    save_original_pic = True
                file_url = image_util.collage(f'{snapImgPath}', save_original=save_original_pic)
                if file_url:
                    kkLogger_log('发送原图包下载链接：' + file_url)
                    portal_client.send_msg(member_id=luhao_task['memberId'], msg=f"【{luhao_task['productSn']}】原图包下载链接：{file_url}")

                kkLogger_log(f'开始上传')

                upload_kk_img_to_oss(f'{snapImgPath}')

                kkLogger_log(f'上传结束 pic_url_arrays:{pic_url_arrays}')
                return get_return_taskEvent("step_img_upload", EventStatus.SUCCESS,"图片上传完成")
            except Exception as e:
                kkLogger_log(f"图片上传异常 error,{e}")
                sleep(2)
                continue

    return get_return_taskEvent("step_img_upload", EventStatus.FAILURE,"图片上传失败")
###########################################################
def safe_step_meta_upload(is_skip):
    for i in range(3):
        try:
            return step_meta_upload(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_meta_upload error,{e}")
            sleep(2)
            continue

    return get_return_taskEvent("step_meta_upload", EventStatus.FAILURE,"录号数据提交异常")
#################################################################
def step_meta_upload(step_meta_upload):
    global product_meta
    if step_meta_upload == 0:
        # kkLogger_log(f"product_meta_json:{product_meta}")
        # kkLogger_log(f"pic_url_arrays:{pic_url_arrays}")
        portal_client.sync_product_info(luhao_task['productSn'], configs.image_server_url + get_head_pic(), pic_url_arrays, product_meta)

    global end_time
    end_time = datetime.datetime.now()
    total_time = end_time - start_time
    minutes = total_time.total_seconds() // 60
    kkLogger_log(f"录号执行了 {minutes} 分钟。")

    return get_return_taskEvent("step_meta_upload", EventStatus.SUCCESS,f"录号数据提交成功，共执行 {minutes} 分钟")
#########################################################################
def step_exit_game(is_skip):
    if is_skip == 0:
        kktouch2(1261,20,2,"关闭游戏")
        kktouch2(1261 , 20 , 2, "关闭游戏")
        kktouch2(1154+10,391+37,1,"确认")
    return get_return_taskEvent("step_meta_upload", EventStatus.SUCCESS,"录号数据提交成功")
#########################################################################
def step_restart_mumu():
    connect_device("windows:///")
    print(f"连接桌面:{device().get_current_resolution()}")
    sys_tool.restart_mumu()
    for i in range(30000):
        sleep(5)
        try:
            if local_ocr_text_target_coords("MuMu模拟器",10,10,1000,700) is None:
                continue
            if local_ocr_text_target_coords("逆水寒",10,10,1000,700) is None:
                continue

            connect_device("Windows:///?title_re=MuMu模拟器12")
            print(f"尝试MuMu模拟器12:{device().get_current_resolution()}")
            if 1288 == device().get_current_resolution()[0]:
                G.DEVICE.move((0, 0))
                if wait_for_ocr_txt("MuMu模拟器", 10,33,2,145,36):
                    sleep(15)
                    break
            else:
                continue
        except Exception as e:
            print(e)
            continue
###############################################
def process_exists(process_name):
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == process_name:
            print(f"{process_name} exist")
            return True
    return False
##################################
def is_between_1_and_9():
    # 获取当前时间
    now = datetime.datetime.now()
    # 获取当前时间的小时部分
    current_hour = now.time().hour

    # 判断当前小时是否在 1 到 10 之间
    if 1 <= current_hour <= 10:
        return True
    else:
        return False

#######################################
def step_restart_nshm(is_skip, is_logout=False):
    global width
    global height
    global snapImgPath
    global projectPath
    projectPath = "D:\\kkzhw\\airtest_log"
    snapImgPath = f"{projectPath}"

    if is_skip == 0:
        connect_device("windows:///")
        (screen_width,screen_height) = device().get_current_resolution()
        print(f"连接桌面，尺寸:{(screen_width,screen_height)}")

        try:
            sys_tool.restart_nshm()
        except Exception as e:
            print(f"重启nshm异常，error:{e}")
            traceback.print_exc()
            print("重启逆水寒启动器失败")
            if luhao_task:
                # 录号时重启
                if is_logout:
                    print('记录任务完成...')
                    task = {
                        'id': luhao_task['id'],
                        'stage': 'step_logout',
                        'status': 'COMPLETED',
                        'snapshot': None,
                        'msg': None
                    }
                    portal_client.sync_task_info(task)
                    sn = luhao_task.get("productSn")
                    portal_client.send_msg(luhao_task.get("memberId", None),
                                           f'亲爱的小主，您的商品编号【{sn}】，录号已完成，请等待客服审核。通过后请核对信息，如有问题请联系客服！')
                else:
                    print("取消任务...")
                    portal_client.cancel_task(luhao_task["id"])
            print("重启电脑...")
            sys_tool.restart_computer()

        max_retry = 60
        retry = 0
        sleep(10)
        for i in range(max_retry):
            sleep(5)
            retry += 1
            try:
                connect_device("Windows:///?title_re=逆水寒手游")
                (width,height) = device().get_current_resolution()
                print(f"尝试逆水寒手游:{(width,height)}")
                if width > 1000:
                    G.DEVICE.move((0, 0))
                    # wait_for_ocr_txt("进入游戏",120,840,610,1400,850)
                    wait_for_ocr_txt("游戏,闯荡",120,516,555,771,619)
                    touch((1095,695))

                    sleep(15)
                    #判断客户端是否启动
                    for i in range(300):
                        # sleep(5)
                        try:
                            target = wait_for_ocr_txt("设备", 5, screen_width // 2, 100, screen_width, screen_height)
                            if target is False:
                                continue

                            if target and local_ocr_text_target_coords("选择服务器",target[0]-825,target[1],target[0],target[1]+400) is None:
                                kktouch3(target[0]-445,target[1]-242+(202-162),1,"关闭扫码弹窗")
                                kktouch3(target[0]-445,target[1]-245,1,"关闭扫码弹窗2")
                                continue

                            connect_device("Windows:///?title_re=逆水寒手游桌面版")
                            (width2, height2) = device().get_current_resolution()
                            print(f"尝试连接逆水寒手游桌面版 {i}:{(width2, height2)}")
                            if width2 > 1200:
                                print(f"连接逆水寒手游桌面版成功:{(width2, height2)}")
                                G.DEVICE.move((0, 0))
                                sleep(1)
                                connect_device("Windows:///")
                                if local_ocr_text_target_coords("手游", 1, 1, 147, 35) is not None:
                                    print("逆水寒手游桌面版客户端准备就绪")
                                    break
                                else:
                                    continue
                            else:
                                continue
                        except Exception as e:
                            print(e)
                            continue
                    #判断是否界面准备好
                    sleep(1)
                    for i in range(120):
                        # sleep(5)
                        print("判断是否界面准备好")
                        # connect_device("Windows:///")
                        touch((802,202))
                        sleep(1)
                        if local_ocr_text_target_coords("选择服务器",499,456,783,538) is not None:
                            connect_device("Windows:///?title_re=逆水寒手游桌面版")
                            print("逆水寒手游桌面版界面准备就绪")
                            break
                        else:
                            continue
                    break
                else:
                    continue
            except Exception as e:
                print(e)
                continue
        if retry >= max_retry:
            print("重启逆水寒启动器失败")
            if luhao_task:
                # 录号时重启
                if is_logout:
                    print('记录任务完成...')
                    task = {
                        'id': luhao_task['id'],
                        'stage': 'step_logout',
                        'status': 'COMPLETED',
                        'snapshot': None,
                        'msg': None
                    }
                    portal_client.sync_task_info(task)
                    sn = luhao_task.get("productSn")
                    portal_client.send_msg(luhao_task.get("memberId", None),
                                       f'亲爱的小主，您的商品编号【{sn}】，录号已完成，请等待客服审核。通过后请核对信息，如有问题请联系客服！')
                else:
                    print("取消任务...")
                    portal_client.cancel_task(luhao_task["id"])
            print("重启电脑...")
            sys_tool.restart_computer()
    return True
#####################################################
def contains_color(start_x, start_y, end_x, end_y, color_hex, tolerance=20):
    try:
        # 1. 颜色空间转换预处理
        h_8bit, s_8bit, v_8bit = hex_to_8bit_hsv(color_hex)

        # 2. 区域坐标有效性验证
        if not (start_x < end_x and start_y < end_y):
            raise ValueError(f"无效区域坐标: ({start_x},{start_y})到({end_x},{end_y})")

        # 3. 屏幕截图处理（仅截取目标区域）
        screen = G.DEVICE.snapshot()
        cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

        # 4. 颜色空间转换（BGR→HSV）
        hsv_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2HSV)

        # 5. 动态计算HSV范围（处理色相环绕问题）
        # OpenCV中色相范围为0-180（需要特别处理）
        h_tolerance = min(tolerance, 90)  # 最大容忍90度色相差
        lower_h = max(0, h_8bit - h_tolerance)
        upper_h = min(180, h_8bit + h_tolerance)

        # 饱和度(S)和明度(V)的范围控制（0-255）
        lower_bound = np.array([
            lower_h,
            max(0, s_8bit - tolerance),
            max(0, v_8bit - tolerance)
        ], dtype=np.uint8)

        upper_bound = np.array([
            upper_h,
            min(255, s_8bit + tolerance),
            min(255, v_8bit + tolerance)
        ], dtype=np.uint8)

        # 6. 颜色匹配检测（优化版）
        mask = cv2.inRange(hsv_img, lower_bound, upper_bound)
        return cv2.countNonZero(mask) > 0  # 比np.any()更高效

    except Exception as e:
        print(f"[颜色检测错误] 坐标({start_x},{start_y})-({end_x},{end_y}) 颜色{color_hex}：{str(e)}")
        return False
################################################################################################################
def check_similar_color_in_region(start_x, start_y, end_x, end_y, target_color, tolerance=10):
    """
    检查屏幕指定矩形区域内是否存在与目标颜色相似的颜色
    参数:
        start_x, start_y: 区域左上角坐标
        end_x, end_y: 区域右下角坐标
        target_color: 目标颜色(R, G, B)三元组
        tolerance: 颜色容差(默认10)
    返回:
        如果找到相似颜色返回True，否则返回False
    """
    # 确保target_color是三元组
    if len(target_color) != 3:
        raise ValueError("target_color 必须是 (R, G, B) 格式的三元组")

    bbox = (start_x, start_y, end_x, end_y)
    screenshot = ImageGrab.grab(bbox=bbox)

    if screenshot.mode != 'RGB':
        screenshot = screenshot.convert('RGB')

    pixels = screenshot.load()
    tr, tg, tb = target_color

    for i in range(screenshot.width):
        for j in range(screenshot.height):
            r, g, b = pixels[i, j]
            if (abs(r - tr) <= tolerance and
                    abs(g - tg) <= tolerance and
                    abs(b - tb) <= tolerance):
                return True
    return False
#######################################################
def step_mumu_logout():
    if has_mumu is False:
        sync_current_snapshot("无mumu模拟器，无需退登",None,False)
        return True

    kkLogger_log("开始退登mumu模拟器云逆水寒手游")
    connect_device("Windows:///?title_re=MuMu模拟器12")
    sleep(1)
    connect_device("Android://127.0.0.1:5037/127.0.0.1:16384?cap_method=JAVACAP")
    for i in range(3):
        # set_current(0)
        target = ocr_text_target_coords("关闭", 479 - 100, 625 - 100, 479 + 100, 625 + 50, False)
        if target is not None:
            kktouch3(target[0], target[1], 1, "关闭实名弹窗2")

        # 登录状态则退出
        target = ocr_text_target_coords("退出", 872, 20, 1270, 214)
        if target is not None:
            kktouch3(target[0], target[1] - 10, 1.2, "右上退出")
            kktouch3(746, 461 - 33, 1.1, "弹窗退出")

        target = ocr_text_target_coords("其他账号登录", 330, 417, 920, 626)
        if target is not None:
            kktouch3(target[0], target[1], 1.2, "其他账号登录")

        target = ocr_text_target_coords("详细", 399, 456, 910, 634)
        if target is not None:
            sync_current_snapshot("云逆水寒退登成功")
            return True
        else:
            stop_app("com.netease.micro.nshm")
            print("停止云逆水寒")
            sleep(3)
            start_app("com.netease.micro.nshm")
            print("启动云逆水寒")
            sleep(10)
    return True

#######################################
def step_logout(is_skip):
    if is_skip == 0:
        global end_time
        for i in range(2):
            try:
                connect_device("windows:///?title_re=逆水寒手游桌面版")
                check_and_try_game_home(4,"logout")
                kktouch2(1227+10, 35+37,1,"菜单")
                kktouch3(1248, 466,2,"右侧设置")
                kktouch3(73, 163,2,"左侧系统")
                sync_current_snapshot("开始退登")
                if ocr_text_target_coords("切换", 224, 293, 442, 345):
                    kktouch3(585, 316,2,"设置中切换账号")
                    kktouch3(582, 393,2,"切换账号.")
                    sleep(4)
                    kktouch3(801,202,1,"关闭登录窗口X")
                    kktouch3(801,162,1,"关闭登录窗口X")


                    end_time = datetime.datetime.now()
                    total_time = end_time - start_time
                    minutes = total_time.total_seconds() // 60
                    kkLogger_log(f"录号执行了 {minutes} 分钟。")

                    # coords = local_ocr_text_target_coords("选择服务器",471, 473,811, 565,False)
                    if not wait_for_ocr_txt("选择",5,471, 473,811, 565):
                        sync_current_snapshot("逆水寒模拟器退登失败，开始重启逆水寒客户端")
                        step_restart_nshm(0, True)

                    kkLogger_log("逆水寒模拟器退登成功")

                    step_mumu_logout()

                    if luhao_task.get("memberId", None):
                        sn = luhao_task.get("productSn")
                        portal_client.send_msg(luhao_task.get("memberId", None),
                                               f'亲爱的小主，您的商品编号【{sn}】，录号已完成，请等待客服审核。通过后请核对信息，如有问题请联系客服！')




                    return get_return_taskEvent("step_logout", EventStatus.SUCCESS,
                                                f"录号完成，退登成功，共录制了{minutes}分钟")
                else:
                    end_time = datetime.datetime.now()
                    total_time = end_time - start_time
                    minutes = total_time.total_seconds() // 60

                    sync_current_snapshot("未识别到切换账号，开始重启客户端")
                    step_restart_nshm(0, True)
                    connect_device("Windows:///?title_re=逆水寒手游桌面版")
                    sync_current_snapshot("未识别到切换账号，重启客户端完成")

                    step_mumu_logout()

                    if luhao_task.get("memberId", None):
                        sn = luhao_task.get("productSn")
                        portal_client.send_msg(luhao_task.get("memberId", None),
                                               f'亲爱的小主，您的商品编号【{sn}】，录号已完成，请等待客服审核。通过后请核对信息，如有问题请联系客服！')

                    return get_return_taskEvent("step_logout", EventStatus.SUCCESS,
                                                f"录号完成，退登成功，共录制了{minutes}分钟")
            except Exception as e:
                print(e)
                continue



# 
if __name__ == '__main__':
    # 记录程序开始执行的时间  
    accounts = ["***********"]
    # accounts = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
    # accounts = ["***********"]
    # password = ["wodejia","Jun135790","Jun135790.","Jun135790."]
    password = ["Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790."]

    i = 0

    for item in accounts:
        luhao_task = {
            #     'gameAccount':'***********',
            'gameAccount': item,
            # 'gamePassword': "Jun135790",
            'gamePassword': password[i],
            "id": 540,
            "status": "IN_PROGRESS",
            # "loginType":"QR_CODE",
            # "loginType":"PASSWORD",
            "loginType":"SMS_CODE",
            "productSn": "TESTNSH20228850",
            "product_meta": requestGameProductCategoryMeta(75),
            "product_info": {
                "product": {
                    "productCategoryId": 75,
                    "productSn": "TESTNSH20228850",
                    "productCategoryName": "逆水寒手游",
                    "updateTime": "2024-09-29T06:36:15.000+00:00",
                    "createTime": "2024-09-28T22:49:57.000+00:00",
                    "gameActiveFlag": 0,
                    "gameAccountQufu": "踏北尘|紫禁之巅"
                }
            }
        }
        i = i +1

        start_time = datetime.datetime.now()
        # step_restart_nshm(0)
        # step_restart_mumu()

        # print(is_chinese_mobile_number("***********"))
        # print(is_chinese_mobile_number("**********"))
        # print(is_chinese_mobile_number("**********"))
        # sleep(30)

        # init_nsh(luhao_task)
        init_local_nsh(luhao_task)

        # print(check_similar_color_in_region(1058,171,1090,200, (214, 212, 209)))
        # print(check_similar_color_in_region(1099, 137,1199, 147, (236, 215, 151)))

        get_ocr_txt = "few·fewji起"
        get_ocr_txt = get_ocr_txt.replace("·", "")
        get_ocr_txt = get_ocr_txt.replace("起", "")
        print(get_ocr_txt)


        #
        # step_chongzhi(0)
        # step_chongzhi(0)

        # ocr_check_and_set_number("aaa",",f沃尔夫叫我服务7，1000")


        print("end...")

        sleep(5000)

        # step_dazao(0)

        # waiguan_substep_shizhuang(0)
        # waiguan_substep_fashi(0)
        # waiguan_substep_jueji(0)
        # step_img_upload(0)

        # pyautogui.scroll(-21)
        # sleep(2)
        # pyautogui.scroll(-21)
        # sleep(2)
        # pyautogui.scroll(-21)
        # sleep(2)
        #
        # step_dazao(0)

        # baili_chuanyin()


        end_time = datetime.datetime.now()
        total_time = end_time - start_time
        minutes = total_time.total_seconds()
        print(f"录号执行了 {minutes} 秒。")



        break

