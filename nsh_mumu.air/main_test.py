import socket
import time

import nsh_mumu_login as nsh

import requests
from requests.auth import HTTPBasicAuth

# Jenkins 配置
jenkins_url = "http://*************:8080/view/录号/job/逆水寒录号-发布/buildWithParameters"

jenkins_user = "admin"
jenkins_token = "119221ac9b42ae7a6720996e9caee88b1b"
build_token = "nsh_deploy"

# 最大重试次数
max_retries = 60
retry_delay = 5  # 每次重试之间的间隔时间（秒）


# 触发 Jenkins 构建
def trigger_jenkins_build(host):
    attempts = 0

    while attempts < max_retries:
        try:
            build_params = {
                "token": build_token,
                "HOSTS": host
            }
            response = requests.post(jenkins_url, params=build_params, auth=HTTPBasicAuth(jenkins_user, jenkins_token))

            if response.status_code == 201:
                print("Jenkins 构建已成功触发。")
                break  # 成功触发后跳出循环
            else:
                print(f"Jenkins 构建触发失败，状态码：{response.status_code}, 响应内容：{response.text}")

        except Exception as e:
            print(f"触发 Jenkins 构建时发生错误：{e}")

        # 增加重试逻辑
        attempts += 1
        if attempts < max_retries:
            print(f"重试第 {attempts} 次，等待 {retry_delay} 秒...")
            time.sleep(retry_delay)
        else:
            print("已达到最大重试次数，构建触发失败。")


if __name__ == '__main__':
    nsh.step_restart_nshm(0)
    hostname = socket.gethostname()
    hostname = hostname.replace("-", "_")
    print('计算机名：' + hostname)
    trigger_jenkins_build(hostname)
