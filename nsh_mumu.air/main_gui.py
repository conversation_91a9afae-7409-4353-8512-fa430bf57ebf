import copy
import json
import os
import queue
import random
import threading
import time
import traceback
from contextlib import contextmanager

import nsh_mumu_login as nsh
import sys_tool
from luhao_models import TaskEvent, EventStatus
from server_client import PortalClient

device_id = os.environ.get('DEVICE_ID', '1')
PRODUCT_SN = os.environ.get('PRODUCT_SN', '')
START_STAGE = os.environ.get('START_STAGE', '')


# Worker类，用于执行录号任务，并通过队列接收指令
class Worker(threading.Thread):
    def __init__(self, task_queue, result_queue):
        super().__init__()
        self.task_queue = task_queue  # 用于接收任务指令
        self.result_queue = result_queue  # 用于返回结果。 任务进度通过result_queue返回master，master进入后续的处理
        self.running = True
        self.busy = False  # 表示是否正在处理任务
        self.current_task = None  # 存储当前任务的信息
        self.portal_client = PortalClient()

    def run(self):
        while self.running:
            try:
                # 等待任务指令
                instruction = self.task_queue.get(timeout=1)
                if instruction["command"] == "run_task":
                    self.busy = True  # 设置为忙状态
                    self.current_task = instruction["task"]  # 记录当前任务信息
                    print(f"【worker】开始执行任务: {instruction['task']}")
                    # time.sleep(10)  # 模拟耗时任务
                    try:
                        # 执行耗时任务，并阶段性返回进度
                        task = instruction.get('task')
                        result = self.execute_long_task(task)
                        if result:
                            self.result_queue.put(
                                TaskEvent(task['id'], stage=0, status=EventStatus.FINISHED))
                        else:
                            self.result_queue.put(
                                TaskEvent(task['id'], stage=0, status=EventStatus.FAILURE))
                    except Exception as e:
                        print(f"【worker】任务出错: {e}")
                        traceback.print_exc()
                        self.result_queue.put(
                            TaskEvent(task['id'], stage=0, status=EventStatus.FAILURE))

                    print("【worker】任务完成")
                    # self.result_queue.put({"status": "completed", "result": "success"})
                    self.busy = False  # 任务完成后，恢复空闲状态
                    self.current_task = None  # 任务完成后清除任务信息
                elif instruction["command"] == "stop":
                    print("【worker】停止任务")
                    self.running = False
                elif instruction["command"] == "task_resume":
                    self.busy = True  # 设置为忙状态
                    print(f"【worker】重新开始执行任务: {instruction['task']}")
                    try:
                        # 执行耗时任务，并阶段性返回进度
                        # task = instruction.get('task')
                        params = instruction.get('params')
                        if params:
                            params = json.loads(params)
                            begin_stage = params.get('beginStage')
                            need_login = params.get('needLogin')
                            task_id = params.get('taskId')
                            task = self.portal_client.get_task_info(task_id)
                            self.current_task = task  # 记录当前任务信息
                            result = self.execute_long_task(task, begin_stage=begin_stage, need_login=need_login)
                        else:
                            result = self.execute_long_task(task)
                        if result:
                            self.result_queue.put(
                                TaskEvent(task['id'], stage=0, status=EventStatus.FINISHED))
                        else:
                            self.result_queue.put(
                                TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FAILURE))
                    except Exception as e:
                        print(f"【worker】任务出错: {e}")
                        traceback.print_exc()
                        self.result_queue.put(
                            TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FAILURE))

                    print("【worker】任务完成")
                    # self.result_queue.put({"status": "completed", "result": "success"})
                    self.busy = False  # 任务完成后，恢复空闲状态
                    self.current_task = None  # 任务完成后清除任务信息
            except queue.Empty:
                continue  # 如果队列空，则继续等待

    # 耗时任务的具体实现
    def execute_long_task(self, task, begin_stage=None, need_login=True):
        metadata = self.portal_client.get_product_category_meta(75)
        task['product_meta'] = metadata
        product = self.portal_client.get_product_detail(task['productId'])
        task['product_info'] = product

        if START_STAGE and START_STAGE != '':
            begin_stage = START_STAGE
            need_login = False

        if need_login:
            init_steps = [
                (nsh.init_nsh, 'init_nsh', task),
                (nsh.step_login, 'step_login', 0),
                (nsh.step_qufu, 'step_qufu', 0),
            ]
        else:
            init_steps = [
                (nsh.init_local_nsh, 'init_local_nsh', task),
            ]

        for step_function, stage, param in init_steps:
            success = self.execute_task_stage(step_function, stage, param, task)
            if not success:
                return False  # 如果任务失败，提前返回

        steps = [
            (nsh.safe_step_gameset, 'safe_step_gameset', 0),
            (nsh.safe_step_gui_task, 'safe_step_gui_task', 0)

            # (nsh.safe_step_juese, 'safe_step_juese', 0),
            # (nsh.safe_step_jueji, 'safe_step_jueji', 0),
            # (nsh.safe_step_neigong, 'safe_step_neigong', 0),
            # (nsh.safe_step_dazao, 'safe_step_dazao', 0),
            # (nsh.safe_step_waiguan, 'safe_step_waiguan', 0),
            # (nsh.safe_step_kaifang_shijie, 'safe_step_kaifang_shijie', 0),
            # (nsh.safe_step_qunxia, 'safe_step_qunxia', 0),
            # (nsh.safe_step_zhuangyuan, 'safe_step_zhuangyuan', 0),
            # (nsh.safe_step_lingcong, 'safe_step_lingcong', 0),
            # (nsh.step_img_upload, 'step_img_upload', 0),
            # (nsh.step_meta_upload, 'safe_step_meta_upload', 0),
            # (nsh.step_logout, 'step_logout', 0)
        ]

        start_execution = begin_stage is None

        # 逐个执行任务
        for step_function, stage, param in steps:
            if not start_execution:
                if stage == begin_stage:
                    start_execution = True  # 达到指定阶段，开始执行后续步骤
                else:
                    continue  # 跳过之前的步骤

            # 执行当前阶段的任务
            success = self.execute_task_stage(step_function, stage, param, task)
            if not success:
                return False  # 如果任务失败，提前返回

        product_sn = task['productSn']
        print(f"Product {product_sn} task completed.")
        return True

    def execute_task_stage(self, stage_function, stage, param, task):
        with time_logger(stage):  # 开始计时
            try:
                stage_event = stage_function(param)
                if stage_event and stage_event.status and stage_event.status == EventStatus.FAILURE:
                    return False
                else:
                    self.result_queue.put(stage_event)
            except Exception as e:
                print(f"【worker】任务出错: {e}")
                self.handle_failure(task, stage)
                traceback.print_exc()
                return False  # 表示失败
        return True  # 表示成功

        # try:
        #     print(f"开始执行阶段: {stage}")
        #     stage_event = stage_function(param)
        #     self.result_queue.put(stage_event)
        # except Exception as e:
        #     print(f"【worker】任务出错: {e}")
        #     self.handle_failure(task, stage)
        #     traceback.print_exc()
        #     return False  # 表示失败
        # return True  # 表示成功

    def handle_failure(self, task, stage):
        step_login_event = TaskEvent(task['id'], stage=stage, status=EventStatus.FAILURE)
        self.result_queue.put(step_login_event)


# Master类，发送任务指令，管理Worker，并处理心跳
class Master:
    def __init__(self):
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.worker = None
        self.control_lock = threading.Lock()  # 用于保护共享资源
        self.running = True
        self.portal_client = PortalClient()
        self.last_instruction = None  # 上一条指令执行情况
        self.test_queue = queue.Queue()  # 测试队列

        task = self.portal_client.get_task_info(88)
        # self.test_queue.put(task)
        #
        # task1 = self.portal_client.get_task_info(20)
        # self.test_queue.put(task1)

    # 启动Worker
    def start_worker(self):
        if self.worker is None or not self.worker.is_alive():
            print("【master】启动新任务...")
            self.worker = Worker(self.task_queue, self.result_queue)
            self.worker.start()

    # 停止Worker
    def stop_worker(self):
        if self.worker is not None and self.worker.is_alive():
            print("【master】发送停止指令...")
            self.task_queue.put({"command": "stop"})
            self.worker.join()

    def resume_worker(self):
        if self.worker is not None and self.worker.is_alive():
            print("【master】发送恢复指令...")
            # self.task_queue.put({"command": "resume"})
            # self.worker.join()

    # 心跳机制，接收指令并管理Worker
    def heartbeat(self):
        while self.running:
            # print("【master】发送心跳...", time.time())

            # 模拟向服务器发送心跳并接收指令
            time.sleep(10)
            instruction = self.send_heartbeat(self.last_instruction)

            # 根据指令控制任务
            if instruction and instruction["status"] == "TODO":  # 当前指令未执行
                if instruction["command"] == "DEVICE_RESTART":
                    with self.control_lock:
                        print("接收到重启设备指令")
                        self.stop_worker()
                        self.start_worker()
                        # 更新指令状态
                        instruction["status"] = "DONE"
                        self.send_heartbeat(instruction)  # 指令执行完成，发送一次心跳
                elif instruction["command"] == "DEVICE_STOP":
                    with self.control_lock:
                        print("接收到停止任务指令, ", instruction)
                        self.stop_worker()
                        # 更新指令状态
                        instruction["status"] = "DONE"
                        instruction["deviceStatus"] = "IDLE"
                        self.send_heartbeat(instruction)

                elif instruction["command"] == "TASK_RESUME":
                    with self.control_lock:
                        print(f"接收到恢复任务指令: {instruction}")
                        self.task_queue.put({"command": "task_resume", "task": None, "params": instruction["params"]})
                        self.start_worker()
                        # 更新指令状态
                        instruction["status"] = "DOING"
                        self.send_heartbeat(instruction)

            # 先检查有没有任务在执行中
            if self.worker is not None and self.worker.is_alive() and self.worker.busy:
                # print("【master】正在录号中...")
                time.sleep(5)
            else:
                print("【master】检查录号任务队列...")
                task = self.check_for_new_task()
                if task:
                    task_id = task['id']
                    product_sn = task['productSn']
                    print(f"【master】有新任务: {product_sn}")
                    self.start_worker()
                    self.task_queue.put({"command": "run_task", "task": task})
                    # 更新设备状态
                    if self.last_instruction:
                        instruction = copy.copy(self.last_instruction)
                        instruction['status'] = 'IN_PROGRESS'
                        self.send_heartbeat(instruction)
                    else:
                        instruction = {
                            'deviceStatus': 'IN_PROGRESS',
                            'timestamp': 1727273906134
                        }
                    self.send_heartbeat(instruction)
                else:
                    print("检查新任务")
                    time.sleep(5)

            # 检查任务状态
            try:
                result = self.result_queue.get_nowait()
                print(f"【master】检查worker任务状态: {result}")
                if result is None:
                    print("【master】没有任务执行结果")
                    continue
                if result.status == EventStatus.FINISHED:
                    print(f"【master】任务 {result.task_id} 完成")
                    print('同步录号任务信息...')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'COMPLETED',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                if result.status == EventStatus.SUCCESS:
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'IN_PROGRESS',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                if result.status == EventStatus.FAILURE:
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'FAILED',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    # self.stop_worker()
                if result.status == EventStatus.MANUAL_REQUIRED:
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'MANUAL_REQUIRED',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
            except queue.Empty:
                pass
                # print("【master】没有任务执行结果")

    # 检查是否有新任务，有则执行录号任务
    def check_for_new_task(self):
        # 环境变量任务
        if PRODUCT_SN and PRODUCT_SN != '':
            # 从环境变量中取task
            task = self.portal_client.get_task_info_by_sn(PRODUCT_SN)
        else:
            task = self.portal_client.get_todo_task_from_kk(device_id)

        if task:
            return task

        try:
            task = self.test_queue.get_nowait()
            return task
        except queue.Empty:
            pass

    # 检查服务器指令/心跳
    def send_heartbeat(self, last_instruction):
        # print('### last_instruction:', last_instruction)
        if last_instruction is None:
            device_status = 'IDLE'
            if self.worker is None or self.worker.busy is False:
                device_status = 'IDLE'
            elif self.worker.busy:
                device_status = 'IN_PROGRESS'

            instruction = {
                'deviceId': device_id,
                'deviceStatus': device_status,
                'timestamp': 1727273906134 + random.randint(0, 60 * 60 * 1000)
            }
        else:
            instruction = last_instruction
            device_status = 'IDLE'
            if self.worker is None or self.worker.busy is False:
                device_status = 'IDLE'
            elif self.worker.busy:
                device_status = 'IN_PROGRESS'
            instruction['deviceId'] = device_id
            instruction['deviceStatus'] = 'IDLE'
            instruction['deviceStatus'] = device_status
        return self.portal_client.send_heartbeat(instruction)

    # 关闭Master
    def shutdown(self):
        self.running = False
        self.stop_worker()
        print("Master 关闭")


# 定义上下文管理器来记录时间
@contextmanager
def time_logger(stage):
    start_time = time.time()  # 记录开始时间
    print(f"开始执行阶段: {stage}")
    try:
        yield  # 执行阶段操作
    finally:
        end_time = time.time()  # 记录结束时间
        duration = end_time - start_time  # 计算耗时
        print(f"阶段 {stage} 耗时: {duration:.2f} 秒")


# 主程序
def main():
    master = Master()

    # 启动心跳线程
    heartbeat_thread = threading.Thread(target=master.heartbeat)
    heartbeat_thread.start()

    # 启动初始任务
    # master.start_worker()

    # 等待心跳线程结束
    heartbeat_thread.join()

    # 关闭Master
    # master.shutdown()


if __name__ == "__main__":
    main()
