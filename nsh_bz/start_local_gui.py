#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import multiprocessing
import os
import queue
import sys
import threading
import time
import tkinter as tk
from concurrent.futures import ThreadPoolExecutor
from tkinter import messagebox, simpledialog
from tkinter import scrolledtext

import local_main as local_main
import logger_config
import sys_tool
from luhao_models import EventStatus, StepManager


# 定义主应用类
class App:
    def __init__(self, root, app_version):
        self.master = None  # 将 master 定义为类的属性
        self.root = root
        self.start_time = None  # 录号开始时间
        self.recording_duration_label = None  # 录号时长标签
        self.is_recording = False  # 标志是否正在录号
        self.is_fast_record = tk.BooleanVar(value=True)
        self.no_watermark_var = tk.BooleanVar(value=True)
        self.create_widgets()
        # 在初始化时绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.duration_job = None

        self.logger = logger_config.setup_logger(__name__, text_widget=self.t1)
        self.task_status_label.config(text='等待中', fg='blue')
        self.status_queue = queue.Queue()
        self.device_id = None
        self.app_version = app_version
        self.step_manager = StepManager()
        self.executor = ThreadPoolExecutor(max_workers=10)  # 设置最大线程数
        self.activation_code_file = "user.json"
        # self.start()

    # 定时更新录号时长
    def update_duration(self):
        if self.is_recording and self.start_time:
            elapsed_time = int(time.time() - self.start_time)
            minutes, seconds = divmod(elapsed_time, 60)
            hours, minutes = divmod(minutes, 60)
            self.recording_duration_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            self.duration_job = self.root.after(2000, self.update_duration)

    # 定义日志输出到 tkinter 类
    class StdoutRedirector:
        def __init__(self, text_widget, max_lines=100):
            self.text_widget = text_widget
            self.max_lines = max_lines
            self.current_lines = 0  # 用于缓存行数

        def write(self, str):
            self.text_widget.insert(tk.END, str)  # 在text末尾追加文字
            self.text_widget.see(tk.END)  # 光标一直追加到文件末尾

            # 不在每次插入时都调用 update，而是在特定的时机调用
            if int(self.text_widget.index('end-1c').split('.')[0]) % 10 == 0:
                self.text_widget.update()  # 每插入10行更新一次

            self.current_lines += str.count('\n')  # 仅在插入新行时增加行数
            if self.current_lines > self.max_lines:
                self.text_widget.delete(1.0, f'{self.current_lines - self.max_lines}.0')
                self.current_lines = self.max_lines  # 重新调整行数

        def flush(self):
            pass

    # 控制台输出函数，输出到t1
    def ternimal_print(self, msg):
        DATE_TIME = time.strftime('[%Y-%m-%d %H:%M:%S]')
        self.t1.insert('end', f'{DATE_TIME}  {msg}\n')  # 向text文本框末尾追加文字
        self.t1.see(tk.END)  # 光标一直追加到文件末尾
        self.t1.update()  # 一直更新输出

    # 检查任务状态并更新任务状态标签
    def check_task_status(self):
        try:
            while self.is_recording:
                work_task_info = self.status_queue.get_nowait()
                task_status = work_task_info.get('status')
                product_sn = work_task_info.get('product_sn')
                phase = None
                if work_task_info.get('stage'):
                    phase, progress = self.step_manager.get_step_info(work_task_info.get('stage'))

                phase_label_value = ''
                if phase:
                    # 更新录号阶段到 phase_label
                    phase_label_value = f"【{phase}(总进度{progress})】"
                    # self.phase_label.config(text=f"阶段 {phase}(总进度{percent}) ")

                if task_status == EventStatus.FINISHED:
                    self.task_status_label.config(text='任务已完成', fg='blue')
                    self.phase_label.config(text='录号完成')
                    self.is_recording = False  # 停止计时
                    self.logger.info('任务已完成')
                    # self.stop_task_thread()
                elif task_status == EventStatus.SUCCESS:
                    self.task_status_label.config(text='阶段任务成功', fg='green')
                    self.logger.info('阶段任务成功')
                    phase_label_value = phase_label_value + '阶段已完成'
                    self.phase_label.config(text=phase_label_value)
                elif task_status == EventStatus.IN_PROGRESS:
                    if self.is_recording:
                        self.task_status_label.config(text='正在录号', fg='green')
                        self.logger.info('正在录号')
                        phase_label_value = phase_label_value + '阶段进行中'
                        self.phase_label.config(text=phase_label_value)
                    else:
                        pass
                        # self.task_status_label.config(text='等待中', fg='blue')
                        # self.logger.info('等待中')
                elif task_status == EventStatus.FAILURE:
                    self.task_status_label.config(text='任务失败', fg='red')
                    self.logger.info('任务失败')
                    self.is_recording = False  # 停止计时
                    self.stop_task_thread()
                elif task_status == EventStatus.MANUAL_REQUIRED:
                    self.task_status_label.config(text='需要人工处理', fg='orange')
                    self.logger.info('需要人工处理')
                elif task_status == EventStatus.ERROR:
                    self.task_status_label.config(text='任务出错', fg='red')
                    self.logger.info('任务出错')
                    self.is_recording = False  # 停止计时
                    self.stop_task_thread()
                else:
                    self.task_status_label.config(text=f'{task_status}', fg='purple')
                    # self.stop_task_thread()

                # 更新UI
                # self.task_status_label.update()
        except queue.Empty:
            pass

        except Exception as e:
            self.ternimal_print(f'发生错误：{e}')
            self.stop_task_thread()
        finally:
            self.root.after(2000, self.check_task_status)

    # 启动录号线程
    def start_thread(self):
        # self.submit_selection()
        # return
        sys.stdout = self.StdoutRedirector(self.t1)
        self.ternimal_print('启动录号软件')

        try:
            # 生成序列号写入文件
            device_id = sys_tool.generate_device_id()
            self.device_id = device_id
            if device_id:
                self.ternimal_print('device_id: ' + device_id)
                self.executor.submit(self.write_device_id_to_file, device_id)
            else:
                self.ternimal_print('error：generate device id failed')
                return

            self.master = local_main.main(text_widget=self.t1, status_queue=self.status_queue,
                                          is_fast_record=self.is_fast_record.get(),
                                          save_original=self.no_watermark_var.get(),
                                          device_addr=device_id, app_version=self.app_version)
            if self.master is None:
                self.ternimal_print('录号软件初始化失败')
            else:
                self.ternimal_print('录号软件初始化成功')

            # self.executor.submit(self.check_task_status)
        except Exception as e:
            self.ternimal_print(f'发生错误：{e}')

    # 启动任务线程
    def start_task_thread(self):
        sys.stdout = self.StdoutRedirector(self.t1)
        self.t1.delete(1.0, tk.END)
        is_fast_record = True

        if self.device_id is None or self.master is None:
            self.ternimal_print('错误：设备初始化失败')
            return

        product_params = {
            'gameAccountQufu': self.server_entry.get(),
        }
        self.ternimal_print(f'新建录号任务')

        # 更新任务状态
        self.task_status_label.config(text='任务进行中', fg='green')
        self.is_recording = True

        try:
            # 记录并显示开始时间
            self.start_time = time.time()  # 记录开始时间的时间戳
            start_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.start_time))
            self.start_time_label.config(text=start_time_str)  # 更新开始时间标签

            self.phase_label.config(text='N/A')
            self.master.gui_queue.put(product_params)
            self.logger.info(f'开始任务...... ')
            self.start_time = time.time()
            self.update_duration()

            # 开启一个新线程来监听任务状态
            threading.Thread(target=self.check_task_status, daemon=True).start()
        except Exception as e:
            self.ternimal_print(f'发生错误：{e}')

    def stop_task_thread(self):
        sys.stdout = self.StdoutRedirector(self.t1)

        try:
            self.master.stop_worker_now(is_canceled=True)
            self.ternimal_print('停止任务成功！')
            # 更新任务状态
            # self.task_status_label.config(text='任务已停止', fg='orange')
            self.is_recording = False
        except Exception as e:
            self.ternimal_print(f'发生错误：{e}')

    # 启动录号
    def start(self):
        t = threading.Thread(target=self.start_thread, daemon=True)
        t.start()

    def start_task_2(self):
        self.executor.submit(self.start_task_thread)

    def stop_task(self):
        self.executor.submit(self.stop_task_thread)

    def on_closing(self):
        try:
            if self.master:
                self.master.shutdown()

            # if hasattr(self, 'duration_job') and self.duration_job:  # 检查 duration_job 是否有效
            #     self.root.after_cancel(self.duration_job)  # 停止 after 回调
            #
            # self.terminate_all_threads()

            # 在关闭窗口时，强制杀掉所有子进程
            # kill_main_process()
            # self.logger.info('窗口关闭，强制终止所有任务')

            self.executor.shutdown(wait=True)

            # 退出主窗口
            self.root.quit()
            self.root.destroy()

        except Exception as e:
            self.ternimal_print(f'发生错误：{e}')

    def terminate_all_threads(self):
        # 遍历所有正在运行的线程
        for thread in threading.enumerate():
            if thread.is_alive() and thread != threading.main_thread():
                self.ternimal_print(f"正在终止线程: {thread.name}")
                try:
                    # 通过设置守护线程或其他机制终止线程
                    thread.join(timeout=1)  # 等待线程结束
                except Exception as e:
                    self.ternimal_print(f"线程终止时发生错误：{e}")

    def init_qufu_list(self):
        attrs = self.master.portal_client.get_attrs_from_sku(17)
        for attr in attrs:
            if attr.get('ename') == 'gameAccountQufu':
                qufu_list = json.loads(attr.get('inputList'))
                print(qufu_list)
                self.regions = qufu_list

    def validate_form_2(self):
        if not self.game_account_entry.get().strip():
            messagebox.showerror("错误", "游戏账号为必填项，请填写！")
            return False
        return True

    # 键盘事件调用 stop_task
    def stop_task_event(self, event):
        self.stop_task()

    # 创建界面组件
    def create_widgets(self):
        frm2 = tk.Frame(self.root)
        frm2.pack(padx=10, pady=10, anchor='w')

        # self.fast_record_checkbox = tk.Checkbutton(frm2, text='开启快速录号', variable=self.is_fast_record)
        # self.fast_record_checkbox.pack(side='left', padx=5)
        #
        # self.no_watermark_checkbox = tk.Checkbutton(frm2, text='保留原图', variable=self.no_watermark_var)
        # self.no_watermark_checkbox.pack(side='left', padx=5)

        # 新增账号详情区域
        frm_account_info = tk.LabelFrame(self.root, text="账号信息", padx=5, pady=5)
        frm_account_info.pack(anchor='w', padx=10, pady=10, fill="x")

        # 区服
        tk.Label(frm_account_info, text="区服:").grid(row=2, column=0, sticky='w', padx=5)
        self.server_entry = tk.Entry(frm_account_info)
        self.server_entry.grid(row=2, column=1, sticky='w', padx=5)

        tk.Label(frm_account_info, text="游戏账号:").grid(row=10, column=0, sticky='w', padx=5)
        self.game_account_entry = tk.Entry(frm_account_info)
        self.game_account_entry.grid(row=10, column=1, sticky='w', padx=5)
        tk.Label(frm_account_info, text="(必填)", fg="red").grid(row=10, column=2, sticky='w', padx=0)

        # 创建一个框架来容纳按钮
        button_frame = tk.Frame(frm_account_info)
        button_frame.grid(row=12, column=0, columnspan=2, pady=5)  # 将框架放在网格中，使用columnspan合并两列

        # 使用pack将按钮放入框架中
        button_start_task = tk.Button(button_frame, text='开始任务', command=self.start_task_2, height=1)
        button_start_task.pack(side='left', padx=5)

        button_exit = tk.Button(button_frame, text='停止任务(F4)', command=self.stop_task, height=1)
        button_exit.pack(side='left', padx=5)

        # 任务信息组框架并分成两列，调整为更紧凑的布局
        frm_task_info = tk.LabelFrame(self.root, text="任务信息", padx=5, pady=5)
        frm_task_info.pack(anchor='w', padx=10, pady=10, fill="x")

        # 修改任务信息的布局
        for i in range(5):
            frm_task_info.grid_rowconfigure(i, weight=1, pad=2)
        frm_task_info.grid_columnconfigure(0, weight=1, uniform="equal")
        frm_task_info.grid_columnconfigure(1, weight=2, uniform="equal")

        # 第二行：录号阶段
        tk.Label(frm_task_info, text="录号进度:").grid(row=1, column=0, sticky='w', padx=5)
        self.phase_label = tk.Label(frm_task_info, text="N/A")
        self.phase_label.grid(row=1, column=1, sticky='w', padx=5)

        # 第三行：任务状态（占两列）
        tk.Label(frm_task_info, text="任务状态:").grid(row=2, column=0, sticky='w', padx=5)
        self.task_status_label = tk.Label(frm_task_info, text='等待中', fg='blue')
        self.task_status_label.grid(row=2, column=1, sticky='w', padx=5)

        # 第四行：开始时间
        tk.Label(frm_task_info, text="开始时间:").grid(row=3, column=0, sticky='w', padx=5)
        self.start_time_label = tk.Label(frm_task_info, text="00:00:00")  # 去掉日期，仅保留时间
        self.start_time_label.grid(row=3, column=1, sticky='w', padx=5)

        # 第五行：录号时长
        tk.Label(frm_task_info, text="录号时长:").grid(row=4, column=0, sticky='w', padx=5)
        self.recording_duration_label = tk.Label(frm_task_info, text="00:00:00")
        self.recording_duration_label.grid(row=4, column=1, sticky='w', padx=5)

        # 定义Label组件
        frm4 = tk.Frame(self.root)
        frm4.pack(anchor='w')
        l1 = tk.Label(frm4, text='运行日志：')
        l1.pack(side='left', padx=10, pady=1, ipadx=1, ipady=1)

        # 定义滚动文本组件
        frm5 = tk.Frame(self.root)
        frm5.pack(anchor='w', fill='both', expand=True)
        self.t1 = scrolledtext.ScrolledText(frm5, width=100, height=300, bg='#FFFFFF', font=('宋体', 10))
        self.t1.pack(fill='both', side='left', expand=True, padx=10, pady=10)

        # 启动时默认打开帮助页面
        sys.stdout = self.StdoutRedirector(self.t1, 100)
        self.ternimal_print(
            '''
    欢迎使用看看账号网逆水寒手游截图工具
    
    使用步骤：

                ''')

    def write_device_id_to_file(self, device_id, file_path="device_id.txt"):
        try:
            with open(file_path, "w") as file:
                file.write(f"{device_id}\n")
            print(f"写入文件成功：{file_path}")
        except Exception as e:
            print(f"写入文件时发生错误: {e}")

    # 激活码验证方法
    def verify_activation_code(self, code):
        # 这里假设你从某个文件或数据库中获取正确的激活码
        correct_code = "123"  # 示例激活码，可以改为从文件或服务器读取
        return code == correct_code

    # 请求用户输入激活码
    def prompt_for_activation(self):
        """提示用户输入激活码"""
        while True:
            activation_code = simpledialog.askstring("激活", "请输入激活码:")
            if activation_code is None:  # 用户点击了“取消”
                self.root.destroy()  # 关闭窗口
                sys.exit(0)  # 退出程序

            if self.verify_activation_code(activation_code):
                messagebox.showinfo("激活成功", "激活码验证成功！")
                self.save_activation_code(activation_code)
                self.start()
                return True
            else:
                messagebox.showerror("激活失败", "激活码错误，请重试！")

    def save_activation_code(self, activation_code):
        """将激活码保存到本地文件"""
        try:
            with open(self.activation_code_file, "w") as file:
                json.dump({"activation_code": activation_code}, file)
            print("激活码已保存。")
        except Exception as e:
            print(f"保存激活码时出错: {e}")

    def load_activation_code(self):
        """从本地文件读取激活码"""
        if os.path.exists(self.activation_code_file):
            try:
                with open(self.activation_code_file, "r") as file:
                    data = json.load(file)
                    return data.get("activation_code", None)
            except Exception as e:
                print(f"读取激活码时出错: {e}")
        return None

    def check_activation_status(self):
        """检查激活状态，如果本地有激活码则验证，没有则提示用户输入"""
        saved_activation_code = self.load_activation_code()
        if saved_activation_code and self.verify_activation_code(saved_activation_code):
            self.start()
            print("已从本地读取并验证激活码，激活成功。")
            return True
        else:
            while True:
                if self.prompt_for_activation():
                    break
                else:
                    # 激活失败后，直接重新显示输入激活码窗口
                    continue


def prompt_for_update(latest_version):
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    answer = messagebox.askyesno("有更新可用",
                                 f"检测到新版本 {latest_version} . 是否继续更新?")
    root.quit()
    return answer


# 启动 Tkinter 主窗口
if __name__ == '__main__':
    multiprocessing.freeze_support()

    root = tk.Tk()
    root.withdraw()

    app = App(root, app_version="1.0")
    app.check_activation_status()

    root.title(f'看看账号网逆水寒手游录号工具V1.0')

    # 获取屏幕宽度和高度
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    window_width = 400
    window_height = 600

    # 计算窗口左上角的位置，使窗口出现在右下角
    position_right = screen_width - window_width
    position_down = screen_height - window_height - 75

    # 设置窗口大小和位置
    root.geometry(f'{window_width}x{window_height}+{position_right}+{position_down}')

    root.deiconify()  # 显示主窗口

    # 在 Tkinter 主循环启动前检查更新
    # check_update(root)

    app = App(root, app_version="1.0")  # 实例化 App 类

    # 启动 Tkinter 窗口
    tk.mainloop()
