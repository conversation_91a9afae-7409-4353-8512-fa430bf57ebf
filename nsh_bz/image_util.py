import os
import shutil
import time
import tkinter as tk
from io import BytesIO

import cv2
import numpy as np
import requests
from PIL import ImageGrab
from PIL import ImageTk, ImageFont, ImageDraw

import sys_tool


def create_collage(image_paths, output_path, background_color=(125, 125, 125), images_per_row=3, width=1280, spacing=7):
    # 打开所有图像并获取原始大小
    images = [Image.open(img_path) for img_path in image_paths]
    # 计算每张图片的新宽度和高度
    image_width = (width - (images_per_row - 1) * spacing) // images_per_row
    image_height = image_width * (images[0].height / images[0].width)

    # 计算拼接后大图的高度
    num_rows = (len(images) + images_per_row - 1) // images_per_row
    collage_height = int(num_rows * image_height + (num_rows - 1) * spacing)

    # 创建底图
    collage = Image.new('RGB', (width, collage_height), background_color)

    # 拼接图像
    for index, img in enumerate(images):
        # 调整大小
        img = img.resize((int(image_width), int(image_height)), Image.LANCZOS)
        x = (index % images_per_row) * (image_width + spacing)
        y = (index // images_per_row) * (int(image_height) + spacing)
        collage.paste(img, (x, y))

    try:
        collage.save(output_path, format='JPEG', quality=95, optimize=True)
        print(f'Collage saved at {output_path} with high quality JPG')
    except Exception as e:
        print(f'Failed to save collage: {e}')


from PIL import Image


def create_collage2(image_paths, output_path, background_color=(125, 125, 125), images_per_row=3, spacing=7):
    # 打开所有图像并获取原始大小
    images = [Image.open(img_path) for img_path in image_paths]

    # 计算每行的宽度和每列的高度，不调整图片大小
    image_widths = [img.width for img in images]
    image_heights = [img.height for img in images]

    # 计算拼接图像的宽度和高度
    num_rows = (len(images) + images_per_row - 1) // images_per_row
    max_heights_per_row = []

    collage_width = 0
    collage_height = 0
    for row in range(num_rows):
        row_images = images[row * images_per_row:(row + 1) * images_per_row]
        row_width = sum(img.width for img in row_images) + spacing * (len(row_images) - 1)
        row_height = max(img.height for img in row_images)
        collage_width = max(collage_width, row_width)  # 确保大图宽度足够容纳所有行
        collage_height += row_height + spacing
        max_heights_per_row.append(row_height)

    collage_height -= spacing  # 最后一行不需要额外的间距

    # 创建底图
    collage = Image.new('RGB', (collage_width, collage_height), background_color)

    # 拼接图像
    y_offset = 0
    for row in range(num_rows):
        row_images = images[row * images_per_row:(row + 1) * images_per_row]
        x_offset = 0
        for img in row_images:
            collage.paste(img, (x_offset, y_offset))
            x_offset += img.width + spacing
        y_offset += max_heights_per_row[row] + spacing

    try:
        collage.save(output_path, format='JPEG', quality=95, optimize=True)
        print(f'Collage saved at {output_path} with high quality JPG')
    except Exception as e:
        print(f'Failed to save collage: {e}')


def create_vertical_collage(image_paths, output_path, background_color=(125, 125, 125)):
    # 打开所有图像并获取原始大小
    images = [Image.open(img_path) for img_path in image_paths]

    # 以第一张图的宽度为准
    image_width = images[0].width
    # 按比例调整所有图片的高度，保持宽度一致
    images = [img.resize((image_width, int(img.height * (image_width / img.width))), Image.LANCZOS) for img in images]

    # 计算拼接后大图的总高度
    collage_height = sum(img.height for img in images)

    # 创建底图
    collage = Image.new('RGB', (image_width, collage_height), background_color)

    # 拼接图像
    y_offset = 0
    for img in images:
        collage.paste(img, (0, y_offset))
        y_offset += img.height

    # 保存结果
    collage.save(output_path)
    print(f'Collage saved at {output_path}')


def add_watermark(image, watermark_path):
    """Tile the watermark across the image without stretching."""
    watermark = Image.open(watermark_path).convert("RGBA")

    transparent_layer = Image.new("RGBA", image.size, (0, 0, 0, 0))

    watermark_width, watermark_height = watermark.size

    for x in range(0, image.size[0], watermark_width):
        for y in range(0, image.size[1], watermark_height):
            transparent_layer.paste(watermark, (x, y), watermark)

    watermarked_image = Image.alpha_composite(image.convert("RGBA"), transparent_layer)
    return watermarked_image


def add_logo_watermark(image, logo_path, scale=0.2, padding=10):
    logo = Image.open(logo_path).convert("RGBA")

    logo_width, logo_height = logo.size
    new_width = int(image.size[0] * scale)
    new_height = int((logo_height / logo_width) * new_width)
    logo = logo.resize((new_width, new_height), Image.LANCZOS)

    transparent_layer = Image.new("RGBA", image.size, (0, 0, 0, 0))
    position = (image.size[0] - new_width - padding, image.size[1] - new_height - padding)
    transparent_layer.paste(logo, position, logo)
    watermarked_image = Image.alpha_composite(image.convert("RGBA"), transparent_layer)
    return watermarked_image


def add_text_watermark(image, text, font_path, font_size=50, color=(255, 255, 255, 160), angle=-30,
                       horizontal_spacing=300, vertical_spacing=300, left_margin=50, top_margin=50):
    """在图像上添加旋转的水印，水印文字先旋转后重复排列，支持横向和竖向间隔以及边距"""
    # 确保图像模式为 'RGBA'
    image = image.convert("RGBA")

    # 根据图像高度调整上边距
    if image.height < 110:
        top_margin = 5

    # 加载字体
    try:
        font = ImageFont.truetype(font_path, font_size)
    except IOError:
        print(f"Font not found at {font_path}. Using default font.")
        font = ImageFont.load_default()

    # 创建一个单独的透明图层来绘制旋转的文字
    text_layer = Image.new("RGBA", (image.width, image.height), (0, 0, 0, 0))
    draw_text = ImageDraw.Draw(text_layer)

    # 在透明层上绘制文字
    draw_text.text((0, 0), text, font=font, fill=color)

    # 旋转文字并将其裁剪为合适的大小
    rotated_text = text_layer.rotate(angle, expand=True)
    rotated_bbox = rotated_text.getbbox()
    rotated_text = rotated_text.crop(rotated_bbox)

    # 获取旋转后的水印宽高
    rotated_width, rotated_height = rotated_text.size

    # 创建一个和图片一样大的透明层
    transparent_layer = Image.new("RGBA", image.size, (0, 0, 0, 0))

    # 将旋转后的水印在图像上水平和竖直方向进行间隔排列
    for y in range(top_margin, image.size[1], rotated_height + vertical_spacing):
        for x in range(left_margin, image.size[0], rotated_width + horizontal_spacing):
            transparent_layer.paste(rotated_text, (x, y), rotated_text)

    # 将透明层叠加到原图上
    watermarked_image = Image.alpha_composite(image, transparent_layer)

    return watermarked_image


# 区服_级别
def add_qufu_watermark(image, qufu_path, scale=0.2, padding=10):
    logo = Image.open(qufu_path).convert("RGBA")

    logo_width, logo_height = logo.size
    new_width = int(image.size[0] * scale)
    new_height = int((logo_height / logo_width) * new_width)
    logo = logo.resize((new_width, new_height), Image.LANCZOS)

    transparent_layer = Image.new("RGBA", image.size, (0, 0, 0, 0))
    # 调整位置到左上角
    position = (padding, padding)
    transparent_layer.paste(logo, position, logo)
    watermarked_image = Image.alpha_composite(image.convert("RGBA"), transparent_layer)
    return watermarked_image


sort_order = [
    "头图", "旅途", "角色背包", "仓库", "自染", "自染头", "自染方案",
    "打造强化", "打造属性", "角色装备", "灵韵", "发式", "套装",
    "武器", "坐骑", "坐骑祥瑞", "武功", "修行", "内功详细属性加成",
    "国色", "开放世界", "时鉴赏一期", "时鉴赏二期", "馆藏总等级", "衣品",
    "群侠", "绝技", "绝技升阶", "神器", "灵宠", "庄园", "赠礼额度", "纹玉兑换"
]

# 创建一个字典以便进行排序
priority_dict = {name: index for index, name in enumerate(sort_order)}


def collage(image_folder, watermark=True, watermark_path="resources/water_pc11.png", logo_path="resources/logo.png",
            save_original=False):
    # 获取当前脚本的绝对路径
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # 将水印和 logo 的相对路径转换为基于当前脚本目录的绝对路径
    watermark_path = os.path.join(script_dir, watermark_path)
    font_path = os.path.join(script_dir, 'resources/din-bold-2.ttf')
    logo_path = os.path.join(script_dir, logo_path)
    qufu_path = None

    original_images_folder = os.path.join(image_folder, 'origin')
    if save_original:
        # 创建原图保存的文件夹
        os.makedirs(original_images_folder, exist_ok=True)

    categories = {
        '发式': [],
        '角色背包': [],
        '角色装备': [],
        '灵韵': [],
        '套装': [],
        '武器': [],
        '坐骑': [],
        '坐骑祥瑞': [],
        '武功': [],
        '打造属性': [],
        '自染头': [],
        '自染方案': [],
        '仓库': [],
        '纹玉兑换': [],
        # 'tianshangfashi': [],
    }
    # 自染头 121*197  自染方案 149 115

    other_images = []

    # 获取所有图像文件
    image_files = [os.path.join(image_folder, f) for f in os.listdir(image_folder) if
                   f.endswith(('jpg', 'jpeg', 'png', 'webp'))]
    sorted_image_files = sorted(image_files, key=lambda x: os.path.basename(x))

    # 过滤出不同类型的图像
    for img_path in sorted_image_files:
        filename = os.path.basename(img_path)
        if filename.startswith('发式'):
            categories['发式'].append(img_path)
        elif filename.startswith('角色-背包'):
            categories['角色背包'].append(img_path)
        elif filename.startswith('角色-装备'):
            categories['角色装备'].append(img_path)
        elif filename.startswith('灵韵'):
            categories['灵韵'].append(img_path)
        elif filename.startswith('套装'):
            categories['套装'].append(img_path)
        elif filename.startswith('武器'):
            categories['武器'].append(img_path)
        elif filename.startswith('坐骑'):
            categories['坐骑'].append(img_path)
        elif filename.startswith('祥瑞'):
            categories['坐骑祥瑞'].append(img_path)
        elif filename.startswith('武功'):
            categories['武功'].append(img_path)
        elif filename.startswith('打造属性'):
            categories['打造属性'].append(img_path)
        elif filename.startswith('自染头'):
            categories['自染头'].append(img_path)
        elif filename.startswith('自染方案'):
            categories['自染方案'].append(img_path)
        elif filename.startswith('仓库'):
            categories['仓库'].append(img_path)
        elif filename.startswith('纹玉兑换'):
            categories['纹玉兑换'].append(img_path)
        # elif filename.startswith('天赏发式'):
        #     categories['tianshangfashi'].append(img_path)
        elif filename.startswith('头图'):
            # 头图不做处理
            pass
        elif filename.startswith('区服'):
            qufu_path = img_path
        else:
            if os.path.splitext(img_path)[1] == '.jpg':
                other_images.append(img_path)

    # 创建拼图
    for category, images in categories.items():
        if images:
            # 排序
            images.sort(key=lambda x: os.path.basename(x))

            filename = os.path.basename(images[0])
            type = filename.split('_')[1]

            if any('天赏外观' in img_path for img_path in images):
                type = '天赏外观'

            suffix = str(int(round(time.time())))
            output_path = os.path.join(image_folder, f"{category}_{type}_{suffix}.jpg")
            if category == '发式':
                create_collage2(images, output_path, images_per_row=9)
            elif category == '角色背包':
                # create_collage(images, output_path, images_per_row=1, spacing=5)
                create_vertical_collage(images, output_path)
            elif category == '角色装备':
                create_collage(images, output_path, images_per_row=4, spacing=1)
            elif category == '灵韵':
                create_collage(images, output_path, images_per_row=4, spacing=1)
            elif category == '套装':
                create_collage(images, output_path, images_per_row=9)
            elif category == '武器':
                create_collage(images, output_path, images_per_row=9, spacing=5)
            elif category == '坐骑':
                create_collage(images, output_path, images_per_row=9)
            elif category == '坐骑祥瑞':
                create_collage(images, output_path, images_per_row=9)
            elif category == '武功':
                create_collage(images, output_path, images_per_row=9)
            elif category == '打造属性':
                create_collage2(images, output_path, images_per_row=3)
            elif category == '自染头':
                create_collage(images, output_path, width=1200, images_per_row=6)
            elif category == '自染方案':
                create_collage(images, output_path, width=1200, images_per_row=6)
            elif category == '仓库':
                create_collage(images, output_path, width=1200, images_per_row=3)
            elif category == "纹玉兑换":
                create_collage2(images, output_path, images_per_row=3)
            # elif category == 'tianshangfashi':
            #     create_collage(images, output_path, images_per_row=6)

            if save_original:
                # 复制原图
                shutil.copy(output_path, original_images_folder)

            # if watermark:
            #     try:
            #         collage_image = Image.open(output_path).convert("RGBA")
            #         collage_image = add_text_watermark(collage_image, "kkzhw.com", font_path=font_path,
            #                                            font_size=90,
            #                                            horizontal_spacing=280, vertical_spacing=250, left_margin=50,
            #                                            top_margin=50)
            #
            #         collage_image = collage_image.convert("RGB")
            #         collage_image.save(output_path, "JPEG", quality=100)
            #         print(f"Watermarked collage saved: {output_path}")
            #     except Exception as e:
            #         print(f"Failed to watermark collage: {e}")

            for img_path in images:
                if img_path is not None:
                    os.remove(img_path)
                    print(f'Deleted original image: {img_path}')
        else:
            print(f"No images found with '{category}' in the filename.")

    # 处理其他图像
    for img_path in other_images:
        output_path = os.path.join(image_folder, img_path)
        if save_original:
            # 复制原图
            shutil.copy(output_path, original_images_folder)
        # 加水印
        # if watermark:
        #     try:
        #         collage_image = Image.open(output_path).convert("RGBA")
        #         # collage_image = add_watermark(collage_image, watermark_path)
        #
        #         collage_image = add_logo_watermark(collage_image, logo_path)
        #         if qufu_path:
        #             collage_image = add_qufu_watermark(collage_image, qufu_path)
        #         collage_image = collage_image.convert("RGB")
        #         collage_image.save(output_path, "JPEG")
        #         print(f"Watermarked collage saved: {output_path}")
        #     except Exception as e:
        #         print(f"Failed to watermark collage: {e}")
    # 删除所有png
    for root, dirs, files in os.walk(image_folder):
        for file in files:
            if file.endswith('.png'):
                os.remove(os.path.join(root, file))
    if qufu_path is not None:
        os.remove(qufu_path)

    # 获取所有 jpg 文件
    files = [f for f in os.listdir(image_folder) if f.endswith('.jpg')]

    # 进行排序
    sorted_files = sorted(files, key=lambda f: priority_dict.get(f.split('_')[0], float('inf')))

    # 重命名文件
    for index, file in enumerate(sorted_files):
        new_name = f"{index + 1}{file}"
        os.rename(os.path.join(image_folder, file), os.path.join(image_folder, new_name))

    if save_original:
        # 添加可见的水印
        add_visible_watermark(original_images_folder, 'kk', 'zhw')
        # original_images_folder 文件夹加密压缩
        # sys_tool.zip_and_encrypt_folder(original_images_folder, f'{image_folder}/原图.zip', '96621679')
        # 删除 original_images_folder
        shutil.rmtree(original_images_folder)

    print("文件排序重命名完成！")


def capture_screen(save_path, region=None):
    """
    截取屏幕的指定区域并保存为图片，若不传入区域，则截取整个屏幕。

    :param save_path: 图片保存的路径
    :param region: 截图的区域，格式为 (left, top, right, bottom)，如果为 None 则截取整个屏幕
    :return: 返回截图的 Image 对象
    """
    # 截取屏幕或指定区域
    screenshot = ImageGrab.grab(bbox=region) if region else ImageGrab.grab()

    # 保存截图
    screenshot.save(save_path)

    # 返回截图
    return screenshot


def show_image(image_url, display_time=5000, window_size=(400, 300), position=(100, 100)):
    try:
        # 初始化 Tkinter 窗口
        window = tk.Tk()
        window.title("Image Display")

        # 设置窗口大小和位置
        window.geometry(f"{window_size[0]}x{window_size[1]}+{position[0]}+{position[1]}")

        # 下载图片
        response = requests.get(image_url)
        response.raise_for_status()
        img = Image.open(BytesIO(response.content))

        # 获取图片的原始尺寸
        img_width, img_height = img.size

        # 计算宽高比，并保持图片比例调整大小
        window_width, window_height = window_size
        img_ratio = img_width / img_height
        window_ratio = window_width / window_height

        if img_ratio > window_ratio:
            # 图片较宽，以宽度为基准缩放
            new_width = window_width
            new_height = int(window_width / img_ratio)
        else:
            # 图片较高，以高度为基准缩放
            new_height = window_height
            new_width = int(window_height * img_ratio)

        # 缩放图片
        img = img.resize((new_width, new_height), Image.LANCZOS)
        img_tk = ImageTk.PhotoImage(img)

        # 创建标签显示图片
        label = tk.Label(window, image=img_tk)
        label.image = img_tk  # 防止垃圾回收
        label.pack(expand=True)

        # 在指定时间后关闭窗口
        window.after(display_time, window.destroy)

        # 启动 Tkinter 事件循环
        window.mainloop()

    except Exception as e:
        print(f"Error fetching or displaying image: {e}")


# 摩斯密码映射
morse_code = {
    'k': '-.-',
    'z': '--..',
    'h': '....',
    'w': '.--',
    ' ': ' '
}


def text_to_morse(text):
    return ' '.join(morse_code[char] for char in text.lower() if char in morse_code)


def add_visible_watermark(folder_path, watermark_text_left, watermark_text_right, alpha=0.2):
    # 遍历文件夹中的所有 JPG 图片
    for filename in os.listdir(folder_path):
        if filename.endswith('.jpg'):
            image_path = os.path.join(folder_path, filename)

            # 将文本转换为摩斯密码
            morse_text_left = text_to_morse(watermark_text_left)
            morse_text_right = text_to_morse(watermark_text_right)

            # 读取原图像
            # image = cv2.imread(image_path)
            image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), -1)
            if image is None:
                print(f"Could not read the image: {image_path}")
                continue

            # 获取图像尺寸
            height, width, _ = image.shape

            # 创建与原图像大小相同的黑色图像
            watermark = np.zeros_like(image)

            # 在黑色图像上添加左下角摩斯密码水印
            cv2.putText(watermark, morse_text_left, (10, height - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255),
                        1)
            # 在黑色图像上添加右下角摩斯密码水印
            cv2.putText(watermark, morse_text_right, (width - 70, height - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.3,
                        (255, 255, 255), 1)

            # 混合水印与原图像
            watermarked_image = cv2.addWeighted(image, 1 - alpha, watermark, alpha, 0)

            # 使用 imencode 保存带水印的图像
            _, buffer = cv2.imencode('.jpg', watermarked_image)
            with open(image_path, 'wb') as f:
                f.write(buffer)

            print(f"Visible watermarked image saved: {image_path}")


if __name__ == '__main__':
    # 使用示例
    # image_folder = '/Users/<USER>/Downloads/拼图/'
    # output_image_path = 'collage.jpg'
    # image_files = [os.path.join(image_folder, f) for f in os.listdir(image_folder) if
    #                f.endswith(('jpg', 'jpeg', 'png', 'webp'))]
    #
    # create_collage(image_files, output_image_path)
    collage('D:\\kkzhw\\airtest_log\\NSH37913380_20241119172324 (2)', save_original=True)
    # 截取整个屏幕并保存
    # image = capture_screen("full_screenshot2.jpg")
    #
    # # 截取指定区域并保存 (left=100, top=100, right=500, bottom=500)
    # image_region = capture_screen("region_screenshot.png", region=(100, 100, 500, 500))
    # image_region.show()

    # image_url = 'https://images2.kkzhw.com/mall/images2/20241015/xwyMtvf_1728935760915.jpg'
    # show_image(image_url, display_time=5000, window_size=(509, 540), position=(778, 320))
    # show_image(image_url, display_time=5000, window_size=(509, 540), position=(778, 320))

    # add_visible_watermark(os.path.join(r'D:\kkzhw\airtest_log\NSH28490016_20241030140958', '原图'), 'kk', 'zhw')
    # create_vertical_collage(
    #     ['/Users/<USER>/workspace/luhao/2001730962082_.pic.jpg', '/Users/<USER>/workspace/luhao/2011730962097_.pic.jpg'],
    #     './out.jpg')
