import os
import tempfile
from io import BytesIO

import PyPDF2
import requests
from PIL import Image as PILImage, Image
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.units import cm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfgen import canvas
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, KeepInFrame

import configs
from server_client import PortalClient
from utils import oss_util

portal_client = PortalClient()


def compress_image(img, quality=75):
    buffer = BytesIO()
    img.save(buffer, format="JPEG", quality=quality)  # 使用 JPEG 压缩图片质量
    buffer.seek(0)
    return Image.open(buffer)


class PDFGenerator:
    def __init__(self):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        font_path = os.path.join(script_dir, 'SimSun.ttf')
        pdfmetrics.registerFont(TTFont('SimSun', font_path))

    # 获取接口数据
    def get_product_info(self, url):
        r = requests.get(url).json()
        return r['data']

    # 生成表格 PDF
    def generate_table_pdf(self, product_info):
        buffer = BytesIO()

        # 设置页面宽度为 15 厘米，无边距
        table_data = []
        styles = getSampleStyleSheet()
        style_normal = styles['Normal']
        style_normal.fontName = 'SimSun'

        # 设置标题
        product_attribute = Paragraph('属性', style_normal)
        value = Paragraph('值', style_normal)
        table_data.append([product_attribute, value])

        attr_set = set()
        for attr in product_info['productAttributeList']:
            if attr['type'] == 1 or attr['type'] == 2:
                attr_set.add(attr['name'])

        for attr_value in product_info['productAttributeValueList']:
            if attr_value['productAttributeName'] in attr_set:
                product_attribute = Paragraph(attr_value['productAttributeName'], style_normal)
                value = Paragraph(attr_value['value'], style_normal)
                table_data.append([product_attribute, value])

        # 设置表格列宽
        table = Table(table_data, colWidths=[6 * cm, 9 * cm])
        table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('BACKGROUND', (0, 0), (-1, 0), colors.gray),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            # ('WRAP', (0, 0), (-1, -1), True),  # 启用自动换行
        ]))

        # 创建 PDF 文档，宽度为 15 厘米，高度根据表格内容调整，设置无边距
        pdf = SimpleDocTemplate(buffer, pagesize=(15 * cm, 30 * cm),
                                leftMargin=0, rightMargin=0, topMargin=0, bottomMargin=0)

        # 使用 KeepInFrame 确保表格不会分页
        table_in_frame = KeepInFrame(15 * cm, 30 * cm, content=[table], mergeSpace=True)

        # 生成 PDF
        pdf.build([table_in_frame])

        buffer.seek(0)
        return buffer

    # 动态计算页面高度，保持图片的宽高比
    def get_image_and_dynamic_page_size(self, url):
        response = requests.get(url)
        img = PILImage.open(BytesIO(response.content))

        # 获取原始宽高
        orig_width, orig_height = img.size

        # 假设页面宽度固定为 15cm，计算图片对应的高度
        page_width = 15 * cm
        new_height = (page_width / orig_width) * orig_height

        # 返回图片和相应的页面大小 (宽度固定，高度自适应)
        return img, (page_width, new_height)

    # 生成图片 PDF
    def generate_pdf(self, product_info):
        buffer = BytesIO()

        # 创建 PDF 文件
        pdf = canvas.Canvas(buffer)

        # 处理主图片
        img, page_size = self.get_image_and_dynamic_page_size(product_info['product']['pic'])
        page_width, page_height = page_size
        pdf.setPageSize((page_width, page_height))

        # 临时保存图片到文件
        with tempfile.NamedTemporaryFile(delete=False) as temp_img_file:
            img.save(temp_img_file, format="PNG")
            temp_img_file_path = temp_img_file.name

        # 绘制主图片
        pdf.drawImage(temp_img_file_path, 0, 0, width=page_width, height=page_height)
        pdf.showPage()  # 开始新页面

        # 处理相册图片
        if 'albumPics' in product_info['product']:
            album_pics = product_info['product']['albumPics'].split(",")
            for pic in album_pics:
                img, page_size = self.get_image_and_dynamic_page_size(pic)
                img = compress_image(img, quality=75)
                page_width, page_height = page_size
                pdf.setPageSize((page_width, page_height))

                # 临时保存相册图片到文件
                with tempfile.NamedTemporaryFile(delete=False) as temp_img_file:
                    img.save(temp_img_file, format="JPEG")
                    temp_img_file_path = temp_img_file.name

                # 绘制相册图片
                pdf.drawImage(temp_img_file_path, 0, 0, width=page_width, height=page_height)
                pdf.showPage()  # 开始新页面

        # 保存 PDF 文件
        pdf.save()

        buffer.seek(0)
        return buffer

    # 合并 PDF 文件
    def merge_pdfs(self, pdf_list):
        buffer = BytesIO()
        pdf_writer = PyPDF2.PdfWriter()

        # 合并每个 PDF 文件
        for pdf_file in pdf_list:
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            for page_num in range(len(pdf_reader.pages)):
                pdf_writer.add_page(pdf_reader.pages[page_num])

        # 将合并后的内容写入目标文件
        pdf_writer.write(buffer)
        buffer.seek(0)
        return buffer

    # 生成并合并 PDF
    def generate_and_merge_pdfs(self, product_id):
        # url = "https://api2.kkzhw.com/mall-portal/product/detail?productSn=" + product_sn
        # product_info = self.get_product_info(url)
        product_info = portal_client.get_product_detail(product_id)

        # 生成表格 PDF 和图片 PDF
        table_pdf_buffer = self.generate_table_pdf(product_info)
        image_pdf_buffer = self.generate_pdf(product_info)

        # 合并 PDF 文件
        final_pdf_buffer = self.merge_pdfs([table_pdf_buffer, image_pdf_buffer])

        return final_pdf_buffer


def generate_pdf(product_id):
    pdf_gen = PDFGenerator()
    final_pdf_buffer = pdf_gen.generate_and_merge_pdfs(product_id)

    # 保存 PDF 到文件
    with open("output.pdf", "wb") as f:
        f.write(final_pdf_buffer.getvalue())
    print("PDF 生成成功")
    oss_file_name = oss_util.upload_file_to_oss('output.pdf', 'pdf')
    return configs.image_server_url + oss_file_name


if __name__ == '__main__':
    pdf_url = generate_pdf(385698)
    print(pdf_url)
