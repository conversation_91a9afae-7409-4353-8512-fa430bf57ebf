import base64
import binascii

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad


class AESCryptoUtil:
    def __init__(self, base64_key: str):
        """
        初始化 AES 加密工具类
        :param base64_key: Base64 编码的 AES 密钥
        """
        # 解码 Base64 密钥
        self.key = base64.b64decode(base64_key)
        # 初始化 AES 加密器，使用 ECB 模式
        self.aes = AES.new(self.key, AES.MODE_ECB)

    def encrypt(self, plaintext: str) -> str:
        """
        加密给定的文本内容
        :param plaintext: 需要加密的文本内容
        :return: 加密后的内容（16 进制字符串）
        """
        # 对内容进行 PKCS7 填充并加密
        padded_data = pad(plaintext.encode('utf-8'), AES.block_size)
        encrypted_data = self.aes.encrypt(padded_data)
        # 将加密后的数据转换为 16 进制字符串
        return binascii.hexlify(encrypted_data).decode('utf-8')

    def decrypt(self, encrypted_hex: str) -> str:
        """
        解密加密后的 16 进制字符串
        :param encrypted_hex: 16 进制加密字符串
        :return: 解密后的原始文本
        """
        # 将 16 进制字符串转换为字节数组
        encrypted_data = binascii.unhexlify(encrypted_hex)
        # 解密数据
        decrypted_data = self.aes.decrypt(encrypted_data)
        # 去除填充并返回原始字符串
        return unpad(decrypted_data, AES.block_size).decode('utf-8')


# 使用示例
if __name__ == "__main__":
    # 初始化密钥（Base64 编码）
    KK_AES_KEY_128 = "aEUAzI8eCEgdo02pfkLY+w=="

    # 实例化 AES 工具类
    aes_util = AESCryptoUtil(KK_AES_KEY_128)

    # 要加密的内容
    content = "391694"

    # 加密
    encrypt_hex = aes_util.encrypt(content)
    print(f"加密后的内容: {encrypt_hex}")

    # 解密
    decrypt_str = aes_util.decrypt(encrypt_hex)
    print(f"解密后的内容: {decrypt_str}")
