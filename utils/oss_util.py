import datetime
import random
import string
import traceback

import oss2

import configs


def generate_oss_path():
    # 获取当前日期并格式化为 yyyyMMdd
    today = datetime.datetime.now().strftime("%Y%m%d")
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=6))
    # 获取当前毫秒时间戳
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
    # 拼接路径
    oss_path = f"{today}/"
    return oss_path


def generate_oss_fileName(file_type):
    # 生成六位随机字符数字串
    # 注意：这里使用了string.ascii_letters + string.digits来确保生成的是字母和数字的混合
    # 如果只想要数字，可以去掉string.ascii_letters部分
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=7))

    # 获取当前毫秒时间戳
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))

    # 拼接路径
    oss_fileName = f"{random_str}_{current_millis}." + file_type

    return oss_fileName


def upload_one_img_to_oss(file_path):
    try:
        bucket_name = 'kkzhw-mall'  # 替换为你的OSS Bucket名称
        endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint
        access_key_id = configs.OSS_ACCESS_KEY_ID
        access_key_secret = configs.OSS_ACCESS_KEY_SECRET
        print(f"开始上传文件{file_path}")

        proxies = {
            "http": None,
            "https": None,
        }
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name, proxies=proxies)

        oss_file_path = "mall/images2/" + generate_oss_path()
        oss_fileName = oss_file_path + generate_oss_fileName('jpg')
        with open(file_path, 'rb') as fileobj:
            bucket.put_object(oss_fileName, fileobj)

        print(f"oss_filename:{oss_fileName}")
        return oss_fileName
    except Exception as e:
        traceback.print_exc()
        print('xxx')
        # kkLogger_log(f"upload_one_img_to_oss 失败：{e}")


def upload_file_to_oss(file_path, file_type, oss_file_path='mall/files/'):
    try:
        bucket_name = 'kkzhw-mall'  # 替换为你的OSS Bucket名称
        endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint
        access_key_id = configs.OSS_ACCESS_KEY_ID
        access_key_secret = configs.OSS_ACCESS_KEY_SECRET
        print(f"开始上传文件{file_path}")

        proxies = {
            "http": None,
            "https": None,
        }
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name, proxies=proxies)

        oss_file_path = oss_file_path + generate_oss_path()
        oss_fileName = oss_file_path + generate_oss_fileName(file_type)
        with open(file_path, 'rb') as fileobj:
            bucket.put_object(oss_fileName, fileobj)

        print(f"oss_filename:{oss_fileName}")
        return oss_fileName
    except Exception as e:
        traceback.print_exc()
        print('xxx')
        # kkLogger_log(f"upload_one_img_to_oss 失败：{e}")


if __name__ == '__main__':
    upload_one_img_to_oss("E:\workspace\luhao\wzry.air\output_collage3.jpg")
