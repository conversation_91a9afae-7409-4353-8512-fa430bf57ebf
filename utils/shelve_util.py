import shelve
import json


class ShelveDB:
    def __init__(self, db_name='default_db'):
        """
        初始化数据库名称
        :param db_name: 数据库文件名
        """
        self.db_name = db_name

    def save(self, key, value):
        """
        保存数据
        :param key: 数据的键
        :param value: 要保存的JSON格式数据
        """
        with shelve.open(self.db_name) as db:
            db[key] = json.dumps(value)

    def get(self, key):
        """
        获取数据
        :param key: 数据的键
        :return: JSON格式的值，或者None如果键不存在
        """
        with shelve.open(self.db_name) as db:
            if key in db:
                return json.loads(db[key])
            return None

    def delete(self, key):
        """
        删除数据
        :param key: 数据的键
        """
        with shelve.open(self.db_name) as db:
            if key in db:
                del db[key]

    def exists(self, key):
        """
        检查键是否存在
        :param key: 数据的键
        :return: 布尔值，True表示存在，False表示不存在
        """
        with shelve.open(self.db_name) as db:
            return key in db

    def clear(self):
        """
        清空数据库
        """
        with shelve.open(self.db_name) as db:
            db.clear()


# 示例使用
if __name__ == '__main__':
    # 创建数据库对象
    db = ShelveDB('heroes_db')

    # 保存英雄数据
    hero_data = {
        "name": "Warrior",
        "image": "warrior.png",
        "description": "A brave warrior skilled in combat."
    }
    db.save("hero_001", hero_data)

    # 获取英雄数据
    result = db.get("hero_001")
    print(f"Hero 001: {result}")

    # 检查键是否存在
    exists = db.exists("hero_001")
    print(f"Hero 001 exists: {exists}")

    # 删除英雄数据
    db.delete("hero_001")

    # 清空数据库
    db.clear()
