import os
from io import BytesIO
from pathlib import Path

from PIL import Image, ImageDraw, ImageFont, ImageOps

# 获取当前脚本所在目录
script_dir = Path(__file__).resolve().parent


def calculate_total_height(skin_group):
    total_height = 0
    for category, skins in skin_group.items():
        # 增加标题高度
        total_height += 324
        # 计算贴图行数
        num_skins = len(skins)
        rows = (num_skins + 5) // 6  # 每行6个，向上取整
        # 增加贴图高度
        total_height += rows * 222

    return total_height


def resize_image_height(img, target_height):
    """
    调整图片高度到目标高度，宽度保持不变，确保颜色模式一致
    :param img: 输入图片（PIL.Image 对象）
    :param target_height: 目标高度（整数）
    :return: 调整高度后的图片（PIL.Image 对象）
    """
    # 获取原始宽度和高度
    width, original_height = img.size

    # 保持宽度不变，仅调整高度
    resized_img = img.resize((width, target_height), Image.Resampling.LANCZOS)

    # 确保调整后的图片颜色模式与原始图片一致
    if resized_img.mode != img.mode:
        resized_img = resized_img.convert(img.mode)

    return resized_img


def extend_image(input_path, new_height):
    # 打开原始图像
    img = Image.open(input_path)
    width, original_height = img.size

    # 检查输入高度合法性
    # if new_height <= original_height:
    #     raise ValueError("新高度必须大于原图高度（1280像素）")
    # if original_height != 990 or width != 1280:
    #     print("警告：输入图像尺寸不是990x1280，继续处理...")

    # 定义扩展区域 (原图1000部分)
    top_cut = 1000
    bottom_section = img.crop((0, top_cut, width, original_height))

    # 计算需要生成的新区域高度
    extended_section_height = new_height - top_cut

    # 拉伸扩展区域
    extended_section = bottom_section.resize(
        (width, extended_section_height),
        Image.Resampling.LANCZOS  # 高质量插值
    )

    # 创建新图像
    new_img = Image.new('RGB', (width, new_height))

    # 粘贴原始顶部内容 (0-880)
    new_img.paste(img.crop((0, 0, width, top_cut)), (0, 0))

    # 粘贴扩展区域
    new_img.paste(extended_section, (0, top_cut))

    return new_img


def paste_overlay(base_img, overlay_path, overlay_position):
    """
    在底图上粘贴贴图
    :param base_img: 底图（PIL.Image 对象）
    :param overlay_path: 贴图文件路径
    :param overlay_position: 贴图位置 (x, y)
    """
    overlay_img = Image.open(overlay_path)
    # 如果贴图不是 RGBA 模式，则转换为 RGBA 模式
    if overlay_img.mode != 'RGBA':
        overlay_img = overlay_img.convert('RGBA')

    # 粘贴贴图，支持透明贴图
    base_img.paste(overlay_img, overlay_position, overlay_img)


def paste_overlay_image(base_img, overlay_img, overlay_position):
    """
    在底图上粘贴贴图（直接接受Image对象）
    :param base_img: 底图（PIL.Image 对象）
    :param overlay_img: 贴图（PIL.Image 对象）
    :param overlay_position: 贴图位置 (x, y)
    """
    # 如果贴图不是 RGBA 模式，则转换为 RGBA 模式
    if overlay_img.mode != 'RGBA':
        overlay_img = overlay_img.convert('RGBA')

    # 粘贴贴图，支持透明贴图
    base_img.paste(overlay_img, overlay_position, overlay_img)


def paste_overlay_resize(base_img, overlay_path, overlay_position, new_width, new_height, corner_radius):
    """
    在底图上粘贴贴图，并支持缩放贴图
    :param base_img: 底图（PIL.Image 对象）
    :param overlay_path: 贴图文件路径
    :param overlay_position: 贴图位置 (x, y)
    :param new_width: 贴图的新宽度
    :param new_height: 贴图的新高度
    """
    # 打开贴图
    overlay_img = Image.open(overlay_path)

    # 如果贴图不是 RGBA 模式，则转换为 RGBA 模式
    if overlay_img.mode != 'RGBA':
        overlay_img = overlay_img.convert('RGBA')

    # 缩放贴图到指定尺寸
    overlay_img = overlay_img.resize((new_width, new_height), Image.LANCZOS)

    # 应用圆角效果
    # overlay_img = round_corners(overlay_img, corner_radius)

    # 粘贴贴图，支持透明贴图
    base_img.paste(overlay_img, overlay_position, overlay_img)


def round_corners(image, radius):
    """
    将图片裁剪为圆角效果
    :param image: 输入的图片
    :param radius: 圆角半径
    :return: 圆角图片
    """
    # 创建一个蒙版
    mask = Image.new('L', image.size, 0)
    draw = ImageDraw.Draw(mask)

    # 绘制圆角矩形
    draw.rounded_rectangle([(0, 0), image.size], radius=radius, fill=255)

    # 应用蒙版
    rounded_image = ImageOps.fit(image, mask.size, centering=(0.5, 0.5))
    rounded_image.putalpha(mask)

    return rounded_image


def add_text_to_image(image, text_list, font_path):
    """
    在图片上添加多个文字块
    :param image: 图片对象
    :param text_list: 文字块列表，每个文字块包含文字内容、位置、字体大小、字体颜色等信息
    :param font_path: 字体文件路径
    :return: 添加文字后的图片
    """
    draw = ImageDraw.Draw(image)

    for text_info in text_list:
        text = text_info["text"]
        position = text_info["position"]
        font_size = text_info["font_size"]
        font_color = text_info["font_color"]
        font_weight = text_info.get("font_weight", "normal")
        letter_spacing = text_info.get("letter_spacing", 0)

        # 加载字体
        font = ImageFont.truetype(font_path, font_size)

        # 设置字体样式
        if font_weight == "bold":
            font = ImageFont.truetype(font_path, font_size, encoding="utf-8")

        # 添加文字
        draw.text(position, text, font=font, fill=font_color, spacing=letter_spacing)

    return image


def create_detail_image(skin_group):
    input_image = f"{script_dir}/tpl/底.png"

    # 皮肤图的x坐标# 29 422
    skin_x = [75, 377, 679, 981, 1283, 1585]  # 皮肤图的x坐标

    # 在生成详情图之前，确保所有图片文件存在
    print("开始检查详情图所需的图片文件...")
    from hpjy import hpjy_image_util

    # 批量检查并重新下载缺失的图片
    processed_skin_group = {}
    for group_name, skin_list in skin_group.items():
        if skin_list:
            print(f"检查分组 '{group_name}' 的图片文件，共 {len(skin_list)} 个物品")
            valid_skins = hpjy_image_util.batch_ensure_images_exist(skin_list)
            processed_skin_group[group_name] = valid_skins
            print(f"分组 '{group_name}' 图片检查完成，有效物品 {len(valid_skins)} 个")
        else:
            processed_skin_group[group_name] = []

    # 使用处理后的skin_group
    skin_group = processed_skin_group

    # 计算总高度
    frame_total_height = calculate_total_height(skin_group)
    print(f"皮肤所需高度: {frame_total_height} 像素")

    # 扩展底框-2
    resize_height = frame_total_height
    if resize_height > 0:
        print(f"扩展底框高度: {resize_height} 像素")

    total_height = resize_height
    # 扩展底图
    print(f"总高度: {total_height} 像素")
    extended_img = extend_image(input_image, total_height)

    # 半透明层
    layer = Image.new("RGBA", (1871, 280), (93, 93, 93, 60))  # RGBA模式，alpha=0（全透明）
    paste_overlay_image(extended_img, layer, (25, 118))

    # 概览部分
    paste_overlay(extended_img, f"{script_dir}/tpl/北慕-主播.png", (24, 0))

    paste_overlay(extended_img, f"{script_dir}/tpl/样式1.png", (456, 160))
    paste_overlay(extended_img, f"{script_dir}/tpl/样式1.png", (804, 160))
    paste_overlay(extended_img, f"{script_dir}/tpl/样式1.png", (1152, 160))
    paste_overlay(extended_img, f"{script_dir}/tpl/样式1.png", (1500, 160))

    paste_overlay(extended_img, f"{script_dir}/tpl/样式1.png", (456, 277))
    paste_overlay(extended_img, f"{script_dir}/tpl/样式1.png", (804, 277))
    paste_overlay(extended_img, f"{script_dir}/tpl/样式1.png", (1152, 277))
    paste_overlay(extended_img, f"{script_dir}/tpl/样式1.png", (1500, 277))

    # logo
    paste_overlay(extended_img, f"{script_dir}/tpl/logo位置.png", (813, 19))
    paste_overlay(extended_img, f"{script_dir}/tpl/logo位置(1).png", (832, total_height - 100))


    # 贴标题和皮肤图
    y_offset = 458  # 初始标题位置
    for group_name, skin_list in skin_group.items():
        if len(skin_list) == 0:
            continue
        # 贴标题
        # y_offset += 110
        paste_overlay(extended_img, f"{script_dir}/tpl/底图.png", (24, y_offset))
        # 贴标题文字
        # 每个字： 46x55
        #  (384 - 46*len(group_name))/2
        text_list = [
            {
                "text": group_name,
                # "position": ((384 - 46 * len(group_name)) / 2, y_offset + 25),
                # todo 文字居中
                "position": (85, y_offset + 7),
                "font_size": 36,
                "font_color": "#333333",
                "font_weight": "normal"
            }]
        font_path = f"{script_dir}/DouyinSansBold.otf"
        extended_img = add_text_to_image(extended_img, text_list, font_path)

        y_offset += 86

        # 贴皮肤图
        row_count = 0

        for skin_item in skin_list:
            if row_count >= 6:
                row_count = 0
                y_offset += 192 + 32  # 皮肤图行高


            if skin_item['quality'] >= 6:
                paste_overlay(extended_img, f'{script_dir}/tpl/高级粉色样式.png', (skin_x[row_count], y_offset))
            else:
                paste_overlay(extended_img, f'{script_dir}/tpl/浅粉色样式.png', (skin_x[row_count], y_offset))
            # 检查文件是否存在，如果不存在则跳过（理论上不应该发生，因为已经预先检查过）
            if os.path.exists(skin_item['path']):
                paste_overlay_resize(extended_img, skin_item['path'], (skin_x[row_count] + 60, y_offset + 4), 132,
                                     132,
                                     0)
            else:
                print(f"警告: 图片文件仍然不存在，跳过显示: {skin_item['path']}")
                # 可以在这里添加占位图逻辑
                # placeholder_path = f'{script_dir}/占位图.png'
                # if os.path.exists(placeholder_path):
                #     paste_overlay_resize(extended_img, placeholder_path, (skin_x[row_count] + 73, y_offset + 4), 248, 248, 0)
            # 蒙层和名字
            # paste_overlay(extended_img, f"{script_dir}/蒙层.png", (skin_x[row_count] + 4, y_offset + 160 + 4))
            if skin_item['newName']:
                name = skin_item['newName']
            else:
                name = skin_item['name']
            label_text = {
                "text": name,
                "position": (skin_x[row_count] + 12, y_offset + 132),
                "font_size": 26,
                "font_color": "#ffffff",
                "font_weight": "normal"
            }
            layer = Image.new("RGBA", (259, 56), (0, 0, 0, 0))  # RGBA模式，alpha=0（全透明）

            extended_img = add_text_to_layer_center(extended_img, layer,
                                                    (skin_x[row_count] + 4, y_offset + 132),
                                                    label_text, font_path)
            row_count += 1

            # 如果当前分类的皮肤图未填满一行，也需要增加高度
        if row_count > 0:
            # 补全剩余位置的默认图
            while row_count < 6:
                paste_overlay(extended_img, f'{script_dir}/tpl/Property_1=占位图-大.png', (skin_x[row_count], y_offset))
                row_count += 1

            y_offset += 192 + 32  # 皮肤高度+边距

    # 保存结果
    rgb_cover = extended_img.convert("RGB")
    # rgb_cover.save(output_path)
    # print(f"图像已扩展并保存至 {output_path}")

    # 将生成的图像保存到内存中的字节流
    img_byte_array = BytesIO()
    rgb_cover.save(img_byte_array, format='JPEG', quality=95)  # 指定格式和质量
    img_byte_array.seek(0)  # 重置指针到开头

    return img_byte_array.getvalue()


def add_text_to_layer_center(image, layer, layer_position, text_info, font_path):
    draw = ImageDraw.Draw(image)

    text = text_info["text"]
    font_size = text_info["font_size"]
    font_color = text_info["font_color"]
    font_weight = text_info.get("font_weight", "normal")
    letter_spacing = text_info.get("letter_spacing", 0)

    # 加载字体
    font = ImageFont.truetype(font_path, font_size)

    # 设置字体样式
    if font_weight == "bold":
        font = ImageFont.truetype(font_path, font_size, encoding="utf-8")

    # 获取文字的边界框
    bbox = font.getbbox(text)
    text_width = bbox[2] - bbox[0]  # 右边界 - 左边界
    text_height = bbox[3] - bbox[1]  # 下边界 - 上边界

    # 获取图层的宽度和高度
    image_width, image_height = layer.size

    # 计算文字的起始位置，使得文字居中
    x = layer_position[0] + (image_width - text_width) / 2
    y = layer_position[1] + (image_height - text_height) / 2

    # 添加文字
    draw.text((x, y), text, font=font, fill=font_color, spacing=letter_spacing)
    return image


if __name__ == "__main__":
    # 测试数据
    skin_group = {
        '角色神装': [
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 6},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
        ],
        '优质套装': [
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 6},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 6},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
        ],

        '传说皮肤': [
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
        ],
        '史诗皮肤': [
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
            {'path': 'image/410014.png', 'name': '套装-黑色伏地魔', 'newName': '套装-黑色伏地魔', 'quality': 5},
        ],
        # '国标': [
        #     {'image': 'hero/105.jpeg', 'name': '九霄神辉'},
        # ]
    }

    text_dict = {
        # '编号': '',
        # '区服': '',
        # '贵族等级': '',
        # '英雄数量': '',
        # '实名情况': '0',
        # '皮肤数量': '',
        # '国标数量': '',
        # '荣耀典藏': ''
    }

    data = create_detail_image(skin_group)
    with open('detail.jpg', 'wb') as f:
        f.write(data)
