import json
import os


def generate_user_equipment_json(equipment_library, user_equipment, output_file="user_equip.json"):
    """
    生成用户装备的 JSON 文件。如果装备库中缺失某些用户的装备，使用用户的数据来填充。
    如果文件已存在，会增量写入数据。

    Args:
        equipment_library (dict): 装备库数据。
        user_equipment (dict): 用户拥有的装备数据。
        output_file (str): 输出文件的路径。
    """
    # 如果文件已存在，先读取现有数据
    if os.path.exists(output_file):
        with open(output_file, "r", encoding="utf-8") as f:
            existing_data = json.load(f)
    else:
        existing_data = {"categories": []}

    # 创建一个新的 JSON 结构，用于存储用户拥有的装备
    new_equipment_library = {
        "categories": []
    }

    # 遍历装备库的每个类别
    for category in equipment_library["categories"]:
        new_category = {
            "dt_name": category["dt_name"],
            "sub_categories": []
        }

        # 遍历每个子类别
        for sub_category in category["sub_categories"]:
            new_sub_category = {
                "gd_id": sub_category["gd_id"],
                "data_name": sub_category["data_name"],
                "items": []
            }

            # 检查用户是否拥有该子类别中的装备
            for tab in user_equipment["data"]["items"]:
                if tab["tabName"] == sub_category["data_name"]:
                    for user_item in tab["items"]:
                        # 检查装备库中是否存在该装备（根据 data_name 判断）
                        found_in_library = False
                        for library_item in sub_category["items"]:
                            if library_item["data_name"] == user_item["itemName"]:
                                # 如果存在，使用装备库中的图片和数据
                                new_sub_category["items"].append({
                                    "data_name": library_item["data_name"],
                                    "data_img": library_item["data_img"],
                                    "class_name": library_item.get("class_name", "")
                                })
                                found_in_library = True
                                break

                        # 如果装备库中不存在，使用用户的装备数据
                        if not found_in_library:
                            new_sub_category["items"].append({
                                "data_name": user_item["itemName"],
                                "data_img": user_item["imageUrl"],
                                "class_name": ""  # todo 从哪个字段判断是pink
                            })

            # 将子类别添加到新类别中
            new_category["sub_categories"].append(new_sub_category)

        # 将类别添加到新的装备库中
        new_equipment_library["categories"].append(new_category)

    # 将新数据与现有数据合并
    merged_data = merge_data(existing_data, new_equipment_library)

    # 将合并后的 JSON 保存到文件
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(merged_data, f, ensure_ascii=False, indent=4)

    print(f"新的装备库 JSON 文件已生成并增量写入：{output_file}")


def merge_data(existing_data, new_data):
    """
    将现有数据与新数据合并，避免重复写入。

    Args:
        existing_data (dict): 现有的数据。
        new_data (dict): 新数据。

    Returns:
        dict: 合并后的数据。
    """
    # 遍历新数据的每个类别
    for new_category in new_data["categories"]:
        # 在现有数据中查找同名类别
        existing_category = next((c for c in existing_data["categories"] if c["dt_name"] == new_category["dt_name"]),
                                 None)

        if existing_category:
            # 类别存在，继续处理子类别
            for new_sub_category in new_category["sub_categories"]:
                existing_sub_category = next((sc for sc in existing_category["sub_categories"] if
                                              sc["data_name"] == new_sub_category["data_name"]), None)

                if existing_sub_category:
                    # 子类别存在，合并装备
                    existing_items = {item["data_name"] for item in existing_sub_category["items"]}
                    for item in new_sub_category["items"]:
                        if item["data_name"] not in existing_items:
                            existing_sub_category["items"].append(item)
                else:
                    # 子类别不存在，直接添加
                    existing_category["sub_categories"].append(new_sub_category)
        else:
            # 类别不存在，直接添加
            existing_data["categories"].append(new_category)

    return existing_data


# 使用封装好的方法
if __name__ == "__main__":
    # 从文件读取装备库数据
    with open("new_equip.json", "r", encoding="utf-8") as f:
        equipment_library = json.load(f)

    # 用户拥有的装备数据（这里可以替换成实际的数据）
    user_equipment = {
        'result': 0,
        'returnCode': 0,
        'returnMsg': '',
        'data': {
            'count': 3,
            'rareCnt': 3,
            'items': [
                {
                    'tabName': '套装',
                    'items': [
                        {
                            'itemId': 410596,
                            'time': 1738948607,
                            'itemName': '套装-战术大师',
                            'count': 1,
                            'quality': 5,
                            'imageUrl': 'https://imgcdn.gp.qq.com/images/itemicon/v15/410596.png'
                        }
                    ],
                    'tabId': 1
                },
                {
                    'tabName': '鞋子',
                    'items': [
                        {
                            'itemId': 405301,
                            'time': 1738948607,
                            'itemName': 'xxx',
                            'count': 1,
                            'quality': 5,
                            'imageUrl': 'https://imgcdn.gp.qq.com/images/itemicon/v15/405301.png'
                        },
                        {
                            'itemId': 405301,
                            'time': 1738948607,
                            'itemName': 'yyy',
                            'count': 1,
                            'quality': 5,
                            'imageUrl': 'https://imgcdn.gp.qq.com/images/itemicon/v15/405301.png'
                        }, {
                            'itemId': 405301,
                            'time': 1738948607,
                            'itemName': '战术大师战靴',
                            'count': 1,
                            'quality': 5,
                            'imageUrl': 'https://imgcdn.gp.qq.com/images/itemicon/v15/405301.png'
                        }
                    ],
                    'tabId': 7
                }
            ]
        }
    }

    # 生成用户装备 JSON 文件，进行增量写入
    # todo productSn.json
    generate_user_equipment_json(equipment_library, user_equipment, "user_equip.json")
