import json
import re
import time
import urllib.parse

import requests
from PIL import Image


def Get_ptqrToken(qrsig):
    n, i, e = len(qrsig), 0, 0
    while n > i:
        e += (e << 5) + ord(qrsig[i])
        i += 1
    return 2147483647 & e


def Get_QRcode():
    # 获取 腾讯网 二维码
    url = 'https://ssl.ptlogin2.qq.com/ptqrshow?appid=716027609&e=2&l=M&s=3&d=72&v=4&t=0.8692955245720428&daid=381&pt_3rd_aid=1105412664'

    try:
        r = requests.get(url)
        qrsig = requests.utils.dict_from_cookiejar(r.cookies).get('qrsig')

        with open(r'QQ.png', 'wb') as f:
            f.write(r.content)
        im = Image.open(r'QQ.png')
        im = im.resize((350, 350))
        im.show()
        print(time.strftime('%H:%M:%S'), ' 登录二维码获取成功')
        return r
    except Exception as e:
        print(time.strftime('%H:%M:%S') + " 获取二维码报错" + str(e))
        print('第' + str(e.__traceback__.tb_lineno) + '行文件报错')


def QQ_login():
    r = Get_QRcode()
    qrsig = r.cookies.get('qrsig')
    ptqrtoken = Get_ptqrToken(qrsig)
    print('ptqrtoken: ', ptqrtoken)

    while True:
        params = {
            "ptqrtoken": ptqrtoken,
            "u1": "http://connect.qq.com",
            "from_ui": "1",
            "daid": "381",
            "aid": "716027609",
            "pt_3rd_aid": "1105412664",
            "pt_openlogin_data": "refer_cgi%3Dm_authorize%26response_type%3Dtoken%26client_id%3D1105412664%26redirect_uri%3Dauth%253A%252F%252Ftauth.qq.com%252F%26",
        }
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        url = f"https://xui.ptlogin2.qq.com/ssl/ptqrlogin?{query_string}"
        res_login = requests.get(
            url=url,
            headers={
                'Cookie':
                    '; '.join([
                        f'{key}={value}'
                        for key, value in r.cookies.get_dict().items()
                    ])
            })
        print(res_login.text)

        if "登录成功" in res_login.text:
            # 4、提取 openid appid access_token
            openid = re.search(r"openid=(\w+)", res_login.text).group(1)
            appid = re.search(r"appid=(\w+)", res_login.text).group(1)
            access_token = re.search(r"access_token=(\w+)",
                                     res_login.text).group(1)
            print(
                f"\nopenid = {openid}\nappid = {appid}\naccess_token = {access_token}"
            )
            return {
                'openid': openid,
                'appid': appid,
                'access_token': access_token
            }

        # url = 'https://ssl.ptlogin2.qq.com/ptqrlogin?u1=http://connect.qq.com&ptqrtoken=' + str(
        #     ptqrtoken) + '&ptredirect=0&h=1&t=1&g=1&from_ui=1&ptlang=2052&action=0-0-' + str(
        #     time.time()) + '&js_ver=20032614&js_type=1&login_sig=&pt_uistyle=40&aid=716027609&daid=381&pt_3rd_aid=1105412664'
        # cookies = {'qrsig': qrsig}
        #
        # try:
        #     r = requests.get(url, cookies=cookies)
        #     print(r.text)
        #     if '二维码未失效' in r.text:
        #         print(time.strftime('%H:%M:%S'), ' 二维码未失效')
        #     elif '二维码认证中' in r.text:
        #         print(time.strftime('%H:%M:%S'), ' 二维码认证中')
        #     elif '二维码已失效' in r.text:
        #         print(time.strftime('%H:%M:%S'), ' 二维码已失效')
        #         qrsig = Get_QRcode()
        #         ptqrtoken = Get_ptqrToken(qrsig)
        #     else:
        #         print(time.strftime('%H:%M:%S'), ' QQ登录成功')
        #
        #         # print(r.headers)
        #         # 提取登录结果中的关键信息
        #         result = parse_login_result(r.text)
        #         return result
        # except Exception as e:
        #     print(time.strftime('%H:%M:%S') + " 获取cookie报错" + str(e))
        time.sleep(2)


# def parse_login_result(login_text):
#     """
#     解析QQ登录结果，提取openid和access_token
#     """
#     # 提取QQ号
#     qq_number = re.findall(r'&uin=(.+?)&service', login_text)[0]
#
#     # 这里需要获取跳转URL中的openid和access_token
#     # 实际应用中可能需要模拟浏览器行为获取这些参数
#     # 以下是模拟数据，实际需要从登录流程中获取
#     return {
#         'qq_number': qq_number,
#         'openid': 'BC9F61196C1C53BB5E5FD6AABB357AC3',  # 需要从实际响应获取
#         'access_token': 'F1C8269983BBE6F8F1BD8BB9197F9C57',  # 需要从实际响应获取
#         'appid': '1105412664'
#     }


def gp_qq_login(login_info):
    """
    调用和平营地登录接口
    """
    headers = {
        'Host': 'formal.api.gp.qq.com',
        'User-Agent': 'okhttp/3.12.1'
    }

    data = {
        'loginType': 'qqconnect',
        'openId': login_info['openid'],
        'cGzip': '1',
        'cDevicePPI': '320',
        'cGameId': '20004',
        'cDeviceImei': '2025040821071826952:54:00:3d:67:5d743462',
        'autoLogin': '0',
        'cDeviceScreenHeight': '1280',
        'cDeviceCPU': 'arm64-v8a$x86_64',
        'appId': login_info['appid'],
        'cDeviceSP': '',
        'cSystemVersionCode': '32',
        'cWifiMac': '',
        'cWifiSsid': '',
        'cDeviceNet': 'WIFI',
        'cClientVersionCode': '2102091434',
        'cDeviceKey': '9561848f2632b84f',
        'cChannelId': '2',
        'cDeviceMem': '127991',
        'accessToken': login_info['access_token'],
        'cDeviceRom': 'emulator',
        'lastGetRemarkTime': '0',
        'cDeviceMac': '',
        'cCurrentGameId': '20004',
        'cRand': str(int(time.time() * 1000)),
        'lastLoginTime': '0',
        'appOpenid': login_info['openid'],
        'cDeviceScreenWidth': '720',
        'cDeviceModel': 'NTH-AN00',
        'pf': 'desktop_m_qq-10000144-android-2002-',
        'cClientVersionName': '3.30.2.1391',
        'cSystem': 'android',
        'cDeviceId': '2025040821071826952:54:00:3d:67:5d743462',
        'cSystemVersionName': '12',
        'cDeviceImsi': '2025040821071826952:54:00:3d:67:5d743462',
        'payToken': ''
    }

    try:
        response = requests.post(
            'https://formal.api.gp.qq.com/user/login',
            headers=headers,
            data=data,
            timeout=10
        )
        print('和平营地登录结果:', response.text)
        return response.json()
    except Exception as e:
        print('和平营地登录出错:', str(e))
        return None


def get_roles_info(openId, userId, token):
    url = "https://formal.api.gp.qq.com/game/chatroles"

    headers = {
        "Host": "formal.api.gp.qq.com",
        "User-Agent": "okhttp/3.12.1",
    }

    data = {
        "openId": openId,
        "cGzip": "1",
        "cDevicePPI": "320",
        "cGameId": "20004",
        "cDeviceImei": "2025040821071826952:54:00:3d:67:5d743462",
        "apiVersion": "6",
        "cDeviceScreenHeight": "1280",
        "cDeviceCPU": "arm64-v8a$x86_64",
        "cDeviceSP": "",
        "cSystemVersionCode": "32",
        "cWifiMac": "",
        "cWifiSsid": "",
        "cDeviceNet": "WIFI",
        "cClientVersionCode": "2102091434",
        "cDeviceKey": "9561848f2632b84f",
        "gameId": "20004",
        "cChannelId": "2",
        "cDeviceMem": "127991",
        "userId": userId,
        "cDeviceRom": "emulator",
        "cDeviceMac": "",
        "token": token,
        "cCurrentGameId": "20004",
        "cRand": "1747296562600",
        "cDeviceScreenWidth": "720",
        "cDeviceModel": "NTH-AN00",
        "pf": "desktop_m_qq-10000144-android-2002-",
        "cClientVersionName": "3.30.2.1391",
        "cSystem": "android",
        "cDeviceId": "2025040821071826952:54:00:3d:67:5d743462",
        "cSystemVersionName": "12",
        "cDeviceImsi": "2025040821071826952:54:00:3d:67:5d743462",
    }

    response = requests.post(url, headers=headers, data=data)
    print(response.json())  # 打印 JSON 响应
    return response.json()['data']['roles']


def get_depot_detail(open_id, user_id, role_id, token):
    """
    获取游戏仓库详情
    """
    headers = {
        'Host': 'formal.api.gp.qq.com',
        'User-Agent': 'okhttp/3.12.1'
    }

    data = {
        'friendRoleId': role_id,
        'openId': open_id,
        'cGzip': '1',
        'cDevicePPI': '320',
        'cGameId': '20004',
        'cDeviceImei': '2025040821071826952:54:00:3d:67:5d743462',
        'gameOpenId': 'EE661D63DB773717AD1666E7B822FD82',
        'cDeviceScreenHeight': '1280',
        'cDeviceCPU': 'arm64-v8a$x86_64',
        'cDeviceSP': '',
        'cSystemVersionCode': '32',
        'cWifiMac': '',
        'cWifiSsid': '',
        'cDeviceNet': 'WIFI',
        'cClientVersionCode': '2102091434',
        'cDeviceKey': '9561848f2632b84f',
        'cChannelId': '2',
        'cDeviceMem': '127991',
        'userId': user_id,
        'cDeviceRom': 'emulator',
        'cDeviceMac': '',
        'token': token,
        'cCurrentGameId': '20004',
        'cRand': str(int(time.time() * 1000)),
        'mainTabId': '1',
        'cDeviceScreenWidth': '720',
        'cDeviceModel': 'NTH-AN00',
        'pf': 'desktop_m_qq-10000144-android-2002-',
        'cClientVersionName': '3.30.2.1391',
        'cSystem': 'android',
        'cDeviceId': '2025040821071826952:54:00:3d:67:5d743462',
        'cSystemVersionName': '12',
        'cDeviceImsi': '2025040821071826952:54:00:3d:67:5d743462'
    }

    try:
        response = requests.post(
            'https://formal.api.gp.qq.com/game/getdepotdetail',
            headers=headers,
            data=data,
            timeout=10
        )
        print('仓库详情获取结果:', response.text)
        return response.json()
    except Exception as e:
        print('获取仓库详情出错:', str(e))
        return None


if __name__ == '__main__':
    # 1. 进行QQ登录
    qq_login_info = QQ_login()
    print('QQ登录信息:', qq_login_info)

    # 2. 使用获取的凭证进行和平营地登录
    if qq_login_info:
        gp_login_result = gp_qq_login(qq_login_info)
        if gp_login_result:
            print('和平营地登录成功!')
            print('和平用户信息:', json.dumps(gp_login_result, indent=2, ensure_ascii=False))


            open_id = qq_login_info['openid']
            user_id = gp_login_result['data']['userId']
            token = gp_login_result['data']['token']

            # 角色信息
            roles = get_roles_info(open_id,user_id, token)
            role_id = roles[0]['roleId']

            # 3. 获取游戏仓库详情
            depot_detail = get_depot_detail(open_id, user_id, role_id, token)
            if depot_detail:
                print('仓库详情获取成功!')
                print('仓库信息:', json.dumps(depot_detail, indent=2, ensure_ascii=False))
