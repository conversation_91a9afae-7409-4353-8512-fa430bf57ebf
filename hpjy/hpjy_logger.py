"""
和平精英录号系统专用日志配置模块
提供结构化的日志记录功能，支持文件轮转和性能优化
"""

import logging
import os
import sys
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from pathlib import Path
from datetime import datetime
import threading

# 获取当前脚本所在目录
script_dir = Path(__file__).resolve().parent

# 日志配置
LOG_DIR = script_dir / "logs"
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(name)s - [%(filename)s:%(lineno)d] - %(message)s"
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# 确保日志目录存在
LOG_DIR.mkdir(exist_ok=True)

# 线程锁，确保日志记录的线程安全
_log_lock = threading.Lock()


class HpjyLogger:
    """和平精英录号系统日志记录器"""
    
    def __init__(self, name="hpjy", level=logging.INFO):
        self.name = name
        self.level = level
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        with _log_lock:
            # 避免重复设置
            if self.logger and self.logger.handlers:
                return self.logger
            
            self.logger = logging.getLogger(self.name)
            self.logger.setLevel(self.level)
            
            # 清除现有的处理器
            for handler in self.logger.handlers[:]:
                self.logger.removeHandler(handler)
            
            # 创建格式化器
            formatter = logging.Formatter(LOG_FORMAT, DATE_FORMAT)
            
            # 1. 控制台处理器 - 只显示INFO及以上级别
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
            
            # 2. 主日志文件处理器 - 按大小轮转
            main_log_file = LOG_DIR / "hpjy_main.log"
            main_handler = RotatingFileHandler(
                main_log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            main_handler.setLevel(logging.DEBUG)
            main_handler.setFormatter(formatter)
            self.logger.addHandler(main_handler)
            
            # 3. 错误日志文件处理器 - 只记录ERROR及以上级别
            error_log_file = LOG_DIR / "hpjy_error.log"
            error_handler = RotatingFileHandler(
                error_log_file,
                maxBytes=5*1024*1024,  # 5MB
                backupCount=3,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(formatter)
            self.logger.addHandler(error_handler)
            
            # 4. 任务日志文件处理器 - 按天轮转
            task_log_file = LOG_DIR / "hpjy_task.log"
            task_handler = TimedRotatingFileHandler(
                task_log_file,
                when='midnight',
                interval=1,
                backupCount=30,  # 保留30天
                encoding='utf-8'
            )
            task_handler.setLevel(logging.INFO)
            task_handler.setFormatter(formatter)
            self.logger.addHandler(task_handler)
            
            # 防止日志传播到根记录器
            self.logger.propagate = False
    
    def get_logger(self):
        """获取日志记录器实例"""
        return self.logger
    
    def log_task_start(self, task_id, task_type="录号", **kwargs):
        """记录任务开始"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        self.logger.info(f"[TASK_START] 任务开始 - ID: {task_id} | 类型: {task_type} | {extra_info}")
    
    def log_task_end(self, task_id, status, duration=None, **kwargs):
        """记录任务结束"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        duration_str = f" | 耗时: {duration:.2f}秒" if duration else ""
        self.logger.info(f"[TASK_END] 任务结束 - ID: {task_id} | 状态: {status}{duration_str} | {extra_info}")
    
    def log_login_step(self, step, status, **kwargs):
        """记录登录步骤"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        self.logger.info(f"[LOGIN] {step} - 状态: {status} | {extra_info}")
    
    def log_data_collection(self, data_type, status, count=None, **kwargs):
        """记录数据收集"""
        count_str = f" | 数量: {count}" if count is not None else ""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        self.logger.info(f"[DATA] {data_type} - 状态: {status}{count_str} | {extra_info}")
    
    def log_api_request(self, method, url, status_code=None, duration=None, **kwargs):
        """记录API请求"""
        status_str = f" | 状态码: {status_code}" if status_code else ""
        duration_str = f" | 耗时: {duration:.3f}秒" if duration else ""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        self.logger.debug(f"[API] {method} {url}{status_str}{duration_str} | {extra_info}")
    
    def log_database_operation(self, operation, collection, status, **kwargs):
        """记录数据库操作"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        self.logger.debug(f"[DB] {operation} - 集合: {collection} | 状态: {status} | {extra_info}")
    
    def log_error_with_context(self, error, context=None, **kwargs):
        """记录带上下文的错误"""
        context_str = f" | 上下文: {context}" if context else ""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        self.logger.error(f"[ERROR] {str(error)}{context_str} | {extra_info}", exc_info=True)
    
    def log_performance(self, operation, duration, **kwargs):
        """记录性能指标"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        level = logging.WARNING if duration > 10 else logging.INFO  # 超过10秒的操作记录为WARNING
        self.logger.log(level, f"[PERF] {operation} - 耗时: {duration:.3f}秒 | {extra_info}")


# 全局日志记录器实例
_global_logger = None


def get_hpjy_logger(name="hpjy", level=logging.INFO):
    """获取和平精英录号系统日志记录器"""
    global _global_logger
    if _global_logger is None:
        _global_logger = HpjyLogger(name, level)
    return _global_logger


def setup_hpjy_logging(level=logging.INFO):
    """设置和平精英录号系统日志"""
    logger_instance = get_hpjy_logger(level=level)
    return logger_instance.get_logger()


# 便捷函数
def log_info(msg, **kwargs):
    """记录信息日志"""
    logger = get_hpjy_logger().get_logger()
    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()]) if kwargs else ""
    full_msg = f"{msg} | {extra_info}" if extra_info else msg
    logger.info(full_msg)


def log_error(msg, error=None, **kwargs):
    """记录错误日志"""
    logger = get_hpjy_logger().get_logger()
    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()]) if kwargs else ""
    full_msg = f"{msg} | {extra_info}" if extra_info else msg
    if error:
        logger.error(full_msg, exc_info=error)
    else:
        logger.error(full_msg)


def log_warning(msg, **kwargs):
    """记录警告日志"""
    logger = get_hpjy_logger().get_logger()
    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()]) if kwargs else ""
    full_msg = f"{msg} | {extra_info}" if extra_info else msg
    logger.warning(full_msg)


def log_debug(msg, **kwargs):
    """记录调试日志"""
    logger = get_hpjy_logger().get_logger()
    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()]) if kwargs else ""
    full_msg = f"{msg} | {extra_info}" if extra_info else msg
    logger.debug(full_msg)


# 上下文管理器，用于记录操作耗时
class LogTimer:
    """日志计时器上下文管理器"""
    
    def __init__(self, operation_name, logger_instance=None, **kwargs):
        self.operation_name = operation_name
        self.logger_instance = logger_instance or get_hpjy_logger()
        self.kwargs = kwargs
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger_instance.get_logger().debug(f"[TIMER] 开始 - {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        if exc_type is not None:
            self.logger_instance.log_error_with_context(
                exc_val, 
                context=f"{self.operation_name} 执行失败",
                duration=duration,
                **self.kwargs
            )
        else:
            self.logger_instance.log_performance(
                self.operation_name, 
                duration, 
                **self.kwargs
            )
