import math
import traceback
from io import BytesIO
from pathlib import Path

from PIL import ImageOps, ImageFont

# 获取当前脚本所在目录
script_dir = Path(__file__).resolve().parent


def get_png_images(folder_path):
    """
    获取文件夹下所有的 JPEG 图片路径
    :param folder_path: 文件夹路径
    :return: JPEG 图片路径列表
    """
    jpeg_images = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith(('.png', '.PNG')):
                jpeg_images.append(os.path.join(root, file))
    return jpeg_images


# def select_random_images(image_list, num_images=12):
#     """
#     从图片列表中随机选取指定数量的图片
#     :param image_list: 图片路径列表
#     :param num_images: 需要选取的图片数量
#     :return: 随机选取的图片路径列表
#     """
#     if len(image_list) <= num_images:
#         return image_list  # 如果图片数量不足，返回所有图片
#     # todo cover_images.append({
#     #                                 'url': image_path,
#     #                                 'name': name
#     #                             })
#     return random.sample(image_list, num_images)

import random
import os


def select_random_images(image_list, num_images=12):
    """
    从图片列表中随机选取指定数量的图片，并返回包含图片URL和名称的字典列表

    :param image_list: 图片路径列表
    :param num_images: 需要选取的图片数量，默认为12
    :return: 包含图片信息的字典列表，格式为 [{'url': 图片路径, 'name': 图片名称}, ...]
    """
    if len(image_list) <= num_images:
        # 如果图片数量不足，返回所有图片的信息
        return [{
            'url': image_path,
            'name': '套装-浪漫天命'
        } for image_path in image_list]

    # 随机选取指定数量的图片
    selected_images = random.sample(image_list, num_images)

    # 返回包含图片信息的字典列表
    return [{
        'url': image_path,
        'name': '套装-浪漫天命'
    } for image_path in selected_images]


def round_corners(image, radius):
    """
    将图片裁剪为圆角效果
    :param image: 输入的图片
    :param radius: 圆角半径
    :return: 圆角图片
    """
    # 创建一个蒙版
    mask = Image.new('L', image.size, 0)
    draw = ImageDraw.Draw(mask)

    # 绘制圆角矩形
    draw.rounded_rectangle([(0, 0), image.size], radius=radius, fill=255)

    # 应用蒙版
    rounded_image = ImageOps.fit(image, mask.size, centering=(0.5, 0.5))
    rounded_image.putalpha(mask)

    return rounded_image


# def round_corners(image, radius, scale_factor=2):
#     """
#     使用超采样抗锯齿将图片裁剪为圆角效果。
#
#     :param image: 输入的图片
#     :param radius: 圆角半径
#     :param scale_factor: 图像放大倍数（默认为 2）
#     :return: 圆角图片
#     """
#     # 计算放大后的尺寸
#     original_size = image.size
#     scaled_size = (original_size[0] * scale_factor, original_size[1] * scale_factor)
#
#     # 放大图像
#     scaled_image = image.resize(scaled_size, Image.LANCZOS)
#
#     # 创建一个放大后的蒙版
#     mask = Image.new('L', scaled_size, 0)
#     draw = ImageDraw.Draw(mask)
#
#     # 绘制圆角矩形（圆角半径按比例放大）
#     scaled_radius = radius * scale_factor
#     draw.rounded_rectangle([(0, 0), scaled_size], radius=scaled_radius, fill=255)
#
#     # 应用蒙版
#     rounded_image = ImageOps.fit(scaled_image, mask.size, centering=(0.5, 0.5))
#     rounded_image.putalpha(mask)
#
#     # 将放大后的图像缩小回原始尺寸
#     rounded_image = rounded_image.resize(original_size, Image.LANCZOS)
#
#     return rounded_image


def add_text_to_image(image, text_list, font_path):
    """
    在图片上添加多个文字块
    :param image: 图片对象
    :param text_list: 文字块列表，每个文字块包含文字内容、位置、字体大小、字体颜色等信息
    :param font_path: 字体文件路径
    :return: 添加文字后的图片
    """
    draw = ImageDraw.Draw(image)

    for text_info in text_list:
        text = text_info["text"]
        position = text_info["position"]
        font_size = text_info["font_size"]
        font_color = text_info["font_color"]
        font_weight = text_info.get("font_weight", "normal")
        letter_spacing = text_info.get("letter_spacing", 0)

        # 加载字体
        font = ImageFont.truetype(font_path, font_size)

        # 设置字体样式
        if font_weight == "bold":
            font = ImageFont.truetype(font_path, font_size, encoding="utf-8")

        # 添加文字
        draw.text(position, str(text), font=font, fill=font_color, spacing=letter_spacing)

    return image


def add_text_to_layer_center(image, layer, layer_position, text_info, font_path):
    draw = ImageDraw.Draw(image)

    text = text_info["text"]
    font_size = text_info["font_size"]
    font_color = text_info["font_color"]
    font_weight = text_info.get("font_weight", "normal")
    letter_spacing = text_info.get("letter_spacing", 0)

    # 加载字体
    font = ImageFont.truetype(font_path, font_size)

    # 设置字体样式
    if font_weight == "bold":
        font = ImageFont.truetype(font_path, font_size, encoding="utf-8")

    # 获取文字的边界框
    bbox = font.getbbox(text)
    text_width = bbox[2] - bbox[0]  # 右边界 - 左边界
    text_height = bbox[3] - bbox[1]  # 下边界 - 上边界

    # 获取图层的宽度和高度
    image_width, image_height = layer.size

    # 计算文字的起始位置，使得文字居中
    x = layer_position[0] + (image_width - text_width) / 2
    y = layer_position[1] + (image_height - text_height) / 2

    # 添加文字
    draw.text((x, y), text, font=font, fill=font_color, spacing=letter_spacing)
    return image


def add_number_to_image(cover, number_objs, number_folder="number"):
    """
    在底图上多个位置贴数字图片，并使数字居中显示（根据实际宽度动态计算位置）
    """
    number_height = 48

    for number_obj in number_objs:
        # 将数字转换为字符串
        number_str = str(number_obj[0])

        # 初始化总宽度
        total_width = 0

        # 遍历每个数字，获取实际宽度并计算总宽度
        digit_widths = []
        for digit in number_str:
            # 构造数字图片路径
            digit_path = f"{script_dir}/{number_folder}/{digit}.png"

            # 打开数字图片
            digit_image = Image.open(digit_path).convert("RGBA")

            # 获取数字图片的实际宽度
            digit_width = digit_image.size[0]
            digit_widths.append(digit_width)

            # 累加总宽度
            total_width += digit_width

        # 计算起始位置，使数字居中
        start_x = number_obj[1] - total_width // 2
        start_y = number_obj[2] - number_height // 2

        # 遍历每个数字，粘贴到指定位置
        current_x = start_x
        for i, digit in enumerate(number_str):
            # 构造数字图片路径
            digit_path = f"{script_dir}/{number_folder}/{digit}.png"

            # 打开数字图片
            digit_image = Image.open(digit_path).convert("RGBA")

            # 计算当前数字的贴图位置
            digit_position = (current_x, start_y)

            # 将数字图片粘贴到底图上
            cover.paste(digit_image, digit_position, digit_image)

            # 更新当前 x 坐标
            current_x += digit_widths[i]

    # 返回贴图后的图片
    return cover


def calculate_distance(point1, point2):
    """
    计算两点之间的欧几里得距离。

    :param point1: 第一个点 (x, y)
    :param point2: 第二个点 (x, y)
    :return: 距离
    """
    return math.sqrt((point1[0] - point2[0]) ** 2 + (point1[1] - point2[1]) ** 2)


def calculate_new_point(center, vertex, a, b, distance):
    """
    根据比例计算新点的位置。

    :param center: 中心点坐标 (x, y)
    :param vertex: 顶点坐标 (x, y)
    :param a: 比例中的分子
    :param b: 比例中的分母
    :param distance: 中心点到顶点的距离
    :return: 新点坐标 (x, y)
    """
    # 计算方向向量
    vector = (vertex[0] - center[0], vertex[1] - center[1])
    # 归一化向量
    unit_vector = (vector[0] / distance, vector[1] / distance)
    # 计算新点的距离
    new_distance = (a / b) * distance
    # 计算新点坐标
    new_point = (center[0] + unit_vector[0] * new_distance, center[1] + unit_vector[1] * new_distance)
    return new_point


from PIL import Image, ImageDraw, ImageColor


def plot_points(image, center, vertices, input_pairs, point_radius=4, point_color="#58E9FF", line_width=2,
                fill_color="#298AEC", fill_alpha=0.6, scale_factor=2):
    """
    使用超采样抗锯齿绘制线条。

    :param scale_factor: 图像放大倍数（默认为 2）
    """
    # 计算放大后的尺寸
    original_size = image.size
    scaled_size = (original_size[0] * scale_factor, original_size[1] * scale_factor)

    # 创建一个放大后的透明图层
    scaled_overlay = Image.new("RGBA", scaled_size, (0, 0, 0, 0))
    scaled_draw = ImageDraw.Draw(scaled_overlay)

    # 计算中心点到每个顶点的距离
    distances = [calculate_distance(center, vertex) for vertex in vertices]

    # 存储新点的坐标
    new_points = []

    # 根据比例计算新点的位置（按比例放大）
    for i, (a, b) in enumerate(input_pairs):
        vertex = vertices[i]
        distance = distances[i]
        new_point = calculate_new_point(center, vertex, a, b, distance)
        new_points.append((new_point[0] * scale_factor, new_point[1] * scale_factor))  # 按比例放大

    # 设置填充颜色的透明度
    fill_rgba = ImageColor.getrgb(fill_color) + (int(255 * fill_alpha),)

    # 先绘制填充图形（最底层）
    scaled_draw.polygon(new_points, fill=fill_rgba)

    # 绘制线条（按比例放大）
    scaled_line_width = line_width * scale_factor  # 线条宽度按比例放大
    for i in range(len(new_points)):
        start_point = new_points[i]
        end_point = new_points[(i + 1) % len(new_points)]  # 连接最后一个点回到第一个点
        scaled_draw.line([start_point, end_point], fill=point_color, width=scaled_line_width)

    # 最后绘制点（按比例放大）
    scaled_point_radius = point_radius * scale_factor  # 点半径按比例放大
    for point in new_points:
        scaled_draw.ellipse(
            (point[0] - scaled_point_radius, point[1] - scaled_point_radius,
             point[0] + scaled_point_radius, point[1] + scaled_point_radius),
            fill=point_color
        )

    # 将放大后的图像缩小回原始尺寸
    overlay = scaled_overlay.resize(original_size, Image.LANCZOS)

    # 将透明图层与底图合并
    image = Image.alpha_composite(image, overlay)
    return image


def paste_overlay(base_img, overlay_path, overlay_position):
    """
    在底图上粘贴贴图
    :param base_img: 底图（PIL.Image 对象）
    :param overlay_path: 贴图文件路径
    :param overlay_position: 贴图位置 (x, y)
    """
    overlay_img = Image.open(overlay_path)
    # 如果贴图不是 RGBA 模式，则转换为 RGBA 模式
    if overlay_img.mode != 'RGBA':
        overlay_img = overlay_img.convert('RGBA')

    # 粘贴贴图，支持透明贴图
    base_img.paste(overlay_img, overlay_position, overlay_img)


def composite_images(cover_path, small_images, positions, sizes, corner_radius, text_list,
                     font_path, script_dir):
    """
    优化版的图像合成函数，解决透明度问题并提升性能

    参数:
        cover_path: 底图路径
        small_images: 小图信息列表 [{'url': 路径, 'name': 名称}]
        positions: 小图位置列表 [(x,y)]
        sizes: 小图大小列表 [(w,h)]
        corner_radius: 圆角半径
        text_list: 文字配置列表
        font_path: 字体文件路径
        script_dir: 资源目录路径

    返回:
        合成图像的JPEG字节数据
    """
    # 1. 预加载常用资源
    purple_bg = Image.open(f"{script_dir}/purple.png").convert("RGBA")
    purple_bottom = Image.open(f"{script_dir}/purple_bottom.png").convert("RGBA")

    pink_bg = Image.open(f"{script_dir}/pink.png").convert("RGBA")
    pink_bottom = Image.open(f"{script_dir}/pink_bottom.png").convert("RGBA")

    # 2. 打开底图
    cover = Image.open(cover_path).convert("RGBA")

    # 3. 处理小图
    for i, (small_image_obj, position, size) in enumerate(zip(small_images, positions, sizes)):
        if small_image_obj is None:
            continue

        try:
            # 3.1 添加背景
            if small_image_obj['quality'] >= 6:
                cover.paste(pink_bg, position, pink_bg)
            else:
                cover.paste(purple_bg, position, purple_bg)

            # 3.2 处理小图
            with Image.open(small_image_obj['path']) as img:
                small_image = img.convert("RGBA").resize(size, Image.Resampling.LANCZOS)
                # 计算粘贴位置
                item_position = (position[0] + 45, position[1] + 11)
                cover.paste(small_image, item_position, small_image)

            # 3.3 添加底部蒙层和文字
            bottom_position = (position[0], position[1] + 110)
            if small_image_obj['quality'] >= 6:
                cover.paste(pink_bottom, (position[0], position[1] + 115), pink_bottom)
            else:
                cover.paste(purple_bottom, (position[0], position[1] + 115), purple_bottom)

            label_text = {
                "text": small_image_obj['newName'],
                "font_size": 14,
                "font_color": "#DEDEDE",
                "font_weight": "normal"
            }
            if small_image_obj['quality'] >= 6:
                add_text_to_layer_center(cover, pink_bottom, bottom_position, label_text, font_path)
            else:
                add_text_to_layer_center(cover, purple_bottom, bottom_position, label_text, font_path)

        except Exception as e:
            traceback.print_exc()
            print(f"处理图片 {small_image_obj.get('image', '未知')} 时出错: {str(e)}")
            continue

    # 4. 添加全局文字
    cover = add_text_to_image(cover, text_list, font_path)

    # 5. 转换为JPEG并返回字节数据
    img_byte_array = BytesIO()
    cover.convert("RGB").save(img_byte_array, format='JPEG', quality=95, optimize=True, progressive=True)
    return img_byte_array.getvalue()


def composite_images2(cover_path, small_images, positions, sizes, corner_radius, text_list,
                      font_path):
    """
    将小图合成到底图上，并应用圆角效果、文字、贴数字和绘制封闭图形
    :param cover_path: 底图路径
    :param small_images: 小图路径列表
    :param positions: 小图位置列表
    :param sizes: 小图大小列表
    :param corner_radius: 圆角半径
    :param text_list: 文字块列表
    :param font_path: 字体文件路径
    """
    # 打开底图
    cover = Image.open(cover_path).convert("RGBA")

    # 遍历每张小图
    for i, (small_image_obj, position, size) in enumerate(zip(small_images, positions, sizes)):
        if small_image_obj == None:
            # 占位
            continue

        cover.paste(Image.open(f"{script_dir}/purple.png"), position)

        # 打开小图
        small_image = Image.open(small_image_obj['url']).convert("RGBA")

        # 调整小图大小
        small_image = small_image.resize(size)

        # 应用圆角效果
        small_image = round_corners(small_image, corner_radius)

        # 将小图粘贴到底图上
        item_position = (position[0] + 45, position[1] + 11)
        cover.paste(small_image, item_position, small_image)

        # 蒙层
        paste_overlay(cover, f"{script_dir}/purple_bottom.png", (position[0], position[1] + 115))
        label_text = {
            "text": small_image_obj['name'],
            "font_size": 14,
            "font_color": "#DEDEDE",
            "font_weight": "normal"
        }
        layer = Image.open(f"{script_dir}/purple_bottom.png").convert("RGBA")
        cover = add_text_to_layer_center(cover, layer, (position[0], position[1] + 110), label_text, font_path)

    # 添加底部价格图片，保持原尺寸
    # bottom_price_path = f"{script_dir}/底部价格.png"  # 替换为你的底部价格图片路径
    # bottom_price_image = Image.open(bottom_price_path).convert("RGBA")
    # bottom_price_position = (551, 749)  # 指定位置
    # cover.paste(bottom_price_image, bottom_price_position, bottom_price_image)

    # 添加文字
    cover = add_text_to_image(cover, text_list, font_path)

    # logo
    # logo = f"{script_dir}/logo_1.png"
    # logo_image = Image.open(logo).convert("RGBA")
    # logo_position = (1085, 721)
    # cover.paste(logo_image, logo_position, logo_image)

    # 保存合成后的图片 jpg
    rgb_cover = cover.convert("RGB")
    # rgb_cover.save(output_path)
    # with open(output_path, 'wb') as f:
    #     rgb_cover.save(f)  # 直接保存到文件对象，确保写入完成
    # print("图片合成完成，已保存为: ", output_path)

    # 将生成的图像保存到内存中的字节流
    img_byte_array = BytesIO()
    rgb_cover.save(img_byte_array, format='JPEG', quality=95)  # 指定格式和质量
    img_byte_array.seek(0)  # 重置指针到开头

    return img_byte_array.getvalue()


def create_cover(small_images, text_dict):
    cover_tpl_path = script_dir / "tpl_cover.jpg"

    # 在生成封面图之前，确保所有图片文件存在
    print("开始检查封面图所需的图片文件...")
    from hpjy import hpjy_image_util

    # 批量检查并重新下载缺失的图片
    if small_images:
        print(f"检查封面图片文件，共 {len(small_images)} 个物品")
        small_images = hpjy_image_util.batch_ensure_images_exist(small_images)
        print(f"封面图片检查完成，有效物品 {len(small_images)} 个")

    # 每张小图的位置 (x, y)
    positions = [
        # 第1行 (y=170)
        (374, 170), (588, 170), (802, 170), (1017, 170), (1231, 170), (1446, 170), (1660, 170),

        # 第2行 (y=341)
        (374, 341), (588, 341), (802, 341), (1017, 341), (1231, 341), (1446, 341), (1660, 341),

        # 第3行 (y=512) 341+171=512
        (374, 512), (588, 512), (802, 512), (1017, 512), (1231, 512), (1446, 512), (1660, 512),

        # 第4行 (y=683) 512+171=683
        (374, 683), (588, 683), (802, 683), (1017, 683), (1231, 683), (1446, 683), (1660, 683),

        # 第5行 (y=854) 683+171=854
        (374, 854), (588, 854), (802, 854), (1017, 854), (1231, 854), (1446, 854), (1660, 854)
    ]

    # 每张小图的大小 (width, height)
    sizes = [(100, 100)] * 35

    # 圆角半径 (10px, 10px, 10px, 10px)
    # corner_radius = 10

    # 文字内容列表
    text_list = [
        {
            "text": text_dict["编号"],
            "position": (158, 214),
            "font_size": 30,
            "font_color": "#ffffff",
            "font_weight": "bold",
            "letter_spacing": -1
        },
        {
            "text": text_dict["区服"],
            "position": (158, 310),
            "font_size": 30,
            "font_color": "#ffffff",
            "font_weight": "bold",
            "letter_spacing": -1
        },
        {
            "text": text_dict["套装"],
            "position": (158, 407),
            "font_size": 30,
            "font_color": "#ffffff",
            "font_weight": "bold",
            "letter_spacing": -1
        },
        {
            "text": text_dict["枪皮"],
            "position": (158, 504),
            "font_size": 30,
            "font_color": "#ffffff",
            "font_weight": "bold",
            "letter_spacing": -1
        },
        {
            "text": text_dict["载具"],
            "position": (158, 600),
            "font_size": 30,
            "font_color": "#ffffff",
            "font_weight": "bold",
            "letter_spacing": -1
        },
        {
            "text": text_dict["粉枪皮"],
            "position": (178, 697),
            "font_size": 30,
            "font_color": "#ffffff",
            "font_weight": "bold",
            "letter_spacing": -1
        },
        {
            "text": text_dict["粉套装"],
            "position": (178, 794),
            "font_size": 30,
            "font_color": "#ffffff",
            "font_weight": "bold",
            "letter_spacing": -1
        }
    ]

    # 字体样式
    font_path = script_dir / "SourceHanSansSC-Bold-2.otf"  # 替换为你的字体文件路径
    corner_radius = 10

    # 调用合成方法
    image_data = composite_images(
        cover_tpl_path,
        small_images,
        positions,
        sizes,
        corner_radius,
        text_list,
        font_path,
        script_dir
    )
    return image_data


if __name__ == "__main__":
    jpeg_images = get_png_images('image')

    # 随机选取 35 张图片
    small_images = select_random_images(jpeg_images, 35)

    text_dict = {
        '编号': 'WZ8888',
        '区服': '安卓QQ',
        '套装': '100',
        '枪皮': '2',
        '载具': '2',
        '粉枪皮': '2',
        '粉套装': '2'
    }
    output_path = script_dir / 'output_cover.jpg'
    image = create_cover(small_images=small_images, text_dict=text_dict)
    with open(output_path, 'wb') as f:
        f.write(image)
