import logging
import re
import time
from io import BytesIO

import requests
from PIL import Image

from common import oss_util
from common.configs import current_config

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def get_qrcode():
    """
    获取QQ登录二维码
    """
    url = 'https://xui.ptlogin2.qq.com/ssl/ptqrshow?appid=716027609&e=2&l=M&s=3&d=72&v=4&t=0.8692955245720428&daid=381&pt_3rd_aid=1105412664'

    try:
        r = requests.get(url)
        im = Image.open(BytesIO(r.content))
        im = im.resize((222, 222))

        # 将 PIL Image 转换为字节数据
        img_byte_array = BytesIO()
        im.save(img_byte_array, format='PNG')  # 确保格式与 file_extension 一致
        img_bytes = img_byte_array.getvalue()

        print(time.strftime('%H:%M:%S'), ' 登录二维码获取成功')
        qrsig = r.cookies.get('qrsig')
        oss_file_name = oss_util.upload_one_img_to_oss_by_data(img_bytes, 'png')
        return {
            'id': qrsig,
            'url': current_config.image_server_url + oss_file_name
        }
    except Exception as e:
        print(time.strftime('%H:%M:%S') + " 获取二维码报错" + str(e))
        print('第' + str(e.__traceback__.tb_lineno) + '行文件报错')


def check_qrlogin_info(qrsig):
    # r = get_qrcode()
    # qrsig = r.cookies.get('qrsig')
    ptqrtoken = get_ptqrToken(qrsig)
    print('ptqrtoken: ', ptqrtoken)

    while True:
        params = {
            "ptqrtoken": ptqrtoken,
            "u1": "http://connect.qq.com",
            "from_ui": "1",
            "daid": "381",
            "aid": "716027609",
            "pt_3rd_aid": "1105412664",
            "pt_openlogin_data": "refer_cgi%3Dm_authorize%26response_type%3Dtoken%26client_id%3D1105412664%26redirect_uri%3Dauth%253A%252F%252Ftauth.qq.com%252F%26",
        }
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        url = f"https://xui.ptlogin2.qq.com/ssl/ptqrlogin?{query_string}"
        res_login = requests.get(
            url=url,
            cookies={
                'qrsig': qrsig
            })
        print(res_login.text)

        if '已失效' in res_login.text:
            print('二维码已失效')
            return {
                'status': 'timeout',
            }

        elif '本次登录已被拒绝' in res_login.text or "ptuiCB('68'" in res_login.text:
            print('用户拒绝登录')
            return {
                'status': 'rejected',
            }

        elif "登录成功" in res_login.text:
            # 提取 openid appid access_token
            openid = re.search(r"openid=(\w+)", res_login.text).group(1)
            appid = re.search(r"appid=(\w+)", res_login.text).group(1)
            access_token = re.search(r"access_token=(\w+)",
                                     res_login.text).group(1)
            print(
                f"\nopenid = {openid}\nappid = {appid}\naccess_token = {access_token}"
            )
            return {
                'status': 'success',
                'openid': openid,
                'appid': appid,
                'access_token': access_token
            }

        time.sleep(2)


def gp_login(login_info):
    """
    调用和平营地登录接口
    """
    headers = {
        'Host': 'formal.api.gp.qq.com',
        'User-Agent': 'okhttp/3.12.1'
    }

    data = {
        'loginType': 'qqconnect',
        'openId': login_info['openid'],
        'cGzip': '1',
        'cDevicePPI': '320',
        'cGameId': '20004',
        'cDeviceImei': '2025040821071826952:54:00:3d:67:5d743462',
        'autoLogin': '0',
        'cDeviceScreenHeight': '1280',
        'cDeviceCPU': 'arm64-v8a$x86_64',
        'appId': login_info['appid'],
        'cDeviceSP': '',
        'cSystemVersionCode': '32',
        'cWifiMac': '',
        'cWifiSsid': '',
        'cDeviceNet': 'WIFI',
        'cClientVersionCode': '2102091434',
        'cDeviceKey': '9561848f2632b84f',
        'cChannelId': '2',
        'cDeviceMem': '127991',
        'accessToken': login_info['access_token'],
        'cDeviceRom': 'emulator',
        'lastGetRemarkTime': '0',
        'cDeviceMac': '',
        'cCurrentGameId': '20004',
        'cRand': str(int(time.time() * 1000)),
        'lastLoginTime': '0',
        'appOpenid': login_info['openid'],
        'cDeviceScreenWidth': '720',
        'cDeviceModel': 'NTH-AN00',
        'pf': 'desktop_m_qq-10000144-android-2002-',
        'cClientVersionName': '3.30.2.1391',
        'cSystem': 'android',
        'cDeviceId': '2025040821071826952:54:00:3d:67:5d743462',
        'cSystemVersionName': '12',
        'cDeviceImsi': '2025040821071826952:54:00:3d:67:5d743462',
        'payToken': ''
    }

    try:
        response = requests.post(
            'https://formal.api.gp.qq.com/user/login',
            headers=headers,
            data=data,
            timeout=10
        )
        print('和平营地登录结果:', response.text)
        return response.json()
    except Exception as e:
        print('和平营地登录出错:', str(e))
        return None


def get_ptqrToken(qrsig):
    n, i, e = len(qrsig), 0, 0
    while n > i:
        e += (e << 5) + ord(qrsig[i])
        i += 1
    return 2147483647 & e


if __name__ == '__main__':
    # get_qq_qrcode()
    login_info = check_qrlogin_info()
    resp = gp_login(login_info)
    print(resp)
