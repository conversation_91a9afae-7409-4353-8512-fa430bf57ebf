import json
import logging
import time

import requests

from common.mongo_manager import MongoDBClient
from hpjy import hpjy_qq_login, hpjy_wx_login

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class HpjyClient(object):
    def __init__(self):
        pass

    def get_login_qrcode(self, type):
        if type == 'qq':
            return hpjy_qq_login.get_qrcode()
        elif type == 'weixin':
            return hpjy_wx_login.get_qrcode()

    def check_login_status(self, qrcode_id, type):
        if type == 'qq':
            return hpjy_qq_login.check_qrlogin_info(qrcode_id)
        elif type == 'weixin':
            return hpjy_wx_login.check_login_info(qrcode_id)

    def gp_login(self, login_type, login_info):
        """
        调用和平营地登录接口
        """
        if login_type == 'qq':
            return hpjy_qq_login.gp_login(login_info)
        elif login_type == 'weixin':
            return hpjy_wx_login.gp_login(login_info['code'])

    def get_roles_info(self, openId, userId, token):
        url = "https://formal.api.gp.qq.com/game/chatroles"

        headers = {
            "Host": "formal.api.gp.qq.com",
            "User-Agent": "okhttp/3.12.1",
        }

        data = {
            "openId": openId,
            "cGzip": "1",
            "cDevicePPI": "320",
            "cGameId": "20004",
            "cDeviceImei": "2025040821071826952:54:00:3d:67:5d743462",
            "apiVersion": "6",
            "cDeviceScreenHeight": "1280",
            "cDeviceCPU": "arm64-v8a$x86_64",
            "cDeviceSP": "",
            "cSystemVersionCode": "32",
            "cWifiMac": "",
            "cWifiSsid": "",
            "cDeviceNet": "WIFI",
            "cClientVersionCode": "2102091434",
            "cDeviceKey": "9561848f2632b84f",
            "gameId": "20004",
            "cChannelId": "2",
            "cDeviceMem": "127991",
            "userId": userId,
            "cDeviceRom": "emulator",
            "cDeviceMac": "",
            "token": token,
            "cCurrentGameId": "20004",
            "cRand": str(int(time.time() * 1000)),
            "cDeviceScreenWidth": "720",
            "cDeviceModel": "NTH-AN00",
            "pf": "desktop_m_qq-10000144-android-2002-",
            "cClientVersionName": "3.30.2.1391",
            "cSystem": "android",
            "cDeviceId": "2025040821071826952:54:00:3d:67:5d743462",
            "cSystemVersionName": "12",
            "cDeviceImsi": "2025040821071826952:54:00:3d:67:5d743462",
        }

        response = requests.post(url, headers=headers, data=data)
        # print(response.json())  # 打印 JSON 响应
        return response.json()['data']['roles']

    def get_assets(self, openId, user_id, role_id, token):
        url = "https://formal.api.gp.qq.com/game/getcharmandcoins"

        headers = {
            'Host': 'formal.api.gp.qq.com',
            'User-Agent': 'okhttp/3.12.1'
        }

        data = {
            "friendRoleId": role_id,
            "openId": openId,
            "cGzip": "1",
            "cDevicePPI": "320",
            "cGameId": "20004",
            "cDeviceImei": "2025040821071826952:54:00:3d:67:5d743462",
            "gameOpenId": "EE661D63DB773717AD1666E7B822FD82",
            "cDeviceScreenHeight": "1280",
            "cDeviceCPU": "arm64-v8a$x86_64",
            "cDeviceSP": "",
            "cSystemVersionCode": "32",
            "cWifiMac": "",
            "cWifiSsid": "",
            "cDeviceNet": "WIFI",
            "cClientVersionCode": "2102091434",
            "cDeviceKey": "9561848f2632b84f",
            "cChannelId": "2",
            "cDeviceMem": "127991",
            "userId": user_id,
            "cDeviceRom": "emulator",
            "cDeviceMac": "",
            "token": token,
            "cCurrentGameId": "20004",
            "cRand": str(int(time.time() * 1000)),
            "cDeviceScreenWidth": "720",
            "cDeviceModel": "NTH-AN00",
            "pf": "desktop_m_qq-10000144-android-2002-",
            "cClientVersionName": "3.30.2.1391",
            "cSystem": "android",
            "cDeviceId": "2025040821071826952:54:00:3d:67:5d743462",
            "cSystemVersionName": "12",
            "cDeviceImsi": "2025040821071826952:54:00:3d:67:5d743462"
        }

        response = requests.post(url, headers=headers, data=data)

        assets = {}
        charm = {}
        if response.status_code == 200:
            result = response.json()
            print(json.dumps(result, indent=2, ensure_ascii=False))
            if response.status_code == 200:
                result = response.json()

                # 检查返回状态
                if result.get("result") == 0 and result.get("returnCode") == 0:
                    data = result.get("data", {})

                    # 1. 基础货币
                    print("===== 货币与资源 =====")
                    print(f"幸运币: {data.get('luckyCoin', 0)}")
                    print(f"钻石: {data.get('diamond', 0)}")
                    print(f"金币: {data.get('gold', 0)}")
                    print(f"票券: {data.get('ticket', '0')}")

                    # 2. 魅力值系统
                    charms_data = data.get("charms", {})
                    print("\n===== 赛季魅力值 =====")
                    print(f"总魅力值: {charms_data.get('totalCharm', 0)}")
                    for charm in charms_data.get("charms", []):
                        print(f"赛季 {charm.get('seasonName')} (ID: {charm.get('seasonId')}):")
                        print(f"  魅力值: {charm.get('charm', 0)}, 等级: {charm.get('lvl', 0)}")

                    # 3. 仓库统计
                    print("\n===== 仓库统计 =====")
                    for item in data.get("depot", []):
                        print(f"{item.get('tabName')} (ID: {item.get('tabId')}):")
                        print(f"  总数: {item.get('count', 0)}, 稀有数: {item.get('rareCnt', 0)}")

                    assets = {
                        "货币和资源": {
                            "幸运币": data.get("luckyCoin", 0),
                            "钻石": data.get("diamond", 0),
                            "金币": data.get("gold", 0),
                            "票券": data.get("ticket", "0"),
                            "道具币": data.get("itemCoin", 0),
                            "铁币": data.get("ironCoin", 0)
                        },
                        "赛季魅力值": {
                            "总魅力值": data.get("charms", {}).get("totalCharm", 0),
                            "赛季列表": [
                                {
                                    "赛季名称": charm.get("seasonName"),
                                    "赛季ID": charm.get("seasonId"),
                                    "魅力值": charm.get("charm", 0),
                                    "等级": charm.get("lvl", 0)
                                }
                                for charm in data.get("charms", {}).get("charms", [])
                            ]
                        },
                        "仓库统计": [
                            {
                                "分类名称": item.get("tabName"),
                                "分类ID": item.get("tabId"),
                                "总数": item.get("count", 0),
                                "稀有数": item.get("rareCnt", 0)
                            }
                            for item in data.get("depot", [])
                        ]
                    }
                else:
                    print(f"接口返回错误: {result.get('returnMsg', '未知错误')}")
            else:
                print(f"HTTP请求失败: {response.status_code}")
        else:
            print(f"请求失败，状态码：{response.status_code}")

        return assets

    def get_honor_info(self, user_id, role_id, token):
        url = "https://formal.api.gp.qq.com/play/gethonorinfo"

        headers = {
            'Host': 'formal.api.gp.qq.com',
            'User-Agent': 'okhttp/3.12.1'
        }

        data = {
            'openId': 'o7KEU0oEFxa2bAw-50sYbeGbvmZM',
            'cGzip': '1',
            'cDevicePPI': '320',
            'cGameId': '20004',
            'cDeviceImei': '2025052023435410452:54:00:a6:85:dc008934',
            'gameOpenId': 'osewR0nnjWfoT9KdyK6KgHKfHUDw',
            'cDeviceScreenHeight': '1280',
            'cDeviceCPU': 'arm64-v8a$x86_64',
            'cDeviceSP': '',
            'cSystemVersionCode': '32',
            'cWifiMac': '',
            'cWifiSsid': '',
            'cDeviceNet': 'WIFI',
            'cClientVersionCode': '2102091434',
            'cDeviceKey': '9561848f2632b84f',
            'gameId': '20004',
            'cChannelId': '2',
            'cDeviceMem': '127991',
            'roleId': role_id,
            'userId': user_id,
            'cDeviceRom': 'emulator',
            'cDeviceMac': '',
            'token': token,
            'cCurrentGameId': '20004',
            'cRand': str(int(time.time() * 1000)),
            'cDeviceScreenWidth': '720',
            'cDeviceModel': 'NTH-AN00',
            'pf': 'desktop_m_qq-10000144-android-2002-',
            'cClientVersionName': '3.30.2.1391',
            'cSystem': 'android',
            'cDeviceId': '2025052023435410452:54:00:a6:85:dc008934',
            'cSystemVersionName': '12',
            'cDeviceImsi': '2025052023435410452:54:00:a6:85:dc008934'
        }

        try:
            response = requests.post(
                url=url,
                headers=headers,
                data=data,
                timeout=10
            )
            print('仓库详情获取结果:', response.text)
            return response.json()['data']
        except Exception as e:
            print('获取荣誉详情出错:', str(e))
            return None

    def get_depot_detail(self, open_id, user_id, role_id, token, category):
        """
            获取游戏仓库详情
            参数 category 接受英文类型，内部转换为对应的 mainTabId：
                'fashion'   -> 1 (时装)
                'weapon'    -> 4 (枪械)
                'gear'      -> 6 (装备)
                'vehicle'   -> 7 (载具)
                'tactical'  -> 5 (战备)
            """
        # 类别到 mainTabId 的映射
        category_map = {
            'fashion': 1,
            'weapon': 4,
            'gear': 6,
            'vehicle': 7,
            'tactical': 5
        }

        # 验证 category 参数是否有效
        if category not in category_map:
            raise ValueError(f"Invalid category: {category}. Expected one of: {list(category_map.keys())}")

        # 获取对应的 mainTabId
        main_tab_id = category_map[category]

        headers = {
            'Host': 'formal.api.gp.qq.com',
            'User-Agent': 'okhttp/3.12.1'
        }

        data = {
            'friendRoleId': role_id,
            'openId': open_id,
            'cGzip': '1',
            'cDevicePPI': '320',
            'cGameId': '20004',
            'cDeviceImei': '2025040821071826952:54:00:3d:67:5d743462',
            'gameOpenId': 'EE661D63DB773717AD1666E7B822FD82',
            'cDeviceScreenHeight': '1280',
            'cDeviceCPU': 'arm64-v8a$x86_64',
            'cDeviceSP': '',
            'cSystemVersionCode': '32',
            'cWifiMac': '',
            'cWifiSsid': '',
            'cDeviceNet': 'WIFI',
            'cClientVersionCode': '2102091434',
            'cDeviceKey': '9561848f2632b84f',
            'cChannelId': '2',
            'cDeviceMem': '127991',
            'userId': user_id,
            'cDeviceRom': 'emulator',
            'cDeviceMac': '',
            'token': token,
            'cCurrentGameId': '20004',
            'cRand': str(int(time.time() * 1000)),
            'mainTabId': main_tab_id,
            'cDeviceScreenWidth': '720',
            'cDeviceModel': 'NTH-AN00',
            'pf': 'desktop_m_qq-10000144-android-2002-',
            'cClientVersionName': '3.30.2.1391',
            'cSystem': 'android',
            'cDeviceId': '2025040821071826952:54:00:3d:67:5d743462',
            'cSystemVersionName': '12',
            'cDeviceImsi': '2025040821071826952:54:00:3d:67:5d743462'
        }

        try:
            response = requests.post(
                'https://formal.api.gp.qq.com/game/getdepotdetail',
                headers=headers,
                data=data,
                timeout=10
            )
            print('仓库详情获取结果:', response.text)
            return response.json()['data']
        except Exception as e:
            print('获取仓库详情出错:', str(e))
            return None

    def validate_token(self, openid, user_id, token):
        """
        验证token是否有效
        通过调用角色信息接口来验证token的有效性
        """
        try:
            roles = self.get_roles_info(openid, user_id, token)
            if roles and len(roles) > 0:
                return True
            return False
        except Exception as e:
            print(f"Token验证失败: {str(e)}")
            return False

    def save_to_json_file(self, data, file_path):
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"[INFO] Data successfully written to file: {file_path}")
        except Exception as error:
            print(f"[ERROR] Error saving data to file: {error}")


def get_data():
    mongo = MongoDBClient(
        uri="******************************************",
        db_name="luhao-prod"
    )

    hpjy_client = HpjyClient()

    # qq_login_info = hpjy_client.qq_login()
    # print('QQ登录信息:', qq_login_info)
    # openid = qq_login_info['openid']
    openid = '23E7ED01043EDE6CBC88AFDC4553C231'
    account_info = mongo.find_one('hpjy', {'openid': openid})
    # if not account_info:
    #     account_info = qq_login_info
    #     account_info['create_time'] = datetime.datetime.now()
    #     mongo.insert_one('hpjy', account_info)
    # else:
    #     account_info['update_time'] = datetime.datetime.now()
    #     mongo.update_one('hpjy', {'openid': openid}, account_info)

    # 时装
    depot_fashion = hpjy_client.get_depot_detail(account_info['openid'], account_info['user_id'],
                                                 account_info['role_id'], account_info['token'], 'fashion')
    if depot_fashion:
        print('时装详情获取成功：')
        print(json.dumps(depot_fashion, indent=2, ensure_ascii=False))
        account_info['depot_fashion'] = depot_fashion

    # 武器
    depot_weapon = hpjy_client.get_depot_detail(account_info['openid'], account_info['user_id'],
                                                account_info['role_id'], account_info['token'], 'weapon')
    if depot_weapon:
        print('武器详情获取成功：')
        print(json.dumps(depot_weapon, indent=2, ensure_ascii=False))
        account_info['depot_weapon'] = depot_weapon

    # 装备
    depot_gear = hpjy_client.get_depot_detail(account_info['openid'], account_info['user_id'],
                                              account_info['role_id'], account_info['token'], 'gear')
    if depot_gear:
        print('装备详情获取成功：')
        print(json.dumps(depot_gear, indent=2, ensure_ascii=False))
        account_info['depot_gear'] = depot_gear

    # 载具
    depot_vehicle = hpjy_client.get_depot_detail(account_info['openid'], account_info['user_id'],
                                                 account_info['role_id'], account_info['token'], 'vehicle')
    if depot_vehicle:
        print('载具详情获取成功：')
        print(json.dumps(depot_vehicle, indent=2, ensure_ascii=False))
        account_info['depot_vehicle'] = depot_vehicle

    # 战备
    depot_tactical = hpjy_client.get_depot_detail(account_info['openid'], account_info['user_id'],
                                                  account_info['role_id'], account_info['token'], 'tactical')
    if depot_tactical:
        print('战备详情获取成功：')
        print(json.dumps(depot_tactical, indent=2, ensure_ascii=False))
        account_info['depot_tactical'] = depot_tactical

    mongo.update_one('hpjy', {'openid': account_info['openid']}, account_info)

    return account_info

# if __name__ == "__main__":
#     get_data()
