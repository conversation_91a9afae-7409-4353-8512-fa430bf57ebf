import datetime
import json
import multiprocessing
import os
import queue
import threading
import time
import traceback
from contextlib import contextmanager
from io import BytesIO
from pathlib import Path

import requests
from PIL import Image

from common import common_utils
from common.blacklist_util import KejinBlacklistAPI
from common.configs import current_config
from common.crypto_util import AESCryptoUtil
from common.luhao_models import TaskEvent, EventStatus
from common.mongo_manager import MongoDBClient
from common.server_client import PortalClient
from hpjy import hpjy_image_util
from hpjy.hpjy_client import HpjyClient
from hpjy.hpjy_logger import get_hpjy_logger, LogTimer, log_info, log_error, log_warning, log_debug

#  环境变量，DEVICE_ID
device_id = os.environ.get('DEVICE_ID', '601')

# 获取当前脚本所在目录
script_dir = Path(__file__).resolve().parent

# 初始化日志记录器
hpjy_logger = get_hpjy_logger()
logger = hpjy_logger.get_logger()

# 排序优先级
priority_order = {
    "荣耀典藏": 2,
    "传说品质": 3,
    "史诗品质": 4,
    "限定": 5,
    "贵族限定": 1,
    "勇者品质": 6
}

mongo = MongoDBClient(
    uri="******************************************",
    db_name="luhao-prod"
)

hpjy_client = HpjyClient()


def get_skin_priority(skin):
    class_types = skin.get("classTypeName", [])
    # 默认优先级是无类别的最低优先级
    return min([priority_order.get(class_type, float('inf')) for class_type in class_types], default=float('inf'))


def save_to_json_file(data, file_path):
    """保存数据到JSON文件"""
    try:
        with LogTimer("保存JSON文件", hpjy_logger, file_path=file_path):
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            log_info("数据成功写入文件", file_path=file_path)
    except Exception as error:
        log_error("保存数据到文件失败", error=error, file_path=file_path)


aes_util = AESCryptoUtil("aEUAzI8eCEgdo02pfkLY+w==")


def to_int(value):
    try:
        return int(float(str(value)))  # 先转字符串，再转浮点数，最后转整数
    except (ValueError, TypeError):
        return 0


def extract_all_items(obj, key):
    """
    递归提取嵌套结构中所有最内层的 items，并保留完整的 tabName 层级路径
    :param obj: 原始数据（字典）
    :param key: 需要提取的键（例如 'items'）
    :return: 包含所有最内层 item 的列表，每个 item 会添加 tabHierarchy 字段记录层级路径
    """
    depot = obj.get(key)
    if not depot:
        return []
    items = depot.get('items', [])
    if not isinstance(items, list):
        return []

    result = []

    def _extract_nested_items(nested_items, parent_tabs=None):
        """递归辅助函数"""
        if parent_tabs is None:
            parent_tabs = []

        for entry in nested_items:
            if not isinstance(entry, dict):
                continue

            # 当前层级的 tabName 路径
            current_tabs = parent_tabs.copy()
            if 'tabName' in entry:
                current_tabs.append(entry['tabName'])

            # 判断是否是嵌套结构
            if 'items' in entry:
                sub_items = entry.get('items', [])
                if sub_items:  # 如果还有嵌套，继续递归
                    _extract_nested_items(sub_items, current_tabs)
                else:  # 空 items 列表的情况
                    if len(sub_items) == 0:  # 空 items跳过
                        continue
                    item_copy = entry.copy()
                    if current_tabs:
                        item_copy['tabHierarchy'] = ' > '.join(current_tabs)
                    result.append(item_copy)
            else:  # 没有 items 键，说明是真正的 item 数据

                item_copy = entry.copy()
                if current_tabs:
                    item_copy['tabHierarchy'] = ' > '.join(current_tabs)
                result.append(item_copy)

    _extract_nested_items(items)
    return result


def download_image(url, name, folder_path):
    """下载图片到指定文件夹"""
    img_path = os.path.join(folder_path, str(name))

    if os.path.exists(img_path):
        log_debug("图片已存在，跳过下载", url=url, path=img_path)
        return

    try:
        with LogTimer("下载图片", hpjy_logger, url=url, name=name):
            # 下载图片
            img_response = requests.get(url, timeout=10)  # 设置超时
            img_response.raise_for_status()  # 检查请求是否成功

            img = Image.open(BytesIO(img_response.content))
            img.save(img_path)

            # 上传oss
            # oss_util.upload_file_to_oss_with_name(img_path, 'mall/statics/wzry/wzskin/', img_name)

            log_info("成功下载图片", url=url, path=img_path)
    except Exception as e:
        log_error("下载图片失败", error=e, url=url, name=name, folder_path=folder_path)


def extract_brackets_simple(text):
    split_char = '('
    if '(' in text:
        split_char = '('
    elif '（' in text:
        split_char = '（'
    left_part = text.split(split_char)[0]  # "测试1"
    right_part = text.split(split_char)[1][:-1]  # "内容1" （去掉末尾的")"）
    return left_part, right_part


def is_name_match(a, b):
    """检查两个名称是否匹配（如果包含'-'则比较后半部分，否则直接比较全名）"""
    # 处理 a
    part_a = a.split('-')[1] if '-' in a else a
    # 处理 b
    part_b = b.split('-')[1] if '-' in b else b
    # 比较
    return part_a == part_b


# Worker类，用于执行录号任务，并通过队列接收指令
class Worker(multiprocessing.Process):
    def __init__(self, task, result_queue, capture_result_queue, params):
        super().__init__()
        self.result_queue = result_queue  # 用于返回结果。 任务进度通过result_queue返回master，master进入后续的处理
        # 清空result_queue
        while not self.result_queue.empty():
            self.result_queue.get()
        self.capture_result_queue = capture_result_queue  # 用于监听抓包结果的队列
        self.current_task = task  # 存储当前任务的信息
        self.params = params

        self.portal_client = PortalClient()
        self.daemon = True  # 守护进程
        # self.hpjy_client = HpjyClient()

    def fill_base_metadata(self, product_meta, account_info):
        role = account_info['role']
        if not role:
            return
        for item in product_meta:
            if item['type'] == 1:
                # todo 检查区服
                # todo 需用户填写的属性，跳过录号
                if item['name'] in []:
                    continue

    def fill_metadata(self, product_meta, depot_list, type_key, count_dict):
        for item in depot_list:
            # print(item)
            item_name = item['itemName']
            item_id = item['itemId']
            quality = item['quality']
            image_url = item['imageUrl']

            if item['tabHierarchy'] == '套装':
                count_dict['套装数量'] += 1
                if quality >= 6:
                    count_dict['粉装数量'] += 1
            elif type_key == 'depot_vehicle':
                count_dict['载具数量'] += 1
            elif type_key == 'depot_weapon':
                count_dict['枪皮数量'] += 1
                if quality >= 6:
                    count_dict['粉枪数量'] += 1

            item_db = mongo.find_one('hpjy_items', {'itemId': item_id})
            if not item_db:
                # todo 下载图片
                # 创建存放图片的文件夹
                folder_path = 'image'
                if not os.path.exists(folder_path):
                    os.makedirs(folder_path)

                download_image(image_url, str(item_id) + '.png', folder_path)
                item['path'] = f'{folder_path}/{item_id}.png'
                item['type'] = type_key
                mongo.insert_one('hpjy_items', item)

            for meta_item in product_meta:
                if meta_item['type'] == 2:
                    if meta_item['name'] == '角色神装':
                        if item['tabHierarchy'] == '套装':
                            input_list = meta_item['inputList'].split(',')
                            for target in input_list:
                                if is_name_match(item_name, target):
                                    if meta_item.get('values') is None:
                                        meta_item['values'] = []
                                    meta_item['values'].append(target)
                                    # 更新mongo
                                    mongo.update_one('hpjy_items', {'itemId': item_id}, {'newName': target})
                    if meta_item['name'] == '优质套装':
                        if item['tabHierarchy'] == '套装':
                            input_list = meta_item['inputList'].split(',')
                            for target in input_list:
                                if is_name_match(item_name, target):
                                    if meta_item.get('values') is None:
                                        meta_item['values'] = []
                                    meta_item['values'].append(target)
                                    # 更新mongo
                                    mongo.update_one('hpjy_items', {'itemId': item_id}, {'newName': target})
                    if meta_item['name'] == '热门套装':
                        if item['tabHierarchy'] == '套装':
                            input_list = meta_item['inputList'].split(',')
                            for target in input_list:
                                if is_name_match(item_name, target):
                                    if meta_item.get('values') is None:
                                        meta_item['values'] = []
                                    meta_item['values'].append(target)
                                    # 更新mongo
                                    mongo.update_one('hpjy_items', {'itemId': item_id}, {'newName': target})
                    if meta_item['name'] == '热门载具':
                        if type_key == 'depot_vehicle':
                            input_list = meta_item['inputList'].split(',')
                            for target in input_list:
                                if '(' in item_name or '（' in item_name:
                                    name_val1, name_val2 = extract_brackets_simple(item_name)
                                    if name_val1 in target and name_val2 in target:
                                        if meta_item.get('values') is None:
                                            meta_item['values'] = []
                                        meta_item['values'].append(target)
                                        # 更新mongo
                                        mongo.update_one('hpjy_items', {'itemId': item_id}, {'newName': target})
                                else:
                                    if item_name == target:
                                        if meta_item.get('values') is None:
                                            meta_item['values'] = []
                                        meta_item['values'].append(target)
                                        # 更新mongo
                                        mongo.update_one('hpjy_items', {'itemId': item_id}, {'newName': target})
                    if meta_item['name'] == '热门枪械':
                        if type_key == 'depot_weapon':
                            input_list = meta_item['inputList'].split(',')
                            for target in input_list:
                                if item_name in target or target in item_name:
                                    if meta_item.get('values') is None:
                                        meta_item['values'] = []
                                    meta_item['values'].append(target)
                                    # 更新mongo
                                    mongo.update_one('hpjy_items', {'itemId': item_id}, {'newName': target})
                    if meta_item['name'] == '飞行器':
                        if type_key == 'depot_gear':
                            input_list = meta_item['inputList'].split(',')
                            for target in input_list:
                                if item_name in target or target in item_name:
                                    if meta_item.get('values') is None:
                                        meta_item['values'] = []
                                    meta_item['values'].append(target)
                                    # 更新mongo
                                    mongo.update_one('hpjy_items', {'itemId': item_id}, {'newName': target})
                    if meta_item['name'] == '降落伞':
                        if type_key == 'depot_tactical':
                            input_list = meta_item['inputList'].split(',')
                            for target in input_list:
                                if item_name in target or target in item_name:
                                    if meta_item.get('values') is None:
                                        meta_item['values'] = []
                                    meta_item['values'].append(target)
                                    # 更新mongo
                                    mongo.update_one('hpjy_items', {'itemId': item_id}, {'newName': target})

                    # input_list = meta_item['inputList'].split(',')
                    # for target in input_list:
                    #     if item_name in target or target in item_name:
                    #         if meta_item.get('values') is None:
                    #             meta_item['values'] = []
                    #         meta_item['values'].append(target)
                    #         # 更新mongo
                    #         mongo.update_one('hpjy_items', {'itemId': item_id}, {'newName': target})
                    #
                    #     if '(' in item_name:
                    #         name_val1, name_val2 = extract_brackets_simple(item_name)
                    #         if name_val1 in target and name_val2 in target:
                    #             if meta_item.get('values') is None:
                    #                 meta_item['values'] = []
                    #             meta_item['values'].append(target)
                    #             # 更新mongo
                    #             mongo.update_one('hpjy_items', {'itemId': item_id}, {'newName': target})

    def run(self):
        task_id = self.current_task.get('id', 'unknown')
        task_type = "重新录号" if self.current_task.get('isRetry', False) else "首次录号"

        # 记录任务开始
        hpjy_logger.log_task_start(
            task_id,
            task_type=task_type,
            device_id=device_id,
            product_id=self.current_task.get('productId'),
            product_sn=self.current_task.get('productSn')
        )

        start_time = time.time()
        try:
            with LogTimer(f"执行{task_type}任务", hpjy_logger, task_id=task_id):
                result = self.execute_long_task(device_id=device_id)

            if result['status'] == EventStatus.SUCCESS:
                obj = result.get('data')
                log_info("录号任务执行成功，开始处理数据", task_id=task_id)

                product_meta = self.portal_client.get_product_category_meta(115)
                self.fill_base_metadata(product_meta, obj)

                count_dict = {
                    '粉装数量': 0,
                    '粉枪数量': 0,
                    '枪皮数量': 0,
                    '套装数量': 0,
                    '载具数量': 0
                }

                # 遍历所有 items
                log_info("开始处理游戏物品数据", task_id=task_id)
                for key in ['depot_fashion', 'depot_weapon', 'depot_gear', 'depot_vehicle', 'depot_tactical']:
                    item_list = extract_all_items(obj, key)
                    hpjy_logger.log_data_collection(f"处理{key}物品", "开始", count=len(item_list), task_id=task_id)
                    self.fill_metadata(product_meta, item_list, key, count_dict)
                    hpjy_logger.log_data_collection(f"处理{key}物品", "完成", count=len(item_list), task_id=task_id)

                honor = obj['honor']
                for meta_item in product_meta:
                    if meta_item['type'] == 1:
                        if meta_item['name'] == '粉装数量':
                            meta_item['values'] = [str(count_dict['粉装数量'])]
                        elif meta_item['name'] == '粉枪数量':
                            meta_item['values'] = [str(count_dict['粉枪数量'])]
                        elif meta_item['name'] == '套装数量':
                            meta_item['values'] = [str(count_dict['套装数量'])]
                        elif meta_item['name'] == '载具数量':
                            meta_item['values'] = [str(count_dict['载具数量'])]
                        elif meta_item['name'] == '热力值':
                            meta_item['values'] = [str(obj['assets']['赛季魅力值']['总魅力值'])]
                        elif meta_item['name'] == '历史最高段位':
                            if honor:
                                meta_item['values'] = [str(honor.get('highestDivName', ''))]
                        elif meta_item['name'] == '王牌印记':
                            if honor:
                                wp_count = honor.get('wangpaiLevelName')
                                if wp_count:
                                    meta_item['values'] = [f'王牌印记{wp_count}']

                product = self.current_task['product_info']

                print('生成详情图...')
                skin_group = {
                    '热门载具': [],
                    '角色神装': [],
                    '优质套装': [],
                    '热门套装': [],
                    '热门枪械': [],
                    '飞行器': [],
                    '降落伞': [],
                }

                # 前面录号数据metadata
                for item in product_meta:
                    if item['name'] == '热门载具':
                        values = item.get('values', [])
                        for item_name in values:
                            item_db = hpjy_image_util.get_item_by_new_name(item_name, 'depot_vehicle')
                            if item_db:
                                skin_group['热门载具'].append(item_db)
                            else:
                                log_warning("获取热门载具物品失败", task_id=task_id, item_name=item_name, category="热门载具")
                    if item['name'] == '角色神装':
                        values = item.get('values', [])
                        for item_name in values:
                            item_db = hpjy_image_util.get_item_by_new_name(item_name, 'depot_fashion')
                            if item_db:
                                skin_group['角色神装'].append(item_db)
                            else:
                                log_warning("获取角色神装物品失败", task_id=task_id, item_name=item_name, category="角色神装")
                    if item['name'] == '优质套装':
                        values = item.get('values', [])
                        for item_name in values:

                            item_db = hpjy_image_util.get_item_by_new_name(item_name, 'depot_fashion')
                            if item_db:
                                skin_group['优质套装'].append(item_db)
                            else:
                                log_warning("获取优质套装物品失败", task_id=task_id, item_name=item_name, category="优质套装")
                    if item['name'] == '热门套装':
                        values = item.get('values', [])
                        for item_name in values:
                            item_db = hpjy_image_util.get_item_by_new_name(item_name, 'depot_fashion')
                            if item_db:
                                skin_group['热门套装'].append(item_db)
                            else:
                                log_warning("获取热门套装物品失败", task_id=task_id, item_name=item_name, category="热门套装")
                    if item['name'] == '热门枪械':
                        values = item.get('values', [])
                        for item_name in values:
                            item_db = hpjy_image_util.get_item_by_new_name(item_name, 'depot_weapon')
                            if item_db:
                                skin_group['热门枪械'].append(item_db)
                            else:
                                log_warning("获取热门枪械物品失败", task_id=task_id, item_name=item_name, category="热门枪械")
                    elif item['name'] == '飞行器':
                        values = item.get('values', [])
                        for item_name in values:
                            item_db = hpjy_image_util.get_item_by_new_name(item_name, 'depot_vehicle')
                            if item_db:
                                skin_group['飞行器'].append(item_db)
                            else:
                                log_warning("获取飞行器物品失败", task_id=task_id, item_name=item_name, category="飞行器")
                    elif item['name'] == '降落伞':
                        values = item.get('values', [])
                        for item_name in values:
                            item_db = hpjy_image_util.get_item_by_new_name(item_name, 'depot_tactical')
                            if item_db:
                                skin_group['降落伞'].append(item_db)
                            else:
                                log_warning("获取降落伞物品失败", task_id=task_id, item_name=item_name, category="降落伞")

                log_info("开始生成详情图", task_id=task_id)
                with LogTimer("生成详情图", hpjy_logger, task_id=task_id):
                    detail = hpjy_image_util.create_hpjy_detail(skin_group)

                log_info("开始生成封面图", task_id=task_id)

                cover_images = []
                for key, value in skin_group.items():
                    if len(cover_images) >= 35:
                        break
                    cover_images += value

                text_dict = {
                    '编号': product.get('product').get('productSn'),
                    '区服': '',
                    '套装': count_dict['套装数量'],
                    '枪皮': count_dict['枪皮数量'],
                    '载具': count_dict['载具数量'],
                    '粉枪皮': count_dict['粉枪数量'],
                    '粉套装': count_dict['粉装数量'],
                }

                # 用户填写的数据
                attr_list = product.get('productAttributeValueList')
                for attr in attr_list:
                    if attr.get('productAttributeName') == '账号类型':
                        text_dict['区服'] = attr.get('value')
                    # elif attr.get('productAttributeName') == '实名类型':
                    #     text_dict['实名情况'] = attr.get('value')

                with LogTimer("生成封面图", hpjy_logger, task_id=task_id):
                    cover = hpjy_image_util.create_cover(cover_images, text_dict)

                log_info("开始同步账号信息到服务器", task_id=task_id, product_sn=self.current_task['productSn'])
                with LogTimer("同步账号信息", hpjy_logger, task_id=task_id):
                    self.portal_client.sync_product_info2(self.current_task['productSn'],
                                                          current_config.image_server_url + cover,
                                                          [current_config.image_server_url + cover,
                                                           current_config.image_server_url + detail], product_meta)

                    self.result_queue.put(
                        TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FINISHED))

                    # 记录任务成功完成
                    duration = time.time() - start_time
                    hpjy_logger.log_task_end(task_id, "SUCCESS", duration=duration)

            elif result['status'] == EventStatus.RETRYING:
                log_warning("任务需要重试", task_id=task_id, reason=result.get('msg'))
                self.result_queue.put(
                    TaskEvent(self.current_task['id'], stage=0, status=EventStatus.RETRYING, msg=result['msg']))

                # 记录任务重试
                duration = time.time() - start_time
                hpjy_logger.log_task_end(task_id, "RETRYING", duration=duration, reason=result.get('msg'))

            else:
                # FAILURE 同步失败原因到审核说明
                log_error("任务执行失败", task_id=task_id, reason=result.get('msg'))
                self.result_queue.put(
                    TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FAILURE, msg=result.get('msg', '任务执行失败')))

                # 记录任务失败
                duration = time.time() - start_time
                hpjy_logger.log_task_end(task_id, "FAILURE", duration=duration, reason=result.get('msg'))

        except Exception as e:
            # 记录异常详情
            duration = time.time() - start_time
            hpjy_logger.log_error_with_context(
                e,
                context=f"执行{task_type}任务时发生异常",
                task_id=task_id,
                duration=duration
            )

            self.result_queue.put(
                TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FAILURE))

            # 记录任务异常结束
            hpjy_logger.log_task_end(task_id, "EXCEPTION", duration=duration, error=str(e))
            self.current_task = None  # 任务完成后清除任务信息

    # 耗时任务的具体实现
    def execute_long_task(self, device_id=None):
        """
        执行录号任务，支持首次录号和重新录号
        """
        task_id = self.current_task.get('id', 'unknown')

        # 检查是否为重新录号任务
        if self.current_task.get('isRetry', False):
            log_info('开始执行重新录号任务', task_id=task_id, device_id=device_id)
            return self.execute_retry_task(device_id)

        # 原有的首次录号逻辑
        log_info('开始执行首次录号任务', task_id=task_id, device_id=device_id)
        return self.execute_first_time_task(device_id)

    def execute_first_time_task(self, device_id=None):
        """
        执行首次录号任务
        """
        task_id = self.current_task.get('id', 'unknown')
        log_info("开始首次录号任务初始化", task_id=task_id)

        product_category_id = self.current_task.get('productCategoryId')
        log_debug("获取产品类别元数据", task_id=task_id, category_id=product_category_id)
        metadata = self.portal_client.get_product_category_meta(product_category_id)
        self.current_task['product_meta'] = metadata

        log_debug("获取产品详情", task_id=task_id, product_id=self.current_task['productId'])
        product = self.portal_client.get_product_detail(self.current_task['productId'])
        self.current_task['product_info'] = product

        # 用户填写的属性
        attr_list = product.get('productAttributeValueList')

        account_type = None
        camp_id = None  # 营地ID，用于用户识别，不同于openId
        for attr in attr_list:
            if attr.get('productAttributeName') == '账号类型':
                account_type = attr.get('value')
            if attr.get('productAttributeName') == '营地ID':
                camp_id = attr.get('value')
                if not camp_id.isdigit():
                    camp_id = aes_util.decrypt(camp_id)

        self.current_task['camp_id'] = camp_id  # 保存营地ID用于显示

        # uid = self.current_task['uid']

        try:
            login_type = None
            if 'QQ' in account_type or 'qq' in account_type:
                login_type = 'qq'
            elif '微信' in account_type or 'weixin' in account_type:
                login_type = 'weixin'

            hpjy_logger.log_login_step("确定登录类型", "成功", task_id=task_id, login_type=login_type, account_type=account_type)

            log_info("开始获取登录二维码", task_id=task_id, login_type=login_type)
            qrcode = hpjy_client.get_login_qrcode(login_type)
            hpjy_logger.log_login_step("获取二维码", "成功", task_id=task_id, qrcode_id=qrcode.get('id'))

            qrcode_url = qrcode['url']
            propertyBag = {
                "state": "need_show_qrcode",
                "state_img": qrcode_url,
                "qrcode_type": login_type
            }
            task = {
                'id': self.current_task['id'],
                # 'qrcode': qrcode_url,
                'status': 'PENDING',
                'propertyBag': json.dumps(propertyBag),
                # 'taskStartTime': current_time
            }
            log_info("同步任务状态 - 等待扫码", task_id=task_id, status='PENDING')
            self.portal_client.sync_task_info(task)

            return self._complete_login_and_data_collection(login_type, qrcode)
        except Exception as e:
            hpjy_logger.log_error_with_context(
                e,
                context="首次录号任务执行失败",
                task_id=task_id,
                device_id=device_id
            )
            return {
                'status': EventStatus.FAILURE,
                'data': None
            }

    def _complete_login_and_data_collection(self, login_type, qrcode):
        """
        完成登录和数据收集的通用方法
        """
        task_id = self.current_task.get('id', 'unknown')
        try:
            # 等待扫码
            hpjy_logger.log_login_step("等待用户扫码", "进行中", task_id=task_id, qrcode_id=qrcode['id'])
            login_result = hpjy_client.check_login_status(qrcode['id'], login_type)
            hpjy_logger.log_login_step("检查登录状态", "完成", task_id=task_id, login_status=login_result.get('status'))
            if login_result['status'] == 'timeout':
                hpjy_logger.log_login_step("扫码登录", "超时", task_id=task_id)
                task = {
                    'id': self.current_task['id'],
                    'propertyBag': '{"state": "login_timeout"}',
                    'status': 'PENDING',
                    'msg': '扫码超时',
                }
                self.portal_client.sync_task_info(task)
                return {
                    'status': EventStatus.FAILURE,
                    'msg': '扫码超时'
                }
            elif login_result['status'] == 'rejected':
                hpjy_logger.log_login_step("扫码登录", "被拒绝", task_id=task_id)
                task = {
                    'id': self.current_task['id'],
                    'propertyBag': '{"state": "login_rejected"}',
                    'status': 'PENDING',
                    'msg': '用户拒绝登录',
                }
                self.portal_client.sync_task_info(task)
                return {
                    'status': EventStatus.FAILURE,
                    'msg': '用户拒绝登录'
                }
            elif login_result['status'] == 'success':
                hpjy_logger.log_login_step("扫码登录", "成功", task_id=task_id)

                log_info("开始和平营地登录", task_id=task_id, login_type=login_type)
                gp_login_result = hpjy_client.gp_login(login_type, login_result)

                if gp_login_result['returnCode'] != 0:
                    hpjy_logger.log_login_step("和平营地登录", "失败", task_id=task_id, return_code=gp_login_result['returnCode'])
                    return {
                        'status': EventStatus.FAILURE,
                        'msg': '和平营地登录失败'
                    }
                openid = gp_login_result['data']['uin']
                user_id = gp_login_result['data']['userId']
                token = gp_login_result['data']['token']

                hpjy_logger.log_login_step("和平营地登录", "成功", task_id=task_id, openid=openid, user_id=user_id)

                task = {
                    'id': self.current_task['id'],
                    'propertyBag': '{"state": "login_success"}',
                    'status': 'PENDING',
                    'msg': '扫码登录成功',
                }
                log_info("同步任务状态 - 登录成功", task_id=task_id, openid=openid)
                self.portal_client.sync_task_info(task)

                # 保存或更新认证信息
                log_info("开始保存认证信息", task_id=task_id, openid=openid)
                account_info = mongo.find_one('hpjy', {'openid': openid})
                if not account_info:
                    account_info = {'openid': openid, 'user_id': user_id, 'token': token,
                                    'create_time': datetime.datetime.now()}
                    mongo.insert_one('hpjy', account_info)
                    hpjy_logger.log_database_operation("插入", "hpjy", "成功", task_id=task_id, openid=openid)
                else:
                    account_info = {'openid': openid, 'user_id': user_id, 'token': token,
                                    'update_time': datetime.datetime.now()}
                    mongo.update_one('hpjy', {'openid': openid}, account_info)
                    hpjy_logger.log_database_operation("更新", "hpjy", "成功", task_id=task_id, openid=openid)

                # 同步openId到任务的uid字段
                self.current_task['uid'] = openid
                task_update = {
                    'id': self.current_task['id'],
                    'uid': openid,
                    'status': 'PENDING',
                    'msg': '已获取账号openId',
                }
                log_info("同步openId到任务", task_id=task_id, openid=openid)
                self.portal_client.sync_task_info(task_update)

                # 收集游戏数据
                return self._collect_game_data(openid, user_id, token, account_info)

        except Exception as e:
            hpjy_logger.log_error_with_context(
                e,
                context="完成登录和数据收集时发生异常",
                task_id=task_id,
                login_type=login_type
            )
            return {
                'status': EventStatus.FAILURE,
                'data': None
            }

    def _collect_game_data(self, openid, user_id, token, account_info):
        """
        收集游戏数据的通用方法
        """
        task_id = self.current_task.get('id', 'unknown')
        log_info("开始收集游戏数据", task_id=task_id, openid=openid)

        try:
            # 角色信息
            time.sleep(3)
            hpjy_logger.log_data_collection("角色信息", "开始获取", task_id=task_id, openid=openid)
            roles = hpjy_client.get_roles_info(openid, user_id, token)
            if roles:
                hpjy_logger.log_data_collection("角色信息", "获取成功", count=len(roles), task_id=task_id, openid=openid)
                role = roles[0]
                log_debug("角色详情", task_id=task_id, role_data=json.dumps(role, ensure_ascii=False))

                account_role = {
                    'roleId': role['roleId'],
                    'areaName': role['areaName'],
                    'serverName': role['serverName'],
                    'level': role['level'],
                    'roleJob': role['roleJob'],
                    'roleName': role['roleName'],
                }

                account_info['role'] = account_role
                role_id = roles[0]['roleId']
                account_info['role'] = account_role
                account_info['role_id'] = role_id

                log_info("角色信息处理完成", task_id=task_id, role_id=role_id, role_name=role['roleName'])

            # 查询游戏资产
            log_info("开始查询游戏资产", task_id=task_id, role_id=role_id)
            time.sleep(3)
            hpjy_logger.log_data_collection("游戏资产", "开始获取", task_id=task_id, role_id=role_id)
            assets = hpjy_client.get_assets(openid, user_id, role_id, token)
            account_info['assets'] = assets
            hpjy_logger.log_data_collection("游戏资产", "获取成功", task_id=task_id, role_id=role_id)

            # 荣誉信息
            time.sleep(3)
            hpjy_logger.log_data_collection("荣誉信息", "开始获取", task_id=task_id, role_id=role_id)
            honor = hpjy_client.get_honor_info(user_id, role_id, token)
            if honor:
                hpjy_logger.log_data_collection("荣誉信息", "获取成功", task_id=task_id, role_id=role_id)
                log_debug("荣誉详情", task_id=task_id, honor_data=json.dumps(honor, ensure_ascii=False))
                account_info['honor'] = honor
            else:
                hpjy_logger.log_data_collection("荣誉信息", "获取失败", task_id=task_id, role_id=role_id)

            # 时装
            time.sleep(3)
            hpjy_logger.log_data_collection("时装信息", "开始获取", task_id=task_id, role_id=role_id)
            depot_fashion = hpjy_client.get_depot_detail(openid, user_id, role_id, token, 'fashion')
            if depot_fashion:
                hpjy_logger.log_data_collection("时装信息", "获取成功", task_id=task_id, role_id=role_id)
                log_debug("时装详情", task_id=task_id, fashion_data=json.dumps(depot_fashion, ensure_ascii=False))
                account_info['depot_fashion'] = depot_fashion
            else:
                hpjy_logger.log_data_collection("时装信息", "获取失败", task_id=task_id, role_id=role_id)

            # 武器
            time.sleep(3)
            hpjy_logger.log_data_collection("武器信息", "开始获取", task_id=task_id, role_id=role_id)
            depot_weapon = hpjy_client.get_depot_detail(openid, user_id, role_id, token, 'weapon')
            if depot_weapon:
                hpjy_logger.log_data_collection("武器信息", "获取成功", task_id=task_id, role_id=role_id)
                log_debug("武器详情", task_id=task_id, weapon_data=json.dumps(depot_weapon, ensure_ascii=False))
                account_info['depot_weapon'] = depot_weapon
            else:
                hpjy_logger.log_data_collection("武器信息", "获取失败", task_id=task_id, role_id=role_id)

            # 装备
            time.sleep(3)
            hpjy_logger.log_data_collection("装备信息", "开始获取", task_id=task_id, role_id=role_id)
            depot_gear = hpjy_client.get_depot_detail(openid, user_id, role_id, token, 'gear')
            if depot_gear:
                hpjy_logger.log_data_collection("装备信息", "获取成功", task_id=task_id, role_id=role_id)
                log_debug("装备详情", task_id=task_id, gear_data=json.dumps(depot_gear, ensure_ascii=False))
                account_info['depot_gear'] = depot_gear
            else:
                hpjy_logger.log_data_collection("装备信息", "获取失败", task_id=task_id, role_id=role_id)

            # 载具
            time.sleep(3)
            hpjy_logger.log_data_collection("载具信息", "开始获取", task_id=task_id, role_id=role_id)
            depot_vehicle = hpjy_client.get_depot_detail(openid, user_id, role_id, token, 'vehicle')
            if depot_vehicle:
                hpjy_logger.log_data_collection("载具信息", "获取成功", task_id=task_id, role_id=role_id)
                log_debug("载具详情", task_id=task_id, vehicle_data=json.dumps(depot_vehicle, ensure_ascii=False))
                account_info['depot_vehicle'] = depot_vehicle
            else:
                hpjy_logger.log_data_collection("载具信息", "获取失败", task_id=task_id, role_id=role_id)

            # 战备
            time.sleep(3)
            hpjy_logger.log_data_collection("战备信息", "开始获取", task_id=task_id, role_id=role_id)
            depot_tactical = hpjy_client.get_depot_detail(openid, user_id, role_id, token, 'tactical')
            if depot_tactical:
                hpjy_logger.log_data_collection("战备信息", "获取成功", task_id=task_id, role_id=role_id)
                log_debug("战备详情", task_id=task_id, tactical_data=json.dumps(depot_tactical, ensure_ascii=False))
                account_info['depot_tactical'] = depot_tactical
            else:
                hpjy_logger.log_data_collection("战备信息", "获取失败", task_id=task_id, role_id=role_id)

            log_info("保存完整账号信息到数据库", task_id=task_id, openid=account_info['openid'])
            mongo.update_one('hpjy', {'openid': account_info['openid']}, account_info)
            hpjy_logger.log_database_operation("更新完整信息", "hpjy", "成功", task_id=task_id, openid=account_info['openid'])

            # 确保任务的uid字段包含openId
            if 'openid' in account_info:
                self.current_task['uid'] = account_info['openid']
                task_update = {
                    'id': self.current_task['id'],
                    'uid': account_info['openid'],
                    'status': 'PENDING',
                    'msg': '数据收集完成',
                }
                log_info("数据收集完成，同步任务状态", task_id=task_id, openid=account_info['openid'])
                self.portal_client.sync_task_info(task_update)

            log_info("游戏数据收集完成", task_id=task_id, openid=account_info['openid'])
            return {
                'status': EventStatus.SUCCESS,
                'data': account_info
            }
        except Exception as e:
            hpjy_logger.log_error_with_context(
                e,
                context="收集游戏数据时发生异常",
                task_id=task_id,
                openid=openid
            )
            return {
                'status': EventStatus.FAILURE,
                'data': None
            }

    def execute_retry_task(self, device_id=None):
        """
        执行重新录号任务
        """
        task_id = self.current_task.get('id', 'unknown')
        log_info('开始执行重新录号任务初始化', task_id=task_id, device_id=device_id)

        # 获取任务基本信息
        product_category_id = self.current_task.get('productCategoryId')
        log_debug("获取产品类别元数据", task_id=task_id, category_id=product_category_id)
        metadata = self.portal_client.get_product_category_meta(product_category_id)
        self.current_task['product_meta'] = metadata

        log_debug("获取产品详情", task_id=task_id, product_id=self.current_task['productId'])
        product = self.portal_client.get_product_detail(self.current_task['productId'])
        self.current_task['product_info'] = product

        # 获取用户填写的属性
        attr_list = product.get('productAttributeValueList')
        account_type = None

        for attr in attr_list:
            if attr.get('productAttributeName') == '账号类型':
                account_type = attr.get('value')

        # 对于重新录号任务，uid字段应该已经包含openId
        openid = self.current_task.get('uid')
        if not openid:
            log_error("重新录号任务缺少openId", task_id=task_id)
            return {
                'status': EventStatus.FAILURE,
                'msg': '缺少账号信息'
            }

        log_info("重新录号任务准备就绪", task_id=task_id, openid=openid, account_type=account_type)
        try:
            # 尝试使用存储的认证信息重新录号
            return self.retry_with_stored_credentials(openid, account_type)
        except Exception as e:
            hpjy_logger.log_error_with_context(
                e,
                context="重新录号任务执行失败",
                task_id=task_id,
                openid=openid,
                device_id=device_id
            )
            return {
                'status': EventStatus.FAILURE,
                'msg': f'重新录号失败: {str(e)}'
            }

    def retry_with_stored_credentials(self, openid, account_type):
        """
        使用存储的认证信息重新录号
        """
        task_id = self.current_task.get('id', 'unknown')
        log_info('尝试使用存储的认证信息重新录号', task_id=task_id, openid=openid)

        # 从MongoDB中根据openId精确查找账号信息
        hpjy_logger.log_database_operation("查询", "hpjy", "开始", task_id=task_id, openid=openid)
        account_info = mongo.find_one('hpjy', {'openid': openid})

        if not account_info:
            hpjy_logger.log_database_operation("查询", "hpjy", "未找到", task_id=task_id, openid=openid)
            log_warning('未找到存储的账号信息，需要重新登录', task_id=task_id, openid=openid)
            return self._handle_relogin(account_type)

        hpjy_logger.log_database_operation("查询", "hpjy", "成功", task_id=task_id, openid=openid)
        last_update = account_info.get("update_time", account_info.get("create_time", "未知"))
        log_info('找到存储的账号信息', task_id=task_id, openid=account_info.get("openid"), last_update=str(last_update))

        # 验证存储的token是否有效
        openid = account_info.get('openid')
        user_id = account_info.get('user_id')
        token = account_info.get('token')

        if not all([openid, user_id, token]):
            log_warning('存储的认证信息不完整，需要重新登录', task_id=task_id, openid=openid, has_user_id=bool(user_id), has_token=bool(token))
            return self._handle_relogin(account_type)

        # 验证token有效性
        log_info("开始验证存储的token有效性", task_id=task_id, openid=openid)
        if self.validate_stored_token(openid, user_id, token):
            log_info('存储的token有效，直接继续录号', task_id=task_id, openid=openid)
            # 更新任务状态
            task = {
                'id': self.current_task['id'],
                'propertyBag': '{"state": "using_stored_credentials"}',
                'status': 'PENDING',
                'msg': '使用存储的认证信息继续录号',
            }
            self.portal_client.sync_task_info(task)

            # 直接进行数据收集
            return self._collect_game_data(openid, user_id, token, account_info)
        else:
            log_warning('存储的token已失效，需要重新登录', task_id=task_id, openid=openid)
            return self._handle_relogin(account_type)

    def validate_stored_token(self, openid, user_id, token):
        """
        验证存储的token是否有效
        """
        task_id = self.current_task.get('id', 'unknown')
        try:
            log_debug('开始验证存储的token有效性', task_id=task_id, openid=openid)
            # 尝试调用一个简单的API来验证token
            with LogTimer("验证token", hpjy_logger, task_id=task_id, openid=openid):
                roles = hpjy_client.get_roles_info(openid, user_id, token)

            if roles and len(roles) > 0:
                log_info('Token验证成功', task_id=task_id, openid=openid, roles_count=len(roles))
                return True
            else:
                log_warning('Token验证失败：无法获取角色信息', task_id=task_id, openid=openid)
                return False
        except Exception as e:
            log_error('Token验证失败', error=e, task_id=task_id, openid=openid)
            return False

    def _handle_relogin(self, account_type):
        """
        处理重新登录流程（用于重新录号）
        """
        task_id = self.current_task.get('id', 'unknown')
        log_info('开始重新登录流程', task_id=task_id, account_type=account_type)

        try:
            login_type = None
            if 'QQ' in account_type or 'qq' in account_type:
                login_type = 'qq'
            elif '微信' in account_type or 'weixin' in account_type:
                login_type = 'weixin'

            if not login_type:
                log_error('无法确定登录类型', task_id=task_id, account_type=account_type)
                return {
                    'status': EventStatus.FAILURE,
                    'msg': '无法确定登录类型'
                }

            # 生成二维码
            hpjy_logger.log_login_step("重新生成二维码", "开始", task_id=task_id, login_type=login_type)
            qrcode = hpjy_client.get_login_qrcode(login_type)
            hpjy_logger.log_login_step("重新生成二维码", "成功", task_id=task_id, qrcode_id=qrcode.get('id'))

            qrcode_url = qrcode['url']
            propertyBag = {
                "state": "need_show_qrcode",
                "state_img": qrcode_url,
                "qrcode_type": login_type
            }
            task = {
                'id': self.current_task['id'],
                'status': 'PENDING',
                'propertyBag': json.dumps(propertyBag),
                'msg': '需要扫码登录'
            }
            log_info("同步任务状态 - 需要重新扫码", task_id=task_id, login_type=login_type)
            self.portal_client.sync_task_info(task)

            # 完成登录和数据收集
            return self._complete_login_and_data_collection(login_type, qrcode)

        except Exception as e:
            hpjy_logger.log_error_with_context(
                e,
                context="重新登录流程失败",
                task_id=task_id,
                account_type=account_type
            )
            return {
                'status': EventStatus.FAILURE,
                'msg': f'登录失败: {str(e)}'
            }

    def execute_task_stage(self, stage_function, stage, param=0):
        with self.time_logger(stage):  # 开始计时

            try:
                self.result_queue.put(TaskEvent(task_id=self.current_task['id'],
                                                stage=stage,
                                                status=EventStatus.IN_PROGRESS,
                                                snapshot=None,
                                                data=None,
                                                msg=None))

                stage_event = stage_function(param)
                self.result_queue.put(stage_event)
            except Exception as e:
                # self.log_queue.put(f"【worker】任务出错: {e}")
                self.handle_failure(self.current_task, stage)
                traceback.print_exc()
                return False  # 表示失败
        return True  # 表示成功

    def handle_failure(self, task, stage):
        step_login_event = TaskEvent(task['id'], stage=stage, status=EventStatus.FAILURE)
        self.result_queue.put(step_login_event)

    @contextmanager
    def time_logger(self, stage):
        start_time = time.time()  # 记录开始时间
        # self.log_queue.put(f"开始执行阶段: {stage}")
        try:
            yield  # 执行阶段操作
        finally:
            end_time = time.time()  # 记录结束时间
            duration = end_time - start_time  # 计算耗时
            # self.log_queue.put(f"阶段 {stage} 耗时: {duration:.2f} 秒")


# Master类，发送任务指令，管理Worker，并处理心跳
class Master:
    def __init__(self, status_queue, capture_result_queue, device_addr=None):
        self.task_queue = multiprocessing.Queue()
        self.result_queue = multiprocessing.Queue()
        self.capture_result_queue = capture_result_queue

        self.worker = None
        self.running = True
        self.check_flag = True
        self.portal_client = PortalClient()
        self.last_instruction = None
        self.test_queue = queue.Queue()  # 测试队列

        # self.gui_queue = queue.Queue()
        self.status_queue = status_queue
        self.device_id = device_id
        self.black_list_api = KejinBlacklistAPI()

    # 启动Worker
    def start_worker(self, task):
        if self.worker is None or not self.worker.is_alive():
            task_id = task.get('id', 'unknown')
            log_info("启动新任务Worker", task_id=task_id, device_id=self.device_id)
            # capture_result_queue = multiprocessing.Queue()
            while not self.capture_result_queue.empty():
                self.capture_result_queue.get()
            self.worker = Worker(task, self.result_queue, self.capture_result_queue, None)
            self.worker.start()
            log_info("Worker启动成功", task_id=task_id, device_id=self.device_id)

    def stop_worker_now(self, is_canceled=False):
        if self.worker is not None:
            log_info("开始停止Worker", device_id=self.device_id)
            try:
                task_id = self.worker.current_task['id']
                self.worker.terminate()  # 立即停止
                time.sleep(2)
                self.worker.kill()
                self.worker = None
                log_info("Worker停止成功", task_id=task_id, device_id=self.device_id)
                self.status_queue.put({'status': '任务已停止'})
                if is_canceled:
                    task = {
                        'id': task_id,
                        'status': 'CANCELLED',
                        'msg': '取消任务'
                    }
                    log_info("同步任务取消状态", task_id=task_id)
                    self.portal_client.sync_task_info(task)
                self.check_flag = True
            except Exception as e:
                hpjy_logger.log_error_with_context(
                    e,
                    context="停止Worker时发生异常",
                    device_id=self.device_id
                )

    def heartbeat(self):
        while self.running:
            log_debug("发送心跳", device_id=self.device_id)
            time.sleep(10)
            if self.worker is not None:
                self.send_heartbeat(device_status='IN_PROGRESS')
            else:
                log_debug("发送心跳开始", device_id=self.device_id, status='IDLE')
                self.send_heartbeat(device_status='IDLE')
                log_debug("发送心跳完成", device_id=self.device_id, status='IDLE')
                self.check_new_task()

    def check_worker_result(self):
        # 检查任务状态, 如果worker在执行中，才检查任务状态
        while self.running:
            try:
                if self.worker is None:
                    time.sleep(5)
                    continue
                result = self.result_queue.get_nowait()
                current_task = self.worker.current_task
                if result is None:
                    continue
                if result.status == EventStatus.FINISHED:
                    log_info('Master同步录号任务信息 - FINISHED', task_id=result.task_id, stage=result.stage)
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'COMPLETED',
                        'snapshot': result.snapshot,
                        'productMeta': common_utils.safe_json_dumps(result.data),
                        'msg': result.msg,
                        'propertyBag': '{"state": "login_success"}'
                    }
                    self.portal_client.sync_task_info(task)
                    # 自动审核
                    log_info('开始自动审核', task_id=result.task_id, operate_man=current_task.get('operateMan'))

                    verify_status = 1
                    verify_array = []
                    verify_detail = ''

                    is_black = False
                    if current_task['operateMan'] != 'API':
                        # 非号商,查询黑号
                        game_account = current_task['gameAccount']
                        username = current_task['operateMan']  # 用户名
                        user_id_number = None
                        real_name_info = self.portal_client.get_real_name_info(current_task['memberId'])
                        if real_name_info:
                            user_id_number = real_name_info['userIdNumber']
                        kk_black_result = self.portal_client.search_black_user(
                            {'phone': username, 'userIdNumber': user_id_number, 'gameAccount': game_account})
                        if kk_black_result:
                            log_warning('发现看看黑号用户', task_id=result.task_id, username=username, black_result=kk_black_result)
                            is_black = True
                            verify_status = 2
                            verify_array += kk_black_result

                        if not is_black:
                            # 看看查询正常，继续查询氪金联盟黑名单
                            black_result = self.black_list_api.search('game_account', current_task['gameAccount'])
                            if black_result:
                                type = f"游戏账号（{current_task['gameAccount']}）"
                                log_warning('发现氪金联盟黑号用户', task_id=result.task_id, username=username, query_type=type, black_result=black_result)
                                is_black = True
                                verify_array += black_result

                            if not is_black:
                                black_result = self.black_list_api.search('game_account', username)
                                if black_result:
                                    type = f'游戏账号({username})'
                                    log_warning('发现氪金联盟黑号用户', task_id=result.task_id, username=username, query_type=type, black_result=black_result)
                                    is_black = True
                                    verify_array += black_result

                            if not is_black:
                                black_result = self.black_list_api.search('mobile', username)
                                if black_result:
                                    type = f'手机号({username})'
                                    log_warning('发现氪金联盟黑号用户', task_id=result.task_id, username=username, query_type=type, black_result=black_result)
                                    is_black = True
                                    verify_array += black_result

                            if not is_black:
                                black_result = self.black_list_api.search('mobile', current_task['gameAccount'])
                                if black_result:
                                    type = f"手机号({current_task['gameAccount']})"
                                    print(f'黑名单用户: {username}, 查询类型：{type}, 黑号查询结果： {black_result}')
                                    is_black = True
                                    verify_array += black_result

                            if not is_black:
                                # 身份证号查询
                                if user_id_number:
                                    black_result = self.black_list_api.search('id_no', user_id_number)
                                    if black_result:
                                        type = f'身份证号{user_id_number}'
                                        print(f'黑名单用户: {username}, 查询类型：{type}, 黑号查询结果： {black_result}')
                                        is_black = True
                                        verify_array += black_result
                            # 如果是黑号，更新看看黑号库
                            if is_black:
                                verify_status = 2
                                self.portal_client.add_black_user(
                                    {
                                        'phone': username,
                                        'userIdName': real_name_info['userIdName'],
                                        'userIdNumber': user_id_number,
                                        'note': json.dumps(verify_array, ensure_ascii=False)
                                    })
                                verify_detail = '黑号'

                    self.portal_client.product_auto_verify(current_task['productId'], verify_status,
                                                           verify_detail)
                    self.status_queue.put({'status': result.status, 'stage': result.stage})
                    self.stop_worker_now()
                if result.status == EventStatus.SUCCESS:
                    log_info('Master同步录号任务信息 - SUCCESS', task_id=result.task_id, stage=result.stage)
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'IN_PROGRESS',
                        'snapshot': result.snapshot,
                        'productMeta': common_utils.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    product_sn = None
                    if current_task:
                        product_sn = current_task['productSn']
                    self.status_queue.put({'status': result.status, 'stage': result.stage,
                                           'product_sn': product_sn})
                if result.status == EventStatus.RETRYING:
                    log_info('Master同步录号任务信息 - RETRYING', task_id=result.task_id, stage=result.stage)
                    # task = {
                    #     'id': result.task_id,
                    #     'stage': result.stage,
                    #     'status': 'PENDING',
                    #     'snapshot': result.snapshot,
                    #     # 'productMeta': common_utils.safe_json_dumps(result.data),
                    #     'msg': result.msg
                    # }
                    # self.portal_client.sync_task_info(task)
                    product_sn = None
                    if current_task:
                        product_sn = current_task['productSn']
                    self.status_queue.put(
                        {'status': result.status, 'stage': result.stage, 'product_sn': current_task['productSn']})
                    self.stop_worker_now()
                if result.status == EventStatus.FAILURE:
                    log_error('Master同步录号任务信息 - FAILURE', task_id=result.task_id, stage=result.stage, error_msg=result.msg)
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'FAILED',
                        'snapshot': result.snapshot,
                        'productMeta': common_utils.safe_json_dumps(result.data),
                        'msg': result.msg,
                        'propertyBag': '{"state": "login_success"}'
                    }

                    self.portal_client.sync_task_info(task)
                    # 失败不审核
                    # time.sleep(1)
                    # self.portal_client.product_auto_verify(current_task['productId'], 2, result.msg)
                    self.status_queue.put(
                        {'status': result.status, 'stage': result.stage, 'product_sn': current_task['productSn']})
                    self.stop_worker_now()
                if result.status == EventStatus.MANUAL_REQUIRED:
                    log_info('Master同步录号任务信息 - MANUAL_REQUIRED', task_id=result.task_id, stage=result.stage)
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'MANUAL_REQUIRED',
                        'snapshot': result.snapshot,
                        'productMeta': common_utils.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    self.status_queue.put(
                        {'status': result.status, 'stage': result.stage, 'product_sn': current_task['product.Sn']})
                if result.status == EventStatus.IN_PROGRESS:
                    product_sn = None
                    if current_task:
                        product_sn = current_task['productSn']
                    self.status_queue.put({'status': result.status, 'stage': result.stage,
                                           'product_sn': product_sn})
            except queue.Empty:
                time.sleep(5)

    def check_new_task(self):
        if self.running and self.check_flag is True:
            try:
                print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} get_todo_task_from_kk start...")
                task = self.portal_client.get_todo_task_from_kk(device_id)
                print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} get_todo_task_from_kk finish.")

                if task is None:
                    return
                self.check_flag = False

                product_sn = task['productSn']
                task_status = task.get('status', 'PENDING')

                # 根据任务状态判断是新任务还是重新录号任务
                if task_status == 'RETRY':
                    print(f"【master】有重新录号任务: {product_sn} (状态: {task_status})")
                    task['isRetry'] = True
                else:
                    print(f"【master】有新任务: {product_sn} (状态: {task_status})")
                    task['isRetry'] = False

                self.start_worker(task)
                self.status_queue.put(
                    {'status': EventStatus.IN_PROGRESS, 'stage': None, 'product_sn': product_sn})
            except queue.Empty:
                pass
            except Exception as e:
                print(f"获取任务错误: {e}")
            finally:
                print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 等待新任务")

    def send_heartbeat(self, device_status='IDLE'):
        instruction = {
            'deviceId': self.device_id,
            'deviceStatus': device_status,
            'timestamp': int(round(time.time() * 1000))
        }
        return self.portal_client.send_heartbeat(instruction)

    # 关闭Master
    def shutdown(self):
        self.stop_worker_now()
        self.running = False
        self.send_heartbeat(device_status='OFFLINE')
        print("Master 关闭")


# 主程序
def main(status_queue=None, device_addr=None):
    status_queue = queue.Queue()
    capture_result_queue = multiprocessing.Queue()

    # 启动服务器的线程
    # server_thread = threading.Thread(target=run_server, args=(capture_result_queue,), daemon=True)
    # server_thread.start()

    master = Master(status_queue, capture_result_queue, device_addr)

    if master.device_id is None:
        print(f"【master】device_id is None, exit")
        return None

    # 启动心跳线程
    heartbeat_thread = threading.Thread(target=master.heartbeat, daemon=True)
    heartbeat_thread.start()

    # 检查worker执行结果
    check_worker_result_thread = threading.Thread(target=master.check_worker_result, daemon=True)
    check_worker_result_thread.start()

    heartbeat_thread.join()
    return master


if __name__ == "__main__":
    main()
