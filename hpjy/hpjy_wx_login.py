import base64
import hashlib
import random
import string
import time
from io import BytesIO

import requests
from PIL import Image

from common import oss_util
from common.configs import current_config

# --- 配置 ---
APPID = 'wxb7659468ecf2f4ce'


def get_gp_ticket():
    """
    获取 gp.qq.com 专用 ticket
    """
    url = 'https://formal.api.gp.qq.com/user/getwxsdkticket'
    headers = {
        'Content-Type': 'application/json;charset=utf-8',
        'Host': 'formal.api.gp.qq.com',
        'User-Agent': 'okhttp/3.12.1'
    }
    r = requests.post(url, headers=headers, data='')
    r.raise_for_status()
    return r.json()['data']


# 生成签名
def make_signature(appid, ticket, nonce, timestamp):
    params = {
        'appid': appid,
        'noncestr': nonce,
        'sdk_ticket': ticket,
        'timestamp': str(timestamp)
    }
    items = sorted(params.items(), key=lambda x: x[0])
    s = '&'.join(f"{k}={v}" for k, v in items)
    return hashlib.sha1(s.encode('utf-8')).hexdigest()


def request_qrcode(appid, nonce, timestamp, signature):
    """
    请求二维码
    """
    url = (
        'https://open.weixin.qq.com/connect/sdk/qrconnect'
        f'?appid={appid}'
        f'&noncestr={nonce}'
        f'&timestamp={timestamp}'
        '&scope=snsapi_userinfo'
        f'&signature={signature}'
    )
    headers = {'User-Agent': 'Apache-HttpClient/UNAVAILABLE (java 1.4)'}
    r = requests.get(url, headers=headers)
    r.raise_for_status()
    return r.json()


def get_qrcode():
    """
    获取二维码信息
    """
    ticket = get_gp_ticket()
    nonce = ''.join(random.choices(string.ascii_letters + string.digits, k=16))
    ts = int(time.time() * 1000)
    signature = make_signature(APPID, ticket, nonce, ts)
    qr = request_qrcode(APPID, nonce, ts, signature)

    uuid = qr['uuid']
    b64 = qr['qrcode']['qrcodebase64']

    # 1. 解码 Base64 并转换为 PIL Image
    img_data = base64.b64decode(b64)
    img = Image.open(BytesIO(img_data))

    # 2. 缩放图片（例如 300x300）
    img = img.resize((222, 222), Image.LANCZOS)  # 使用 LANCZOS 抗锯齿

    # 3. 转换为字节数据
    img_byte_array = BytesIO()
    img.save(img_byte_array, format='PNG')  # 确保格式与 file_extension 一致
    img_bytes = img_byte_array.getvalue()

    # 4. 上传到 OSS
    oss_file_name = oss_util.upload_one_img_to_oss_by_data(img_bytes, 'png')

    return {
        'id': uuid,
        'url': current_config.image_server_url + oss_file_name
    }


# 轮询扫码状态
def check_login_info(uuid):
    url = f'https://long.open.weixin.qq.com/connect/l/qrconnect?f=json&uuid={uuid}'
    headers = {'User-Agent': 'Apache-HttpClient/UNAVAILABLE (java 1.4)'}

    while True:
        r = requests.get(url, headers=headers)
        r.raise_for_status()

        resp = r.json()
        print(resp)
        if resp.get('wx_errcode') == 402:
            print("扫码超时")
            return {
                'status': 'timeout'
            }
        if resp.get('wx_errcode') == 403:
            print("用户拒绝授权")
            return {
                'status': 'rejected'
            }
        if resp.get('wx_errcode') == 405:
            code = resp['wx_code']
            print("扫码成功，code:", code)
            return {
                'status': 'success',
                'code': code
            }
        time.sleep(2)


def gp_login(code):
    url = 'https://formal.api.gp.qq.com/user/login'
    headers = {
        'Host': 'formal.api.gp.qq.com',
        'User-Agent': 'okhttp/3.12.1',
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    timestamp = int(time.time() * 1000)
    default_data = {
        'code': code,
        'loginType': 'wx',
        'payToken': '',
        'cGzip': 1,
        'cDevicePPI': 320,
        'cGameId': '20004',
        'cCurrentGameId': '20004',
        'cDeviceImei': f"{timestamp}:54:00:3d:67:5d743462",
        'cDeviceId': f"{timestamp}:54:00:3d:67:5d743462",
        'cDeviceImsi': f"{timestamp}:54:00:3d:67:5d743462",
        'autoLogin': 0,
        'cDeviceScreenWidth': 720,
        'cDeviceScreenHeight': 1280,
        'cDeviceCPU': 'arm64-v8a$x86_64',
        'cDeviceRom': 'emulator',
        'cDeviceKey': ''.join(random.choices(string.ascii_lowercase + string.digits, k=16)),
        'cSystem': 'android',
        'cSystemVersionCode': 32,
        'cSystemVersionName': '12',
        'cClientVersionName': '3.30.2.1391',
        'cClientVersionCode': 2102091434,
        'cChannelId': 2,
        'cDeviceNet': 'WIFI',
        'cDeviceMem': 127991,
        'cDeviceSP': '',
        'cWifiMac': '',
        'cWifiSsid': '',
        'cDeviceMac': '',
        'lastGetRemarkTime': 0,
        'lastLoginTime': int(time.time()),
        'cRand': timestamp
    }
    response = requests.post(url, headers=headers, data=default_data)
    response.raise_for_status()
    return response.json()


if __name__ == '__main__':
    # 1. 获取二维码
    qr = get_qrcode()
    uuid = qr['uuid']
    b64 = qr['qrcode']['qrcodebase64']

    # 2. 展示二维码
    img = Image.open(BytesIO(base64.b64decode(b64)))
    img.show()
    print(f"请使用微信扫码，UUID={uuid}")

    # 3. 轮询直到扫码成功
    while True:
        status = check_login_info(uuid)
        if status.get('wx_errcode') == 405:
            code = status['wx_code']
            print("扫码成功，code:", code)
            break
        time.sleep(2)

    # 4. 登录和平营地
    result = gp_login(code)
    print('登录结果:', result)
