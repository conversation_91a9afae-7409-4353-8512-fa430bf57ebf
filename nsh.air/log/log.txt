{"tag": "function", "depth": 1, "time": 1727281394.6552012, "data": {"name": "connect_device", "call_args": {"uri": "Android:///?cap_method=javacap&ori_method=adbori"}, "start_time": 1727281393.8059642, "ret": "<Android '127.0.0.1:50000'>", "end_time": 1727281394.6552012}}
{"tag": "function", "depth": 3, "time": 1727281414.6075096, "data": {"name": "_cv_match", "call_args": {"self": {"filename": "tpl1727258357328.png", "_filepath": "D:\\airtest_script\\nsh0910.air\\nsh.air\\tpl1727258357328.png", "threshold": 0.7, "target_pos": 5, "record_pos": [-0.001, 0.026], "resolution": [1280, 720], "rgb": false, "scale_max": 800, "scale_step": 0.005, "__class__": "Template"}, "screen": "array([[[21, 15, 10],\n        [21, 15, 10],\n        [22, 16, 11],\n        ...,\n        [39, 30, 21],\n        [39, 30, 21],\n        [39, 30, 21]],\n\n       [[23, 17, 12],\n        [23, 17, 12],\n        [23, 17, 12],\n        ...,\n        [43, 34, 25],\n        [43, 34, 25],\n        [43, 34, 25]],\n\n       [[25, 19, 14],\n        [25, 19, 14],\n        [26, 20, 15],\n        ...,\n        [46, 37, 28],\n        [46, 37, 28],\n        [46, 37, 28]],\n\n       ...,\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [26, 18, 18],\n        [26, 18, 18],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [27, 19, 19],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [28, 20, 20],\n        [28, 20, 20]]], dtype=uint8)"}, "start_time": 1727281413.2562718, "ret": {"result": [638, 391], "rectangle": [[337.72, 250.27999999999997], [337.72, 533.28], [939.72, 533.28], [939.72, 250.27999999999997]], "confidence": 0.9773665070533752, "time": 1.347249984741211}, "end_time": 1727281414.6075096}}
{"tag": "function", "depth": 3, "time": 1727281414.6623626, "data": {"name": "try_log_screen", "call_args": {"screen": "array([[[21, 15, 10],\n        [21, 15, 10],\n        [22, 16, 11],\n        ...,\n        [39, 30, 21],\n        [39, 30, 21],\n        [39, 30, 21]],\n\n       [[23, 17, 12],\n        [23, 17, 12],\n        [23, 17, 12],\n        ...,\n        [43, 34, 25],\n        [43, 34, 25],\n        [43, 34, 25]],\n\n       [[25, 19, 14],\n        [25, 19, 14],\n        [26, 20, 15],\n        ...,\n        [46, 37, 28],\n        [46, 37, 28],\n        [46, 37, 28]],\n\n       ...,\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [26, 18, 18],\n        [26, 18, 18],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [27, 19, 19],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [28, 20, 20],\n        [28, 20, 20]]], dtype=uint8)", "quality": null, "max_size": null}, "start_time": 1727281414.6085062, "ret": {"screen": "1727281414608.jpg", "resolution": [1280, 720]}, "end_time": 1727281414.6623626}}
{"tag": "function", "depth": 2, "time": 1727281414.66336, "data": {"name": "loop_find", "call_args": {"query": {"filename": "tpl1727258357328.png", "_filepath": "D:\\airtest_script\\nsh0910.air\\nsh.air\\tpl1727258357328.png", "threshold": 0.7, "target_pos": 5, "record_pos": [-0.001, 0.026], "resolution": [1280, 720], "rgb": false, "scale_max": 800, "scale_step": 0.005, "__class__": "Template"}, "timeout": 3, "threshold": null, "interval": 0.5, "intervalfunc": null}, "start_time": 1727281407.5948293, "ret": [638, 391], "end_time": 1727281414.66336}}
{"tag": "function", "depth": 1, "time": 1727281414.66336, "data": {"name": "exists", "call_args": {"v": {"filename": "tpl1727258357328.png", "_filepath": "D:\\airtest_script\\nsh0910.air\\nsh.air\\tpl1727258357328.png", "threshold": 0.7, "target_pos": 5, "record_pos": [-0.001, 0.026], "resolution": [1280, 720], "rgb": false, "scale_max": 800, "scale_step": 0.005, "__class__": "Template"}}, "start_time": 1727281407.5948293, "ret": [638, 391], "end_time": 1727281414.66336}}
{"tag": "function", "depth": 3, "time": 1727281415.4072092, "data": {"name": "_cv_match", "call_args": {"self": {"filename": "tpl1727259849115.png", "_filepath": "D:\\airtest_script\\nsh0910.air\\nsh.air\\tpl1727259849115.png", "threshold": 0.7, "target_pos": 5, "record_pos": [-0.152, 0.159], "resolution": [1280, 720], "rgb": false, "scale_max": 800, "scale_step": 0.005, "__class__": "Template"}, "screen": "array([[[21, 15, 10],\n        [21, 15, 10],\n        [22, 16, 11],\n        ...,\n        [39, 30, 21],\n        [39, 30, 21],\n        [39, 30, 21]],\n\n       [[23, 17, 12],\n        [23, 17, 12],\n        [23, 17, 12],\n        ...,\n        [43, 34, 25],\n        [43, 34, 25],\n        [43, 34, 25]],\n\n       [[25, 19, 14],\n        [25, 19, 14],\n        [26, 20, 15],\n        ...,\n        [46, 37, 28],\n        [46, 37, 28],\n        [46, 37, 28]],\n\n       ...,\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [26, 18, 18],\n        [26, 18, 18],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [27, 19, 19],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [28, 20, 20],\n        [28, 20, 20]]], dtype=uint8)"}, "start_time": 1727281415.0728438, "ret": null, "end_time": 1727281415.4072092}}
{"tag": "function", "depth": 3, "time": 1727281416.8244295, "data": {"name": "_cv_match", "call_args": {"self": {"filename": "tpl1727259849115.png", "_filepath": "D:\\airtest_script\\nsh0910.air\\nsh.air\\tpl1727259849115.png", "threshold": 0.7, "target_pos": 5, "record_pos": [-0.152, 0.159], "resolution": [1280, 720], "rgb": false, "scale_max": 800, "scale_step": 0.005, "__class__": "Template"}, "screen": "array([[[21, 15, 10],\n        [21, 15, 10],\n        [22, 16, 11],\n        ...,\n        [39, 30, 21],\n        [39, 30, 21],\n        [39, 30, 21]],\n\n       [[23, 17, 12],\n        [23, 17, 12],\n        [23, 17, 12],\n        ...,\n        [43, 34, 25],\n        [43, 34, 25],\n        [43, 34, 25]],\n\n       [[25, 19, 14],\n        [25, 19, 14],\n        [26, 20, 15],\n        ...,\n        [46, 37, 28],\n        [46, 37, 28],\n        [46, 37, 28]],\n\n       ...,\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [26, 18, 18],\n        [26, 18, 18],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [27, 19, 19],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [28, 20, 20],\n        [28, 20, 20]]], dtype=uint8)"}, "start_time": 1727281416.497094, "ret": null, "end_time": 1727281416.8244295}}
{"tag": "function", "depth": 3, "time": 1727281418.124388, "data": {"name": "_cv_match", "call_args": {"self": {"filename": "tpl1727259849115.png", "_filepath": "D:\\airtest_script\\nsh0910.air\\nsh.air\\tpl1727259849115.png", "threshold": 0.7, "target_pos": 5, "record_pos": [-0.152, 0.159], "resolution": [1280, 720], "rgb": false, "scale_max": 800, "scale_step": 0.005, "__class__": "Template"}, "screen": "array([[[21, 15, 10],\n        [21, 15, 10],\n        [22, 16, 11],\n        ...,\n        [39, 30, 21],\n        [39, 30, 21],\n        [39, 30, 21]],\n\n       [[23, 17, 12],\n        [23, 17, 12],\n        [23, 17, 12],\n        ...,\n        [43, 34, 25],\n        [43, 34, 25],\n        [43, 34, 25]],\n\n       [[25, 19, 14],\n        [25, 19, 14],\n        [26, 20, 15],\n        ...,\n        [46, 37, 28],\n        [46, 37, 28],\n        [46, 37, 28]],\n\n       ...,\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [26, 18, 18],\n        [26, 18, 18],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [27, 19, 19],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [28, 20, 20],\n        [28, 20, 20]]], dtype=uint8)"}, "start_time": 1727281417.780339, "ret": null, "end_time": 1727281418.124388}}
{"tag": "function", "depth": 3, "time": 1727281418.1383505, "data": {"name": "try_log_screen", "call_args": {"screen": "array([[[21, 15, 10],\n        [21, 15, 10],\n        [22, 16, 11],\n        ...,\n        [39, 30, 21],\n        [39, 30, 21],\n        [39, 30, 21]],\n\n       [[23, 17, 12],\n        [23, 17, 12],\n        [23, 17, 12],\n        ...,\n        [43, 34, 25],\n        [43, 34, 25],\n        [43, 34, 25]],\n\n       [[25, 19, 14],\n        [25, 19, 14],\n        [26, 20, 15],\n        ...,\n        [46, 37, 28],\n        [46, 37, 28],\n        [46, 37, 28]],\n\n       ...,\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [26, 18, 18],\n        [26, 18, 18],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [27, 19, 19],\n        [27, 19, 19]],\n\n       [[ 9,  7,  7],\n        [ 9,  7,  7],\n        [ 9,  7,  7],\n        ...,\n        [27, 19, 19],\n        [28, 20, 20],\n        [28, 20, 20]]], dtype=uint8)", "quality": null, "max_size": null}, "start_time": 1727281418.125386, "ret": {"screen": "1727281418125.jpg", "resolution": [1280, 720]}, "end_time": 1727281418.1383505}}
{"tag": "function", "depth": 2, "time": 1727281418.1393485, "data": {"name": "loop_find", "call_args": {"query": {"filename": "tpl1727259849115.png", "_filepath": "D:\\airtest_script\\nsh0910.air\\nsh.air\\tpl1727259849115.png", "threshold": 0.7, "target_pos": 5, "record_pos": [-0.152, 0.159], "resolution": [1280, 720], "rgb": false, "scale_max": 800, "scale_step": 0.005, "__class__": "Template"}, "timeout": 3, "threshold": null, "interval": 0.5, "intervalfunc": null}, "start_time": 1727281414.66336, "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\airtest\\utils\\logwraper.py\", line 131, in wrapper\n    res = f(*args, **kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\airtest\\core\\cv.py\", line 80, in loop_find\n    raise TargetNotFoundError('Picture %s not found in screen' % query)\nairtest.core.error.TargetNotFoundError: 'Picture Template(D:\\\\airtest_script\\\\nsh0910.air\\\\nsh.air\\\\tpl1727259849115.png) not found in screen'\n", "end_time": 1727281418.1393485}}
{"tag": "function", "depth": 1, "time": 1727281418.1403458, "data": {"name": "exists", "call_args": {"v": {"filename": "tpl1727259849115.png", "_filepath": "D:\\airtest_script\\nsh0910.air\\nsh.air\\tpl1727259849115.png", "threshold": 0.7, "target_pos": 5, "record_pos": [-0.152, 0.159], "resolution": [1280, 720], "rgb": false, "scale_max": 800, "scale_step": 0.005, "__class__": "Template"}}, "start_time": 1727281414.66336, "ret": false, "end_time": 1727281418.1403458}}
{"tag": "function", "depth": 2, "time": 1727281418.4927533, "data": {"name": "try_log_screen", "call_args": {"screen": null, "quality": null, "max_size": null}, "start_time": 1727281418.1403458, "ret": {"screen": "1727281418476.jpg", "resolution": [1280, 720]}, "end_time": 1727281418.4927533}}
{"tag": "function", "depth": 1, "time": 1727281419.8769078, "data": {"name": "touch", "call_args": {"v": [444, 565], "kwargs": {}, "times": 1}, "start_time": 1727281418.1403458, "ret": [444, 565], "end_time": 1727281419.8769078}}
{"tag": "function", "depth": 1, "time": 1727281420.8890214, "data": {"name": "sleep", "call_args": {"secs": 1}, "start_time": 1727281419.8769078, "ret": null, "end_time": 1727281420.8890214}}
{"tag": "function", "depth": 2, "time": 1727281421.3747904, "data": {"name": "try_log_screen", "call_args": {"screen": null, "quality": null, "max_size": null}, "start_time": 1727281420.8890214, "ret": {"screen": "1727281421356.jpg", "resolution": [1280, 720]}, "end_time": 1727281421.3747904}}
{"tag": "function", "depth": 1, "time": 1727281421.5924833, "data": {"name": "touch", "call_args": {"v": [783, 499], "kwargs": {}, "times": 1}, "start_time": 1727281420.8890214, "ret": [783, 499], "end_time": 1727281421.5924833}}
{"tag": "function", "depth": 1, "time": 1727281422.5977974, "data": {"name": "sleep", "call_args": {"secs": 1}, "start_time": 1727281421.5924833, "ret": null, "end_time": 1727281422.5977974}}
{"tag": "function", "depth": 2, "time": 1727281423.3251474, "data": {"name": "try_log_screen", "call_args": {"screen": null, "quality": null, "max_size": null}, "start_time": 1727281422.5987697, "ret": {"screen": "1727281423310.jpg", "resolution": [1280, 720]}, "end_time": 1727281423.3251474}}
{"tag": "function", "depth": 1, "time": 1727281423.5446541, "data": {"name": "touch", "call_args": {"v": [585, 243], "kwargs": {}, "times": 1}, "start_time": 1727281422.5987697, "ret": [585, 243], "end_time": 1727281423.5446541}}
{"tag": "function", "depth": 1, "time": 1727281424.546527, "data": {"name": "sleep", "call_args": {"secs": 1}, "start_time": 1727281423.5446541, "ret": null, "end_time": 1727281424.546527}}
{"tag": "function", "depth": 1, "time": 1727281426.6248338, "data": {"name": "text", "call_args": {"text": "<EMAIL>", "kwargs": {}, "enter": true}, "start_time": 1727281424.546527, "ret": null, "end_time": 1727281426.6248338}}
{"tag": "function", "depth": 2, "time": 1727281427.0472562, "data": {"name": "try_log_screen", "call_args": {"screen": null, "quality": null, "max_size": null}, "start_time": 1727281426.6248338, "ret": {"screen": "1727281427029.jpg", "resolution": [1280, 720]}, "end_time": 1727281427.0472562}}
{"tag": "function", "depth": 1, "time": 1727281427.2666998, "data": {"name": "touch", "call_args": {"v": [591, 346], "kwargs": {}, "times": 1}, "start_time": 1727281426.6248338, "ret": [591, 346], "end_time": 1727281427.2666998}}
{"tag": "function", "depth": 1, "time": 1727281428.2735348, "data": {"name": "sleep", "call_args": {"secs": 1}, "start_time": 1727281427.2666998, "ret": null, "end_time": 1727281428.2735348}}
{"tag": "function", "depth": 1, "time": 1727281429.0858712, "data": {"name": "text", "call_args": {"text": "Qwer1234", "kwargs": {}, "enter": true}, "start_time": 1727281428.2735348, "ret": null, "end_time": 1727281429.0858712}}
{"tag": "function", "depth": 2, "time": 1727281429.4090288, "data": {"name": "try_log_screen", "call_args": {"screen": null, "quality": null, "max_size": null}, "start_time": 1727281429.0858712, "ret": {"screen": "1727281429395.jpg", "resolution": [1280, 720]}, "end_time": 1727281429.408031}}
{"tag": "function", "depth": 1, "time": 1727281429.6324537, "data": {"name": "touch", "call_args": {"v": [674, 435], "kwargs": {}, "times": 1}, "start_time": 1727281429.0858712, "ret": [674, 435], "end_time": 1727281429.6324537}}
{"tag": "function", "depth": 1, "time": 1727281430.6491494, "data": {"name": "sleep", "call_args": {"secs": 1}, "start_time": 1727281429.633451, "ret": null, "end_time": 1727281430.6491494}}
{"tag": "function", "depth": 1, "time": 1727281440.6552403, "data": {"name": "sleep", "call_args": {"secs": 10}, "start_time": 1727281430.6491494, "ret": null, "end_time": 1727281440.6552403}}
{"tag": "function", "depth": 3, "time": 1727281441.882271, "data": {"name": "_cv_match", "call_args": {"self": {"filename": "tpl1727259252787.png", "_filepath": "D:\\airtest_script\\nsh0910.air\\nsh.air\\tpl1727259252787.png", "threshold": 0.7, "target_pos": 5, "record_pos": [-0.002, 0.187], "resolution": [1280, 720], "rgb": false, "scale_max": 800, "scale_step": 0.005, "__class__": "Template"}, "screen": "array([[[ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        ...,\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24]],\n\n       [[ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        ...,\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24]],\n\n       [[ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        ...,\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24]],\n\n       ...,\n\n       [[255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255],\n        ...,\n        [255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255]],\n\n       [[255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255],\n        ...,\n        [255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255]],\n\n       [[255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255],\n        ...,\n        [255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255]]], dtype=uint8)"}, "start_time": 1727281441.075735, "ret": {"result": [637, 597], "rectangle": [[359.44000000000005, 547.36], [359.44000000000005, 648.36], [916.44, 648.36], [916.44, 547.36]], "confidence": 0.9759865999221802, "time": 0.802546501159668}, "end_time": 1727281441.882271}}
{"tag": "function", "depth": 3, "time": 1727281441.8942392, "data": {"name": "try_log_screen", "call_args": {"screen": "array([[[ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        ...,\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24]],\n\n       [[ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        ...,\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24]],\n\n       [[ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        ...,\n        [ 31,  28,  24],\n        [ 31,  28,  24],\n        [ 31,  28,  24]],\n\n       ...,\n\n       [[255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255],\n        ...,\n        [255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255]],\n\n       [[255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255],\n        ...,\n        [255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255]],\n\n       [[255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255],\n        ...,\n        [255, 255, 255],\n        [255, 255, 255],\n        [255, 255, 255]]], dtype=uint8)", "quality": null, "max_size": null}, "start_time": 1727281441.8832955, "ret": {"screen": "1727281441883.jpg", "resolution": [1280, 720]}, "end_time": 1727281441.8942392}}
{"tag": "function", "depth": 2, "time": 1727281441.8952577, "data": {"name": "loop_find", "call_args": {"query": {"filename": "tpl1727259252787.png", "_filepath": "D:\\airtest_script\\nsh0910.air\\nsh.air\\tpl1727259252787.png", "threshold": 0.7, "target_pos": 5, "record_pos": [-0.002, 0.187], "resolution": [1280, 720], "rgb": false, "scale_max": 800, "scale_step": 0.005, "__class__": "Template"}, "timeout": 3, "threshold": null, "interval": 0.5, "intervalfunc": null}, "start_time": 1727281440.6552403, "ret": [637, 597], "end_time": 1727281441.8952577}}
{"tag": "function", "depth": 1, "time": 1727281441.8952577, "data": {"name": "exists", "call_args": {"v": {"filename": "tpl1727259252787.png", "_filepath": "D:\\airtest_script\\nsh0910.air\\nsh.air\\tpl1727259252787.png", "threshold": 0.7, "target_pos": 5, "record_pos": [-0.002, 0.187], "resolution": [1280, 720], "rgb": false, "scale_max": 800, "scale_step": 0.005, "__class__": "Template"}}, "start_time": 1727281440.6552403, "ret": [637, 597], "end_time": 1727281441.8952577}}
{"tag": "function", "depth": 1, "time": 1727281442.908527, "data": {"name": "sleep", "call_args": {"secs": 1}, "start_time": 1727281441.8952577, "ret": null, "end_time": 1727281442.908527}}
{"tag": "function", "depth": 1, "time": 1727281443.6932652, "data": {"name": "sleep", "call_args": {"secs": 0.5}, "start_time": 1727281443.1810246, "ret": null, "end_time": 1727281443.6932652}}
