# -*- mode: python; coding: utf-8 -*-
import glob
import os
import shutil
import subprocess
import sys


# 定义全局变量
SRC_DIR = './'
BUILD_DIR = './build'
DIST_DIR = './dist'
OUTPUT_DIR = os.path.join(DIST_DIR, 'bin')  # 目标重命名文件夹
VERSION_FILE_PATH = r"../version.txt"
RESOURCES_SRC = '../resources'
MODELS_SRC = '../models'

PY_FILES = ['../configs.py', '../image_util.py', '../logger_config.py', '../luhao_models.py', '../server_client.py',
            '../sys_tool.py', '../product_util.py']


def clear_directory(directory, excluded_files=None):
    """清理指定目录，排除特定文件."""
    if excluded_files is None:
        excluded_files = []

    for item in os.listdir(directory):
        item_path = os.path.join(directory, item)

        if os.path.isfile(item_path) and item not in excluded_files:
            os.remove(item_path)
            print(f"已删除文件: {item_path}")
        elif os.path.isdir(item_path):
            shutil.rmtree(item_path)
            print(f"已删除文件夹及其内容: {item_path}")


def prepare_directories():
    """准备构建和输出目录."""
    # 清理构建文件夹和打包输出文件夹
    if os.path.exists(BUILD_DIR):
        shutil.rmtree(BUILD_DIR)

    if os.path.exists(DIST_DIR):
        shutil.rmtree(DIST_DIR)

    # 如果目录不存在，则手动创建
    if not os.path.exists(BUILD_DIR):
        os.makedirs(BUILD_DIR)


def copy_files():
    """复制必要的文件."""
    shutil.copy('../nshpc_mumu.air/nshpc_mumu_login.py', SRC_DIR)
    shutil.copy('../nshpc_mumu.air/nshpc_image_util.py', SRC_DIR)
    shutil.copy('../nshpc_mumu.air/main.py', SRC_DIR)

    # 复制 .py 文件
    # for py_file in glob.glob('../*.py'):
    #     shutil.copy(py_file, SRC_DIR)
    for py_file in PY_FILES:
        shutil.copy(py_file, SRC_DIR)


def compile_pyd():
    """编译 Python 文件为二进制 .pyd 文件."""
    print("正在编译二进制文件 (.pyd)...")

    # 获取当前目录下所有的 .py 文件，排除 setup.py
    py_files = [f for f in os.listdir(SRC_DIR) if f.endswith('.py') and f != 'setup.py']

    # 使用 subprocess 调用 setup.py 进行编译
    result = subprocess.run([sys.executable, 'setup.py', 'build_ext', '--inplace'])

    if result.returncode != 0:
        print("编译失败。")
        sys.exit(1)
    else:
        print("编译完成。")


def copy_pyd_to_bin():
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    pyd_files = glob.glob('*.pyd')
    for pyd_file in pyd_files:
        shutil.copy(pyd_file, OUTPUT_DIR)
        print(f"已复制 {pyd_file} 到 {OUTPUT_DIR}")

def rename_and_copy_resources():
    """重命名输出文件夹并复制资源文件."""
    # 重命名 dist/main 文件夹为 dist/local_luhao
    if os.path.exists(os.path.join(DIST_DIR, 'main')):
        shutil.move(os.path.join(DIST_DIR, 'main'), OUTPUT_DIR)

    # 复制 resources 文件夹及其内容到 output_dir/_internal
    resources_dst = os.path.join(OUTPUT_DIR, 'resources')
    if os.path.exists(RESOURCES_SRC):
        if os.path.exists(resources_dst):
            shutil.rmtree(resources_dst)
        shutil.copytree(RESOURCES_SRC, resources_dst)
    else:
        print(f"未找到资源文件夹：{RESOURCES_SRC}")

    # 复制 version.txt
    shutil.copy2(VERSION_FILE_PATH, os.path.join(OUTPUT_DIR, 'version.txt'))

    # 复制 models 文件夹
    if os.path.exists(MODELS_SRC):
        shutil.copytree(MODELS_SRC, os.path.join(OUTPUT_DIR, 'models'), dirs_exist_ok=True)
        print(f"已复制整个文件夹: {MODELS_SRC} 到 {OUTPUT_DIR}")
    else:
        print(f"未找到 models 文件夹：{MODELS_SRC}")


def get_version():
    """读取版本号."""
    version = '0.5.20'  # 默认版本
    try:
        with open(VERSION_FILE_PATH, 'r') as file:
            version = file.read().strip()
    except FileNotFoundError:
        print(f"未找到版本文件：{VERSION_FILE_PATH}，使用默认版本号 {version}")
    return version


if __name__ == '__main__':
    # 清理源目录，排除特定文件
    clear_directory(SRC_DIR, excluded_files=['main_bin.py', 'setup.py', 'start_nshpc.ps1', 'requirements.txt'])

    # 准备构建和输出目录
    prepare_directories()

    # 复制必要的文件
    copy_files()

    # 编译二进制文件 (.pyd)
    compile_pyd()

    copy_pyd_to_bin()

    # 重命名输出文件夹并复制资源
    rename_and_copy_resources()

    # 打印版本号
    version = get_version()
    print(f"版本号: {version}")
