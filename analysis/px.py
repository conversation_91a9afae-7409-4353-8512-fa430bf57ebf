import re
import numpy as np
import pandas as pd

# 1. 读入 Excel（需要先 pip install openpyxl）
df = pd.read_excel('附件2-螃蟹王者总表_分列.xlsx', engine='openpyxl')

# 2. 提取“珍品传说”数量：标题里形如“珍品传说2”
df['珍品传说_数'] = (
    df['标题']
    .astype(str)
    .map(lambda txt: int(re.search(r'珍品传说(\d+)', txt).group(1))
    if re.search(r'珍品传说(\d+)', txt) else 0)
)

# 3. 确保其他字段都是数值型
for col in ['无双皮肤', '荣耀典藏', '传说皮肤', '史诗皮肤', '总皮肤', '大国标', '小国标', '价格']:
    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(int)


# 4. 通用分箱函数
def bin_counts(series, bins, labels):
    cats = pd.cut(series, bins=bins, labels=labels, right=True, include_lowest=True)
    return cats.value_counts().reindex(labels, fill_value=0)


# 5. 各分布统计
distributions = {}

# 5.1 账号类型分布（以“区服”列为例）
distributions['账号类型分布'] = df['区服'].fillna('其他').value_counts()

# 5.2 总皮肤数量分布
total_bins = [0, 200, 400, 500, 600, np.inf]
total_labels = ['0-200', '201-400', '401-500', '501-600', '600以上']
distributions['总皮肤数量分布'] = bin_counts(df['总皮肤'], total_bins, total_labels)

# 5.3 珍品传说皮肤数量分布
rare_bins = [-1, 0, 1, 3, np.inf]
rare_labels = ['0', '1', '2-3', '3以上']
distributions['珍品传说皮肤数量分布'] = bin_counts(df['珍品传说_数'], rare_bins, rare_labels)

# 5.4 无双皮肤数量分布
wushuang_bins = [-1, 0, 1, 5, np.inf]
wushuang_labels = ['0', '1', '2-5', '5以上']
distributions['无双皮肤数量分布'] = bin_counts(df['无双皮肤'], wushuang_bins, wushuang_labels)

# 5.5 荣耀典藏数量分布
glory_bins = [-1, 0, 1, 5, 9, np.inf]
glory_labels = ['0', '1', '2-4', '5-8', '8以上']
distributions['荣耀典藏数量分布'] = bin_counts(df['荣耀典藏'], glory_bins, glory_labels)

# 5.6 传说皮肤数量分布
legend_bins = [0, 1, 10, 20, 50, np.inf]
legend_labels = ['0', '1-10', '11-20', '21-50', '50以上']
distributions['传说皮肤数量分布'] = bin_counts(df['传说皮肤'], legend_bins, legend_labels)

# 5.7 史诗皮肤数量分布
epic_bins = [0, 50, 100, 200, np.inf]
epic_labels = ['0-50', '51-100', '101-200', '200以上']
distributions['史诗皮肤数量分布'] = bin_counts(df['史诗皮肤'], epic_bins, epic_labels)

# 5.8 贵族等级分布
noble = df['贵族等级'].astype(str).str.extract(r'(\d+)').fillna(0).astype(int)[0]
distributions['贵族等级分布'] = pd.Series({
    '贵族10及以上': int((noble >= 10).sum()),
    '贵族10以下': int((noble < 10).sum())
})

# 5.9 国标数量分布（同小/大国标相加后，再拆小/大）
df['国标总数'] = df['小国标'] + df['大国标']
distributions['国标数量分布'] = pd.Series({
    '国标号数为0': int((df['国标总数'] == 0).sum()),
    '小国标1个': int((df['小国标'] == 1).sum()),
    '大国标1个': int((df['大国标'] == 1).sum()),
    '大国标2-3个': int(((df['大国标'] >= 2) & (df['大国标'] <= 3)).sum()),
    '大国标3个以上': int((df['大国标'] > 3).sum()),
})

# 5.10 挂售价格分布
price_bins = [0, 500, 1000, 2000, 4000, 6000, 10000, np.inf]
price_labels = ['500以内', '501-1000', '1001-2000', '2001-4000', '4001-6000', '6001-10000', '10000以上']
distributions['挂售价格分布'] = bin_counts(df['价格'], price_bins, price_labels)

# 6. 将每个分布输出为单独 CSV
for name, series in distributions.items():
    out = series.reset_index()
    out.columns = ['区间', '数量']
    out.to_csv(f'螃蟹_{name}.csv', index=False, encoding='utf-8-sig')
    print(f'已生成: 螃蟹_{name}.csv')
