from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import binascii

from common import  mysql_util

def encrypt(id_number):
    key = "7a70e111c3b72acfa3982fbcf781e542".encode('utf-8')
    cipher = AES.new(key, AES.MODE_ECB)  # Java's SecureUtil.aes defaults to ECB mode
    padded_data = pad(id_number.encode('utf-8'), AES.block_size)
    encrypted = cipher.encrypt(padded_data)
    return binascii.hexlify(encrypted).decode('utf-8')

if __name__ == '__main__':
    r = encrypt('1234')
    print(r)

    blackusers = mysql_util.execute_sql_dict("select * from cms_blackuser")
    print(blackusers)

    for user in blackusers:
        user_id = user['id']
        if user_id == 1:
            continue
        original_name = user.get('user_id_name', '')
        original_number = user.get('user_id_number', '')

        # 加密姓名和身份证号，非空则加密
        encrypted_name = encrypt(original_name) if original_name else original_name
        encrypted_number = encrypt(original_number) if original_number else original_number

        # 更新数据库
        update_sql = """
                UPDATE cms_blackuser 
                SET user_id_name = %s, user_id_number = %s 
                WHERE id = %s
            """
        params = (encrypted_name, encrypted_number, user_id)
        mysql_util.execute_sql(update_sql, params)

    print("加密更新完成。")




