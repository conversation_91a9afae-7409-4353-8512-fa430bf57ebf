#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

import pandas as pd

# 中文数字转阿拉伯数字映射（处理到 100 以内）
cn2digit = {
    '零': 0, '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
    '六': 6, '七': 7, '八': 8, '九': 9, '十': 10, '百': 100
}


def chinese_to_digit(cn: str) -> int:
    """简单中文数字转阿拉伯数字（只支持零–百以内）"""
    if not cn:
        return 0
    total = 0
    unit = 1
    num = 0
    for char in reversed(cn):
        val = cn2digit.get(char)
        if val is None:
            continue
        # 如果是“十”或“百”，代表单位
        if val in (10, 100):
            unit = val
            if num == 0:
                num = 1
        else:
            total += val * unit
    if total == 0:
        total = num
    return total


def count_rare_in_desc(text: str) -> int:
    s = str(text)

    # 1) 如果包含“满珍品”，直接返回 4
    if '满珍品' in s:
        return 4

    # 2) 尝试匹配阿拉伯数字
    m = re.search(r'(\d+)珍品', s)
    if m:
        return int(m.group(1))

    # 3) 再尝试匹配中文数字
    m_cn = re.search(r'([零一二三四五六七八九十百]+)珍品', s)
    if m_cn:
        return chinese_to_digit(m_cn.group(1))

    # 4) 都没匹配到，返回 0
    return 0


def extract_wushuang(text: str) -> int:
    """
    提取“无双X”中的 X：
    - 如果出现“满无双”，返回 10
    - 否则匹配阿拉伯数字 + 无双
    """
    s = str(text)
    # 1) 优先判断“满无双”
    if '满无双' in s:
        return 10
    # 2) 匹配数字+无双
    m = re.search(r'(\d+)无双', s)
    return int(m.group(1)) if m else 0


def extract_guobiao_counts(text: str):
    """返回 (小国标 数量, 大国标 数量)"""
    s = str(text)
    small = len(re.findall(r'小国标', s))
    large = len(re.findall(r'大国标', s))
    return small, large


def main():
    # 1. 读入原始 CSV
    df = pd.read_csv('附件1-盼之.csv', encoding='gbk')

    # 2. 增加“珍品传说皮肤数量”列
    df['珍品传说皮肤数量'] = df['卖家说'].apply(count_rare_in_desc)

    # 3. 增加“无双皮肤数量”列
    df['无双皮肤数量'] = df['卖家说'].apply(extract_wushuang)

    # 4. 增加“小国标数量”和“大国标数量”列
    guobiao = df['卖家说'].apply(extract_guobiao_counts)
    df['小国标数量'] = guobiao.map(lambda x: x[0])
    df['大国标数量'] = guobiao.map(lambda x: x[1])

    # 5. 保存到新的 CSV 文件
    df.to_csv('盼之_附加列.csv', index=False, encoding='utf-8-sig')
    print("已保存：盼之_附加列.csv （包含 新增的 4 列）")


if __name__ == '__main__':
    main()
