import re

import numpy as np
import pandas as pd

# 1. 读入盼之平台数据
df = pd.read_csv('附件1-盼之.csv', encoding='gbk')

# 2. 账号类型分布（textregion 列）
acct_dist = df['textregion'].fillna('其他').value_counts()


# 3. 通用分箱函数
def bin_counts(series, bins, labels):
    cats = pd.cut(series.fillna(0), bins=bins, labels=labels, right=True, include_lowest=True)
    return cats.value_counts().reindex(labels, fill_value=0)


# 4. 总皮肤数量分布
total_bins = [0, 200, 400, 500, 600, np.inf]
total_labels = ['0-200', '201-400', '401-500', '501-600', '600以上']
dist_total = bin_counts(df['皮肤数量'], total_bins, total_labels)

# 5. 荣耀典藏数量分布
glory_bins = [-1, 0, 1, 5, 9, np.inf]
glory_labels = ['0', '1', '2-4', '5-8', '8以上']
dist_glory = bin_counts(df['荣耀典藏数量'], glory_bins, glory_labels)

# 6. 传说皮肤数量分布
legend_bins = [0, 1, 10, 20, 50, np.inf]
legend_labels = ['0', '1-10', '11-20', '21-50', '50以上']
dist_legend = bin_counts(df['传说皮肤数量'], legend_bins, legend_labels)

# 7. 史诗皮肤数量分布（示例分箱）
epic_bins = [0, 50, 100, 200, np.inf]
epic_labels = ['0-50', '51-100', '101-200', '200以上']
dist_epic = bin_counts(df['史诗皮肤数量'], epic_bins, epic_labels)

# 8. 英雄数量分布（示例分箱）
hero_bins = [0, 50, 100, 200, np.inf]
hero_labels = ['0-50', '51-100', '101-200', '200以上']
dist_hero = bin_counts(df['英雄数量'], hero_bins, hero_labels)

# 9. 无双皮肤数量分布（从“卖家说”字段提取）
df['无双皮肤_数'] = df['卖家说'].map(
    lambda x: int(re.search(r'(\d+)无双', str(x)).group(1))
    if re.search(r'(\d+)无双', str(x)) else 0
)
wushuang_bins = [-1, 0, 1, 5, np.inf]
wushuang_labels = ['0', '1', '2-5', '5以上']
dist_wushuang = bin_counts(df['无双皮肤_数'], wushuang_bins, wushuang_labels)


# —— 提取国标数量 —— #
def extract_guobiao_counts(text):
    s = str(text)
    # 分别统计“子串”出现次数
    small = len(re.findall(r'小国标', s))
    large = len(re.findall(r'大国标', s))
    return small, large


# 批量应用，生成两列
df[['小国标_数', '大国标_数']] = df['卖家说'].apply(
    lambda x: pd.Series(extract_guobiao_counts(x))
)

# 按你的规则统计分布
dist_guobiao = pd.Series({
    '国标号数为0': int(((df['小国标_数'] + df['大国标_数']) == 0).sum()),
    '小国标1个': int((df['小国标_数'] == 1).sum()),
    '大国标1个': int((df['大国标_数'] == 1).sum()),
    '大国标2-3个': int(((df['大国标_数'] >= 2) & (df['大国标_数'] <= 3)).sum()),
    '大国标3个以上': int((df['大国标_数'] > 3).sum()),
})

# 9. 贵族等级分布
noble = pd.to_numeric(df['贵族'], errors='coerce').fillna(0).astype(int)
noble_dist = pd.Series({
    '贵族10及以上': int((noble >= 10).sum()),
    '贵族10以下': int((noble < 10).sum())
})

# 10. 挂售价格分布（价格 列）
price_bins = [0, 500, 1000, 2000, 4000, 6000, 10000, np.inf]
price_labels = ['500以内', '501-1000', '1001-2000', '2001-4000', '4001-6000', '6001-10000', '10000以上']
dist_price = bin_counts(df['价格'], price_bins, price_labels)

# —— 新增：珍品数量提取 —— #
# 中文数字转阿拉伯数字
cn2digit = {
    '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
    '六': 6, '七': 7, '八': 8, '九': 9, '十': 10, '百': 100
}


def chinese_to_digit(cn):
    """简单中文数字转阿拉伯数字（处理到100以内）"""
    if not cn:
        return 0
    total = 0
    unit = 1
    num = 0
    for char in reversed(cn):
        if char in cn2digit:
            val = cn2digit[char]
            if val == 10 or val == 100:
                unit = val
                if num == 0:
                    num = 1
            else:
                total += val * unit
        else:
            continue
    if total == 0:
        total = num
    return total


def count_rare_in_desc(text):
    if pd.isna(text):
        return 0
    total = 0
    matches_digit = re.findall(r'(\d+)珍品', text)
    total += sum(map(int, matches_digit))

    matches_cn = re.findall(r'([一二三四五六七八九十百]+)珍品', text)
    total += sum(map(chinese_to_digit, matches_cn))

    return total


# 应用到 df 上
df['珍品_数'] = df['卖家说'].apply(count_rare_in_desc)

# 按你的风格，做分布统计
rare_bins = [-1, 0, 1, 3, np.inf]
rare_labels = ['0', '1', '2-3', '3以上']
dist_rare = bin_counts(df['珍品_数'], rare_bins, rare_labels)

# 11. 导出为独立 CSV
# 总数量
total_count = df['textregion'].count()
pd.DataFrame({'总数量': [total_count]}).to_csv('盼之_总数量.csv', index=False, encoding='utf-8-sig')

# 各分布
distributions = {
    '账号类型分布': acct_dist,
    '总皮肤数量分布': dist_total,
    '荣耀典藏数量分布': dist_glory,
    '传说皮肤数量分布': dist_legend,
    '史诗皮肤数量分布': dist_epic,
    '英雄数量分布': dist_hero,
    '无双皮肤数量分布': dist_wushuang,
    '国标数量分布': dist_guobiao,
    '贵族等级分布': noble_dist,
    '挂售价格分布': dist_price,
    '珍品数量分布': dist_rare
}

for name, series in distributions.items():
    df_dim = series.reset_index()
    df_dim.columns = ['区间', '数量']
    filename = f'盼之_{name}.csv'
    df_dim.to_csv(filename, index=False, encoding='utf-8-sig')
