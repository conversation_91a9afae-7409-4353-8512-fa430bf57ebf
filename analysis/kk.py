import pandas as pd

# 1. 读入 CSV 并 pivot
df = pd.read_csv('1226-0426.csv')
df_wide = df.pivot_table(
    index=['id', 'price'],
    columns='product_attribute_name',
    values='value',
    aggfunc='first'
).reset_index()

# 2. 把逗号分隔的皮肤名称列表，转换为“数量”列
for attr in ['珍品传说', '无双皮肤', '荣耀典藏皮肤', '传说皮肤', '小国标', '大国标']:
    col_count = attr + '_数'
    # 先 fillna('')，再 split(',') 计数；空字符串会得到 1 项，所以我们再判断 x.strip()=='' 当成 0
    df_wide[col_count] = (
        df_wide[attr]
        .fillna('')
        .map(lambda x: 0 if x.strip() == '' else len(x.split(',')))
    )

# 3. 生成“国标号数” = 小国标_数 + 大国标_数
df_wide['国标号数'] = df_wide['小国标_数'] + df_wide['大国标_数']


# 3. 通用分箱函数（接收数值 Series）
def bin_counts_num(series, bins, labels):
    cats = pd.cut(series, bins=bins, labels=labels, right=True, include_lowest=True)
    return cats.value_counts().reindex(labels, fill_value=0)


# 4. 各类分布统计
# 4.1 账号类型分布（同前）
acct = df_wide['账号类型'].fillna('其他')
fixed = ['安卓QQ', '苹果QQ', '安卓微信', '苹果微信']
acct_counts = acct.value_counts().to_dict()
acct_dist = {k: acct_counts.get(k, 0) for k in fixed}
acct_dist['其他'] = sum(v for k, v in acct_counts.items() if k not in fixed)

# 4.2 皮肤总数分布
total_bins = [0, 200, 400, 500, 600, float('inf')]
total_labels = ['0-200', '201-400', '401-500', '501-600', '600以上']
dist_total = bin_counts_num(df_wide['皮肤数量'].fillna(0).astype(int), total_bins, total_labels)

# 4.3 珍品传说皮肤数量分布
rare_bins = [-1, 0, 1, 3, float('inf')]
rare_labels = ['0', '1', '2-3', '3以上']
dist_rare = bin_counts_num(df_wide['珍品传说_数'], rare_bins, rare_labels)

# 4.4 无双皮肤数量分布
wushuang_bins = [-1, 0, 1, 5, float('inf')]
wushuang_labels = ['0', '1', '2-5', '5以上']
dist_wushuang = bin_counts_num(df_wide['无双皮肤_数'], wushuang_bins, wushuang_labels)

# 4.5 荣耀典藏数量分布
glory_bins = [-1, 0, 1, 5, 9, float('inf')]
glory_labels = ['0', '1', '2-4', '5-8', '8以上']
dist_glory = bin_counts_num(df_wide['荣耀典藏皮肤_数'], glory_bins, glory_labels)

# 4.6 传说皮肤数量分布
legend_bins = [-1, 0, 10, 20, 50, float('inf')]
legend_labels = ['0', '1-10', '11-20', '21-50', '50以上']
dist_legend = bin_counts_num(df_wide['传说皮肤_数'], legend_bins, legend_labels)

# 4.7 贵族等级分布
# 处理贵族等级：去掉 "贵族" 字样，只保留数字
noble = df_wide['贵族等级'].fillna('').str.replace('贵族', '', regex=False)

# 然后转成数字，如果转不了（比如空值）就当成 0
noble = pd.to_numeric(noble, errors='coerce').fillna(0).astype(int)

# 重新计算贵族等级分布
noble_dist = {
    '贵族10及以上': int((noble >= 10).sum()),
    '贵族10以下': int((noble < 10).sum())
}
#
# noble = pd.to_numeric(df_wide['贵族等级'], errors='coerce').fillna(0).astype(int)
# noble_dist = {
#     '贵族10及以上': int((noble >= 10).sum()),
#     '贵族10以下': int((noble < 10).sum())
# }

# 4.8 国标数量分布
nb_small = df_wide['小国标_数'].fillna(0).astype(int)
nb_large = df_wide['大国标_数'].fillna(0).astype(int)

dist_country = pd.Series({
    '国标号数为0': int(((nb_small + nb_large) == 0).sum()),
    '小国标1个': int((nb_small == 1).sum()),
    '大国标1个': int((nb_large == 1).sum()),
    '大国标2-3个': int(((nb_large >= 2) & (nb_large <= 3)).sum()),
    '大国标3个以上': int((nb_large > 3).sum()),
})

# 4.9 挂售价格分布
price_bins = [0, 500, 1000, 2000, 4000, 6000, 10000, float('inf')]
price_labels = ['500以内', '501-1000', '1001-2000', '2001-4000', '4001-6000', '6001-10000', '10000以上']
dist_price = bin_counts_num(df_wide['price'].fillna(0), price_bins, price_labels)

# 5. 汇总输出
dist_all = {
    '账号类型分布': pd.Series(acct_dist),
    '总皮肤数量分布': dist_total,
    '珍品传说皮肤数量分布': dist_rare,
    '无双皮肤数量分布': dist_wushuang,
    '荣耀典藏数量分布': dist_glory,
    '传说皮肤数量分布': dist_legend,
    '贵族等级分布': pd.Series(noble_dist),
    '国标数量分布': pd.Series(dist_country),
    '挂售价格分布': dist_price
}

df_dist = pd.DataFrame(dist_all)
print(df_dist)

# 先计算总数量（总账号数）
total_count = df_wide['id'].nunique()  # 或者 len(df_wide) 看你要去重与否
df_total = pd.DataFrame({'总数量': [total_count]})

# # 直接用 openpyxl 引擎
# with pd.ExcelWriter('多维度分布统计1.xlsx', engine='openpyxl') as writer:
#     # 0. 写入总数量
#     df_total.to_excel(writer, sheet_name='总数量', index=False)
#
#     # 1. 写入各维度分布
#     for name, series in dist_all.items():
#         df_dim = series.reset_index()
#         df_dim.columns = ['区间', '数量']
#         sheet = name if len(name) <= 31 else name[:31]
#         df_dim.to_excel(writer, sheet_name=sheet, index=False)
# # 离开 with 块后会自动调用 close() 并保存
# print("已生成 Excel：多维度分布统计1.xlsx")
# 导出“总数量”
df_total.to_csv('总数量.csv', index=False, encoding='utf-8-sig')
print("已生成：总数量.csv")

# 导出每个维度分布，各自一个文件
for name, series in dist_all.items():
    df_dim = series.reset_index()
    df_dim.columns = ['区间', '数量']
    # 文件名中避免特殊字符
    filename = name.replace('/', '_') + '.csv'
    df_dim.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"已生成：{filename}")
