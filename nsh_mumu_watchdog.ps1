$appName = "python.exe"
$scriptPath = "start_nsh_mumu.ps1"  # �滻Ϊ Python �����ű�·��
$checkInterval = 10  # ��������룩


function Get-CurrentTime {
    return (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
}

Write-Host "$(Get-CurrentTime) - �ػ����������������Ӧ�ã�$appName $scriptPath"

while ($true) {
    # ���Ӧ���Ƿ�������
    $process = Get-Process | Where-Object { $_.Path -like "*$appName*" }

    if (-not $process) {
        Write-Host "$(Get-CurrentTime) - $appName δ���У���������..."
        # ʹ�� psexec �Թ���ԱȨ������ PowerShell �ű�
        Start-Process -FilePath "psexec.exe" -ArgumentList "-accepteula -s powershell.exe -NoProfile -ExecutionPolicy Bypass -File `"$scriptPath`""
    }

    Start-Sleep -Seconds $checkInterval  # ÿ checkInterval ����һ��
}
