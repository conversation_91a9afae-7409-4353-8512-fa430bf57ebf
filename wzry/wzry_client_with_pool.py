"""
集成Token池的王者荣耀客户端
基于原有的wzry_client2.py，集成SimpleTokenPool进行Token管理
"""
import json
import logging
import os
import sys
import time
import traceback
from time import sleep

import requests

from common.configs import current_config as config
from common.crypto_util import AESCryptoUtil
from common.luhao_models import FuncResponse
from common.dingtalk_robot import DingTalkRobot
from common.mongo_manager import MongoDBClient
from wzry.simple_token_pool import SimpleTokenPool
from wzry.token_models import TokenStatus

aes_util = AESCryptoUtil("aEUAzI8eCEgdo02pfkLY+w==")

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

translation = {
    'activityLimited': '活动限定',
    'annualLimited': '年度限定',
    'battlePass': '战令',
    'epic': '史诗',
    'glory': '荣耀',
    'legend': '传说',
    'seasonal': '赛季',
    'warrior': '战士'
}


class WzryClientWithPool(object):
    """集成Token池的王者荣耀客户端"""
    
    def __init__(self):
        """初始化客户端"""
        # 初始化MongoDB连接
        self.mongo = MongoDBClient(
            uri="******************************************",
            db_name="luhao-prod"
        )
        
        # 初始化Token池
        self.token_pool = SimpleTokenPool(self.mongo, "wzry_token_pool")
        
        # 当前使用的Token配置
        self.current_token_config = None
        self.current_token_id = None
        
        # 初始化钉钉机器人
        self.dingtalk_robot = None
        self._init_dingtalk_robot()
        
        logging.info("WzryClientWithPool初始化完成")
    
    def _init_dingtalk_robot(self):
        """初始化钉钉机器人"""
        try:
            ACCESS_TOKEN = "08a8285420b344660b4125d7d78a65936ccd279a9b9c7bb810ae8188027a70ee"
            SECRET = "SEC9d5fa738d5a41623384485934d4365cac024c94c42e63521917a38e14d2e5742"
            self.dingtalk_robot = DingTalkRobot(ACCESS_TOKEN, SECRET)
            logging.info("钉钉机器人初始化成功")
        except Exception as e:
            logging.warning(f"钉钉机器人初始化失败: {e}")
            self.dingtalk_robot = None
    
    def _send_dingtalk_alert(self, title: str, message: str, level: str = "ERROR"):
        """发送钉钉告警消息"""
        if not self.dingtalk_robot:
            return
        
        try:
            level_icons = {
                "ERROR": "🚨",
                "WARNING": "⚠️",
                "INFO": "ℹ️"
            }
            
            icon = level_icons.get(level, "🔔")
            alert_title = f"{icon} 王者荣耀录号告警"
            device_id = os.getenv('DEVICE_ID', '未知设备')
            
            alert_content = f"""{alert_title}

设备ID: {device_id}
告警级别: {level}
告警标题: {title}
详细信息: {message}
时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            result = self.dingtalk_robot.send_text(alert_content, at_mobiles=["13176667929"])
            logging.info(f"钉钉告警发送成功: {title}, 设备ID: {device_id}")
            
            # if level == "ERROR":
            #     logging.critical(f"检测到严重错误，程序即将停止。告警标题: {title}")
            #     sys.exit(1)
                
        except Exception as e:
            logging.error(f"钉钉告警发送异常: {e}")
            # if level == "ERROR":
            #     sys.exit(1)
    
    def _get_token_config(self):
        """获取Token配置"""
        if not self.current_token_config:
            token_config = self.token_pool.get_next_token()
            if not token_config:
                logging.error("没有可用的Token")
                return None
            
            self.current_token_config = token_config
            self.current_token_id = token_config['_id']
            logging.info(f"获取到Token: {token_config['token'][:8]}...")
        
        return self.current_token_config
    
    def _handle_token_error(self, error_code: int, error_msg: str):
        """处理Token错误"""
        if not self.current_token_id:
            return

        # 获取Token的comment信息用于告警
        token_comment = self.current_token_config.get('comment', '未知Token') if self.current_token_config else '未知Token'
        token_id_short = self.current_token_config['token'][:8] if self.current_token_config else '未知'
        device_id = os.getenv('DEVICE_ID', '未知设备')

        if error_code == -30003:  # 登录态失效
            logging.warning(f"Token登录态失效: {token_id_short}... (comment: {token_comment})")
            self.token_pool.update_token_status(self.current_token_id, TokenStatus.LOGIN_EXPIRED)

            # 构建详细的告警消息
            alert_title = "Token登录态失效"
            alert_message = f"""Token状态异常告警

Token信息:
- Token ID: {self.current_token_id}
- Token前缀: {token_id_short}...
- Token备注: {token_comment}
- 状态变更: NORMAL → LOGIN_EXPIRED
- 设备ID: {device_id}

错误详情: {error_msg}"""

            self._send_dingtalk_alert(alert_title, alert_message, "ERROR")

        elif error_code == -30107:  # 访问受限
            logging.warning(f"Token访问受限: {token_id_short}... (comment: {token_comment})")
            self.token_pool.update_token_status(self.current_token_id, TokenStatus.ACCESS_LIMITED)

            # 构建详细的告警消息
            alert_title = "Token访问受限"
            alert_message = f"""Token状态异常告警

Token信息:
- Token ID: {self.current_token_id}
- Token前缀: {token_id_short}...
- Token备注: {token_comment}
- 状态变更: NORMAL → ACCESS_LIMITED
- 设备ID: {device_id}

错误详情: {error_msg}"""

            self._send_dingtalk_alert(alert_title, alert_message, "ERROR")

        # 清空当前Token配置，下次会重新获取
        self.current_token_config = None
        self.current_token_id = None

    def release_current_token(self):
        """释放当前使用的Token"""
        if self.current_token_id:
            try:
                success = self.token_pool.release_token(self.current_token_id)
                if success:
                    logging.info(f"Token已释放: {self.current_token_id}")
                else:
                    logging.warning(f"Token释放失败: {self.current_token_id}")
                return success
            except Exception as e:
                logging.error(f"释放Token时发生错误: {e}")
                return False
            finally:
                # 无论释放是否成功，都清空当前Token配置
                self.current_token_config = None
                self.current_token_id = None
        return True

    def cleanup_and_release_token(self):
        """清理并释放Token（在任务完成或异常时调用）"""
        try:
            self.release_current_token()
            logging.info("Token清理完成")
        except Exception as e:
            logging.error(f"Token清理时发生错误: {e}")
            # 确保Token配置被清空
            self.current_token_config = None
            self.current_token_id = None
    
    def get_role_id(self, target_user_id):
        """通过Token池获取roleId"""
        token_config = self._get_token_config()
        if not token_config:
            return FuncResponse.fail('没有可用的Token')
        
        headers = {
            'Host': 'ssl.kohsocialapp.qq.com:10001',
            'cchannelid': '10014911',
            'cclientversioncode': '2037902808',
            'cclientversionname': '8.94.0919',
            'ccurrentgameid': '20001',
            'cgameid': '20001',
            'cgzip': '1',
            'cisarm64': 'true',
            'csupportarm64': 'true',
            'csystem': 'android',
            'csystemversioncode': '32',
            'csystemversionname': '12',
            'cpuhardware': 'HONOR',
            'gameareaid': token_config['gameareaid'],
            'gameid': '20001',
            'gameroleid': token_config['gameroleid'],
            'gameserverid': token_config['gameserverid'],
            'gameusersex': token_config['gameusersex'],
            'openid': token_config['openid'],
            'tinkerid': '2037902808_64_0',
            'content-encrypt': '',
            'accept-encrypt': '',
            'noencrypt': '1',
            'x-client-proto': 'https',
            'kohdimgender': token_config['kohdimgender'],
            'user-agent': 'okhttp/4.9.1',
            'encodeparam': token_config['encodeParam'],
            'token': token_config['token'],
            'userid': token_config['user_id'],
            'content-type': 'application/x-www-form-urlencoded'
        }
        
        data = {
            "friendNickname": "",
            "friendUserId": target_user_id,
            "cChannelId": "10014911",
            "cClientVersionCode": "2037902808",
            "cClientVersionName": "8.94.0919",
            "cCurrentGameId": "20001",
            "cGameId": "20001",
            "cGzip": "1",
            "cIsArm64": "true",
            "cRand": str(int(time.time() * 1000)),
            "cSupportArm64": "true",
            "cSystem": "android",
            "cSystemVersionCode": "32",
            "cSystemVersionName": "12",
            "cpuHardware": "HONOR",
            "encodeParam": token_config['encodeParam'],
            "gameAreaId": token_config['gameareaid'],
            "gameId": "20001",
            "gameRoleId": token_config['gameroleid'],
            "gameServerId": token_config['gameserverid'],
            "gameUserSex": token_config['gameusersex'],
            "openId": token_config['openid'],
            "tinkerId": "2037902808_64_0",
            "token": token_config['token'],
            "userId": token_config['user_id']
        }
        
        try:
            response = requests.post(
                url='https://ssl.kohsocialapp.qq.com:10001/user/useradapter',
                headers=headers,
                data=data,
                timeout=10
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get('result') == 0:
                if result.get('returnCode') == -30003:
                    self._handle_token_error(-30003, str(result))
                    return FuncResponse.fail('获取roleId失败')
                if not result.get('data'):
                    return FuncResponse.fail('营地ID不存在')
                if 'roleId' not in result.get('data') or result.get('data').get('roleId') == '0':
                    return FuncResponse.fail('营地ID不存在')
                
                role_id = result.get('data', {}).get('roleId')
                logging.info(f"获取成功: roleId = {role_id}")
                return FuncResponse.ok(role_id)
            else:
                error_msg = f"接口错误: {result.get('returnMsg')}, 用户ID: {target_user_id}"
                logging.error(error_msg)
                self._send_dingtalk_alert("王者荣耀API接口错误", error_msg, "ERROR")
                return FuncResponse.fail_need_retry("接口错误")
                
        except requests.exceptions.RequestException as e:
            error_msg = f"获取roleId网络请求失败: {str(e)}, 用户ID: {target_user_id}"
            logging.error(f"请求失败: {traceback.format_exc()}")
            self._send_dingtalk_alert("网络请求异常", error_msg, "ERROR")
            raise e

    def get_user_profile(self, target_user_id, role_id):
        """获取用户资料"""
        token_config = self._get_token_config()
        if not token_config:
            return FuncResponse.fail('没有可用的Token')

        headers = {
            'Host': 'kohcamp.qq.com',
            'istrpcrequest': 'true',
            'encodeparam': token_config['encodeParam'],
            'token': token_config['token'],
            'userid': token_config['user_id'],
            'content-type': 'application/json; charset=UTF-8'
        }

        data = {
            "resVersion": 3,
            "recommendPrivacy": 0,
            "apiVersion": 2,
            "targetUserId": target_user_id,
            "targetRoleId": role_id,
            "itsMe": False
        }

        try:
            response = requests.post(
                url='https://kohcamp.qq.com/game/koh/profile',
                headers=headers,
                json=data,
                timeout=10
            )
            response.raise_for_status()
            logging.info(f"HTTP状态码: {response.status_code}")

            profile = response.json()
            if profile['returnCode'] != 0:
                return FuncResponse.fail(profile['returnMsg'])

            target_role_id = profile['data']['targetRoleId']
            result = None
            for item in profile['data']['roleList']:
                if item['roleId'] == target_role_id:
                    role_job_name = item.get('roleJobName')
                    result = {
                        'roleJobName': role_job_name,
                        'userId': target_user_id,
                        'targetRoleId': target_role_id
                    }
                    break
            return FuncResponse.ok(result)
        except Exception as e:
            logging.error(f"请求失败: {str(e)}")
            return FuncResponse.fail_need_retry()

    def get_hero_list(self, role_id):
        """获取英雄列表"""
        token_config = self._get_token_config()
        if not token_config:
            return None

        try:
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "token": token_config['token'],
                "userId": token_config['user_id'],
            }
            data = f"roleId={role_id}&cClientVersionName=8.94.0919&token={token_config['token']}&userId={token_config['user_id']}"
            response = requests.post("https://ssl.kohsocialapp.qq.com:10001/play/h5getherolist",
                                   data=data, headers=headers)
            logging.info("[INFO] Hero list successfully fetched.")
            return response.json()
        except Exception as error:
            logging.error(f"[ERROR] Error fetching hero skin list: {error}")
            return None

    def get_skin_list(self, role_id):
        """获取皮肤列表"""
        token_config = self._get_token_config()
        if not token_config:
            return None

        headers = {
            'Host': 'kohcamp.qq.com',
            'istrpcrequest': 'true',
            'encodeparam': token_config['encodeParam'],
            'token': token_config['token'],
            'userid': token_config['user_id'],
            'content-type': 'application/json; charset=UTF-8'
        }

        payload = {
            "recommendPrivacy": 0,
            "roleId": role_id
        }

        try:
            response = requests.post(
                url='https://kohcamp.qq.com/game/itempage/skinlist',
                headers=headers,
                json=payload,
                timeout=10
            )
            response.raise_for_status()
            logging.info(f"获取皮肤列表成功")
            result = response.json()

            # 检查错误码
            if result.get('returnCode') == -30107:
                self._handle_token_error(-30107, result.get('returnMsg', ''))
                return None

            return result
        except requests.exceptions.RequestException as e:
            logging.error(f"获取皮肤列表失败: {str(e)}")
            return None

    def get_target_user_info(self, target_user_id):
        """
        获取目标用户信息（使用Token池）

        Args:
            target_user_id: 目标用户ID

        Returns:
            FuncResponse对象
        """
        try:
            # 1. 获取roleId
            result = self.get_role_id(target_user_id)
            if result.is_ok():
                role_id = result.data
                logging.info(f"role_id: {role_id}")
            else:
                return result
        except Exception as error:
            logging.error(f"[ERROR] Error fetching role id: {error}")
            return FuncResponse.fail_need_retry()

        sleep(1)

        # 2. 获取用户资料
        result = self.get_user_profile(target_user_id, role_id)
        if not result.is_ok():
            return result

        user_profile = result.data
        sleep(1)

        # 3. 获取英雄列表
        hero_response = self.get_hero_list(role_id)
        if hero_response is None:
            return FuncResponse.fail_need_retry()
        if hero_response['returnCode'] != 0:
            return FuncResponse.fail('用户隐藏了英雄')

        sleep(1)

        # 4. 获取皮肤列表
        skin_response = self.get_skin_list(role_id)
        if skin_response is None:
            return FuncResponse.fail_need_retry()
        elif skin_response.get('returnCode', 0) == -30107:
            error_msg = f"皮肤接口返回错误-30107: {skin_response['returnMsg']}"
            self._send_dingtalk_alert("皮肤接口错误", error_msg, "ERROR")
            return FuncResponse.fail(skin_response['returnMsg'])
        if 'skinCountInfo' not in skin_response:
            return FuncResponse.fail('获取皮肤失败，可能是用户隐藏了皮肤')

        # 5. 处理数据
        obj = self.do_report(user_profile, hero_response, skin_response)
        return FuncResponse.ok(obj)

    def do_report(self, result, hero_response, skin_response):
        """处理报告数据"""
        obj = {
            "heroList": [],
            "skinList": [],
            "roleJobName": result.get('roleJobName'),
            "userId": result.get('userId'),
            "encrypted_user_id": aes_util.encrypt(result.get('userId')),
        }

        hero_list = hero_response.get('data', {}).get('heroList', [])
        all_hero = {str(hero['heroId']): hero for hero in hero_list}

        for hero in hero_list:
            if not hero.get('notOwnedFlag'):
                obj['heroList'].append({
                    'heroId': hero['heroId'],
                    'heroType': hero['heroType'],
                    'name': hero['name'],
                })

        if skin_response:
            skin_count_info = skin_response.get('skinCountInfo', {})
            logging.info(f'ownedSkinTypeCount: {skin_count_info}')

            obj['skinCountInfo'] = skin_count_info

            skin_data = skin_response.get('skinData', [])
            all_skin_conf = skin_response.get('allSkinConf', [])
            all_hero_conf = skin_response.get('allHeroConf', [])
            skin_num_obj = {}

            all_hero_and_skin = {
                "allSkinConf": all_skin_conf,
                "allHeroConf": all_hero_conf
            }
            self.save_to_json_file(all_hero_and_skin, "all_hero_and_skin.json")

            skin_conf_by_id = {}
            for skin in all_skin_conf:
                if skin.get('isHidden') != 1:
                    skin_conf_by_id[skin['skinId']] = skin
                    hero_name = skin.get('heroName')
                    skin_num_obj[hero_name] = skin_num_obj.get(hero_name, 0) + 1

            for skin in skin_data:
                skin_id = skin.get('skinId')
                find_it = skin_conf_by_id.get(skin_id)
                if find_it:
                    hero_id = str(find_it['heroId'])
                    obj['skinList'].append({
                        'skinId': find_it['skinId'],
                        'skinName': find_it['skinName'],
                        'heroId': hero_id,
                        'heroName': find_it['heroName'],
                        'classTypeName': find_it['classTypeName'],
                        'heroType': all_hero[hero_id]['heroType'],
                        'accessWay': find_it['accessWay'],
                        'skinNum': skin_num_obj[find_it['heroName']],
                    })

        logging.info('[INFO] Report successfully generated.')
        return obj

    def save_to_json_file(self, data, file_path):
        """保存数据到JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logging.info(f"[INFO] Data successfully written to file: {file_path}")
        except Exception as error:
            logging.error(f"[ERROR] Error saving data to file: {error}")

    def add_wzry_detail(self, obj):
        """发送报告到外部服务"""
        logging.info(f"[INFO] Sending report to external service for userId: {obj.get('userId')}")
        try:
            url = f"http://api.yyd8.com/mall-portal/import/product/addWzryDetail?wzryId={obj.get('encrypted_user_id')}"
            response = requests.post(url, json=obj)
            logging.info("[INFO] Report successfully sent.")
        except Exception as err:
            error_msg = f"发送报告到portal服务失败: {str(err)}, 用户ID: {obj.get('userId')}"
            traceback.print_exc()
            logging.error(f"[ERROR] Failed to send report: {err}")
            self._send_dingtalk_alert("外部服务报告发送失败", error_msg, "ERROR")

    def get_pool_status(self):
        """获取Token池状态"""
        return self.token_pool.get_pool_status()


if __name__ == "__main__":
    # 测试集成的Token池客户端
    logging.info("开始测试集成Token池的王者荣耀客户端...")

    # 创建客户端
    wzry_client = WzryClientWithPool()

    # 检查Token池状态
    status = wzry_client.get_pool_status()
    logging.info(f"Token池状态: {status}")

    if status.get('normal_tokens', 0) == 0:
        logging.error("没有可用的Token，请先导入Token")
        logging.info("运行命令: python wzry/import_tokens.py import")
        sys.exit(1)

    # 测试用户ID列表
    # test_user_ids = ["1866747812", "2124113433"]
    test_user_ids = ["350289130"]

    for user_id in test_user_ids:
        logging.info(f"\n--- 测试用户: {user_id} ---")
        try:
            result = wzry_client.get_target_user_info(target_user_id=user_id)
            if result.is_ok():
                data = result.data
                logging.info(f"查询成功:")
                logging.info(f"  用户ID: {data.get('userId')}")
                logging.info(f"  段位: {data.get('roleJobName')}")
                logging.info(f"  英雄数量: {len(data.get('heroList', []))}")
                logging.info(f"  皮肤数量: {len(data.get('skinList', []))}")
                if data.get('skinCountInfo'):
                    logging.info(f"  皮肤价值: {data['skinCountInfo'].get('totalValue', 0)}")
            else:
                logging.warning(f"查询失败: {result.msg}")
        except Exception as e:
            logging.error(f"查询异常: {e}")
            traceback.print_exc()

        # 短暂延迟
        time.sleep(2)

    # 显示最终Token池状态
    final_status = wzry_client.get_pool_status()
    logging.info(f"\n最终Token池状态: {final_status}")

    logging.info("测试完成")
