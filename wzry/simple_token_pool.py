"""
简化的Token池管理系统 - 随机选择和状态管理
"""
import logging
import random
import threading
from datetime import datetime
from typing import List, Optional, Dict, Any

from common.mongo_manager import MongoDBClient
from wzry.token_models import Token, TokenStatus

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SimpleTokenPool:
    """简化的Token池管理类"""
    
    def __init__(self, mongo_client: MongoDBClient, collection_name: str = "wzry_token_pool"):
        """
        初始化Token池

        Args:
            mongo_client: MongoDB客户端实例
            collection_name: 集合名称
        """
        self.mongo_client = mongo_client
        self.collection_name = collection_name
        self._lock = threading.RLock()  # 线程安全锁

        # 创建索引
        self._ensure_indexes()

        logger.info(f"SimpleTokenPool初始化完成，使用集合: {collection_name}")
    
    def _ensure_indexes(self):
        """确保必要的索引存在"""
        try:
            collection = self.mongo_client.get_collection(self.collection_name)
            collection.create_index("token", unique=True)
            collection.create_index("status")
            logger.info("数据库索引创建完成")
        except Exception as e:
            logger.warning(f"创建索引时出现警告: {e}")
    
    def get_next_token(self) -> Optional[Dict[str, Any]]:
        """
        原子获取一个可用Token并标记为使用中

        Returns:
            Token配置字典或None
        """
        try:
            # 使用findOneAndUpdate原子操作
            # 查询条件：状态为NORMAL的Token
            query = {"status": TokenStatus.NORMAL.value}

            # 更新操作：设置状态为IN_USE，更新时间
            update = {
                "$set": {
                    "status": TokenStatus.IN_USE.value,
                    "updated_at": datetime.now()
                }
            }

            # 执行原子操作，返回更新前的文档
            collection = self.mongo_client.get_collection(self.collection_name)
            token_data = collection.find_one_and_update(
                query,
                update,
                return_document=False  # 返回更新前的文档
            )

            if not token_data:
                logger.warning("没有可用的Token")
                return None

            logger.info(f"原子获取Token: {token_data.get('comment', 'Unknown')[:8]}... (状态已设为IN_USE)")

            # 返回适合请求使用的配置格式
            return {
                'token': token_data['token'],
                'user_id': token_data['user_id'],
                'encodeParam': token_data['encodeParam'],
                'gameareaid': token_data['gameareaid'],
                'gameroleid': token_data['gameroleid'],
                'gameserverid': token_data['gameserverid'],
                'gameopenid': token_data['gameopenid'],
                'kohdimgender': token_data['kohdimgender'],
                'gameusersex': token_data['gameusersex'],
                'openid': token_data['openid'],
                'comment': token_data.get('comment', ''),
                '_id': str(token_data['_id'])  # 用于状态更新
            }

        except Exception as e:
            logger.error(f"获取Token时发生错误: {e}")
            return None
    
    def update_token_status(self, token_id: str, status: TokenStatus) -> bool:
        """
        更新Token状态
        
        Args:
            token_id: Token的MongoDB文档ID
            status: 新的状态
            
        Returns:
            操作是否成功
        """
        with self._lock:
            try:
                from bson import ObjectId
                
                # 处理ObjectId
                try:
                    query_id = ObjectId(token_id)
                except:
                    query_id = token_id
                
                update_data = {
                    "status": status.value,
                    "updated_at": datetime.now()
                }
                
                result = self.mongo_client.update_one(
                    self.collection_name,
                    {"_id": query_id},
                    update_data
                )
                
                if result > 0:
                    logger.info(f"Token状态已更新: {token_id} -> {status.value}")
                    return True
                else:
                    logger.warning(f"Token状态更新失败: {token_id}")
                    return False
                    
            except Exception as e:
                logger.error(f"更新Token状态时发生错误: {e}")
                return False

    def release_token(self, token_id: str) -> bool:
        """
        释放Token，将状态从IN_USE恢复为NORMAL

        Args:
            token_id: Token的MongoDB文档ID

        Returns:
            操作是否成功
        """
        with self._lock:
            try:
                from bson import ObjectId

                # 处理ObjectId
                try:
                    query_id = ObjectId(token_id)
                except:
                    query_id = token_id

                # 只有状态为IN_USE的Token才能被释放
                query = {"_id": query_id, "status": TokenStatus.IN_USE.value}
                update_data = {
                    "status": TokenStatus.NORMAL.value,
                    "updated_at": datetime.now()
                }

                result = self.mongo_client.update_one(
                    self.collection_name,
                    query,
                    update_data
                )

                if result > 0:
                    logger.info(f"Token已释放: {token_id} (IN_USE -> NORMAL)")
                    return True
                else:
                    logger.warning(f"Token释放失败，可能不是IN_USE状态: {token_id}")
                    return False

            except Exception as e:
                logger.error(f"释放Token时发生错误: {e}")
                return False

    def cleanup_expired_tokens(self, timeout_minutes: int = 30) -> int:
        """
        清理超时的IN_USE状态Token，将其恢复为NORMAL状态

        Args:
            timeout_minutes: 超时时间（分钟），默认30分钟

        Returns:
            清理的Token数量
        """
        try:
            from datetime import timedelta

            # 计算超时时间点
            timeout_time = datetime.now() - timedelta(minutes=timeout_minutes)

            # 查询条件：状态为IN_USE且更新时间超过超时时间的Token
            query = {
                "status": TokenStatus.IN_USE.value,
                "updated_at": {"$lt": timeout_time}
            }

            # 更新操作：恢复为NORMAL状态
            update_data = {
                "status": TokenStatus.NORMAL.value,
                "updated_at": datetime.now()
            }

            # 执行批量更新
            result = self.mongo_client.update_many(
                self.collection_name,
                query,
                update_data
            )

            if result > 0:
                logger.info(f"清理了 {result} 个超时的IN_USE状态Token (超时时间: {timeout_minutes}分钟)")

            return result

        except Exception as e:
            logger.error(f"清理超时Token时发生错误: {e}")
            return 0
    
    def add_token(self, token_data: Dict[str, Any]) -> Optional[str]:
        """
        添加新Token到池中
        
        Args:
            token_data: Token数据字典
            
        Returns:
            新Token的MongoDB文档ID，失败返回None
        """
        with self._lock:
            try:
                # 创建Token对象
                token = Token(
                    token=token_data['token'],
                    user_id=token_data['user_id'],
                    encodeParam=token_data['encodeParam'],
                    gameareaid=token_data['gameareaid'],
                    gameroleid=token_data['gameroleid'],
                    gameserverid=token_data['gameserverid'],
                    gameopenid=token_data['gameopenid'],
                    kohdimgender=token_data['kohdimgender'],
                    gameusersex=token_data['gameusersex'],
                    openid=token_data['openid'],
                    comment=token_data.get('comment', '')
                )
                
                # 检查Token是否已存在
                existing_token = self.mongo_client.find_one(
                    self.collection_name, 
                    {"token": token.token}
                )
                
                if existing_token:
                    logger.warning(f"Token已存在: {token.token}")
                    return None
                
                # 插入新Token
                token_id = self.mongo_client.insert_one(
                    self.collection_name, 
                    token.to_dict()
                )
                
                logger.info(f"新Token已添加: {token.token[:8]}..., ID: {token_id}")
                return token_id
                
            except Exception as e:
                logger.error(f"添加Token时发生错误: {e}")
                return None
    
    def remove_token(self, token_id: str) -> bool:
        """
        从池中移除Token
        
        Args:
            token_id: Token的MongoDB文档ID
            
        Returns:
            操作是否成功
        """
        with self._lock:
            try:
                from bson import ObjectId
                
                try:
                    query_id = ObjectId(token_id)
                except:
                    query_id = token_id
                
                result = self.mongo_client.delete_one(
                    self.collection_name,
                    {"_id": query_id}
                )
                
                if result > 0:
                    logger.info(f"Token已移除: {token_id}")
                    return True
                else:
                    logger.warning(f"Token移除失败，可能不存在: {token_id}")
                    return False
                    
            except Exception as e:
                logger.error(f"移除Token时发生错误: {e}")
                return False
    
    def get_all_tokens(self) -> List[Token]:
        """获取所有Token"""
        try:
            tokens_data = self.mongo_client.find_many(self.collection_name, {})
            return [Token.from_dict(data) for data in tokens_data]
        except Exception as e:
            logger.error(f"获取所有Token时发生错误: {e}")
            return []
    
    def get_pool_status(self) -> Dict[str, Any]:
        """
        获取Token池状态

        Returns:
            状态信息字典
        """
        try:
            total_count = self.mongo_client.count_documents(self.collection_name, {})

            normal_count = self.mongo_client.count_documents(
                self.collection_name,
                {"status": TokenStatus.NORMAL.value}
            )

            in_use_count = self.mongo_client.count_documents(
                self.collection_name,
                {"status": TokenStatus.IN_USE.value}
            )

            limited_count = self.mongo_client.count_documents(
                self.collection_name,
                {"status": TokenStatus.ACCESS_LIMITED.value}
            )

            expired_count = self.mongo_client.count_documents(
                self.collection_name,
                {"status": TokenStatus.LOGIN_EXPIRED.value}
            )

            return {
                "total_tokens": total_count,
                "normal_tokens": normal_count,
                "in_use_tokens": in_use_count,
                "limited_tokens": limited_count,
                "expired_tokens": expired_count
            }

        except Exception as e:
            logger.error(f"获取状态信息时发生错误: {e}")
            return {}


if __name__ == "__main__":
    mongo = MongoDBClient(
        uri="******************************************",
        db_name="luhao-prod"
    )

    # 初始化Token池
    token_pool = SimpleTokenPool(mongo, "wzry_token_pool")
    token = token_pool.get_next_token()
    print(token)