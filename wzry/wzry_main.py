import datetime
import json
import multiprocessing
import os
import queue
import random
import threading
import time
import traceback
from contextlib import contextmanager
from pathlib import Path

from common import common_utils
from common import oss_util
from common.blacklist_util import KejinBlacklistAPI
from common.configs import current_config
from common.crypto_util import AESCryptoUtil
from common.luhao_models import TaskEvent, EventStatus
from common.server_client import PortalClient
from wzry import wzry_image_util
from wzry.wzry_client2 import WzryClient
from wzry.wzry_client_with_pool import WzryClientWithPool

#  环境变量，DEVICE_ID
device_id = os.environ.get('DEVICE_ID', '51')

# 获取当前脚本所在目录
script_dir = Path(__file__).resolve().parent


# 排序优先级
# priority_order = {
#     "荣耀典藏": 2,
#     "传说品质": 3,
#     "史诗品质": 4,
#     "限定": 5,
#     "贵族限定": 1,
#     "勇者品质": 6
# }
#
#
# def get_skin_priority(skin):
#     class_types = skin.get("classTypeName", [])
#     # 默认优先级是无类别的最低优先级
#     return min([priority_order.get(class_type, float('inf')) for class_type in class_types], default=float('inf'))


def save_to_json_file(data, file_path):
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"[INFO] Data successfully written to file: {file_path}")
    except Exception as error:
        print(f"[ERROR] Error saving data to file: {error}")


aes_util = AESCryptoUtil("aEUAzI8eCEgdo02pfkLY+w==")


def to_int(value):
    try:
        return int(float(str(value)))  # 先转字符串，再转浮点数，最后转整数
    except (ValueError, TypeError):
        return 0


def get_complete_skin_hero_names(skin_list):
    hero_dict = {}

    for skin in skin_list:
        hero_id = skin["heroId"]
        hero_name = skin["heroName"]
        total_skins = skin["skinNum"]

        if hero_id not in hero_dict:
            hero_dict[hero_id] = {
                "heroName": hero_name,
                "totalSkins": total_skins,
                "ownedSkins": 1  # 第一次出现，已有皮肤数为1
            }
        else:
            hero_dict[hero_id]["ownedSkins"] += 1

    # 过滤出全皮英雄，并仅返回英雄名称
    complete_hero_names = [
        hero["heroName"] for hero in hero_dict.values()
        if hero["ownedSkins"] == hero["totalSkins"]
    ]

    return complete_hero_names


def filter_and_format_complete_heroes(complete_hero_names, input_json):
    """
    根据实际拥有的全皮英雄列表，过滤并格式化 input_json
    返回格式如：法师|小乔全皮,法师|西施全皮
    """
    formatted_entries = []

    for category in json.loads(input_json):
        position = category["p"]
        # 遍历该分类下的全皮英雄条目（例如 "小乔全皮"）
        for hero_entry in category["c"]:
            # 从条目中提取英雄名称（去掉"全皮"后缀）
            hero_name = hero_entry.replace("全皮", "").strip()
            # 检查该英雄是否在实际拥有的全皮列表中
            if hero_name in complete_hero_names:
                # 保留原始条目（带"全皮"后缀）
                # formatted_entry = f"{position}|{hero_entry}"
                formatted_entry = f"{hero_entry}"
                formatted_entries.append(formatted_entry)

    # 用逗号拼接所有符合条件的条目
    return ",".join(formatted_entries) if formatted_entries else ""


# Worker类，用于执行录号任务，并通过队列接收指令
class Worker(multiprocessing.Process):
    def __init__(self, task, result_queue, capture_result_queue, params):
        super().__init__()
        self.result_queue = result_queue  # 用于返回结果。 任务进度通过result_queue返回master，master进入后续的处理
        # 清空result_queue
        while not self.result_queue.empty():
            self.result_queue.get()
        self.capture_result_queue = capture_result_queue  # 用于监听抓包结果的队列
        self.current_task = task  # 存储当前任务的信息
        self.params = params

        self.portal_client = PortalClient()
        self.daemon = True  # 守护进程
        # 延迟初始化wzry_client，避免pickle序列化问题
        self.wzry_client = None

    def _init_wzry_client(self):
        """
        在Worker进程中初始化wzry_client，避免pickle序列化问题
        """
        if self.wzry_client is None:
            try:
                self.wzry_client = WzryClientWithPool()
                print("Worker进程中wzry_client初始化成功")
            except Exception as e:
                print(f"Worker进程中wzry_client初始化失败: {e}")
                raise

    def run(self):
        try:
            # 在Worker进程中初始化wzry_client
            self._init_wzry_client()

            result = self.execute_long_task(device_id=device_id)
            if result['status'] == EventStatus.SUCCESS:
                obj = result.get('data')

                role_job_name = obj.get('roleJobName')
                if role_job_name:
                    role_job_name = role_job_name[:4]
                glory_collection = 0
                skin_names = []
                skin_ids = []

                # sorted_skin_list = sorted(obj['skinList'], key=get_skin_priority)
                sorted_skin_list = obj['skinList']

                for skin in sorted_skin_list:
                    skin_names.append(skin['skinName'])
                    skin_ids.append(skin['skinId'])
                    if '荣耀典藏' in skin['classTypeName']:
                        glory_collection = glory_collection + 1

                product_meta = self.portal_client.get_product_category_meta(82)
                account_type = '*'
                noble_level = '*'
                real_name_type = '*'

                obj['allAttrNames'] = [
                    '珍品无双',
                    '珍品传说',
                    '无双皮肤',
                    '荣耀典藏皮肤',
                    '传说皮肤',
                    '史诗皮肤',
                    '年限皮肤',
                    '内测皮肤',
                    '其他',
                ]

                for item in product_meta:
                    if item['name'] == '营地ID':
                        item['values'] = [obj.get('encrypted_user_id')]
                    elif item['name'] == '段位':
                        if role_job_name in item['inputList']:
                            item['values'] = [role_job_name]
                        elif role_job_name == '秩序白银':
                            item['values'] = ['秩序白银']
                        elif role_job_name == '倔强青铜':
                            item['values'] = ['倔强青铜']

                    elif item['type'] == 2:
                        if item['name'] in ['限定天幕', '大国标', '小国标', '星元皮肤', '小兵皮肤']:
                            continue
                        input_list = item['inputList'].split(',')
                        # 英雄
                        if item['name'] in ['英雄', '稀有英雄']:
                            for target in input_list:
                                heros = obj.get('heroList', [])
                                for hero in heros:
                                    if target == hero['name']:
                                        if item.get('values') is None:
                                            item['values'] = []
                                        item['values'].append(target)
                            continue
                        # 全皮
                        # if item['name'] == '全皮英雄':
                        #     complete_hero_names = get_complete_skin_hero_names(obj.get('skinList'))
                        #     print('全皮英雄', complete_hero_names)
                        #
                        #     # 新增代码：过滤并格式化 input_list
                        #     formatted_result = filter_and_format_complete_heroes(
                        #         complete_hero_names=complete_hero_names,
                        #         input_json=item['inputList']
                        #     )
                        #
                        #     print("格式化结果:", formatted_result)  # 示例输出：法师|小乔全皮,法师|西施全皮
                        #     item['values'] = [formatted_result]
                        #
                        #     continue
                        for skin in sorted_skin_list:
                            for target in input_list:
                                if skin['skinName'].replace('·', '').replace(' ', '') in target and skin[
                                    'heroName'] in target:  # 包含在选项值中，并且英雄也要对应
                                    if item.get('values') is None:
                                        item['values'] = []
                                    item['values'].append(target)
                                    # 皮肤SKU分类
                                    # attrName
                                    for obj_skin in obj.get('skinList'):
                                        if obj_skin['skinName'].replace('·', '').replace(' ', '') in target and \
                                                obj_skin[
                                                    'heroName'] in target:
                                            if obj_skin.get('attrNames'):
                                                obj_skin['attrNames'].append(item['name'])
                                            else:
                                                obj_skin['attrNames'] = [item['name']]


                    elif item['type'] == 1:
                        if item['name'] == '传说皮肤数量':
                            item['values'] = [obj.get('skinCountInfo').get('ownedSkinTypeCount').get('legend', '0')]
                        elif item['name'] == '史诗皮肤数量':
                            item['values'] = [obj.get('skinCountInfo').get('ownedSkinTypeCount').get('epic', '0')]
                        elif item['name'] == '皮肤数量':
                            item['values'] = [obj.get('skinCountInfo').get('owned', '0')]
                        elif item['name'] == '皮肤价值':
                            item['values'] = [obj.get('skinCountInfo').get('totalValue', '0')]
                        elif item['name'] == '英雄数量':
                            item['values'] = [str(len(obj.get('heroList')))]

                for obj_skin in obj.get('skinList'):
                    if obj_skin.get('attrNames'):
                        # 其他
                        for attr in obj_skin['attrNames']:
                            if attr not in obj['allAttrNames']:
                                # 改成其他
                                obj_skin['attrNames'].remove(attr)
                                obj_skin['attrNames'].append('其他')
                        # 值去重
                        obj_skin['attrNames'] = list(set(obj_skin['attrNames']))
                    # 名称规范化
                    obj_skin['skinName'] = obj_skin['skinName'].replace('·', '').replace(' ', '')

                now = datetime.datetime.now()
                filename = f"result-{now.strftime('%Y%m%d-%H%M%S')}.json"
                save_to_json_file(obj, filename)
                oss_file_name = oss_util.upload_file_to_oss(filename, 'json', 'mall/statics/wzry/')
                data_url = current_config.image_server_url + oss_file_name
                for item in product_meta:
                    if item['name'] == '账号元数据':
                        item['values'] = [data_url]

                # TODO 20250324 兼容线上前端代码，暂时也传营地ID的json。正式上线后去掉
                self.wzry_client.add_wzry_detail(obj)

                product = self.current_task['product_info']

                text_dict = {
                    '账号编号': product.get('product').get('productSn'),
                    '区服信息': '',
                    '实名情况': '',
                    '防沉迷': '',
                    '国标数量': '0',
                    '游戏段位': '',
                    '皮肤价值': obj.get('skinCountInfo').get('totalValue', '0')
                }

                # national_flag_count = 0
                attr_list = product.get('productAttributeValueList')
                noble_level = 0
                big_nationals = []
                small_nationals = []
                sheng_biao_list = []

                # 定义防沉迷系统的有效值列表
                valid_anti_addiction_values = ["有防沉迷", "无防沉迷"]
                anti_addiction_found = False  # 标记是否找到防沉迷系统属性

                # 用户填写的数据
                for attr in attr_list:
                    if attr.get('productAttributeName') == '账号类型':
                        text_dict['区服信息'] = attr.get('value')
                    elif attr.get('productAttributeName') == '贵族等级':
                        noble_level = attr.get('value')
                        if len(noble_level) >= 3:
                            if '无双' in noble_level:
                                noble_level = 11
                            elif '荣耀' in noble_level:
                                noble_level = 12
                            else:
                                noble_level = noble_level[2:]
                    elif attr.get('productAttributeName') == '实名类型':
                        text_dict['实名情况'] = attr.get('value')
                    # elif attr.get('productAttributeName') == '段位':
                    #     text_dict['游戏段位'] = attr.get('value')
                    elif attr.get('productAttributeName') == '防沉迷系统':
                        anti_addiction_found = True
                        user_value = attr.get('value')
                        # 验证用户传入的防沉迷系统值是否有效
                        if user_value in valid_anti_addiction_values:
                            text_dict['防沉迷'] = user_value
                            # 同时更新 product_meta 中的对应项目
                            for item in product_meta:
                                if item['name'] == '防沉迷系统':
                                    item['values'] = [user_value]
                                    break
                        else:
                            # 如果用户传入的值无效，设置为默认值
                            text_dict['防沉迷'] = "无防沉迷"
                            # 同时更新 product_meta 中的对应项目
                            for item in product_meta:
                                if item['name'] == '防沉迷系统':
                                    item['values'] = ["无防沉迷"]
                                    break
                            print(f"[WARNING] 防沉迷系统属性值 '{user_value}' 无效，已设置为默认值 '无防沉迷'")
                    elif attr.get('productAttributeName') == '大国标':
                        value = attr.get('value')
                        if value:
                            big_nationals = value.split(',')
                            # national_flag_count += len(big_nationals)
                            # 用户填的数据回填metadata
                            for item in product_meta:
                                if item['name'] == '大国标':
                                    item['values'] = big_nationals
                    elif attr.get('productAttributeName') == '小国标':
                        value = attr.get('value')
                        if value:
                            small_nationals = value.split(',')
                            # national_flag_count += len(small_nationals)
                            for item in product_meta:
                                if item['name'] == '小国标':
                                    item['values'] = small_nationals
                    elif attr.get('productAttributeName') == '省标':
                        value = attr.get('value')
                        if value:
                            sheng_biao_list = value.split(',')
                            for item in product_meta:
                                if item['name'] == '省标':
                                    item['values'] = sheng_biao_list

                # 检查是否缺少防沉迷系统属性，如果没有找到则设置默认值
                if not anti_addiction_found:
                    text_dict['防沉迷'] = "无防沉迷"
                    # 同时更新 product_meta 中的对应项目
                    for item in product_meta:
                        if item['name'] == '防沉迷系统':
                            item['values'] = ["无防沉迷"]
                            break
                    print("[INFO] 用户未传入防沉迷系统属性，已设置默认值 '无防沉迷'")

                # 最终确保 product_meta 中防沉迷系统属性有值（双重保险）
                # for item in product_meta:
                #     if item['name'] == '防沉迷系统':
                #         # 如果 values 字段为空或不存在，设置默认值
                #         if not item.get('values'):
                #             item['values'] = ["无防沉迷"]
                #             print("[INFO] product_meta 中防沉迷系统属性缺少默认值，已设置为 ['无防沉迷']")
                #         break

                # text_dict['国标数量'] = str(national_flag_count)
                text_dict['游戏段位'] = role_job_name

                nums_dict = {
                    '英雄数量': len(obj.get('heroList')),
                    '典藏数量': int(glory_collection),
                    '传说数量': int(obj.get('skinCountInfo').get('ownedSkinTypeCount').get('legend', 0)),
                    '史诗数量': int(obj.get('skinCountInfo').get('ownedSkinTypeCount').get('epic', 0)),
                    '皮肤总数': int(obj.get('skinCountInfo').get('owned', 0)),
                    '贵族等级': to_int(noble_level),
                }

                print('生成详情图...')
                skin_group = {
                    '大国标': [],
                    '小国标': [],
                    # '稀有英雄': [],
                    '珍品无双': [],
                    '珍品传说': [],
                    '无双皮肤': [],
                    '荣耀典藏': [],
                    '传说皮肤': [],
                    '史诗皮肤': [],
                    '年限皮肤': [],
                    '内测皮肤': [],
                    # '限定皮肤': []
                    '其他': [],
                }
                # 防止重复
                added_skins = {
                    '大国标': set(),
                    '小国标': set(),
                    # '稀有英雄': set(),
                    '珍品无双': set(),
                    '珍品传说': set(),
                    '无双皮肤': set(),
                    '荣耀典藏': set(),
                    '传说皮肤': set(),
                    '史诗皮肤': set(),
                    '年限皮肤': set(),
                    '内测皮肤': set(),
                    # '限定皮肤': set(),
                    '其他': set(),
                }

                cover_images = []
                cover_images_limit = 12

                # 前面录号数据metadata
                for item in product_meta:
                    if item['name'] == '珍品无双':
                        values = item.get('values', [])
                        for hero_skin in values:
                            skin_id = wzry_image_util.get_skin_id_by_skin_name(hero_skin)
                            if skin_id:
                                skin_group['珍品无双'].append(
                                    {'image': f'{script_dir}/skin/{str(skin_id)}.jpeg', 'name': hero_skin})
                                added_skins['珍品无双'].add(hero_skin)  # 标记为已添加
                            else:
                                print(f'WARNING, get skinId error, hero_skin:[{hero_skin}]')

                    if item['name'] == '珍品传说':
                        values = item.get('values', [])
                        for hero_skin in values:
                            skin_id = wzry_image_util.get_skin_id_by_skin_name(hero_skin)
                            if skin_id:
                                skin_group['珍品传说'].append(
                                    {'image': f'{script_dir}/skin/{str(skin_id)}.jpeg', 'name': hero_skin})
                                added_skins['珍品传说'].add(hero_skin)  # 标记为已添加
                            else:
                                print(f'WARNING, get skinId error, hero_skin:[{hero_skin}]')

                    if item['name'] == '无双皮肤':
                        values = item.get('values', [])
                        for hero_skin in values:
                            skin_id = wzry_image_util.get_skin_id_by_skin_name(hero_skin)
                            if skin_id:
                                skin_group['无双皮肤'].append(
                                    {'image': f'{script_dir}/skin/{str(skin_id)}.jpeg', 'name': hero_skin})
                                added_skins['无双皮肤'].add(hero_skin)  # 标记为已添加
                            else:
                                print(f'WARNING, get skinId error, hero_skin:[{hero_skin}]')

                    if item['name'] == '荣耀典藏皮肤':
                        values = item.get('values', [])
                        for hero_skin in values:
                            skin_id = wzry_image_util.get_skin_id_by_skin_name(hero_skin)
                            if skin_id:
                                # 使用新的图片路径获取函数，支持自动下载
                                img_path = wzry_image_util.get_skin_image_path(skin_id, auto_download=True)
                                if img_path:
                                    skin_group['荣耀典藏'].append(
                                        {'image': img_path, 'name': hero_skin})
                                    added_skins['荣耀典藏'].add(hero_skin)  # 标记为已添加
                                else:
                                    print(f'WARNING, 图片获取失败, hero_skin:[{hero_skin}], skin_id:[{skin_id}]')
                            else:
                                print(f'WARNING, get skinId error, hero_skin:[{hero_skin}]')

                    if item['name'] == '传说皮肤':
                        values = item.get('values', [])
                        for hero_skin in values:
                            # 特殊处理：传说和原皮都叫齐天大圣，这里取传说
                            if hero_skin == '孙悟空齐天大圣':
                                skin_id = 16709
                            else:
                                skin_id = wzry_image_util.get_skin_id_by_skin_name(hero_skin)
                            if skin_id:
                                # 使用新的图片路径获取函数，支持自动下载
                                img_path = wzry_image_util.get_skin_image_path(skin_id, auto_download=True)
                                if img_path:
                                    skin_group['传说皮肤'].append(
                                        {'image': img_path, 'name': hero_skin})
                                    added_skins['传说皮肤'].add(hero_skin)  # 标记为已添加
                                else:
                                    print(f'WARNING, 图片获取失败, hero_skin:[{hero_skin}], skin_id:[{skin_id}]')
                            else:
                                print(f'WARNING, get skinId error, hero_skin:[{hero_skin}]')

                    if item['name'] == '史诗皮肤':
                        values = item.get('values', [])
                        for hero_skin in values:
                            skin_id = wzry_image_util.get_skin_id_by_skin_name(hero_skin)
                            if skin_id:
                                # 使用新的图片路径获取函数，支持自动下载
                                img_path = wzry_image_util.get_skin_image_path(skin_id, auto_download=True)
                                if img_path:
                                    skin_group['史诗皮肤'].append(
                                        {'image': img_path, 'name': hero_skin})
                                    added_skins['史诗皮肤'].add(hero_skin)  # 标记为已添加
                                else:
                                    print(f'WARNING, 图片获取失败, hero_skin:[{hero_skin}], skin_id:[{skin_id}]')
                            else:
                                print(f'WARNING, get skinId error, hero_skin:[{hero_skin}]')

                    if item['name'] == '年限皮肤':
                        values = item.get('values', [])
                        for hero_skin in values:
                            skin_id = wzry_image_util.get_skin_id_by_skin_name(hero_skin)
                            if skin_id:
                                # 使用新的图片路径获取函数，支持自动下载
                                img_path = wzry_image_util.get_skin_image_path(skin_id, auto_download=True)
                                if img_path:
                                    skin_group['年限皮肤'].append(
                                        {'image': img_path, 'name': hero_skin})
                                    added_skins['年限皮肤'].add(hero_skin)  # 标记为已添加
                                else:
                                    print(f'WARNING, 图片获取失败, hero_skin:[{hero_skin}], skin_id:[{skin_id}]')
                            else:
                                print(f'WARNING, get skinId error, hero_skin:[{hero_skin}]')

                    if item['name'] == '内测皮肤':
                        values = item.get('values', [])
                        for hero_skin in values:
                            skin_id = wzry_image_util.get_skin_id_by_skin_name(hero_skin)
                            if skin_id:
                                # 使用新的图片路径获取函数，支持自动下载
                                img_path = wzry_image_util.get_skin_image_path(skin_id, auto_download=True)
                                if img_path:
                                    skin_group['内测皮肤'].append(
                                        {'image': img_path, 'name': hero_skin})
                                    added_skins['内测皮肤'].add(hero_skin)  # 标记为已添加
                                else:
                                    print(f'WARNING, 图片获取失败, hero_skin:[{hero_skin}], skin_id:[{skin_id}]')
                            else:
                                print(f'WARNING, get skinId error, hero_skin:[{hero_skin}]')

                    if item['name'] == '大国标':
                        values = item.get('values', [])
                        for meta_hero in values:
                            # 统一替换成英文括号
                            meta_hero = meta_hero.replace('（', '(').replace('）', ')')
                            for hero in obj.get('heroList'):
                                if hero.get('name') in meta_hero and meta_hero not in added_skins['大国标']:
                                    skin_group['大国标'].append(
                                        {'image': f'{script_dir}/hero/{hero.get("heroId")}.jpeg',
                                         'name': meta_hero})
                                    added_skins['大国标'].add(meta_hero)

                    elif item['name'] == '小国标':
                        values = item.get('values', [])
                        for meta_hero in values:
                            for hero in obj.get('heroList'):
                                if hero.get('name') in meta_hero and meta_hero not in added_skins['小国标']:
                                    skin_group['小国标'].append(
                                        {'image': f'{script_dir}/hero/{hero.get("heroId")}.jpeg',
                                         'name': meta_hero})
                                    added_skins['小国标'].add(meta_hero)

                for item in product_meta:
                    if item['name'] == '珍品无双皮肤数量':
                        item['values'] = [len(skin_group['珍品无双'])]
                    elif item['name'] == '珍品皮肤数量':
                        item['values'] = [len(skin_group['珍品传说'])]
                    elif item['name'] == '无双皮肤数量':
                        item['values'] = [len(skin_group['无双皮肤'])]
                    elif item['name'] == '典藏皮肤数量':
                        item['values'] = [len(skin_group['荣耀典藏'])]
                    elif item['name'] == '大国标数量':
                        item['values'] = [len(skin_group['大国标'])]
                    elif item['name'] == '小国标数量':
                        item['values'] = [len(skin_group['小国标'])]
                    elif item['name'] == '省标数量':
                        item['values'] = [len(sheng_biao_list)]

                all_skins = set().union(*added_skins.values())
                print(all_skins)

                # 其他皮肤
                for skin in sorted_skin_list:
                    skin_name = skin['skinName']
                    hero_name = skin['heroName']
                    hero_skin = hero_name + skin_name
                    if hero_skin not in all_skins:
                        skin_id = wzry_image_util.get_skin_id_by_skin_name_hero_name(skin_name, hero_name)
                        if skin_id:
                            # 使用新的图片路径获取函数，支持自动下载
                            img_path = wzry_image_util.get_skin_image_path(skin_id, auto_download=True)
                            if img_path:
                                skin_group['其他'].append(
                                    {'image': img_path, 'name': skin_name})
                                added_skins['其他'].add(skin_name)
                            else:
                                print(f'WARNING, 图片获取失败, skin_name:[{skin_name}], skin_id:[{skin_id}]')
                        else:
                            print(f'WARNING, get skinId error, skin_name:[{skin_name}]')

                detail_text_dict = {
                    '编号': text_dict['账号编号'],
                    '区服': text_dict['区服信息'],
                    '贵族等级': str(noble_level) + '级',
                    '英雄数量': str(nums_dict['英雄数量']),
                    '实名情况': text_dict['实名情况'],
                    '皮肤数量': str(nums_dict['皮肤总数']),
                    '国标数量': str(len(skin_group['大国标']) + len(skin_group['小国标'])),
                    '荣耀典藏': str(nums_dict["典藏数量"]),
                }

                text_dict['国标数量'] = detail_text_dict['国标数量']

                # 移除值为空列表的键
                skin_group = {k: v for k, v in skin_group.items() if v}

                # 批量预加载可能缺失的图片
                skin_ids_to_preload = []
                hero_ids_to_preload = []

                for group_name, skin_list in skin_group.items():
                    for skin_item in skin_list:
                        if 'skin/' in skin_item['image']:
                            # 从路径中提取skin_id
                            try:
                                skin_id = skin_item['image'].split('/')[-1].replace('.jpeg', '')
                                skin_ids_to_preload.append(skin_id)
                            except:
                                pass
                        elif 'hero/' in skin_item['image']:
                            # 从路径中提取hero_id
                            try:
                                hero_id = skin_item['image'].split('/')[-1].replace('.jpeg', '')
                                hero_ids_to_preload.append(hero_id)
                            except:
                                pass

                # 批量确保图片存在
                if skin_ids_to_preload or hero_ids_to_preload:
                    print(f"[ImageCache] 开始批量预加载图片: {len(skin_ids_to_preload)} 个皮肤, {len(hero_ids_to_preload)} 个英雄")
                    wzry_image_util.batch_ensure_images(skin_ids_to_preload, hero_ids_to_preload)

                detail = wzry_image_util.create_wzry_detail(skin_group, detail_text_dict)

                # 封面
                for group in skin_group:
                    if len(cover_images) >= cover_images_limit:
                        break
                    if '国标' not in group:
                        for skin in skin_group[group]:
                            if len(cover_images) < cover_images_limit:
                                cover_images.append({
                                    'url': skin['image'],
                                    'name': skin['name']
                                })
                            else:
                                break

                while len(cover_images) < 12:
                    cover_images.append(None)

                national_added = 0
                for name in big_nationals:
                    name = name.replace('（', '(').replace('）', ')')
                    for hero in obj.get('heroList'):
                        if hero.get('name') in name and national_added < 2:
                            cover_images.append({
                                'url': f'{script_dir}/hero/{hero.get("heroId")}.jpeg',
                                'name': name
                            })
                            national_added += 1

                # 如果 big_national_added 长度小于 2，则补充默认图片
                while national_added < 2:  # 确保 images 列表中有至少两张图片
                    cover_images.append(None)
                    national_added += 1

                for name in small_nationals:
                    for hero in obj.get('heroList', []):  # 使用 get('heroList', []) 避免 KeyError
                        hero_id = hero.get("heroId")
                        if not hero_id:  # 如果 heroId 不存在，跳过
                            continue

                        if hero.get('name') in name and national_added < 4:
                            # 使用新的图片路径获取函数，支持自动下载
                            image_path = wzry_image_util.get_hero_image_path(hero_id, auto_download=True)

                            if not image_path:
                                print(f"跳过：英雄图片获取失败 - hero_id: {hero_id}, name: {name}")
                                continue

                            # 图片存在，添加到 images 列表
                            cover_images.append({
                                'url': image_path,
                                'name': name
                            })

                print('生成封面图...')
                cover = wzry_image_util.create_wzry_cover2(cover_images, text_dict, nums_dict)

                print('同步账号信息...')
                self.portal_client.sync_product_info2(self.current_task['productSn'],
                                                      current_config.image_server_url + cover,
                                                      [current_config.image_server_url + cover,
                                                       current_config.image_server_url + detail], product_meta,
                                                      productAttributeCategoryId=13)

                self.result_queue.put(
                    TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FINISHED))
            elif result['status'] == EventStatus.RETRYING:
                self.result_queue.put(
                    TaskEvent(self.current_task['id'], stage=0, status=EventStatus.RETRYING, msg=result['msg']))
            elif result['status'] == EventStatus.CANCELLED:
                self.result_queue.put(
                    TaskEvent(self.current_task['id'], stage=0, status=EventStatus.CANCELLED, msg=result['msg']))
            else:
                # FAILURE 同步失败原因到审核说明
                self.result_queue.put(
                    TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FAILURE, msg=result['msg']))

        except Exception as e:
            traceback.print_exc()
            self.result_queue.put(
                TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FAILURE))
        finally:
            # 确保在任务完成或异常时释放Token
            try:
                if hasattr(self, 'wzry_client') and self.wzry_client:
                    self.wzry_client.cleanup_and_release_token()
                    print("Worker任务完成，Token已释放")
            except Exception as e:
                print(f"Worker释放Token时发生错误: {e}")

            self.current_task = None  # 任务完成后清除任务信息

    # 耗时任务的具体实现
    def execute_long_task(self, device_id=None):
        product_category_id = self.current_task.get('productCategoryId')
        metadata = self.portal_client.get_product_category_meta(product_category_id)
        self.current_task['product_meta'] = metadata
        product = self.portal_client.get_product_detail(self.current_task['productId'])
        #  检查状态
        if product['product']['publishStatus'] == -2:
            self.result_queue.put(
                TaskEvent(self.current_task['id'], stage=0, status=EventStatus.CANCELLED, msg='用户已下架该商品'))
            return {
                'status': EventStatus.CANCELLED,
                'msg': '用户已下架该商品'
            }

        self.current_task['product_info'] = product

        attr_list = product.get('productAttributeValueList')
        print('###### product: ', product)
        print('###### attr_list: ', attr_list)

        # 233王者估号
        if product_category_id == 82 or product_category_id == 233:
            for attr in attr_list:
                if attr.get('productAttributeName') == '营地ID':
                    self.current_task['uid'] = attr.get('value')
                    if not self.current_task['uid'].isdigit():
                        self.current_task['uid'] = aes_util.decrypt(self.current_task['uid'])

        uid = self.current_task['uid']

        # todo 如果该营地id一周内录过号，就不再录号，只更新时间和用户传的属性

        # 记录营地ID
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.current_task['startTime'] = current_time
        task = {
            'id': self.current_task['id'],
            'uid': uid,
            'status': 'PENDING',
            'taskStartTime': current_time
        }
        self.portal_client.sync_task_info(task)
        try:
            # 确保wzry_client已初始化
            if self.wzry_client is None:
                self._init_wzry_client()

            # 使用集成Token池的客户端，不再需要device_id参数
            user_result = self.wzry_client.get_target_user_info(uid)
            if user_result.is_ok():
                data = user_result.data
                return {
                    'status': EventStatus.SUCCESS,
                    'data': data
                }
            elif user_result.need_retry():
                # 最多重试 3 次
                if self.current_task['retryCount'] < 3:
                    self.current_task['retryCount'] += 1
                    self.current_task['status'] = 'PENDING'

                    # 获取当前 deviceId（如果有）
                    current_device_id = self.current_task.get('deviceId', None)

                    # 创建可选的设备列表（排除当前设备）
                    available_devices = [d for d in current_config.retry_device_list if d != current_device_id]

                    # 从可选设备中随机选择
                    if available_devices:  # 确保列表不为空
                        device_id = random.choice(available_devices)
                        self.current_task['deviceId'] = device_id
                        self.current_task['msg'] = (
                            f"账号数据获取异常，正在重试（{self.current_task['retryCount']}/3）。当前设备: {current_device_id} → 切换至: {device_id}"
                        )
                    self.portal_client.sync_task_info(self.current_task)
                    # return {
                    #     'status': EventStatus.RETRYING,
                    #     'msg': '账号数据获取异常'
                    # }
                    # todo 有bug，暂时关闭重试
                    return {
                        'status': EventStatus.FAILURE,
                        'msg': '账号数据获取异常'
                    }

                print('超过最大重试次数')

                return {
                    'status': EventStatus.FAILURE,
                    'msg': '账号数据获取异常'
                }
            else:
                return {
                    'status': EventStatus.FAILURE,
                    'msg': user_result.msg
                }
        except Exception as e:
            traceback.print_exc()
            return {
                'status': EventStatus.FAILURE,
                'data': None
            }

    def execute_task_stage(self, stage_function, stage, param=0):
        with self.time_logger(stage):  # 开始计时

            try:
                self.result_queue.put(TaskEvent(task_id=self.current_task['id'],
                                                stage=stage,
                                                status=EventStatus.IN_PROGRESS,
                                                snapshot=None,
                                                data=None,
                                                msg=None))

                stage_event = stage_function(param)
                self.result_queue.put(stage_event)
            except Exception as e:
                # self.log_queue.put(f"【worker】任务出错: {e}")
                self.handle_failure(self.current_task, stage)
                traceback.print_exc()
                return False  # 表示失败
        return True  # 表示成功

    def handle_failure(self, task, stage):
        step_login_event = TaskEvent(task['id'], stage=stage, status=EventStatus.FAILURE)
        self.result_queue.put(step_login_event)

    @contextmanager
    def time_logger(self, stage):
        start_time = time.time()  # 记录开始时间
        # self.log_queue.put(f"开始执行阶段: {stage}")
        try:
            yield  # 执行阶段操作
        finally:
            end_time = time.time()  # 记录结束时间
            duration = end_time - start_time  # 计算耗时
            # self.log_queue.put(f"阶段 {stage} 耗时: {duration:.2f} 秒")


# Master类，发送任务指令，管理Worker，并处理心跳
class Master:
    def __init__(self, status_queue, capture_result_queue, device_addr=None):
        self.task_queue = multiprocessing.Queue()
        self.result_queue = multiprocessing.Queue()
        self.capture_result_queue = capture_result_queue

        self.worker = None
        self.running = True
        self.check_flag = True
        self.portal_client = PortalClient()
        self.last_instruction = None
        self.test_queue = queue.Queue()  # 测试队列

        # self.gui_queue = queue.Queue()
        self.status_queue = status_queue
        self.device_id = device_id
        self.black_list_api = KejinBlacklistAPI()

    # 启动Worker
    def start_worker(self, task):
        if self.worker is None or not self.worker.is_alive():
            print("【master】启动新任务...")
            # capture_result_queue = multiprocessing.Queue()
            while not self.capture_result_queue.empty():
                self.capture_result_queue.get()
            self.worker = Worker(task, self.result_queue, self.capture_result_queue, None)
            self.worker.start()

    def stop_worker_now(self, is_canceled=False):
        if self.worker is not None:
            print("【master】停止worker...")
            try:
                task_id = self.worker.current_task['id']

                # 在停止worker前，尝试清理Token（如果worker还活着的话）
                try:
                    if hasattr(self.worker, 'wzry_client') and self.worker.wzry_client:
                        self.worker.wzry_client.cleanup_and_release_token()
                        print("【master】Worker Token已清理")
                except Exception as e:
                    print(f"【master】清理Worker Token时出错: {e}")

                self.worker.terminate()  # 立即停止
                time.sleep(2)
                self.worker.kill()
                self.worker = None
                print("【master】停止worker成功")
                self.status_queue.put({'status': '任务已停止'})
                if is_canceled:
                    task = {
                        'id': task_id,
                        'status': 'CANCELLED',
                        'msg': '取消任务'
                    }
                    self.portal_client.sync_task_info(task)
                self.check_flag = True
            except Exception as e:
                error_message = f"【worker】停止任务出错: {e}\n{traceback.format_exc()}"
                print(error_message)

    def heartbeat(self):
        while self.running:
            print("heartbeat... ")
            time.sleep(10)
            if self.worker is not None:
                self.send_heartbeat(device_status='IN_PROGRESS')
            else:
                print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} send_heartbeat start...")
                self.send_heartbeat(device_status='IDLE')
                print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} send_heartbeat finish.")
                self.check_new_task()

    def check_worker_result(self):
        # 检查任务状态, 如果worker在执行中，才检查任务状态
        while self.running:
            try:
                if self.worker is None:
                    time.sleep(5)
                    continue
                result = self.result_queue.get_nowait()
                current_task = self.worker.current_task
                if result is None:
                    continue
                if result.status == EventStatus.FINISHED:
                    print('【master】同步录号任务信息...FINISHED')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'COMPLETED',
                        'snapshot': result.snapshot,
                        'productMeta': common_utils.safe_json_dumps(result.data),
                        'msg': result.msg,
                        'propertyBag': '{"state": "login_success"}'
                    }
                    self.portal_client.sync_task_info(task)
                    # 自动审核
                    product_category_id = current_task.get('productCategoryId')
                    if product_category_id == 82:
                        print('自动审核...')

                        verify_status = 1
                        verify_array = []
                        verify_detail = ''

                        is_black = False
                        if current_task['operateMan'] != 'API':
                            # 非号商,查询黑号
                            game_account = current_task['gameAccount']
                            username = current_task['operateMan']  # 用户名
                            user_id_number = None
                            real_name_info = self.portal_client.get_real_name_info(current_task['memberId'])
                            if real_name_info:
                                user_id_number = real_name_info['userIdNumber']
                            kk_black_result = self.portal_client.search_black_user(
                                {'phone': username, 'userIdNumber': user_id_number, 'gameAccount': game_account})
                            if kk_black_result:
                                print(f'黑名单用户: {username}, 看看黑号查询结果： {kk_black_result}')
                                is_black = True
                                verify_status = 2
                                verify_array += kk_black_result

                            if not is_black:
                                # 看看查询正常，继续查询氪金联盟黑名单
                                black_result = self.black_list_api.search('game_account', current_task['gameAccount'])
                                if black_result:
                                    type = f"游戏账号（{current_task['gameAccount']}）"
                                    print(f'黑名单用户: {username}, 查询类型：{type}, 黑号查询结果： {black_result}')
                                    is_black = True
                                    verify_array += black_result

                                if not is_black:
                                    black_result = self.black_list_api.search('game_account', username)
                                    if black_result:
                                        type = f'游戏账号({username})'
                                        print(f'黑名单用户: {username}, 查询类型：{type}, 黑号查询结果： {black_result}')
                                        is_black = True
                                        verify_array += black_result

                                if not is_black:
                                    black_result = self.black_list_api.search('mobile', username)
                                    if black_result:
                                        type = f'手机号({username})'
                                        print(f'黑名单用户: {username}, 查询类型：{type}, 黑号查询结果： {black_result}')
                                        is_black = True
                                        verify_array += black_result

                                if not is_black:
                                    black_result = self.black_list_api.search('mobile', current_task['gameAccount'])
                                    if black_result:
                                        type = f"手机号({current_task['gameAccount']})"
                                        print(f'黑名单用户: {username}, 查询类型：{type}, 黑号查询结果： {black_result}')
                                        is_black = True
                                        verify_array += black_result

                                if not is_black:
                                    # 身份证号查询
                                    if user_id_number:
                                        black_result = self.black_list_api.search('id_no', user_id_number)
                                        if black_result:
                                            type = f'身份证号{user_id_number}'
                                            print(f'黑名单用户: {username}, 查询类型：{type}, 黑号查询结果： {black_result}')
                                            is_black = True
                                            verify_array += black_result
                                # 如果是黑号，更新看看黑号库
                                if is_black:
                                    verify_status = 2
                                    self.portal_client.add_black_user(
                                        {
                                            'phone': username,
                                            'userIdName': real_name_info['userIdName'],
                                            'userIdNumber': user_id_number,
                                            'note': json.dumps(verify_array, ensure_ascii=False)
                                        })
                                    verify_detail = '黑号'

                        self.portal_client.product_auto_verify(current_task['productId'], verify_status,
                                                               verify_detail)
                    elif product_category_id == 233:
                        pass
                        # TODO 执行估价
                        self.portal_client.product_valuation(current_task['productSn'])


                    self.status_queue.put({'status': result.status, 'stage': result.stage})
                    self.stop_worker_now()
                if result.status == EventStatus.SUCCESS:
                    print('【master】同步录号任务信息...SUCCESS')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'IN_PROGRESS',
                        'snapshot': result.snapshot,
                        'productMeta': common_utils.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    product_sn = None
                    if current_task:
                        product_sn = current_task['productSn']
                    self.status_queue.put({'status': result.status, 'stage': result.stage,
                                           'product_sn': product_sn})
                if result.status == EventStatus.RETRYING:
                    print('【master】同步录号任务信息...RETRYING')
                    # task = {
                    #     'id': result.task_id,
                    #     'stage': result.stage,
                    #     'status': 'PENDING',
                    #     'snapshot': result.snapshot,
                    #     # 'productMeta': common_utils.safe_json_dumps(result.data),
                    #     'msg': result.msg
                    # }
                    # self.portal_client.sync_task_info(task)
                    product_sn = None
                    if current_task:
                        product_sn = current_task['productSn']
                    self.status_queue.put(
                        {'status': result.status, 'stage': result.stage, 'product_sn': current_task['productSn']})
                    self.stop_worker_now()
                if result.status == EventStatus.FAILURE:
                    print('【master】同步录号任务信息...FAILURE')
                    print(result.msg)
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'FAILED',
                        'snapshot': result.snapshot,
                        'productMeta': common_utils.safe_json_dumps(result.data),
                        'msg': result.msg,
                        'propertyBag': '{"state": "failure"}'
                    }

                    self.portal_client.sync_task_info(task)
                    # 失败不审核
                    # time.sleep(1)
                    # self.portal_client.product_auto_verify(current_task['productId'], 2, result.msg)
                    self.status_queue.put(
                        {'status': result.status, 'stage': result.stage, 'product_sn': current_task['productSn']})
                    self.stop_worker_now()
                if result.status == EventStatus.CANCELLED:
                    print('【master】同步录号任务信息...CANCELLED')
                    print(result.msg)
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'CANCELLED',
                        'snapshot': result.snapshot,
                        'productMeta': common_utils.safe_json_dumps(result.data),
                        'msg': result.msg,
                        'propertyBag': '{"state": "cancelled"}'
                    }

                    self.portal_client.sync_task_info(task)
                    # 失败不审核
                    # time.sleep(1)
                    # self.portal_client.product_auto_verify(current_task['productId'], 2, result.msg)
                    self.status_queue.put(
                        {'status': result.status, 'stage': result.stage, 'product_sn': current_task['productSn']})
                    self.stop_worker_now()
                if result.status == EventStatus.MANUAL_REQUIRED:
                    print('【master】同步录号任务信息...MANUAL_REQUIRED')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'MANUAL_REQUIRED',
                        'snapshot': result.snapshot,
                        'productMeta': common_utils.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    self.status_queue.put(
                        {'status': result.status, 'stage': result.stage, 'product_sn': current_task['product.Sn']})
                if result.status == EventStatus.IN_PROGRESS:
                    product_sn = None
                    if current_task:
                        product_sn = current_task['productSn']
                    self.status_queue.put({'status': result.status, 'stage': result.stage,
                                           'product_sn': product_sn})
            except queue.Empty:
                time.sleep(5)

    def check_new_task(self):
        if self.running and self.check_flag is True:
            try:
                print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} get_todo_task_from_kk start...")
                task = self.portal_client.get_todo_task_from_kk(device_id)
                print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} get_todo_task_from_kk finish.")

                if task is None:
                    return
                self.check_flag = False

                product_sn = task['productSn']
                print(f"【master】有新任务: {product_sn}")
                self.start_worker(task)
                self.status_queue.put(
                    {'status': EventStatus.IN_PROGRESS, 'stage': None, 'product_sn': product_sn})
            except queue.Empty:
                pass
            except Exception as e:
                print(f"获取任务错误: {e}")
            finally:
                print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 等待新任务")

    def send_heartbeat(self, device_status='IDLE'):
        instruction = {
            'deviceId': self.device_id,
            'deviceStatus': device_status,
            'timestamp': int(round(time.time() * 1000))
        }
        return self.portal_client.send_heartbeat(instruction)

    # 关闭Master
    def shutdown(self):
        self.stop_worker_now()
        self.running = False
        self.send_heartbeat(device_status='OFFLINE')
        print("Master 关闭")


# 主程序
def main(status_queue=None, device_addr=None):
    status_queue = queue.Queue()
    capture_result_queue = multiprocessing.Queue()

    # 启动服务器的线程
    # server_thread = threading.Thread(target=run_server, args=(capture_result_queue,), daemon=True)
    # server_thread.start()

    master = Master(status_queue, capture_result_queue, device_addr)

    if master.device_id is None:
        print(f"【master】device_id is None, exit")
        return None

    # 启动心跳线程
    heartbeat_thread = threading.Thread(target=master.heartbeat, daemon=True)
    heartbeat_thread.start()

    # 检查worker执行结果
    check_worker_result_thread = threading.Thread(target=master.check_worker_result, daemon=True)
    check_worker_result_thread.start()

    heartbeat_thread.join()
    return master


if __name__ == "__main__":
    main()
