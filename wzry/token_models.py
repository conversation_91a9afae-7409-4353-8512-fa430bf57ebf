"""
Token数据模型定义
"""
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional


class TokenStatus(Enum):
    """Token状态枚举"""
    NORMAL = "NORMAL"  # 正常
    IN_USE = "IN_USE"  # 使用中
    ACCESS_LIMITED = "ACCESS_LIMITED"  # 访问限制
    LOGIN_EXPIRED = "LOGIN_EXPIRED"  # 登录失效


class Token:
    """Token数据模型"""

    def __init__(self,
                 token: str,
                 user_id: str,
                 encodeParam: str,
                 gameareaid: str,
                 gameroleid: str,
                 gameserverid: str,
                 gameopenid: str,
                 kohdimgender: str,
                 gameusersex: str,
                 openid: str,
                 comment: str = '',
                 status: TokenStatus = TokenStatus.NORMAL,
                 created_at: Optional[datetime] = None,
                 updated_at: Optional[datetime] = None,
                 _id: Optional[str] = None):
        """
        初始化Token对象

        Args:
            token: Token字符串
            user_id: 用户ID
            encodeParam: 编码参数
            gameareaid: 游戏区域ID
            gameroleid: 游戏角色ID
            gameserverid: 游戏服务器ID
            gameopenid: 游戏OpenID
            kohdimgender: 性别信息
            gameusersex: 游戏用户性别
            openid: OpenID
            comment: 备注信息
            status: Token状态
            created_at: 创建时间
            updated_at: 更新时间
            _id: MongoDB文档ID
        """
        self.token = token
        self.user_id = user_id
        self.encodeParam = encodeParam
        self.gameareaid = gameareaid
        self.gameroleid = gameroleid
        self.gameserverid = gameserverid
        self.gameopenid = gameopenid
        self.kohdimgender = kohdimgender
        self.gameusersex = gameusersex
        self.openid = openid
        self.comment = comment
        self.status = status
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        self._id = _id

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，用于MongoDB存储"""
        return {
            'token': self.token,
            'user_id': self.user_id,
            'encodeParam': self.encodeParam,
            'gameareaid': self.gameareaid,
            'gameroleid': self.gameroleid,
            'gameserverid': self.gameserverid,
            'gameopenid': self.gameopenid,
            'kohdimgender': self.kohdimgender,
            'gameusersex': self.gameusersex,
            'openid': self.openid,
            'comment': self.comment,
            'status': self.status.value,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Token':
        """从字典创建Token对象"""
        return cls(
            token=data['token'],
            user_id=data['user_id'],
            encodeParam=data['encodeParam'],
            gameareaid=data['gameareaid'],
            gameroleid=data['gameroleid'],
            gameserverid=data['gameserverid'],
            gameopenid=data['gameopenid'],
            kohdimgender=data['kohdimgender'],
            gameusersex=data['gameusersex'],
            openid=data['openid'],
            comment=data.get('comment', ''),
            status=TokenStatus(data.get('status', TokenStatus.NORMAL.value)),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at'),
            _id=data.get('_id')
        )

    def __str__(self) -> str:
        return f"Token(user_id={self.user_id}, token={self.token[:8]}..., status={self.status.value})"

    def __repr__(self) -> str:
        return self.__str__()
