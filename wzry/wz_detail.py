import os
from io import BytesIO

from PIL import Image, ImageDraw, ImageFont, ImageOps

from pathlib import Path

# 获取当前脚本所在目录
script_dir = Path(__file__).resolve().parent

def calculate_total_height(skin_group):
    total_height = 0
    for category, skins in skin_group.items():
        # 增加标题高度
        total_height += 200
        # 计算贴图行数
        num_skins = len(skins)
        rows = (num_skins + 6) // 7  # 每行7个，向上取整
        # 增加贴图高度
        if category == '国标':
            total_height += rows * 152
        else:
            total_height += rows * 243

    return total_height


def resize_image_height(img, target_height):
    """
    调整图片高度到目标高度，宽度保持不变，确保颜色模式一致
    :param img: 输入图片（PIL.Image 对象）
    :param target_height: 目标高度（整数）
    :return: 调整高度后的图片（PIL.Image 对象）
    """
    # 获取原始宽度和高度
    width, original_height = img.size

    # 保持宽度不变，仅调整高度
    resized_img = img.resize((width, target_height), Image.Resampling.LANCZOS)

    # 确保调整后的图片颜色模式与原始图片一致
    if resized_img.mode != img.mode:
        resized_img = resized_img.convert(img.mode)

    return resized_img


def extend_image(input_path, new_height):
    # 打开原始图像
    img = Image.open(input_path)
    width, original_height = img.size

    # 检查输入高度合法性
    if new_height <= original_height:
        raise ValueError("新高度必须大于原图高度（1280像素）")
    if original_height != 990 or width != 1280:
        print("警告：输入图像尺寸不是990x1280，继续处理...")

    # 定义扩展区域 (原图880-1280部分)
    top_cut = 880
    bottom_section = img.crop((0, top_cut, width, original_height))

    # 计算需要生成的新区域高度
    extended_section_height = new_height - top_cut

    # 拉伸扩展区域
    extended_section = bottom_section.resize(
        (width, extended_section_height),
        Image.Resampling.LANCZOS  # 高质量插值
    )

    # 创建新图像
    new_img = Image.new('RGB', (width, new_height))

    # 粘贴原始顶部内容 (0-880)
    new_img.paste(img.crop((0, 0, width, top_cut)), (0, 0))

    # 粘贴扩展区域
    new_img.paste(extended_section, (0, top_cut))

    return new_img


def paste_overlay(base_img, overlay_path, overlay_position):
    """
    在底图上粘贴贴图
    :param base_img: 底图（PIL.Image 对象）
    :param overlay_path: 贴图文件路径
    :param overlay_position: 贴图位置 (x, y)
    """
    overlay_img = Image.open(overlay_path)
    # 如果贴图不是 RGBA 模式，则转换为 RGBA 模式
    if overlay_img.mode != 'RGBA':
        overlay_img = overlay_img.convert('RGBA')

    # 粘贴贴图，支持透明贴图
    base_img.paste(overlay_img, overlay_position, overlay_img)


def paste_overlay_resize(base_img, overlay_path, overlay_position, new_width, new_height, corner_radius):
    """
    在底图上粘贴贴图，并支持缩放贴图
    :param base_img: 底图（PIL.Image 对象）
    :param overlay_path: 贴图文件路径
    :param overlay_position: 贴图位置 (x, y)
    :param new_width: 贴图的新宽度
    :param new_height: 贴图的新高度
    """
    # 打开贴图
    overlay_img = Image.open(overlay_path)

    # 如果贴图不是 RGBA 模式，则转换为 RGBA 模式
    if overlay_img.mode != 'RGBA':
        overlay_img = overlay_img.convert('RGBA')

    # 缩放贴图到指定尺寸
    overlay_img = overlay_img.resize((new_width, new_height), Image.LANCZOS)

    # 应用圆角效果
    overlay_img = round_corners(overlay_img, corner_radius)

    # 粘贴贴图，支持透明贴图
    base_img.paste(overlay_img, overlay_position, overlay_img)


def round_corners(image, radius):
    """
    将图片裁剪为圆角效果
    :param image: 输入的图片
    :param radius: 圆角半径
    :return: 圆角图片
    """
    # 创建一个蒙版
    mask = Image.new('L', image.size, 0)
    draw = ImageDraw.Draw(mask)

    # 绘制圆角矩形
    draw.rounded_rectangle([(0, 0), image.size], radius=radius, fill=255)

    # 应用蒙版
    rounded_image = ImageOps.fit(image, mask.size, centering=(0.5, 0.5))
    rounded_image.putalpha(mask)

    return rounded_image


def add_text_to_image(image, text_list, font_path):
    """
    在图片上添加多个文字块
    :param image: 图片对象
    :param text_list: 文字块列表，每个文字块包含文字内容、位置、字体大小、字体颜色等信息
    :param font_path: 字体文件路径
    :return: 添加文字后的图片
    """
    draw = ImageDraw.Draw(image)

    for text_info in text_list:
        text = text_info["text"]
        position = text_info["position"]
        font_size = text_info["font_size"]
        font_color = text_info["font_color"]
        font_weight = text_info.get("font_weight", "normal")
        letter_spacing = text_info.get("letter_spacing", 0)

        # 加载字体
        font = ImageFont.truetype(font_path, font_size)

        # 设置字体样式
        if font_weight == "bold":
            font = ImageFont.truetype(font_path, font_size, encoding="utf-8")

        # 添加文字
        draw.text(position, text, font=font, fill=font_color, spacing=letter_spacing)

    return image


def create_detail_image(skin_group, text_dict, output_path):
    input_image = f"{script_dir}/tpl_detail.png"  # 输入图像路径

    skin_x = [86, 248, 410, 572, 734, 896, 1058]  # 皮肤图的x坐标

    # 计算总高度
    frame_total_height = calculate_total_height(skin_group)
    print(f"皮肤所需高度: {frame_total_height} 像素")

    # 底框-1、底框-3 高度
    base_frame_height = 200 + 200
    # 底部预留高度 90 + 110
    bottom_reserve_height = 110

    # 扩展底框-2
    resize_height = frame_total_height - base_frame_height + bottom_reserve_height
    resize_mid_frame = None
    if resize_height > 0:
        print(f"扩展底框高度: {resize_height} 像素")
        resize_mid_frame = resize_image_height(Image.open(f'{script_dir}/底框-2.png'), resize_height)
        # resize_mid_frame.save('底框-2-resize.png')

    # 底部150
    # 550 + 510 + resize_height
    total_height = 1060 + resize_height
    # 扩展底图
    print(f"总高度: {total_height} 像素")
    extended_img = extend_image(input_image, total_height)

    # 底部花纹
    paste_overlay(extended_img, f"{script_dir}/底部.png", (0, total_height - 300))

    # 底框-1
    paste_overlay(extended_img, f"{script_dir}/底框-1.png", (20, 510))
    # 底框-2
    # paste_overlay(extended_img, f"{script_dir}/底框-2-resize.png", (20, 710))
    # 粘贴贴图，支持透明贴图
    extended_img.paste(resize_mid_frame, (20, 710), resize_mid_frame)

    # 底框-3
    paste_overlay(extended_img, f"{script_dir}/底框-3.png", (20, total_height - 350))

    # 贴标题和皮肤图
    y_offset = 600  # 初始标题位置
    for group_name, skin_list in skin_group.items():
        # 贴标题
        paste_overlay(extended_img, f"{script_dir}/标题底.png", (437, y_offset))
        # 贴标题文字
        # 每个字： 46x55
        #  (384 - 46*len(group_name))/2
        text_list = [
            {
                "text": group_name,
                "position": (437 + (384 - 46 * len(group_name)) / 2, y_offset + 25),
                "font_size": 46,
                "font_color": "#2B56B9",
                "font_weight": "normal"
            }]
        font_path = f"{script_dir}/DouyinSansBold.otf"
        extended_img = add_text_to_image(extended_img, text_list, font_path)

        y_offset += 90 + 20 + 33

        # 贴皮肤图
        row_count = 0

        if group_name != '大国标' and group_name != '小国标':
            for skin_item in skin_list:
                if row_count >= 7:  # 每行7个，换行
                    row_count = 0
                    y_offset += 243  # 皮肤图行高

                paste_overlay(extended_img, f'{script_dir}/占位图.png', (skin_x[row_count], y_offset))
                # 检查图片文件是否存在，如果不存在则使用占位图
                if skin_item['image'] and os.path.exists(skin_item['image']):
                    paste_overlay_resize(extended_img, skin_item['image'], (skin_x[row_count] + 4, y_offset + 4), 136, 210,
                                     10)
                else:
                    print(f"[WzDetail] 皮肤图片不存在，使用占位图: {skin_item.get('image', 'None')}")
                # 蒙层和名字
                paste_overlay(extended_img, f"{script_dir}/蒙层.png", (skin_x[row_count] + 4, y_offset + 160 + 4))
                label_text = {
                    "text": skin_item['name'],
                    # "position": (skin_x[row_count] + 11, y_offset + 160 + 12),
                    "font_size": 14,
                    "font_color": "#DEDEDE",
                    "font_weight": "normal"
                }
                layer = Image.open(f"{script_dir}/蒙层.png").convert("RGBA")

                extended_img = add_text_to_layer_center(extended_img, layer,
                                                        (skin_x[row_count] + 4, y_offset + 160 + 8),
                                                        label_text, font_path)
                row_count += 1

            # 如果当前分类的皮肤图未填满一行，也需要增加高度
            if row_count > 0:
                # 补全剩余位置的默认图
                while row_count < 7:
                    paste_overlay(extended_img, f'{script_dir}/占位图.png', (skin_x[row_count], y_offset))
                    row_count += 1

                y_offset += 210 + 84  # 皮肤高度+边距
        else:
            for skin_item in skin_list:
                if row_count >= 7:  # 每行7个，换行
                    row_count = 0
                    y_offset += 152  # 国标图行高

                paste_overlay(extended_img, f'{script_dir}/占位图1.png', (skin_x[row_count], y_offset))
                # 检查图片文件是否存在，如果不存在则使用占位图
                if skin_item['image'] and os.path.exists(skin_item['image']):
                    paste_overlay_resize(extended_img, skin_item['image'], (skin_x[row_count] + 17, y_offset + 13), 100,
                                         100,
                                         10)
                else:
                    print(f"[WzDetail] 国标图片不存在，使用占位图: {skin_item.get('image', 'None')}")
                row_count += 1

            # 如果当前分类的皮肤图未填满一行，也需要增加高度
            if row_count > 0:
                # 补全剩余位置的默认图
                while row_count < 7:
                    paste_overlay(extended_img, f'{script_dir}/占位图1.png', (skin_x[row_count], y_offset))
                    row_count += 1

                y_offset += 126  + 84  # 皮肤高度+边距(改成国标高度)

    #####
    # 文字内容列表
    text_list = [
        {
            "text": "编号",
            "position": (449, 226),
            "font_size": 24,
            "font_color": "#C7E2FF",
            "font_weight": "normal"
        },
        {
            "text": text_dict['编号'],
            "position": (450, 260),
            "font_size": 30,
            "font_color": "#FFFFFF",
            "font_weight": "normal"
        },
        {
            "text": "区服",
            "position": (668, 226),
            "font_size": 24,
            "font_color": "#C7E2FF",
            "font_weight": "normal"
        },
        {
            "text": text_dict['区服'],
            "position": (669, 260),
            "font_size": 30,
            "font_color": "#FFFFFF",
            "font_weight": "normal"
        },
        {
            "text": "贵族等级",
            "position": (858, 226),
            "font_size": 24,
            "font_color": "#C7E2FF",
            "font_weight": "normal"
        },
        {
            "text": text_dict['贵族等级'],
            "position": (859, 260),
            "font_size": 30,
            "font_color": "#FFFFFF",
            "font_weight": "normal"
        },
        {
            "text": "英雄数量",
            "position": (1048, 226),
            "font_size": 24,
            "font_color": "#C7E2FF",
            "font_weight": "normal"
        },
        {
            "text": text_dict['英雄数量'],
            "position": (1049, 260),
            "font_size": 30,
            "font_color": "#FFFFFF",
            "font_weight": "normal"
        },
        {
            "text": "实名情况",
            "position": (449, 354),
            "font_size": 24,
            "font_color": "#C7E2FF",
            "font_weight": "normal"
        },
        {
            "text": text_dict['实名情况'],
            "position": (450, 388),
            "font_size": 30,
            "font_color": "#FFFFFF",
            "font_weight": "normal"
        },
        {
            "text": "皮肤数量",
            "position": (668, 354),
            "font_size": 24,
            "font_color": "#C7E2FF",
            "font_weight": "normal"
        },
        {
            "text": text_dict['皮肤数量'],
            "position": (669, 388),
            "font_size": 30,
            "font_color": "#FFFFFF",
            "font_weight": "normal"
        },
        {
            "text": "国标数量",
            "position": (858, 354),
            "font_size": 24,
            "font_color": "#C7E2FF",
            "font_weight": "normal"
        },
        {
            "text": text_dict['国标数量'],
            "position": (859, 388),
            "font_size": 30,
            "font_color": "#FFFFFF",
            "font_weight": "normal"
        },
        {
            "text": "荣耀典藏",
            "position": (1048, 354),
            "font_size": 24,
            "font_color": "#C7E2FF",
            "font_weight": "normal"
        },
        {
            "text": text_dict['荣耀典藏'],
            "position": (1049, 388),
            "font_size": 30,
            "font_color": "#FFFFFF",
            "font_weight": "normal"
        }
    ]

    # 字体样式
    font_path = f"{script_dir}/SourceHanSansSC-Bold-2.otf"
    result_image = add_text_to_image(extended_img, text_list, font_path)

    # logo
    paste_overlay(result_image, f"{script_dir}/logo_2.png", (352, 81))

    # 保存结果
    rgb_cover = result_image.convert("RGB")
    # rgb_cover.save(output_path)
    # print(f"图像已扩展并保存至 {output_path}")

    # 将生成的图像保存到内存中的字节流
    img_byte_array = BytesIO()
    rgb_cover.save(img_byte_array, format='JPEG', quality=95)  # 指定格式和质量
    img_byte_array.seek(0)  # 重置指针到开头

    return img_byte_array.getvalue()


def add_text_to_layer_center(image, layer, layer_position, text_info, font_path):
    draw = ImageDraw.Draw(image)

    text = text_info["text"]
    font_size = text_info["font_size"]
    font_color = text_info["font_color"]
    font_weight = text_info.get("font_weight", "normal")
    letter_spacing = text_info.get("letter_spacing", 0)

    # 加载字体
    font = ImageFont.truetype(font_path, font_size)

    # 设置字体样式
    if font_weight == "bold":
        font = ImageFont.truetype(font_path, font_size, encoding="utf-8")

    # 获取文字的边界框
    bbox = font.getbbox(text)
    text_width = bbox[2] - bbox[0]  # 右边界 - 左边界
    text_height = bbox[3] - bbox[1]  # 下边界 - 上边界

    # 获取图层的宽度和高度
    image_width, image_height = layer.size

    # 计算文字的起始位置，使得文字居中
    x = layer_position[0] + (image_width - text_width) / 2
    y = layer_position[1] + (image_height - text_height) / 2

    # 添加文字
    draw.text((x, y), text, font=font, fill=font_color, spacing=letter_spacing)
    return image


if __name__ == "__main__":
    # 测试数据
    skin_group = {
        '荣耀典藏': [
            {'image': 'skin/10500.jpeg', 'name': '九霄神辉'},
        ],
        # '传说皮肤': [
        #     {'image': '占位图.png', 'name': '九霄神辉'},
        #     {'image': '占位图.png', 'name': '九霄神辉'},
        # ],
        # '史诗皮肤': [
        #     {'image': '占位图.png', 'name': '九霄神辉'},
        # ],
        '国标': [
            {'image': 'hero/105.jpeg', 'name': '九霄神辉'},
        ]
    }

    text_dict = {
        '编号': '',
        '区服': '',
        '贵族等级': '',
        '英雄数量': '',
        '实名情况': '0',
        '皮肤数量': '',
        '国标数量': '',
        '荣耀典藏': ''
    }
    create_detail_image(skin_group, text_dict)
