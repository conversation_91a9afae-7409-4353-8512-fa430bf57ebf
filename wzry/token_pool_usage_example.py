"""
Token池使用示例 - 展示如何正确使用修改后的Token池
"""
from common.mongo_manager import MongoDBClient
from wzry.simple_token_pool import SimpleTokenPool
from wzry.token_models import TokenStatus


class TokenPoolUsageExample:
    """Token池使用示例类"""
    
    def __init__(self):
        """初始化"""
        self.mongo = MongoDBClient(
            uri="******************************************",
            db_name="luhao-prod"
        )
        self.token_pool = SimpleTokenPool(self.mongo, "wzry_token_pool")
    
    def example_basic_usage(self):
        """基本使用示例"""
        print("=== 基本使用示例 ===")
        
        # 1. 获取Token（原子操作，自动标记为IN_USE）
        token_config = self.token_pool.get_next_token()
        
        if not token_config:
            print("没有可用的Token")
            return
        
        token_id = token_config['_id']
        print(f"获取到Token: {token_id}")
        
        try:
            # 2. 使用Token进行API调用
            # 这里模拟API调用
            print("正在使用Token进行API调用...")
            
            # 模拟API调用结果
            api_success = True  # 假设API调用成功
            
            if api_success:
                print("API调用成功")
                # 3. 使用完成后释放Token
                if self.token_pool.release_token(token_id):
                    print(f"Token已释放: {token_id}")
                else:
                    print(f"Token释放失败: {token_id}")
            else:
                # 如果API调用失败，可能需要更新Token状态
                print("API调用失败，更新Token状态")
                self.token_pool.update_token_status(token_id, TokenStatus.ACCESS_LIMITED)
                
        except Exception as e:
            print(f"使用Token时发生错误: {e}")
            # 发生异常时也要尝试释放Token
            self.token_pool.release_token(token_id)
    
    def example_with_error_handling(self):
        """带错误处理的使用示例"""
        print("\n=== 带错误处理的使用示例 ===")
        
        token_config = None
        token_id = None
        
        try:
            # 获取Token
            token_config = self.token_pool.get_next_token()
            if not token_config:
                print("没有可用的Token")
                return
            
            token_id = token_config['_id']
            print(f"获取到Token: {token_id}")
            
            # 模拟可能失败的API调用
            self._simulate_api_call(token_config)
            
        except Exception as e:
            print(f"处理过程中发生错误: {e}")
            
            # 根据错误类型决定Token状态
            if "登录失效" in str(e):
                if token_id:
                    self.token_pool.update_token_status(token_id, TokenStatus.LOGIN_EXPIRED)
                    print(f"Token已标记为登录失效: {token_id}")
            elif "访问受限" in str(e):
                if token_id:
                    self.token_pool.update_token_status(token_id, TokenStatus.ACCESS_LIMITED)
                    print(f"Token已标记为访问受限: {token_id}")
            else:
                # 其他错误，释放Token供其他实例使用
                if token_id:
                    self.token_pool.release_token(token_id)
                    print(f"Token已释放: {token_id}")
        
        finally:
            # 确保正常情况下Token被释放
            if token_id and token_config:
                # 检查Token当前状态，如果还是IN_USE则释放
                try:
                    self.token_pool.release_token(token_id)
                except:
                    pass  # 释放失败可能是因为已经被其他操作更新了状态
    
    def _simulate_api_call(self, token_config):
        """模拟API调用"""
        import random
        
        # 随机模拟不同的结果
        result = random.choice(['success', 'login_expired', 'access_limited', 'other_error'])
        
        if result == 'success':
            print("API调用成功")
            # 成功时释放Token
            self.token_pool.release_token(token_config['_id'])
        elif result == 'login_expired':
            raise Exception("登录失效")
        elif result == 'access_limited':
            raise Exception("访问受限")
        else:
            raise Exception("其他API错误")
    
    def example_pool_management(self):
        """Token池管理示例"""
        print("\n=== Token池管理示例 ===")
        
        # 1. 查看池状态
        status = self.token_pool.get_pool_status()
        print("当前Token池状态:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        # 2. 清理超时的Token
        print("\n清理超时Token...")
        cleaned_count = self.token_pool.cleanup_expired_tokens(timeout_minutes=30)
        print(f"清理了 {cleaned_count} 个超时Token")
        
        # 3. 再次查看状态
        status_after = self.token_pool.get_pool_status()
        print("\n清理后Token池状态:")
        for key, value in status_after.items():
            print(f"  {key}: {value}")
    
    def example_concurrent_safe_usage(self):
        """并发安全使用示例"""
        print("\n=== 并发安全使用示例 ===")
        
        import threading
        import time
        
        def worker(worker_id):
            """工作线程函数"""
            token_config = self.token_pool.get_next_token()
            if token_config:
                print(f"工作线程 {worker_id}: 获取到Token {token_config['_id']}")
                
                # 模拟工作时间
                time.sleep(1)
                
                # 释放Token
                if self.token_pool.release_token(token_config['_id']):
                    print(f"工作线程 {worker_id}: Token已释放")
                else:
                    print(f"工作线程 {worker_id}: Token释放失败")
            else:
                print(f"工作线程 {worker_id}: 没有可用Token")
        
        # 创建多个工作线程
        threads = []
        for i in range(3):
            thread = threading.Thread(target=worker, args=(i + 1,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print("所有工作线程已完成")


def main():
    """主函数"""
    example = TokenPoolUsageExample()
    
    try:
        # 运行各种使用示例
        example.example_basic_usage()
        example.example_with_error_handling()
        example.example_pool_management()
        example.example_concurrent_safe_usage()
        
        print("\n=== 所有示例运行完成 ===")
        
    except Exception as e:
        print(f"运行示例时发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
