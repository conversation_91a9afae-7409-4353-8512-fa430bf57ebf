{"heroList": [{"heroId": "114", "heroType": "辅助", "name": "刘禅"}, {"heroId": "105", "heroType": "坦克", "name": "廉颇"}, {"heroId": "156", "heroType": "法师", "name": "张良"}, {"heroId": "513", "heroType": "法师", "name": "上官婉儿"}, {"heroId": "167", "heroType": "刺客", "name": "孙悟空"}, {"heroId": "169", "heroType": "射手", "name": "后羿"}, {"heroId": "132", "heroType": "射手", "name": "马可波罗"}, {"heroId": "189", "heroType": "辅助", "name": "鬼谷子"}, {"heroId": "131", "heroType": "刺客", "name": "<PERSON>白"}, {"heroId": "133", "heroType": "射手", "name": "狄仁杰"}, {"heroId": "525", "heroType": "辅助", "name": "鲁班大师"}, {"heroId": "162", "heroType": "刺客", "name": "娜可露露"}, {"heroId": "193", "heroType": "战士", "name": "铠"}, {"heroId": "118", "heroType": "辅助", "name": "孙膑"}, {"heroId": "174", "heroType": "射手", "name": "虞姬"}, {"heroId": "125", "heroType": "刺客", "name": "元歌"}, {"heroId": "187", "heroType": "辅助", "name": "东皇太一"}, {"heroId": "149", "heroType": "坦克", "name": "刘邦"}, {"heroId": "111", "heroType": "射手", "name": "孙尚香"}, {"heroId": "171", "heroType": "辅助", "name": "张飞"}, {"heroId": "505", "heroType": "辅助", "name": "瑶"}, {"heroId": "528", "heroType": "刺客", "name": "澜"}, {"heroId": "502", "heroType": "刺客", "name": "裴擒虎"}, {"heroId": "537", "heroType": "战士", "name": "司空震"}, {"heroId": "517", "heroType": "战士", "name": "大司命"}, {"heroId": "196", "heroType": "射手", "name": "百里守约"}, {"heroId": "123", "heroType": "战士", "name": "吕布"}, {"heroId": "112", "heroType": "射手", "name": "鲁班七号"}, {"heroId": "130", "heroType": "战士", "name": "宫本武藏"}, {"heroId": "518", "heroType": "战士", "name": "马超"}, {"heroId": "194", "heroType": "辅助", "name": "苏烈"}, {"heroId": "508", "heroType": "射手", "name": "伽罗"}, {"heroId": "154", "heroType": "战士", "name": "花木兰"}, {"heroId": "501", "heroType": "辅助", "name": "明世隐"}, {"heroId": "126", "heroType": "坦克", "name": "夏侯惇"}, {"heroId": "511", "heroType": "坦克", "name": "猪八戒"}, {"heroId": "191", "heroType": "辅助", "name": "大乔"}, {"heroId": "184", "heroType": "辅助", "name": "蔡文姬"}, {"heroId": "182", "heroType": "法师", "name": "干将莫邪"}, {"heroId": "108", "heroType": "法师", "name": "墨子"}, {"heroId": "522", "heroType": "战士", "name": "曜"}, {"heroId": "542", "heroType": "刺客", "name": "暃"}, {"heroId": "107", "heroType": "战士", "name": "赵云"}, {"heroId": "134", "heroType": "战士", "name": "达摩"}, {"heroId": "137", "heroType": "刺客", "name": "司马懿"}, {"heroId": "180", "heroType": "战士", "name": "哪吒"}, {"heroId": "150", "heroType": "刺客", "name": "韩信"}, {"heroId": "117", "heroType": "战士", "name": "钟无艳"}, {"heroId": "545", "heroType": "射手", "name": "莱西奥"}, {"heroId": "157", "heroType": "刺客", "name": "不知火舞"}, {"heroId": "192", "heroType": "射手", "name": "黄忠"}, {"heroId": "113", "heroType": "辅助", "name": "庄周"}, {"heroId": "168", "heroType": "辅助", "name": "牛魔"}, {"heroId": "533", "heroType": "射手", "name": "阿古朵"}, {"heroId": "135", "heroType": "坦克", "name": "项羽"}, {"heroId": "510", "heroType": "战士", "name": "孙策"}, {"heroId": "175", "heroType": "辅助", "name": "钟馗"}, {"heroId": "148", "heroType": "法师", "name": "姜子牙"}, {"heroId": "121", "heroType": "法师", "name": "芈月"}, {"heroId": "124", "heroType": "法师", "name": "周瑜"}, {"heroId": "190", "heroType": "法师", "name": "诸葛亮"}, {"heroId": "153", "heroType": "刺客", "name": "兰陵王"}, {"heroId": "173", "heroType": "射手", "name": "李元芳"}, {"heroId": "144", "heroType": "坦克", "name": "程咬金"}, {"heroId": "531", "heroType": "刺客", "name": "镜"}, {"heroId": "312", "heroType": "法师", "name": "沈梦溪"}, {"heroId": "166", "heroType": "战士", "name": "亚瑟"}, {"heroId": "109", "heroType": "法师", "name": "妲己"}, {"heroId": "141", "heroType": "法师", "name": "貂蝉"}, {"heroId": "142", "heroType": "法师", "name": "安琪拉"}, {"heroId": "163", "heroType": "刺客", "name": "橘右京"}, {"heroId": "534", "heroType": "辅助", "name": "桑启"}, {"heroId": "129", "heroType": "战士", "name": "典韦"}, {"heroId": "106", "heroType": "法师", "name": "小乔"}, {"heroId": "186", "heroType": "辅助", "name": "太乙真人"}, {"heroId": "504", "heroType": "法师", "name": "米莱狄"}, {"heroId": "540", "heroType": "法师", "name": "金蝉"}, {"heroId": "509", "heroType": "辅助", "name": "盾山"}, {"heroId": "507", "heroType": "战士", "name": "李信"}, {"heroId": "140", "heroType": "战士", "name": "关羽"}, {"heroId": "146", "heroType": "刺客", "name": "露娜"}, {"heroId": "503", "heroType": "战士", "name": "狂铁"}, {"heroId": "183", "heroType": "战士", "name": "雅典娜"}, {"heroId": "536", "heroType": "战士", "name": "夏洛特"}, {"heroId": "179", "heroType": "法师", "name": "女娲"}, {"heroId": "195", "heroType": "刺客", "name": "百里玄策"}, {"heroId": "120", "heroType": "坦克", "name": "白起"}, {"heroId": "177", "heroType": "射手", "name": "苍"}, {"heroId": "127", "heroType": "法师", "name": "甄姬"}, {"heroId": "524", "heroType": "射手", "name": "蒙犽"}, {"heroId": "115", "heroType": "法师", "name": "高渐离"}, {"heroId": "128", "heroType": "战士", "name": "曹操"}, {"heroId": "152", "heroType": "法师", "name": "王昭君"}, {"heroId": "197", "heroType": "法师", "name": "弈星"}, {"heroId": "515", "heroType": "法师", "name": "嫦娥"}, {"heroId": "199", "heroType": "射手", "name": "公孙离"}, {"heroId": "521", "heroType": "法师", "name": "海月"}, {"heroId": "538", "heroType": "战士", "name": "云缨"}, {"heroId": "548", "heroType": "射手", "name": "戈娅"}, {"heroId": "176", "heroType": "法师", "name": "杨玉环"}, {"heroId": "155", "heroType": "射手", "name": "艾琳"}, {"heroId": "170", "heroType": "战士", "name": "刘备"}, {"heroId": "178", "heroType": "战士", "name": "杨戬"}, {"heroId": "198", "heroType": "坦克", "name": "梦奇"}, {"heroId": "514", "heroType": "战士", "name": "亚连"}, {"heroId": "523", "heroType": "法师", "name": "西施"}, {"heroId": "529", "heroType": "战士", "name": "盘古"}, {"heroId": "544", "heroType": "战士", "name": "赵怀真"}, {"heroId": "116", "heroType": "刺客", "name": "阿轲"}, {"heroId": "527", "heroType": "坦克", "name": "蒙恬"}, {"heroId": "582", "heroType": "法师", "name": "元流之子(法师)"}, {"heroId": "110", "heroType": "法师", "name": "嬴政"}, {"heroId": "119", "heroType": "法师", "name": "扁鹊"}, {"heroId": "139", "heroType": "战士", "name": "老夫子"}, {"heroId": "159", "heroType": "辅助", "name": "朵莉亚"}, {"heroId": "506", "heroType": "刺客", "name": "云中君"}, {"heroId": "558", "heroType": "战士", "name": "影"}, {"heroId": "563", "heroType": "法师", "name": "海诺"}, {"heroId": "564", "heroType": "战士", "name": "姬小满"}, {"heroId": "577", "heroType": "辅助", "name": "少司缘"}, {"heroId": "581", "heroType": "坦克", "name": "元流之子(坦克)"}], "skinList": [{"skinId": 50901, "skinName": "极冰防御线", "heroId": "509", "heroName": "盾山", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4, "attrNames": ["盾山", "其他"]}, {"skinId": 56301, "skinName": "时空谍影", "heroId": "563", "heroName": "海诺", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 2, "attrNames": ["海诺", "其他"]}, {"skinId": 12901, "skinName": "黄金武士", "heroId": "129", "heroName": "典韦", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 7, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 51403, "skinName": "糖绘人间", "heroId": "514", "heroName": "亚连", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 4, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 57701, "skinName": "灵卦秘语", "heroId": "577", "heroName": "少司缘", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "", "skinNum": 2, "attrNames": ["其他", "少司缘"]}, {"skinId": 13406, "skinName": "爆裂喵拳", "heroId": "134", "heroName": "达摩", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 6, "attrNames": ["其他", "达摩"]}, {"skinId": 17606, "skinName": "星之鸣奏", "heroId": "176", "heroName": "杨玉环", "classTypeName": ["传说品质", "限定", "宇宙歌姬"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 5, "attrNames": ["其他", "传说皮肤"]}, {"skinId": 11403, "skinName": "天才门将", "heroId": "114", "heroName": "刘禅", "classTypeName": ["史诗品质"], "heroType": "辅助", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 13005, "skinName": "地狱之眼", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["传说品质"], "heroType": "战士", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "传说皮肤"]}, {"skinId": 53703, "skinName": "愿照山河定", "heroId": "537", "heroName": "司空震", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 3, "attrNames": ["其他", "传说皮肤", "年限皮肤"]}, {"skinId": 17410, "skinName": "愿照岁时盈", "heroId": "174", "heroName": "虞姬", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 9, "attrNames": ["其他", "传说皮肤", "年限皮肤"]}, {"skinId": 52806, "skinName": "愿照九州拓", "heroId": "528", "heroName": "澜", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 5, "attrNames": ["其他", "传说皮肤", "年限皮肤"]}, {"skinId": 16301, "skinName": "修罗", "heroId": "163", "heroName": "橘右京", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 2, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 53802, "skinName": "幻光神枪", "heroId": "538", "heroName": "云缨", "classTypeName": ["传说品质"], "heroType": "战士", "accessWay": "", "skinNum": 3, "attrNames": ["其他", "传说皮肤"]}, {"skinId": 18705, "skinName": "金福满堂", "heroId": "187", "heroName": "东皇太一", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "", "skinNum": 5, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 50208, "skinName": "祥瑞亨通", "heroId": "502", "heroName": "裴擒虎", "classTypeName": ["传说品质", "FMVP"], "heroType": "刺客", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "传说皮肤"]}, {"skinId": 14806, "skinName": "星梦巡礼", "heroId": "148", "heroName": "姜子牙", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "钻石夺宝获取", "skinNum": 5, "attrNames": ["其他", "姜子牙"]}, {"skinId": 52901, "skinName": "创世神祝", "heroId": "529", "heroName": "盘古", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 5, "attrNames": ["其他", "盘古"]}, {"skinId": 19101, "skinName": "伊势巫女", "heroId": "191", "heroName": "大乔", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 9, "attrNames": ["其他", "大乔"]}, {"skinId": 12006, "skinName": "苍鳞隐世", "heroId": "120", "heroName": "白起", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 10910, "skinName": "灵卜魔法", "heroId": "109", "heroName": "妲己", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 11, "attrNames": ["其他", "妲己"]}, {"skinId": 52307, "skinName": "雪境奇遇", "heroId": "523", "heroName": "西施", "classTypeName": ["勇者品质", "限定", "冰雪奇缘"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 7, "attrNames": ["其他", "西施"]}, {"skinId": 13303, "skinName": "超时空战士", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["传说品质", "超时空小队"], "heroType": "射手", "accessWay": "", "skinNum": 9, "attrNames": ["其他", "传说皮肤"]}, {"skinId": 17601, "skinName": "霓裳曲", "heroId": "176", "heroName": "杨玉环", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 5, "attrNames": ["其他", "杨玉环"]}, {"skinId": 16806, "skinName": "星界战将", "heroId": "168", "heroName": "牛魔", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证", "星界纪事"], "heroType": "辅助", "accessWay": "时空之境获取", "skinNum": 7, "attrNames": ["赛季皮肤", "其他", "史诗皮肤"]}, {"skinId": 51806, "skinName": "访茗客", "heroId": "518", "heroName": "马超", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 5, "attrNames": ["其他", "战令限定"]}, {"skinId": 12907, "skinName": "铁骨偃魂", "heroId": "129", "heroName": "典韦", "classTypeName": ["勇者品质", "活动专属"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 7, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 19603, "skinName": "特工魅影", "heroId": "196", "heroName": "百里守约", "classTypeName": ["史诗品质"], "heroType": "射手", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 55801, "skinName": "魅影绮裳", "heroId": "558", "heroName": "影", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 1, "attrNames": ["其他", "影"]}, {"skinId": 11808, "skinName": "茶境仙", "heroId": "118", "heroName": "孙膑", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "辅助", "accessWay": "荣耀战令获取", "skinNum": 8, "attrNames": ["其他", "战令限定", "史诗皮肤"]}, {"skinId": 11802, "skinName": "天使之翼", "heroId": "118", "heroName": "孙膑", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "", "skinNum": 8, "attrNames": ["孙膑", "其他"]}, {"skinId": 18606, "skinName": "谧流熔炉", "heroId": "186", "heroName": "太乙真人", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S38赛季专属", "skinNum": 5, "attrNames": ["太乙真人", "其他"]}, {"skinId": 17704, "skinName": "苍林狼骑", "heroId": "177", "heroName": "苍", "classTypeName": ["勇者品质", "活动专属"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 3, "attrNames": ["苍", "其他"]}, {"skinId": 17003, "skinName": "汉昭烈帝", "heroId": "170", "heroName": "刘备", "classTypeName": ["勇者品质", "御龙在天"], "heroType": "战士", "accessWay": "", "skinNum": 8, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 14603, "skinName": "紫霞仙子", "heroId": "146", "heroName": "露娜", "classTypeName": ["史诗品质", "CP皮肤", "大话西游"], "heroType": "刺客", "accessWay": "", "skinNum": 8, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 14101, "skinName": "异域舞娘", "heroId": "141", "heroName": "貂蝉", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 11, "attrNames": ["貂蝉", "其他"]}, {"skinId": 50401, "skinName": "精准探案法", "heroId": "504", "heroName": "米莱狄", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 6, "attrNames": ["米莱狄", "其他"]}, {"skinId": 12601, "skinName": "战争骑士", "heroId": "126", "heroName": "夏侯惇", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 6, "attrNames": ["夏侯惇", "其他"]}, {"skinId": 52702, "skinName": "蔚蓝守将", "heroId": "527", "heroName": "蒙恬", "classTypeName": ["勇者品质", "活动专属", "胡桃异想国"], "heroType": "坦克", "accessWay": "限时活动获取", "skinNum": 3, "attrNames": ["蒙恬", "其他"]}, {"skinId": 52801, "skinName": "孤猎", "heroId": "528", "heroName": "澜", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 5, "attrNames": ["澜", "其他"]}, {"skinId": 18704, "skinName": "噬灭天穹", "heroId": "187", "heroName": "东皇太一", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S37赛季专属", "skinNum": 5, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 19607, "skinName": "百相守梦", "heroId": "196", "heroName": "百里守约", "classTypeName": ["史诗品质", "战令限定", "限定", "百相守梦"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 7, "attrNames": ["百里守约", "其他", "史诗皮肤"]}, {"skinId": 13906, "skinName": "百相守梦", "heroId": "139", "heroName": "老夫子", "classTypeName": ["勇者品质", "战令限定", "限定", "百相守梦"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 7, "attrNames": ["其他", "战令限定"]}, {"skinId": 15403, "skinName": "水晶猎龙者", "heroId": "154", "heroName": "花木兰", "classTypeName": ["史诗品质", "五五朋友节"], "heroType": "战士", "accessWay": "", "skinNum": 9, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 50501, "skinName": "森", "heroId": "505", "heroName": "瑶", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "瑶"]}, {"skinId": 58200, "skinName": "万妙之心", "heroId": "582", "heroName": "元流之子(法师)", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 1}, {"skinId": 58100, "skinName": "止戈之道", "heroId": "581", "heroName": "元流之子(坦克)", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 1}, {"skinId": 52703, "skinName": "荣光圣徽", "heroId": "527", "heroName": "蒙恬", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证"], "heroType": "坦克", "accessWay": "时空之境获取", "skinNum": 3, "attrNames": ["赛季皮肤", "其他", "史诗皮肤"]}, {"skinId": 10802, "skinName": "龙骑士", "heroId": "108", "heroName": "墨子", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 6, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 10504, "skinName": "功夫炙烤", "heroId": "105", "heroName": "廉颇", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "钻石夺宝获取", "skinNum": 5, "attrNames": ["廉颇", "其他"]}, {"skinId": 14906, "skinName": "剑破天穹", "heroId": "149", "heroName": "刘邦", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "坦克", "accessWay": "S35赛季专属", "skinNum": 5, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 53401, "skinName": "画中游", "heroId": "534", "heroName": "桑启", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4, "attrNames": ["其他", "桑启"]}, {"skinId": 19302, "skinName": "曙光守护者", "heroId": "193", "heroName": "铠", "classTypeName": ["史诗品质", "五五朋友节"], "heroType": "战士", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 10701, "skinName": "忍炎影", "heroId": "107", "heroName": "赵云", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 10, "attrNames": ["赵云", "其他"]}, {"skinId": 12003, "skinName": "星夜王子", "heroId": "120", "heroName": "白起", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 18905, "skinName": "天穹祈灯", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S34赛季专属", "skinNum": 5, "attrNames": ["其他", "赛季皮肤"]}, {"skinId": 12701, "skinName": "冰雪圆舞曲", "heroId": "127", "heroName": "甄姬", "classTypeName": ["史诗品质", "冰雪之歌"], "heroType": "法师", "accessWay": "", "skinNum": 8, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 15607, "skinName": "古海寻踪", "heroId": "156", "heroName": "张良", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S33赛季专属", "skinNum": 8, "attrNames": ["张良", "其他"]}, {"skinId": 52504, "skinName": "探海日志", "heroId": "525", "heroName": "鲁班大师", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S32赛季专属", "skinNum": 4, "attrNames": ["其他", "赛季皮肤"]}, {"skinId": 15302, "skinName": "暗隐猎兽者", "heroId": "153", "heroName": "兰陵王", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 11905, "skinName": "奇幻香踪", "heroId": "119", "heroName": "扁鹊", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 6, "attrNames": ["其他", "史诗皮肤", "扁鹊"]}, {"skinId": 17001, "skinName": "万事如意", "heroId": "170", "heroName": "刘备", "classTypeName": ["勇者品质", "新春专属"], "heroType": "战士", "accessWay": "", "skinNum": 8, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 50206, "skinName": "擒涛扼浪", "heroId": "502", "heroName": "裴擒虎", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "刺客", "accessWay": "S31赛季专属", "skinNum": 7, "attrNames": ["其他", "裴擒虎"]}, {"skinId": 17805, "skinName": "潮玩骑兵", "heroId": "178", "heroName": "杨戬", "classTypeName": ["勇者品质", "战令限定", "限定", "奇趣潮玩"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 6, "attrNames": ["其他", "战令限定"]}, {"skinId": 19402, "skinName": "坚韧之力", "heroId": "194", "heroName": "苏烈", "classTypeName": ["勇者品质", "五路精神"], "heroType": "辅助", "accessWay": "", "skinNum": 5, "attrNames": ["其他", "苏烈"]}, {"skinId": 11902, "skinName": "化身博士", "heroId": "119", "heroName": "扁鹊", "classTypeName": ["勇者品质", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 6, "attrNames": ["其他", "扁鹊"]}, {"skinId": 18701, "skinName": "东海龙王", "heroId": "187", "heroName": "东皇太一", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 5, "attrNames": ["其他皮肤", "其他"]}, {"skinId": 18403, "skinName": "舞动绿茵", "heroId": "184", "heroName": "蔡文姬", "classTypeName": ["史诗品质"], "heroType": "辅助", "accessWay": "", "skinNum": 8, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 19004, "skinName": "掌控之力", "heroId": "190", "heroName": "诸葛亮", "classTypeName": ["勇者品质", "五路精神"], "heroType": "法师", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "诸葛亮"]}, {"skinId": 11404, "skinName": "秘密基地", "heroId": "114", "heroName": "刘禅", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "钻石夺宝获取", "skinNum": 7, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 10604, "skinName": "缤纷独角兽", "heroId": "106", "heroName": "小乔", "classTypeName": ["史诗品质", "夏日海滩"], "heroType": "法师", "accessWay": "", "skinNum": 10, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 17901, "skinName": "尼罗河女神", "heroId": "179", "heroName": "女娲", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 4, "attrNames": ["女娲", "其他"]}, {"skinId": 54202, "skinName": "星界游侠", "heroId": "542", "heroName": "暃", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "刺客", "accessWay": "荣耀战令获取", "skinNum": 4, "attrNames": ["其他", "暃", "史诗皮肤"]}, {"skinId": 14402, "skinName": "星际陆战队", "heroId": "144", "heroName": "程咬金", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "", "skinNum": 9, "attrNames": ["程咬金", "其他"]}, {"skinId": 16201, "skinName": "晚萤", "heroId": "162", "heroName": "娜可露露", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 2, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 12001, "skinName": "白色死神", "heroId": "120", "heroName": "白起", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "", "skinNum": 7, "attrNames": ["白起", "其他"]}, {"skinId": 13401, "skinName": "拳王", "heroId": "134", "heroName": "达摩", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 6, "attrNames": ["其他", "达摩"]}, {"skinId": 10501, "skinName": "地狱岩魂", "heroId": "105", "heroName": "廉颇", "classTypeName": ["史诗品质", "地狱火"], "heroType": "坦克", "accessWay": "", "skinNum": 5, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 52204, "skinName": "山海苍雷引", "heroId": "522", "heroName": "曜", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 5, "attrNames": ["其他", "传说皮肤", "年限皮肤"]}, {"skinId": 12303, "skinName": "末日机甲", "heroId": "123", "heroName": "吕布", "classTypeName": ["史诗品质", "末日机甲"], "heroType": "战士", "accessWay": "", "skinNum": 10, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 13206, "skinName": "山海玄木吟", "heroId": "132", "heroName": "马可波罗", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 9, "attrNames": ["年限皮肤", "其他", "史诗皮肤"]}, {"skinId": 19601, "skinName": "绝影神枪", "heroId": "196", "heroName": "百里守约", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 7, "attrNames": ["百里守约", "其他"]}, {"skinId": 12803, "skinName": "幽灵船长", "heroId": "128", "heroName": "曹操", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 19701, "skinName": "踏雪寻梅", "heroId": "197", "heroName": "弈星", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 5, "attrNames": ["弈星", "其他"]}, {"skinId": 51301, "skinName": "修竹墨客", "heroId": "513", "heroName": "上官婉儿", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 6, "attrNames": ["其他皮肤", "其他"]}, {"skinId": 15501, "skinName": "女武神", "heroId": "155", "heroName": "艾琳", "classTypeName": ["勇者品质", "限定", "珍宝阁专属"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 5, "attrNames": ["内测皮肤", "其他", "艾琳"]}, {"skinId": 51104, "skinName": "潮玩探月行", "heroId": "511", "heroName": "猪八戒", "classTypeName": ["勇者品质", "奇趣潮玩"], "heroType": "坦克", "accessWay": "钻石夺宝获取", "skinNum": 4, "attrNames": ["其他", "猪八戒"]}, {"skinId": 18305, "skinName": "黎明之约", "heroId": "183", "heroName": "雅典娜", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证"], "heroType": "战士", "accessWay": "时空之境获取", "skinNum": 6, "attrNames": ["雅典娜", "其他", "史诗皮肤"]}, {"skinId": 18901, "skinName": "阿摩司公爵", "heroId": "189", "heroName": "鬼谷子", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 5, "attrNames": ["其他", "鬼谷子"]}, {"skinId": 53402, "skinName": "海盐诗旅", "heroId": "534", "heroName": "桑启", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S30赛季专属", "skinNum": 4, "attrNames": ["其他", "桑启"]}, {"skinId": 52101, "skinName": "幻泉雾影", "heroId": "521", "heroName": "海月", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 4, "attrNames": ["海月", "其他"]}, {"skinId": 51504, "skinName": "暖冬兔眠", "heroId": "515", "heroName": "嫦娥", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 6, "attrNames": ["其他", "嫦娥"]}, {"skinId": 17303, "skinName": "逐浪之夏", "heroId": "173", "heroName": "李元芳", "classTypeName": ["史诗品质", "夏日海滩"], "heroType": "射手", "accessWay": "", "skinNum": 9, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 19803, "skinName": "顽趣", "heroId": "198", "heroName": "梦奇", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证", "顽趣"], "heroType": "坦克", "accessWay": "时空之境获取", "skinNum": 4, "attrNames": ["赛季皮肤", "其他", "史诗皮肤"]}, {"skinId": 15605, "skinName": "缤纷绘卷", "heroId": "156", "heroName": "张良", "classTypeName": ["勇者品质", "活动专属", "青春校园"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 8, "attrNames": ["张良", "其他"]}, {"skinId": 13302, "skinName": "魔术师", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["史诗品质", "限定", "贵族限定"], "heroType": "射手", "accessWay": "贵族6级赠送", "skinNum": 9, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 13107, "skinName": "诗剑行", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": ["传说品质", "世冠皮肤"], "heroType": "刺客", "accessWay": "", "skinNum": 8, "attrNames": ["其他", "传说皮肤"]}, {"skinId": 17102, "skinName": "乱世虎臣", "heroId": "171", "heroName": "张飞", "classTypeName": ["史诗品质", "御龙在天"], "heroType": "辅助", "accessWay": "", "skinNum": 5, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 15002, "skinName": "教廷特使", "heroId": "150", "heroName": "韩信", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "钻石夺宝获取", "skinNum": 8, "attrNames": ["其他", "韩信"]}, {"skinId": 11501, "skinName": "金属狂潮", "heroId": "115", "heroName": "高渐离", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 5, "attrNames": ["高渐离", "其他"]}, {"skinId": 13402, "skinName": "大发明家", "heroId": "134", "heroName": "达摩", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 6, "attrNames": ["其他", "达摩"]}, {"skinId": 12503, "skinName": "无心", "heroId": "125", "heroName": "元歌", "classTypeName": ["史诗品质", "限定", "源梦皮肤"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 3, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 51304, "skinName": "神器万象笔", "heroId": "513", "heroName": "上官婉儿", "classTypeName": ["史诗品质", "限定", "永宁纪"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 14407, "skinName": "无双福将", "heroId": "144", "heroName": "程咬金", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "钻石夺宝获取", "skinNum": 9, "attrNames": ["程咬金", "其他"]}, {"skinId": 11704, "skinName": "超时空战士", "heroId": "117", "heroName": "钟无艳", "classTypeName": ["史诗品质", "战令限定", "限定", "超时空小队"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 6, "attrNames": ["钟无艳", "其他", "史诗皮肤"]}, {"skinId": 12602, "skinName": "乘风破浪", "heroId": "126", "heroName": "夏侯惇", "classTypeName": ["史诗品质", "夏日海滩"], "heroType": "坦克", "accessWay": "", "skinNum": 6, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 18904, "skinName": "五谷丰年", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["史诗品质", "限定", "周年限定"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 5, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 11102, "skinName": "水果甜心", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["史诗品质"], "heroType": "射手", "accessWay": "", "skinNum": 10, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 12706, "skinName": "落雪兰心", "heroId": "127", "heroName": "甄姬", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 8, "attrNames": ["其他", "战令限定"]}, {"skinId": 19102, "skinName": "守护之力", "heroId": "191", "heroName": "大乔", "classTypeName": ["勇者品质", "五路精神"], "heroType": "辅助", "accessWay": "", "skinNum": 9, "attrNames": ["其他", "大乔"]}, {"skinId": 13502, "skinName": "苍穹之光", "heroId": "135", "heroName": "项羽", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 8, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 13903, "skinName": "功夫老勺", "heroId": "139", "heroName": "老夫子", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "钻石夺宝获取", "skinNum": 7, "attrNames": ["老夫子", "其他"]}, {"skinId": 52502, "skinName": "乓乓大师", "heroId": "525", "heroName": "鲁班大师", "classTypeName": ["勇者品质", "活动专属", "五环荣耀"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 4, "attrNames": ["其他", "鲁班大师"]}, {"skinId": 17306, "skinName": "云中旅人", "heroId": "173", "heroName": "李元芳", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "射手", "accessWay": "S29赛季专属", "skinNum": 9, "attrNames": ["其他", "李元芳"]}, {"skinId": 11602, "skinName": "暗夜猫娘", "heroId": "116", "heroName": "阿轲", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 6, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 13002, "skinName": "未来纪元", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 7, "attrNames": ["宫本武藏", "其他"]}, {"skinId": 14901, "skinName": "圣殿之光", "heroId": "149", "heroName": "刘邦", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 5, "attrNames": ["其他", "刘邦"]}, {"skinId": 50304, "skinName": "电玩高手", "heroId": "503", "heroName": "狂铁", "classTypeName": ["史诗品质", "战令限定", "限定", "电玩狂想"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 5, "attrNames": ["其他", "史诗皮肤", "狂铁"]}, {"skinId": 16801, "skinName": "西部大镖客", "heroId": "168", "heroName": "牛魔", "classTypeName": ["勇者品质", "西部大镖客"], "heroType": "辅助", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "牛魔"]}, {"skinId": 52404, "skinName": "胡桃狂想曲", "heroId": "524", "heroName": "蒙犽", "classTypeName": ["勇者品质", "胡桃异想国"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 6, "attrNames": ["其他", "蒙犽"]}, {"skinId": 15402, "skinName": "兔女郎", "heroId": "154", "heroName": "花木兰", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 9, "attrNames": ["花木兰", "其他"]}, {"skinId": 11203, "skinName": "电玩小子", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["史诗品质", "电玩狂想"], "heroType": "射手", "accessWay": "", "skinNum": 11, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 18902, "skinName": "幻乐之宴", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "钻石夺宝获取", "skinNum": 5, "attrNames": ["其他", "鬼谷子"]}, {"skinId": 10904, "skinName": "少女阿狸", "heroId": "109", "heroName": "妲己", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "教学关卡获取", "skinNum": 11, "attrNames": ["其他", "妲己"]}, {"skinId": 10803, "skinName": "进击墨子号", "heroId": "108", "heroName": "墨子", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6, "attrNames": ["其他", "墨子"]}, {"skinId": 15207, "skinName": "午后时光", "heroId": "152", "heroName": "王昭君", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 9, "attrNames": ["其他", "战令限定"]}, {"skinId": 31205, "skinName": "大漠名商", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S28赛季专属", "skinNum": 8, "attrNames": ["其他", "沈梦溪"]}, {"skinId": 50303, "skinName": "特工战影", "heroId": "503", "heroName": "狂铁", "classTypeName": ["勇者品质", "活动专属"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 5, "attrNames": ["其他", "狂铁"]}, {"skinId": 17701, "skinName": "维京掠夺者", "heroId": "177", "heroName": "苍", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 3, "attrNames": ["苍", "其他"]}, {"skinId": 12005, "skinName": "夜都怪侠", "heroId": "120", "heroName": "白起", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "坦克", "accessWay": "荣耀战令获取", "skinNum": 7, "attrNames": ["其他", "战令限定"]}, {"skinId": 50104, "skinName": "吟游魔法", "heroId": "501", "heroName": "明世隐", "classTypeName": ["史诗品质", "战令限定", "限定", "魔法世界"], "heroType": "辅助", "accessWay": "荣耀战令获取", "skinNum": 6, "attrNames": ["其他", "明世隐", "史诗皮肤"]}, {"skinId": 15701, "skinName": "魅语", "heroId": "157", "heroName": "不知火舞", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 3, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 13001, "skinName": "鬼剑武藏", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 11405, "skinName": "唤灵魔甲", "heroId": "114", "heroName": "刘禅", "classTypeName": ["勇者品质", "活动专属"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 7, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 15305, "skinName": "金庭之子", "heroId": "153", "heroName": "兰陵王", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "刺客", "accessWay": "赛季之旅", "skinNum": 7, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 11402, "skinName": "绅士熊喵", "heroId": "114", "heroName": "刘禅", "classTypeName": ["史诗品质"], "heroType": "辅助", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 12105, "skinName": "幻夜卜梦", "heroId": "121", "heroName": "芈月", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 7, "attrNames": ["其他", "战令限定"]}, {"skinId": 19204, "skinName": "火炮绅士", "heroId": "192", "heroName": "黄忠", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 4, "attrNames": ["黄忠", "其他", "史诗皮肤"]}, {"skinId": 19503, "skinName": "原初追逐者", "heroId": "195", "heroName": "百里玄策", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证"], "heroType": "刺客", "accessWay": "时空之境获取", "skinNum": 5, "attrNames": ["赛季皮肤", "其他", "史诗皮肤"]}, {"skinId": 11106, "skinName": "沉稳之力", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["勇者品质", "五路精神"], "heroType": "射手", "accessWay": "", "skinNum": 10, "attrNames": ["孙尚香", "其他"]}, {"skinId": 10703, "skinName": "皇家上将", "heroId": "107", "heroName": "赵云", "classTypeName": ["史诗品质", "限定", "贵族限定"], "heroType": "战士", "accessWay": "贵族5级赠送", "skinNum": 10, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 18203, "skinName": "久胜战神", "heroId": "182", "heroName": "干将莫邪", "classTypeName": ["传说品质", "FMVP"], "heroType": "法师", "accessWay": "", "skinNum": 7, "attrNames": ["其他", "传说皮肤"]}, {"skinId": 18302, "skinName": "冰冠公主", "heroId": "183", "heroName": "雅典娜", "classTypeName": ["史诗品质", "冰雪之歌"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 6, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 13405, "skinName": "沙漠行僧", "heroId": "134", "heroName": "达摩", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S26赛季专属", "skinNum": 6, "attrNames": ["其他", "赛季皮肤"]}, {"skinId": 17504, "skinName": "驱傩正仪", "heroId": "175", "heroName": "钟馗", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S24赛季专属", "skinNum": 5, "attrNames": ["其他", "赛季皮肤"]}, {"skinId": 31202, "skinName": "鲨炮海盗猫", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "钻石夺宝获取", "skinNum": 8, "attrNames": ["其他", "沈梦溪"]}, {"skinId": 13306, "skinName": "万华元夜", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "射手", "accessWay": "S22赛季专属", "skinNum": 9, "attrNames": ["狄仁杰", "其他"]}, {"skinId": 14001, "skinName": "龙腾万里", "heroId": "140", "heroName": "关羽", "classTypeName": ["勇者品质", "新春专属"], "heroType": "战士", "accessWay": "", "skinNum": 7, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 50204, "skinName": "李小龙", "heroId": "502", "heroName": "裴擒虎", "classTypeName": ["史诗品质", "限定", "周年限定"], "heroType": "刺客", "accessWay": "限时活动获取", "skinNum": 7, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 18903, "skinName": "原初探秘者", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S21赛季专属", "skinNum": 5, "attrNames": ["其他", "赛季皮肤"]}, {"skinId": 53301, "skinName": "熊喵少女", "heroId": "533", "heroName": "阿古朵", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 4, "attrNames": ["其他", "阿古朵"]}, {"skinId": 13904, "skinName": "醍醐杖", "heroId": "139", "heroName": "老夫子", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S20赛季专属", "skinNum": 7, "attrNames": ["老夫子", "其他"]}, {"skinId": 18003, "skinName": "次元突破", "heroId": "180", "heroName": "哪吒", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "钻石夺宝获取", "skinNum": 5, "attrNames": ["哪吒", "其他"]}, {"skinId": 15401, "skinName": "剑舞者", "heroId": "154", "heroName": "花木兰", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 9, "attrNames": ["花木兰", "其他"]}, {"skinId": 50903, "skinName": "圆桌骑士", "heroId": "509", "heroName": "盾山", "classTypeName": ["勇者品质", "圆桌骑士", "活动专属"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 4, "attrNames": ["盾山", "其他"]}, {"skinId": 10702, "skinName": "未来纪元", "heroId": "107", "heroName": "赵云", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 10, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 19201, "skinName": "芝加哥教父", "heroId": "192", "heroName": "黄忠", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 4, "attrNames": ["黄忠", "其他"]}, {"skinId": 50103, "skinName": "疑决卦", "heroId": "501", "heroName": "明世隐", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S19赛季专属", "skinNum": 6, "attrNames": ["其他", "明世隐"]}, {"skinId": 31201, "skinName": "棒球奇才", "heroId": "312", "heroName": "沈梦溪", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 8, "attrNames": ["其他", "沈梦溪"]}, {"skinId": 18201, "skinName": "第七人偶", "heroId": "182", "heroName": "干将莫邪", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 7, "attrNames": ["干将莫邪", "其他"]}, {"skinId": 11804, "skinName": "归虚梦演", "heroId": "118", "heroName": "孙膑", "classTypeName": ["勇者品质", "活动专属", "归虚梦演"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 8, "attrNames": ["孙膑", "其他"]}, {"skinId": 14201, "skinName": "玩偶对对碰", "heroId": "142", "heroName": "安琪拉", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 8, "attrNames": ["安琪拉", "其他"]}, {"skinId": 16902, "skinName": "阿尔法小队", "heroId": "169", "heroName": "后羿", "classTypeName": ["勇者品质"], "heroType": "射手", "accessWay": "", "skinNum": 10, "attrNames": ["其他", "后羿"]}, {"skinId": 11202, "skinName": "福禄兄弟", "heroId": "112", "heroName": "鲁班七号", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 11, "attrNames": ["其他", "鲁班七号"]}, {"skinId": 17501, "skinName": "地府判官", "heroId": "175", "heroName": "钟馗", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 5, "attrNames": ["钟馗", "其他"]}, {"skinId": 19702, "skinName": "混沌棋", "heroId": "197", "heroName": "弈星", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S18赛季专属", "skinNum": 5, "attrNames": ["弈星", "其他"]}, {"skinId": 13201, "skinName": "激情绿茵", "heroId": "132", "heroName": "马可波罗", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 9, "attrNames": ["马可波罗", "其他"]}, {"skinId": 14601, "skinName": "哥特玫瑰", "heroId": "146", "heroName": "露娜", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "", "skinNum": 8, "attrNames": ["其他", "露娜"]}, {"skinId": 13305, "skinName": "鹰眼统帅", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["史诗品质", "信誉专属"], "heroType": "射手", "accessWay": "信誉系统专属", "skinNum": 9, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 51302, "skinName": "梁祝", "heroId": "513", "heroName": "上官婉儿", "classTypeName": ["史诗品质", "限定", "周年限定", "中华曲艺"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 6, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 12604, "skinName": "朔风刀", "heroId": "126", "heroName": "夏侯惇", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "坦克", "accessWay": "S17赛季专属", "skinNum": 6, "attrNames": ["夏侯惇", "其他"]}, {"skinId": 14802, "skinName": "炽热元素使", "heroId": "148", "heroName": "姜子牙", "classTypeName": ["勇者品质", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 5, "attrNames": ["其他", "姜子牙"]}, {"skinId": 50102, "skinName": "虹云星官", "heroId": "501", "heroName": "明世隐", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "钻石夺宝获取", "skinNum": 6, "attrNames": ["其他", "明世隐"]}, {"skinId": 12702, "skinName": "花好人间", "heroId": "127", "heroName": "甄姬", "classTypeName": ["勇者品质", "新春专属"], "heroType": "法师", "accessWay": "", "skinNum": 8, "attrNames": ["甄姬", "其他"]}, {"skinId": 51001, "skinName": "海之征途", "heroId": "510", "heroName": "孙策", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 6, "attrNames": ["其他", "孙策"]}, {"skinId": 17101, "skinName": "五福同心", "heroId": "171", "heroName": "张飞", "classTypeName": ["勇者品质", "新春专属"], "heroType": "辅助", "accessWay": "", "skinNum": 5, "attrNames": ["其他", "张飞"]}, {"skinId": 14903, "skinName": "德古拉伯爵", "heroId": "149", "heroName": "刘邦", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 5, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 14002, "skinName": "天启骑士", "heroId": "140", "heroName": "关羽", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 7, "attrNames": ["其他皮肤", "其他"]}, {"skinId": 50902, "skinName": "御銮", "heroId": "509", "heroName": "盾山", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S16赛季专属", "skinNum": 4, "attrNames": ["盾山", "其他"]}, {"skinId": 16803, "skinName": "御旌", "heroId": "168", "heroName": "牛魔", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S15赛季专属", "skinNum": 7, "attrNames": ["其他", "牛魔"]}, {"skinId": 11004, "skinName": "白昼王子", "heroId": "110", "heroName": "嬴政", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 5, "attrNames": ["其他", "战令限定", "史诗皮肤"]}, {"skinId": 16706, "skinName": "大圣娶亲", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定", "大话西游"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 10, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 16702, "skinName": "西部大镖客", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["西部大镖客"], "heroType": "刺客", "accessWay": "", "skinNum": 10, "attrNames": ["孙悟空", "其他"]}, {"skinId": 11601, "skinName": "爱心护理", "heroId": "116", "heroName": "阿轲", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "", "skinNum": 6, "attrNames": ["阿轲", "其他"]}, {"skinId": 13503, "skinName": "海滩派对", "heroId": "135", "heroName": "项羽", "classTypeName": ["勇者品质", "限定", "贵族限定", "夏日海滩"], "heroType": "坦克", "accessWay": "贵族4级赠送", "skinNum": 8, "attrNames": ["项羽", "其他"]}, {"skinId": 19303, "skinName": "青龙志", "heroId": "193", "heroName": "铠", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 7, "attrNames": ["年限皮肤", "其他", "史诗皮肤"]}, {"skinId": 12301, "skinName": "圣诞狂欢", "heroId": "123", "heroName": "吕布", "classTypeName": ["史诗品质", "CP皮肤", "圣诞颂歌"], "heroType": "战士", "accessWay": "", "skinNum": 10, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 12304, "skinName": "猎兽之王", "heroId": "123", "heroName": "吕布", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 10, "attrNames": ["其他", "吕布"]}, {"skinId": 15001, "skinName": "街头霸王", "heroId": "150", "heroName": "韩信", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "", "skinNum": 8, "attrNames": ["其他", "韩信"]}, {"skinId": 50402, "skinName": "御霄", "heroId": "504", "heroName": "米莱狄", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S14赛季专属", "skinNum": 6, "attrNames": ["米莱狄", "其他"]}, {"skinId": 11703, "skinName": "海滩丽影", "heroId": "117", "heroName": "钟无艳", "classTypeName": ["勇者品质", "夏日海滩"], "heroType": "战士", "accessWay": "钻石夺宝获取", "skinNum": 6, "attrNames": ["钟无艳", "其他"]}, {"skinId": 15201, "skinName": "精灵公主", "heroId": "152", "heroName": "王昭君", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 9, "attrNames": ["王昭君", "其他"]}, {"skinId": 13304, "skinName": "阴阳师", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["勇者品质", "限定", "成就限定"], "heroType": "射手", "accessWay": "成就系统专属", "skinNum": 9, "attrNames": ["狄仁杰", "其他"]}, {"skinId": 50302, "skinName": "御狮", "heroId": "503", "heroName": "狂铁", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S13赛季专属", "skinNum": 5, "attrNames": ["其他", "狂铁"]}, {"skinId": 12902, "skinName": "穷奇", "heroId": "129", "heroName": "典韦", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S12赛季专属", "skinNum": 7, "attrNames": ["勇者皮肤", "其他"]}, {"skinId": 10902, "skinName": "魅力维加斯", "heroId": "109", "heroName": "妲己", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "碎片商店兑换", "skinNum": 11, "attrNames": ["其他", "史诗皮肤"]}, {"skinId": 12805, "skinName": "烛龙", "heroId": "128", "heroName": "曹操", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S10赛季专属", "skinNum": 7, "attrNames": ["曹操", "其他"]}, {"skinId": 16601, "skinName": "死亡骑士", "heroId": "166", "heroName": "亚瑟", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "碎片商店兑换", "skinNum": 8, "attrNames": ["勇者皮肤", "其他"]}], "roleJobName": "无双王者", "userId": "463863935", "encrypted_user_id": "7ac02e483f99b5bda089076d2ff20634", "skinCountInfo": {"allSkinTypeCount": {"activityLimited": 327, "annualLimited": 11, "battlePass": 57, "epic": 251, "glory": 18, "legend": 124, "seasonal": 42, "warrior": 210}, "ownedSkinTypeCount": {"activityLimited": 68, "annualLimited": 3, "battlePass": 19, "epic": 58, "legend": 11, "seasonal": 30, "warrior": 96}, "notForSale": "86", "totalValue": "135484", "owned": "196", "totalSkinNum": "709"}, "allAttrNames": ["珍品无双", "珍品传说", "无双皮肤", "荣耀典藏皮肤", "传说皮肤", "史诗皮肤", "年限皮肤", "内测皮肤", "其他"]}