import json
import logging
import os
import sys
import time
import traceback
from time import sleep

import requests

from common.configs import current_config as config
from common.crypto_util import AESCryptoUtil
from common.luhao_models import FuncResponse
from common.dingtalk_robot import DingTalkRobot

aes_util = AESCryptoUtil("aEUAzI8eCEgdo02pfkLY+w==")

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

translation = {
    'activityLimited': '活动限定',
    'annualLimited': '年度限定',
    'battlePass': '战令',
    'epic': '史诗',
    'glory': '荣耀',
    'legend': '传说',
    'seasonal': '赛季',
    'warrior': '战士'
}


class WzryClient(object):
    def __init__(self):
        self.CONFIG = {
            'token': '',
            'user_id': '',
            'headers': {
                'Host': 'ssl.kohsocialapp.qq.com:10001',
                'cchannelid': '10014911',
                'cclientversioncode': '2037902808',
                'cclientversionname': '8.94.0919',
                'ccurrentgameid': '20001',
                'cgameid': '20001',
                'cgzip': '1',
                'cisarm64': 'true',
                'csupportarm64': 'true',
                'csystem': 'android',
                'csystemversioncode': '32',
                'csystemversionname': '12',
                'cpuhardware': 'HONOR',
                'gameareaid': '',
                'gameid': '20001',
                'gameroleid': '',
                'gameserverid': '',
                'gameusersex': '',
                'openid': '',
                'tinkerid': '2037902808_64_0',
                'content-encrypt': '',
                'accept-encrypt': '',
                'noencrypt': '1',
                'x-client-proto': 'https',
                'kohdimgender': '1',
                'user-agent': 'okhttp/4.9.1'
            }
        }

        # 初始化钉钉机器人
        self.dingtalk_robot = None
        self._init_dingtalk_robot()

    def _init_dingtalk_robot(self):
        """初始化钉钉机器人"""
        try:
            # 钉钉机器人配置（直接写死在代码中）
            ACCESS_TOKEN = "08a8285420b344660b4125d7d78a65936ccd279a9b9c7bb810ae8188027a70ee"
            SECRET = "SEC9d5fa738d5a41623384485934d4365cac024c94c42e63521917a38e14d2e5742"

            # 创建钉钉机器人实例
            self.dingtalk_robot = DingTalkRobot(ACCESS_TOKEN, SECRET)
            logging.info("钉钉机器人初始化成功")
        except Exception as e:
            logging.warning(f"钉钉机器人初始化失败: {e}")
            self.dingtalk_robot = None

    def _send_dingtalk_alert(self, title: str, message: str, level: str = "ERROR"):
        """发送钉钉告警消息"""
        if not self.dingtalk_robot:
            return

        try:
            # 根据告警级别设置不同的图标和颜色
            level_icons = {
                "ERROR": "🚨",
                "WARNING": "⚠️",
                "INFO": "ℹ️"
            }

            icon = level_icons.get(level, "🔔")
            alert_title = f"{icon} 王者荣耀录号告警"

            # 从环境变量获取设备ID
            device_id = os.getenv('DEVICE_ID', '未知设备')

            # 构建文本格式的告警消息
            alert_content = f"""{alert_title}

设备ID: {device_id}
告警级别: {level}
告警标题: {title}
详细信息: {message}
时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""

            # 发送告警消息
            result = self.dingtalk_robot.send_text(alert_content, at_mobiles=["13176667929"])
            logging.info(f"钉钉告警发送成功: {title}, 设备ID: {device_id}, 响应: {result}")

            # 如果是ERROR级别的告警，发送完消息后停止进程
            if level == "ERROR":
                logging.critical(f"检测到严重错误，程序即将停止。告警标题: {title}, 设备ID: {device_id}")
                logging.critical("因严重错误而停止进程，避免继续执行可能造成更多问题")
                sys.exit(1)

        except Exception as e:
            logging.error(f"钉钉告警发送异常: {e}")
            # 即使钉钉发送失败，如果是ERROR级别也要停止进程
            if level == "ERROR":
                logging.critical(f"钉钉告警发送失败，但检测到严重错误，程序仍需停止。告警标题: {title}")
                logging.critical("因严重错误而停止进程，避免继续执行可能造成更多问题")
                sys.exit(1)

    def get_role_id(self, target_user_id):
        """通过固定参数获取 roleId"""
        headers = self.CONFIG['headers'].copy()
        headers.update({
            'encodeparam': config.wz_req_config['encodeParam'],
            'token': self.CONFIG['token'],
            'userid': self.CONFIG['user_id'],
            'content-type': 'application/x-www-form-urlencoded'
        })

        data = {
            "friendNickname": "",
            "friendUserId": target_user_id,
            "cChannelId": "10014911",
            "cClientVersionCode": "2037902808",
            "cClientVersionName": "8.94.0919",
            "cCurrentGameId": "20001",
            "cGameId": "20001",
            "cGzip": "1",
            "cIsArm64": "true",
            "cRand": str(int(time.time() * 1000)),
            "cSupportArm64": "true",
            "cSystem": "android",
            "cSystemVersionCode": "32",
            "cSystemVersionName": "12",
            "cpuHardware": "HONOR",
            "encodeParam": config.wz_req_config['encodeParam'],
            "gameAreaId": config.wz_req_config['gameareaid'],
            "gameId": "20001",
            "gameRoleId": config.wz_req_config['gameroleid'],
            "gameServerId": config.wz_req_config['gameserverid'],
            "gameUserSex": config.wz_req_config['gameusersex'],
            "openId": config.wz_req_config['openid'],
            "tinkerId": "2037902808_64_0",
            "token": self.CONFIG['token'],
            "userId": self.CONFIG['user_id']
        }

        try:
            response = requests.post(
                url='https://ssl.kohsocialapp.qq.com:10001/user/useradapter',
                headers=headers,
                data=data,
                timeout=10
            )
            response.raise_for_status()
            result = response.json()

            print('#####', result)

            if result.get('result') == 0:
                if result.get('returnCode') == -30003:
                    self._send_dingtalk_alert("登录态失效", result, "ERROR")
                    return FuncResponse.fail('获取roleId失败')
                if not result.get('data'):
                    # error_msg = f'营地ID不存在: {target_user_id}'
                    # self._send_dingtalk_alert("营地ID查询失败", error_msg, "WARNING")
                    return FuncResponse.fail('营地ID不存在')
                if 'roleId' not in result.get('data') or result.get('data').get('roleId') == '0':
                    # error_msg = f'营地ID无效或roleId为空: {target_user_id}'
                    # self._send_dingtalk_alert("营地ID查询失败", error_msg, "WARNING")
                    return FuncResponse.fail('营地ID不存在')
                role_id = result.get('data', {}).get('roleId')
                logging.info(f"获取成功: roleId = {role_id}")
                return FuncResponse.ok(role_id)
            else:
                error_msg = f"接口错误: {result.get('returnMsg')}, 用户ID: {target_user_id}"
                logging.error(error_msg)
                self._send_dingtalk_alert("王者荣耀API接口错误", error_msg, "ERROR")
                return FuncResponse.fail_need_retry("接口错误")

        except requests.exceptions.RequestException as e:
            error_msg = f"获取roleId网络请求失败: {str(e)}, 用户ID: {target_user_id}"
            logging.error(f"请求失败: {traceback.format_exc()}")
            self._send_dingtalk_alert("网络请求异常", error_msg, "ERROR")
            raise e

    def get_user_profile(self, target_user_id, role_id):
        headers = self.CONFIG['headers'].copy()
        headers.update({
            'Host': 'kohcamp.qq.com',
            'istrpcrequest': 'true',
            'encodeparam': self.CONFIG['encodeParam'],
            'token': self.CONFIG['token'],
            'userid': self.CONFIG['user_id'],
            'content-type': 'application/json; charset=UTF-8'
        })

        data = {
            "resVersion": 3,
            "recommendPrivacy": 0,
            "apiVersion": 2,
            "targetUserId": target_user_id,
            "targetRoleId": role_id,
            "itsMe": False
        }

        err_msg = None
        try:
            response = requests.post(
                url='https://kohcamp.qq.com/game/koh/profile',
                headers=headers,
                json=data,
                timeout=10
            )
            response.raise_for_status()
            logging.info(f"HTTP状态码: {response.status_code}")
            logging.info(f"响应数据: {response.json()}")

            profile = response.json()
            if profile['returnCode'] != 0:
                # error_msg = f"获取用户资料失败: {profile['returnMsg']}, 用户ID: {target_user_id}, roleId: {role_id}"
                # self._send_dingtalk_alert("用户资料获取失败", error_msg, "WARNING")
                return FuncResponse.fail(profile['returnMsg'])
            target_role_id = profile['data']['targetRoleId']

            result = None
            for item in profile['data']['roleList']:
                if item['roleId'] == target_role_id:
                    role_job_name = item.get('roleJobName')
                    result = {
                        'roleJobName': role_job_name,
                        'userId': target_user_id,
                        'targetRoleId': target_role_id
                    }
                    break
            return FuncResponse.ok(result)
        except Exception as e:
            # error_msg = f"获取用户资料异常: {str(e)}, 用户ID: {target_user_id}, roleId: {role_id}"
            logging.error(f"请求失败: {str(e)}")
            # self._send_dingtalk_alert("用户资料获取异常", error_msg, "ERROR")
            return FuncResponse.fail_need_retry()

    def get_hero_list(self, role_id):
        user_id = self.CONFIG['user_id']
        logging.info(f"[INFO] Fetching hero list for roleId: {role_id}, userId: {user_id}")
        try:
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "token": self.CONFIG['token'],
                "userId": user_id,
            }
            data = f"roleId={role_id}&cClientVersionName=8.94.0919&token={self.CONFIG['token']}&userId={user_id}"
            response = requests.post("https://ssl.kohsocialapp.qq.com:10001/play/h5getherolist", data=data,
                                     headers=headers)
            logging.info("[INFO] Hero list successfully fetched.")
            return response.json()
        except Exception as error:
            # error_msg = f"获取英雄列表失败: {str(error)}, roleId: {role_id}"
            logging.error(f"[ERROR] Error fetching hero skin list: {error}")
            # self._send_dingtalk_alert("英雄列表获取失败", error_msg, "ERROR")
            return None

    def get_target_user_info(self, target_user_id, device_id='default'):
        client_config = config.wz_req_config.get(device_id)
        if client_config is None:
            client_config = config.wz_req_config.get('default')
        self.CONFIG.update(client_config)

        try:
            result = self.get_role_id(target_user_id)
            if result.is_ok():
                role_id = result.data
                print(f"role_id: {role_id}")
            else:
                return result
        except Exception as error:
            # error_msg = f"获取roleId异常: {str(error)}, 用户ID: {target_user_id}"
            logging.error(f"[ERROR] Error fetching role id: {error}")
            # self._send_dingtalk_alert("roleId获取异常", error_msg, "ERROR")
            return FuncResponse.fail_need_retry()

        sleep(1)

        result = self.get_user_profile(target_user_id, role_id)
        if not result.is_ok():
            return result

        user_profile = result.data

        sleep(1)
        hero_response = self.get_hero_list(role_id)
        if hero_response is None:
            # error_msg = f"英雄列表获取失败(返回None): 用户ID: {target_user_id}, roleId: {role_id}"
            # self._send_dingtalk_alert("英雄列表获取失败", error_msg, "ERROR")
            return FuncResponse.fail_need_retry()
        if hero_response['returnCode'] != 0:
            # error_msg = f"用户隐藏了英雄: 用户ID: {target_user_id}, returnCode: {hero_response['returnCode']}"
            # self._send_dingtalk_alert("用户隐藏英雄", error_msg, "WARNING")
            return FuncResponse.fail('用户隐藏了英雄')

        sleep(1)

        skin_response = self.get_skin_list(role_id)
        if skin_response is None:
            # error_msg = f"皮肤列表获取失败(返回None): 用户ID: {target_user_id}, roleId: {role_id}"
            # self._send_dingtalk_alert("皮肤列表获取失败", error_msg, "ERROR")
            return FuncResponse.fail_need_retry()
        elif skin_response.get('returnCode', 0) == -30107:
            error_msg = f"皮肤接口返回错误-30107: {skin_response['returnMsg']}"
            print(skin_response['returnMsg'])
            self._send_dingtalk_alert("皮肤接口错误", error_msg, "ERROR")
            return FuncResponse.fail(skin_response['returnMsg'])
        if 'skinCountInfo' not in skin_response:
            # error_msg = f"皮肤数据缺失skinCountInfo: 用户ID: {target_user_id}, 可能用户隐藏了皮肤"
            # self._send_dingtalk_alert("皮肤数据获取失败", error_msg, "WARNING")
            return FuncResponse.fail('获取皮肤失败，可能是用户隐藏了皮肤')

        obj = self.do_report(user_profile, hero_response, skin_response)
        return FuncResponse.ok(obj)

    def do_report(self, result, hero_response, skin_response):
        obj = {
            "heroList": [],
            "skinList": [],
            "roleJobName": result.get('roleJobName'),
            "userId": result.get('userId'),
            "encrypted_user_id": aes_util.encrypt(result.get('userId')),
        }

        hero_list = hero_response.get('data', {}).get('heroList', [])
        all_hero = {str(hero['heroId']): hero for hero in hero_list}

        for hero in hero_list:
            if not hero.get('notOwnedFlag'):
                obj['heroList'].append({
                    'heroId': hero['heroId'],
                    'heroType': hero['heroType'],
                    'name': hero['name'],
                })

        if skin_response:
            skin_count_info = skin_response.get('skinCountInfo', {})
            print('ownedSkinTypeCount:', skin_count_info)

            print("已拥有皮肤的详细分类:")
            for skin_type, count in skin_count_info['ownedSkinTypeCount'].items():
                total = skin_count_info['allSkinTypeCount'][skin_type]
                print(f"{translation[skin_type]}({skin_type})：{count}/{total}")

            print(f"未售出皮肤数量: {skin_count_info['notForSale']}")
            print(f"已拥有皮肤总数: {skin_count_info['owned']}")
            print(f"所有皮肤总数: {skin_count_info['totalSkinNum']}")
            print(f"皮肤价值: {skin_count_info['totalValue']}")

            obj['skinCountInfo'] = skin_count_info

            skin_data = skin_response.get('skinData', [])
            all_skin_conf = skin_response.get('allSkinConf', [])
            all_hero_conf = skin_response.get('allHeroConf', [])
            skin_num_obj = {}

            all_hero_and_skin = {
                "allSkinConf": all_skin_conf,
                "allHeroConf": all_hero_conf
            }
            self.save_to_json_file(all_hero_and_skin, "all_hero_and_skin.json")

            skin_conf_by_id = {}
            for skin in all_skin_conf:
                if skin.get('isHidden') != 1:
                    skin_conf_by_id[skin['skinId']] = skin
                    hero_name = skin.get('heroName')
                    skin_num_obj[hero_name] = skin_num_obj.get(hero_name, 0) + 1

            for skin in skin_data:
                skin_id = skin.get('skinId')
                find_it = skin_conf_by_id.get(skin_id)
                if find_it:
                    hero_id = str(find_it['heroId'])
                    obj['skinList'].append({
                        'skinId': find_it['skinId'],
                        'skinName': find_it['skinName'],
                        'heroId': hero_id,
                        'heroName': find_it['heroName'],
                        'classTypeName': find_it['classTypeName'],
                        'heroType': all_hero[hero_id]['heroType'],
                        'accessWay': find_it['accessWay'],
                        'skinNum': skin_num_obj[find_it['heroName']],
                    })

        print('[INFO] Report successfully sent.')
        return obj

    def save_to_json_file(self, data, file_path):
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"[INFO] Data successfully written to file: {file_path}")
        except Exception as error:
            # error_msg = f"文件保存失败: {str(error)}, 文件路径: {file_path}"
            print(f"[ERROR] Error saving data to file: {error}")
            # self._send_dingtalk_alert("文件保存失败", error_msg, "ERROR")

    def get_skin_list(self, role_id):
        """获取皮肤列表"""
        headers = self.CONFIG['headers'].copy()
        headers.update({
            'Host': 'kohcamp.qq.com',
            'istrpcrequest': 'true',
            'encodeparam': 'HIuTo%2FwxIg9ZtVmukdU%2B5nssrmo%2BpJ%2FNOmRZj4BQDS3PNKK65vWjBOhOPYv851Nux5fyiXNvm79X3kYtaSfCVv8ud5qV3mg6YAej8spND%2BIDhVOaN4rwdYzjahgXBmdsd%2FXy%2Fg%3D%3D',
            'token': self.CONFIG['token'],
            'userid': self.CONFIG['user_id'],
            'content-type': 'application/json; charset=UTF-8'
        })

        payload = {
            "recommendPrivacy": 0,
            "roleId": role_id
        }

        try:
            response = requests.post(
                url='https://kohcamp.qq.com/game/itempage/skinlist',
                headers=headers,
                json=payload,
                timeout=10
            )
            response.raise_for_status()
            logging.info(f"获取皮肤列表成功")
            # print(response.json())
            # save to file
            # save_to_json_file(response.json(), "skin_list.json")
            return response.json()
        except requests.exceptions.RequestException as e:
            # error_msg = f"获取皮肤列表网络请求失败: {str(e)}, roleId: {role_id}"
            logging.error(f"获取皮肤列表失败: {str(e)}")
            # self._send_dingtalk_alert("皮肤列表网络请求失败", error_msg, "ERROR")
            return None

    def add_wzry_detail(self, obj):
        print(f"[INFO] Sending report to external service for userId: {obj.get('userId')}")
        try:
            url = f"http://api.yyd8.com/mall-portal/import/product/addWzryDetail?wzryId={obj.get('encrypted_user_id')}"
            response = requests.post(url, json=obj)
            print("[INFO] Report successfully sent.")
        except Exception as err:
            error_msg = f"发送报告到portal服务失败: {str(err)}, 用户ID: {obj.get('userId')}"
            traceback.print_exc()
            print(f"[ERROR] Failed to send report: {err}")
            self._send_dingtalk_alert("外部服务报告发送失败", error_msg, "ERROR")


if __name__ == "__main__":
    # 隐藏英雄 462981260
    # 隐藏皮肤 338130766

    # 正常 1866747812
    wzry_client = WzryClient()
    r = wzry_client.get_target_user_info(target_user_id='2124113433', device_id='51')
    if r.is_ok():
        print('success:', r.data)
    else:
        print('fail:', r.msg)
