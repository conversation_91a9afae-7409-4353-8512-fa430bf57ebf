# Token池使用说明

## 文件说明

- `token_models.py` - Token数据模型
- `simple_token_pool.py` - Token池管理核心
- `wzry_client_with_pool.py` - 集成Token池的客户端
- `import_tokens.py` - Token导入工具
- `wzry_main.py` - 主程序（已集成Token池）

## 使用步骤

### 1. 导入Token
```bash
python wzry/import_tokens.py import
```

### 2. 查看状态
```bash
python wzry/import_tokens.py
```

### 3. 启动主程序
```bash
python wzry/wzry_main.py
```

## Token配置

在配置文件中添加comment字段：
```python
'device1': {
    'token': 'xxx',
    'user_id': 'xxx', 
    'encodeParam': 'xxx',
    'comment': 'DX_QQ1',  # 新增备注字段
    # ... 其他字段
}
```

## 功能特点

- ✅ 随机选择Token
- ✅ 自动状态管理
- ✅ 支持comment备注
- ✅ 线程安全
- ✅ 钉钉告警

## 注意事项

- Token失效时会自动跳过
- 支持并发调用
- 自动记录comment字段到MongoDB
