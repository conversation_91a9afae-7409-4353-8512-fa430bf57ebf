import math
import os
import random
from io import BytesIO

from PIL import Image, ImageDraw, ImageOps, ImageFont, ImageColor, ImageFilter
from pathlib import Path

# 获取当前脚本所在目录
script_dir = Path(__file__).resolve().parent


def get_jpeg_images(folder_path):
    """
    获取文件夹下所有的 JPEG 图片路径
    :param folder_path: 文件夹路径
    :return: JPEG 图片路径列表
    """
    jpeg_images = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg')):  # 支持 .jpg 和 .jpeg 文件
                jpeg_images.append(os.path.join(root, file))
    return jpeg_images


def select_random_images(image_list, num_images=12):
    """
    从图片列表中随机选取指定数量的图片
    :param image_list: 图片路径列表
    :param num_images: 需要选取的图片数量
    :return: 随机选取的图片路径列表
    """
    if len(image_list) <= num_images:
        return image_list  # 如果图片数量不足，返回所有图片
    return random.sample(image_list, num_images)


def round_corners(image, radius):
    """
    将图片裁剪为圆角效果
    :param image: 输入的图片
    :param radius: 圆角半径
    :return: 圆角图片
    """
    # 创建一个蒙版
    mask = Image.new('L', image.size, 0)
    draw = ImageDraw.Draw(mask)

    # 绘制圆角矩形
    draw.rounded_rectangle([(0, 0), image.size], radius=radius, fill=255)

    # 应用蒙版
    rounded_image = ImageOps.fit(image, mask.size, centering=(0.5, 0.5))
    rounded_image.putalpha(mask)

    return rounded_image


# def round_corners(image, radius, scale_factor=2):
#     """
#     使用超采样抗锯齿将图片裁剪为圆角效果。
#
#     :param image: 输入的图片
#     :param radius: 圆角半径
#     :param scale_factor: 图像放大倍数（默认为 2）
#     :return: 圆角图片
#     """
#     # 计算放大后的尺寸
#     original_size = image.size
#     scaled_size = (original_size[0] * scale_factor, original_size[1] * scale_factor)
#
#     # 放大图像
#     scaled_image = image.resize(scaled_size, Image.LANCZOS)
#
#     # 创建一个放大后的蒙版
#     mask = Image.new('L', scaled_size, 0)
#     draw = ImageDraw.Draw(mask)
#
#     # 绘制圆角矩形（圆角半径按比例放大）
#     scaled_radius = radius * scale_factor
#     draw.rounded_rectangle([(0, 0), scaled_size], radius=scaled_radius, fill=255)
#
#     # 应用蒙版
#     rounded_image = ImageOps.fit(scaled_image, mask.size, centering=(0.5, 0.5))
#     rounded_image.putalpha(mask)
#
#     # 将放大后的图像缩小回原始尺寸
#     rounded_image = rounded_image.resize(original_size, Image.LANCZOS)
#
#     return rounded_image


def add_text_to_image(image, text_list, font_path):
    """
    在图片上添加多个文字块
    :param image: 图片对象
    :param text_list: 文字块列表，每个文字块包含文字内容、位置、字体大小、字体颜色等信息
    :param font_path: 字体文件路径
    :return: 添加文字后的图片
    """
    draw = ImageDraw.Draw(image)

    for text_info in text_list:
        text = text_info["text"]
        position = text_info["position"]
        font_size = text_info["font_size"]
        font_color = text_info["font_color"]
        font_weight = text_info.get("font_weight", "normal")
        letter_spacing = text_info.get("letter_spacing", 0)

        # 加载字体
        font = ImageFont.truetype(font_path, font_size)

        # 设置字体样式
        if font_weight == "bold":
            font = ImageFont.truetype(font_path, font_size, encoding="utf-8")

        # 添加文字
        draw.text(position, text, font=font, fill=font_color, spacing=letter_spacing)

    return image


def add_text_to_layer_center(image, layer, layer_position, text_info, font_path):
    draw = ImageDraw.Draw(image)

    text = text_info["text"]
    font_size = text_info["font_size"]
    font_color = text_info["font_color"]
    font_weight = text_info.get("font_weight", "normal")
    letter_spacing = text_info.get("letter_spacing", 0)

    # 加载字体
    font = ImageFont.truetype(font_path, font_size)

    # 设置字体样式
    if font_weight == "bold":
        font = ImageFont.truetype(font_path, font_size, encoding="utf-8")

    # 获取文字的边界框
    bbox = font.getbbox(text)
    text_width = bbox[2] - bbox[0]  # 右边界 - 左边界
    text_height = bbox[3] - bbox[1]  # 下边界 - 上边界

    # 获取图层的宽度和高度
    image_width, image_height = layer.size

    # 计算文字的起始位置，使得文字居中
    x = layer_position[0] + (image_width - text_width) / 2
    y = layer_position[1] + (image_height - text_height) / 2

    # 添加文字
    draw.text((x, y), text, font=font, fill=font_color, spacing=letter_spacing)
    return image


def add_number_to_image(cover, number_objs, number_folder="number"):
    """
    在底图上多个位置贴数字图片，并使数字居中显示（根据实际宽度动态计算位置）
    """
    number_height = 48

    for number_obj in number_objs:
        # 将数字转换为字符串
        number_str = str(number_obj[0])

        # 初始化总宽度
        total_width = 0

        # 遍历每个数字，获取实际宽度并计算总宽度
        digit_widths = []
        for digit in number_str:
            # 构造数字图片路径
            digit_path = f"{script_dir}/{number_folder}/{digit}.png"

            # 打开数字图片
            digit_image = Image.open(digit_path).convert("RGBA")

            # 获取数字图片的实际宽度
            digit_width = digit_image.size[0]
            digit_widths.append(digit_width)

            # 累加总宽度
            total_width += digit_width

        # 计算起始位置，使数字居中
        start_x = number_obj[1] - total_width // 2
        start_y = number_obj[2] - number_height // 2

        # 遍历每个数字，粘贴到指定位置
        current_x = start_x
        for i, digit in enumerate(number_str):
            # 构造数字图片路径
            digit_path = f"{script_dir}/{number_folder}/{digit}.png"

            # 打开数字图片
            digit_image = Image.open(digit_path).convert("RGBA")

            # 计算当前数字的贴图位置
            digit_position = (current_x, start_y)

            # 将数字图片粘贴到底图上
            cover.paste(digit_image, digit_position, digit_image)

            # 更新当前 x 坐标
            current_x += digit_widths[i]

    # 返回贴图后的图片
    return cover


def calculate_distance(point1, point2):
    """
    计算两点之间的欧几里得距离。

    :param point1: 第一个点 (x, y)
    :param point2: 第二个点 (x, y)
    :return: 距离
    """
    return math.sqrt((point1[0] - point2[0]) ** 2 + (point1[1] - point2[1]) ** 2)


def calculate_new_point(center, vertex, a, b, distance):
    """
    根据比例计算新点的位置。

    :param center: 中心点坐标 (x, y)
    :param vertex: 顶点坐标 (x, y)
    :param a: 比例中的分子
    :param b: 比例中的分母
    :param distance: 中心点到顶点的距离
    :return: 新点坐标 (x, y)
    """
    # 计算方向向量
    vector = (vertex[0] - center[0], vertex[1] - center[1])
    # 归一化向量
    unit_vector = (vector[0] / distance, vector[1] / distance)
    # 计算新点的距离
    new_distance = (a / b) * distance
    # 计算新点坐标
    new_point = (center[0] + unit_vector[0] * new_distance, center[1] + unit_vector[1] * new_distance)
    return new_point


from PIL import Image, ImageDraw, ImageColor


def plot_points(image, center, vertices, input_pairs, point_radius=4, point_color="#58E9FF", line_width=2,
                fill_color="#298AEC", fill_alpha=0.6, scale_factor=2):
    """
    使用超采样抗锯齿绘制线条。

    :param scale_factor: 图像放大倍数（默认为 2）
    """
    # 计算放大后的尺寸
    original_size = image.size
    scaled_size = (original_size[0] * scale_factor, original_size[1] * scale_factor)

    # 创建一个放大后的透明图层
    scaled_overlay = Image.new("RGBA", scaled_size, (0, 0, 0, 0))
    scaled_draw = ImageDraw.Draw(scaled_overlay)

    # 计算中心点到每个顶点的距离
    distances = [calculate_distance(center, vertex) for vertex in vertices]

    # 存储新点的坐标
    new_points = []

    # 根据比例计算新点的位置（按比例放大）
    for i, (a, b) in enumerate(input_pairs):
        vertex = vertices[i]
        distance = distances[i]
        new_point = calculate_new_point(center, vertex, a, b, distance)
        new_points.append((new_point[0] * scale_factor, new_point[1] * scale_factor))  # 按比例放大

    # 设置填充颜色的透明度
    fill_rgba = ImageColor.getrgb(fill_color) + (int(255 * fill_alpha),)

    # 先绘制填充图形（最底层）
    scaled_draw.polygon(new_points, fill=fill_rgba)

    # 绘制线条（按比例放大）
    scaled_line_width = line_width * scale_factor  # 线条宽度按比例放大
    for i in range(len(new_points)):
        start_point = new_points[i]
        end_point = new_points[(i + 1) % len(new_points)]  # 连接最后一个点回到第一个点
        scaled_draw.line([start_point, end_point], fill=point_color, width=scaled_line_width)

    # 最后绘制点（按比例放大）
    scaled_point_radius = point_radius * scale_factor  # 点半径按比例放大
    for point in new_points:
        scaled_draw.ellipse(
            (point[0] - scaled_point_radius, point[1] - scaled_point_radius,
             point[0] + scaled_point_radius, point[1] + scaled_point_radius),
            fill=point_color
        )

    # 将放大后的图像缩小回原始尺寸
    overlay = scaled_overlay.resize(original_size, Image.LANCZOS)

    # 将透明图层与底图合并
    image = Image.alpha_composite(image, overlay)
    return image


def paste_overlay(base_img, overlay_path, overlay_position):
    """
    在底图上粘贴贴图
    :param base_img: 底图（PIL.Image 对象）
    :param overlay_path: 贴图文件路径
    :param overlay_position: 贴图位置 (x, y)
    """
    overlay_img = Image.open(overlay_path)
    # 如果贴图不是 RGBA 模式，则转换为 RGBA 模式
    if overlay_img.mode != 'RGBA':
        overlay_img = overlay_img.convert('RGBA')

    # 粘贴贴图，支持透明贴图
    base_img.paste(overlay_img, overlay_position, overlay_img)


def composite_images(cover_path, small_images, positions, sizes, corner_radius, text_list,
                     font_path, number_objs, output_path, number_folder="number"):
    """
    将小图合成到底图上，并应用圆角效果、文字、贴数字和绘制封闭图形
    :param cover_path: 底图路径
    :param small_images: 小图路径列表
    :param positions: 小图位置列表
    :param sizes: 小图大小列表
    :param corner_radius: 圆角半径
    :param text_list: 文字块列表
    :param font_path: 字体文件路径
    :param number: 要贴的数字
    :param number_center_positions: 多个贴图区域的中心点列表 [(x1, y1), (x2, y2), ...]
    :param number_height: 每个数字图片的高度
    :param number_folder: 数字图片所在的文件夹路径
    """
    # 打开底图
    cover = Image.open(cover_path).convert("RGBA")

    # 遍历每张小图
    for i, (small_image_obj, position, size) in enumerate(zip(small_images, positions, sizes)):
        if small_image_obj == None:
            # 占位
            continue
        # 打开小图，添加错误处理
        try:
            small_image = Image.open(small_image_obj['url']).convert("RGBA")
        except Exception as e:
            print(f"[WzCover] 无法打开图片: {small_image_obj['url']}, 错误: {e}")
            continue  # 跳过这张图片

        # 调整小图大小
        small_image = small_image.resize(size)

        # 应用圆角效果
        small_image = round_corners(small_image, corner_radius)

        # 将小图粘贴到底图上
        cover.paste(small_image, position, small_image)

        # 蒙层
        if i <= 11:
            paste_overlay(cover, f"{script_dir}/蒙层.png", (position[0], position[1] + 160))
            label_text = {
                "text": small_image_obj['name'],
                # "position": (position[0] + 11, position[1] + 160 + 12),
                "font_size": 14,
                "font_color": "#DEDEDE",
                "font_weight": "normal"
            }
            layer = Image.open(f"{script_dir}/蒙层.png").convert("RGBA")

            cover = add_text_to_layer_center(cover, layer, (position[0], position[1] + 160), label_text, font_path)

    # 添加底部价格图片，保持原尺寸
    bottom_price_path = f"{script_dir}/底部价格.png"  # 替换为你的底部价格图片路径
    bottom_price_image = Image.open(bottom_price_path).convert("RGBA")
    bottom_price_position = (551, 749)  # 指定位置
    cover.paste(bottom_price_image, bottom_price_position, bottom_price_image)

    # 添加文字
    cover = add_text_to_image(cover, text_list, font_path)

    # 绘制封闭图形
    center = (1147, 412)
    vertices = [
        (1147, 178),
        (1347, 293),
        (1347, 523),
        (1147, 638),
        (947, 523),
        (947, 293)
    ]

    input_pairs = []
    for number_obj in number_objs:
        input_pairs.append((number_obj[0], number_obj[3]))
    cover = plot_points(cover, center, vertices, input_pairs)

    # 圆圈
    circle = f"{script_dir}/圈.png"
    circle_image = Image.open(circle).convert("RGBA")
    circle_position = (870, 104)  # 指定位置
    cover.paste(circle_image, circle_position, circle_image)

    # 贴数字
    cover = add_number_to_image(cover, number_objs, number_folder)

    # logo
    logo = f"{script_dir}/logo_1.png"
    logo_image = Image.open(logo).convert("RGBA")
    logo_position = (1085, 721)
    cover.paste(logo_image, logo_position, logo_image)


    # 保存合成后的图片 jpg
    rgb_cover = cover.convert("RGB")
    # rgb_cover.save(output_path)
    # with open(output_path, 'wb') as f:
    #     rgb_cover.save(f)  # 直接保存到文件对象，确保写入完成
    # print("图片合成完成，已保存为: ", output_path)

    # 将生成的图像保存到内存中的字节流
    img_byte_array = BytesIO()
    rgb_cover.save(img_byte_array, format='JPEG', quality=95)  # 指定格式和质量
    img_byte_array.seek(0)  # 重置指针到开头

    return img_byte_array.getvalue()


def create_cover(small_images, text_dict, nums_dict, output_path):
    cover_tpl_path = script_dir / "tpl_cover.png"

    # 每张小图的位置 (x, y)
    positions = [
        (52, 109),  # 第 1 张小图的位置
        (208, 109),  # 第 2 张小图的位置
        (364, 109),  # 第 3 张小图的位置
        (520, 109),  # 第 4 张小图的位置
        (52, 339),  # 第 5 张小图的位置
        (208, 339),  # 第 6 张小图的位置
        (364, 339),  # 第 7 张小图的位置
        (520, 339),  # 第 8 张小图的位置
        (52, 569),  # 第 9 张小图的位置
        (208, 569),  # 第 10 张小图的位置
        (364, 569),  # 第 11 张小图的位置
        (520, 569),  # 第 12 张小图的位置

        # 大国标
        (706, 167),
        (706, 259),

        # 小国标
        (706, 417),
        (706, 509),
        (706, 601),
        (706, 693)

    ]

    # 每张小图的大小 (width, height)
    sizes = [(136, 210)] * 12 + [(66, 66)] * 6  # 所有小图的大小相同

    # 圆角半径 (10px, 10px, 10px, 10px)
    corner_radius = 10

    # 文字内容列表
    text_list = [
        {
            "text": "王者荣耀交流群:1026229360",
            "position": (38, 24),
            "font_size": 30,
            "font_color": "#ffffff",
            "font_weight": "bold",
            "letter_spacing": -1
        },
        {
            "text": "账号编号:" + text_dict["账号编号"],
            "position": (456, 24),
            "font_size": 30,
            "font_color": "#ffffff",
            "font_weight": "bold",
            "letter_spacing": -1
        },
        {
            "text": "区服信息",
            "position": (775, 14),
            "font_size": 20,
            "font_color": "#c7e2ff",
            "font_weight": "normal"
        },
        {
            "text": text_dict["区服信息"],
            "position": (775, 42),
            "font_size": 22,
            "font_color": "#ffffff",
            "font_weight": "bold"
        },
        {
            "text": "实名情况",
            "position": (916, 14),
            "font_size": 20,
            "font_color": "#c7e2ff",
            "font_weight": "normal"
        },
        {
            "text": text_dict["实名情况"],
            "position": (916, 42),
            "font_size": 22,
            "font_color": "#ffffff",
            "font_weight": "bold"
        },
        {
            "text": "防沉迷",
            "position": (1087, 14),
            "font_size": 20,
            "font_color": "#c7e2ff",
            "font_weight": "normal"
        },
        {
            "text": text_dict["防沉迷"],
            "position": (1088, 42),
            "font_size": 22,
            "font_color": "#ffffff",
            "font_weight": "bold"
        },
        {
            "text": "国标数量",
            "position": (1228, 14),
            "font_size": 20,
            "font_color": "#c7e2ff",
            "font_weight": "normal"
        },
        {
            "text": text_dict["国标数量"],
            "position": (1229, 42),
            "font_size": 22,
            "font_color": "#ffffff",
            "font_weight": "bold"
        },
        {
            "text": "游戏段位",
            "position": (1369, 14),
            "font_size": 20,
            "font_color": "#c7e2ff",
            "font_weight": "normal"
        },
        {
            "text": text_dict["游戏段位"],
            "position": (1370, 42),
            "font_size": 22,
            "font_color": "#ffffff",
            "font_weight": "bold"
        },
        {
            "text": "皮肤价值(点券):",
            "position": (603, 790),
            "font_size": 30,
            "font_color": "#E7DDBD",
            "font_weight": "normal"
        },
        {
            "text": text_dict["皮肤价值"],
            "position": (834, 758),
            "font_size": 60,
            "font_color": "#FDE90A",
            "font_weight": "normal"
        }
    ]

    # 字体样式
    font_path = script_dir / "SourceHanSansSC-Bold-2.otf"  # 替换为你的字体文件路径

    # 多个贴图区域的中心点
    number_objs = [
        # 数字, x, y, 最大数字
        (nums_dict['英雄数量'], 1153, 205, 123),  # 顶点
        (nums_dict['典藏数量'], 1388, 314, 18),
        (nums_dict['传说数量'], 1354, 506, 123),
        (nums_dict['史诗数量'], 1153, 617, 187),
        (nums_dict['皮肤总数'], 970, 506, 684),
        (nums_dict['贵族等级'], 927, 314, 12),
    ]

    # 调用合成方法
    image_data = composite_images(
        cover_tpl_path,
        small_images,
        positions,
        sizes,
        corner_radius,
        text_list,
        font_path,
        number_objs,
        output_path
    )
    return image_data

if __name__ == "__main__":
    # 获取所有 JPEG 图片
    jpeg_images = get_jpeg_images('skin')  # 替换为你的文件夹路径
    hero_images = get_jpeg_images('hero')  # 替换为你的文件夹路径

    # 随机选取 12 张图片
    small_images = select_random_images(jpeg_images)

    hero_big = select_random_images(hero_images, 1)
    hero_small = select_random_images(hero_images, 1)

    small_images = small_images + hero_big + hero_small

    text_dict = {
        '账号编号': 'WZ8888',
        '区服信息': '安卓QQ',
        '实名情况': '可二次实名',
        '防沉迷': '无防沉迷',
        '国标数量': '2',
        '游戏段位': '最强王者',
        '皮肤价值': '2309482'
    }
    nums_dict = {
        '英雄数量': 1,
        '典藏数量': 1,
        '传说数量': 1,
        '史诗数量': 1,
        '皮肤总数': 1,
        '贵族等级': 1,
    }
    create_cover(small_images=small_images, text_dict=text_dict, nums_dict=nums_dict)
