import json
import os
import random
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
from io import BytesIO
from pathlib import Path
import threading

import requests
from PIL import Image, ImageDraw, ImageFont

from common import oss_util
from wzry import wz_cover, wz_detail

# 获取当前脚本所在目录
script_dir = Path(__file__).resolve().parent


class ImageCacheManager:
    """图片缓存管理器，负责自动下载和管理皮肤、英雄图片缓存"""

    def __init__(self):
        self.skin_folder = script_dir / 'skin'
        self.hero_folder = script_dir / 'hero'
        self.download_lock = threading.Lock()  # 防止并发下载同一图片
        self.downloading_files = set()  # 正在下载的文件集合

        # 确保目录存在
        self.skin_folder.mkdir(exist_ok=True)
        self.hero_folder.mkdir(exist_ok=True)

    def ensure_skin_image(self, skin_id):
        """
        确保皮肤图片存在，如果不存在则自动下载
        :param skin_id: 皮肤ID
        :return: 图片路径，如果下载失败返回None
        """
        img_path = self.skin_folder / f"{skin_id}.jpeg"

        if img_path.exists():
            return str(img_path)

        # 防止重复下载
        with self.download_lock:
            if str(img_path) in self.downloading_files:
                # 等待其他线程下载完成
                while str(img_path) in self.downloading_files:
                    time.sleep(0.1)
                return str(img_path) if img_path.exists() else None

            self.downloading_files.add(str(img_path))

        try:
            print(f"[ImageCache] 开始下载皮肤图片: {skin_id}")
            success = self._download_single_skin(skin_id)
            if success:
                print(f"[ImageCache] 皮肤图片下载成功: {skin_id}")
                return str(img_path)
            else:
                print(f"[ImageCache] 皮肤图片下载失败: {skin_id}")
                return None
        finally:
            with self.download_lock:
                self.downloading_files.discard(str(img_path))

    def ensure_hero_image(self, hero_id):
        """
        确保英雄图片存在，如果不存在则自动下载
        :param hero_id: 英雄ID
        :return: 图片路径，如果下载失败返回None
        """
        img_path = self.hero_folder / f"{hero_id}.jpeg"

        if img_path.exists():
            return str(img_path)

        # 防止重复下载
        with self.download_lock:
            if str(img_path) in self.downloading_files:
                # 等待其他线程下载完成
                while str(img_path) in self.downloading_files:
                    time.sleep(0.1)
                return str(img_path) if img_path.exists() else None

            self.downloading_files.add(str(img_path))

        try:
            print(f"[ImageCache] 开始下载英雄图片: {hero_id}")
            success = self._download_single_hero(hero_id)
            if success:
                print(f"[ImageCache] 英雄图片下载成功: {hero_id}")
                return str(img_path)
            else:
                print(f"[ImageCache] 英雄图片下载失败: {hero_id}")
                return None
        finally:
            with self.download_lock:
                self.downloading_files.discard(str(img_path))

    def _download_single_skin(self, skin_id):
        """下载单个皮肤图片"""
        try:
            skin_dict = get_skin_dict()
            skin = skin_dict.get(int(skin_id))
            if not skin:
                print(f"[ImageCache] 未找到皮肤配置: {skin_id}")
                return False

            img_url = skin['szLargeIcon']
            label_url = skin['classLabel']
            img_path = self.skin_folder / f"{skin_id}.jpeg"

            # 下载主图
            img_response = requests.get(img_url, timeout=10)
            img_response.raise_for_status()

            # 下载标签图（如果存在）
            label = None
            if label_url:
                try:
                    label_response = requests.get(label_url, timeout=10)
                    label_response.raise_for_status()
                    label = Image.open(BytesIO(label_response.content))
                except Exception as e:
                    print(f"[ImageCache] 标签图下载失败，继续处理主图: {e}")

            # 智能缩放和裁切到目标尺寸 180x280
            def smart_resize_and_crop(image, target_width=180, target_height=280):
                """
                智能缩放和裁切图像到目标尺寸
                如果宽高比不匹配，采用中心裁切策略
                """
                original_width, original_height = image.size
                target_ratio = target_width / target_height
                original_ratio = original_width / original_height

                if original_ratio > target_ratio:
                    # 图像过宽，保持高度，从中心裁切宽度
                    new_height = target_height
                    new_width = int(original_width * (target_height / original_height))
                    resized_img = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # 从中心裁切宽度
                    left = (new_width - target_width) // 2
                    cropped_img = resized_img.crop((left, 0, left + target_width, target_height))
                else:
                    # 图像过高，保持宽度，从中心裁切高度
                    new_width = target_width
                    new_height = int(original_height * (target_width / original_width))
                    resized_img = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # 从中心裁切高度
                    top = (new_height - target_height) // 2
                    cropped_img = resized_img.crop((0, top, target_width, top + target_height))

                return cropped_img

            # 处理图片
            img = Image.open(BytesIO(img_response.content))

            # 对主图进行智能缩放和裁切
            processed_img = smart_resize_and_crop(img)

            # 将处理后的图片转换为 RGBA，以便支持透明背景
            img_rgba = processed_img.convert("RGBA")

            # 创建一个新的空白图像，使用处理后的尺寸 180x280
            new_img = Image.new('RGBA', (180, 280))
            new_img.paste(img_rgba, (0, 0), mask=img_rgba)

            # 如果有标签图，贴在上面
            if label:
                label = label.convert("RGBA")
                new_img.paste(label, (180 - label.width - 15, 8), mask=label)

            final_img = new_img.convert("RGB")
            final_img.save(img_path, quality=95, optimize=True)

            # 上传到OSS（保持原有逻辑）
            try:
                oss_util.upload_file_to_oss_with_name(str(img_path), 'mall/statics/wzry/wzskin/', f"{skin_id}.jpeg")
            except Exception as e:
                print(f"[ImageCache] OSS上传失败，但本地文件已保存: {e}")

            return True

        except Exception as e:
            print(f"[ImageCache] 下载皮肤图片失败 {skin_id}: {e}")
            traceback.print_exc()
            return False

    def _download_single_hero(self, hero_id):
        """下载单个英雄图片"""
        try:
            hero_dict = get_hero_dict()
            hero = hero_dict.get(int(hero_id))
            if not hero:
                print(f"[ImageCache] 未找到英雄配置: {hero_id}")
                return False

            img_url = hero['heroIcon']
            img_path = self.hero_folder / f"{hero_id}.jpeg"

            # 下载图片
            img_response = requests.get(img_url, timeout=10)
            img_response.raise_for_status()

            # 处理图片
            img = Image.open(BytesIO(img_response.content))
            final_img = img.convert("RGB")
            final_img.save(img_path, quality=95, optimize=True)

            return True

        except Exception as e:
            print(f"[ImageCache] 下载英雄图片失败 {hero_id}: {e}")
            traceback.print_exc()
            return False


# 全局图片缓存管理器实例
image_cache_manager = ImageCacheManager()


def add_rounded_corners_with_border(image, radius, border_size=2, border_color=(255, 255, 255)):
    img_width, img_height = image.size
    new_img = Image.new('RGBA', (img_width + 2 * border_size, img_height + 2 * border_size), (0, 0, 0, 0))

    # 创建圆角遮罩
    mask = Image.new('L', (img_width, img_height), 0)
    draw = ImageDraw.Draw(mask)
    draw.rounded_rectangle([0, 0, img_width, img_height], radius=radius, fill=255)

    # 贴上圆角图片
    new_img.paste(image, (border_size, border_size), mask=mask)

    # 创建边框图层
    border_img = Image.new('RGBA', (img_width + 2 * border_size, img_height + 2 * border_size), (0, 0, 0, 0))
    border_draw = ImageDraw.Draw(border_img)
    border_draw.rounded_rectangle([border_size, border_size, img_width + border_size, img_height + border_size],
                                  radius=radius, outline=border_color, width=border_size)

    # 将边框图层贴到图像上
    new_img.paste(border_img, (0, 0), border_img)

    return new_img


def add_text_to_image(image, text, position, font_path=None, font_size=40, color=(255, 255, 255)):
    draw = ImageDraw.Draw(image)

    if font_path:
        font = ImageFont.truetype(font_path, font_size)
    else:
        font = ImageFont.load_default()

    draw.text(position, text, font=font, fill=color)
    return image


def create_collage(image_paths, background_path, output_path, images_per_row=8, spacing=10, left_margin=20,
                   right_margin=20, row_spacing=10, radius=10, border_size=2, border_color=(255, 255, 255),
                   text_info=None, text_info2=None, font_path=None, font_size=40):
    # 打开背景图并获取其尺寸
    background = Image.open(background_path).convert("RGBA")
    bg_width, bg_height = background.size

    # 打开所有小图并获取其原始大小
    images = [Image.open(img_path).convert("RGBA") for img_path in image_paths]

    # 计算每行的图片数量
    total_images = len(images)
    num_rows = (total_images + images_per_row - 1) // images_per_row

    # 计算可用的宽度和每张小图的宽度
    total_spacing = (images_per_row - 1) * spacing  # 总的图片间距
    total_left_right_margin = left_margin + right_margin  # 左右边距总宽度
    total_width_available = bg_width - total_left_right_margin - total_spacing  # 可用于放置小图的宽度
    small_img_width = total_width_available // images_per_row  # 每个小图的宽度

    # 目标宽高比为9:14
    target_aspect_ratio = 9 / 14
    small_img_height = int(small_img_width / target_aspect_ratio)  # 小图的高度根据9:14的比例计算

    # 计算垂直方向的总高度（包含图片高度和间距）
    total_height_needed = num_rows * small_img_height + (num_rows - 1) * row_spacing

    # 确保总高度不超过背景高度
    if total_height_needed > bg_height:
        raise ValueError(f"Background height is too small. Needed: {total_height_needed}, but got: {bg_height}")

    # 计算拼图的Y轴起点，确保上下居中
    top_margin = (bg_height - total_height_needed) // 2

    # 如果有文本信息，先在背景图上绘制文本
    if text_info:
        text = f"实名：{text_info['real_name_type']} 贵族等级：{text_info['noble_level']} 英雄：{text_info['hero_count']} 皮肤：{text_info['skin_count']} 荣耀典藏：{text_info['glory_collection']}"
        background = add_text_to_image(background, text, (left_margin + 20, 40), font_path=font_path,
                                       font_size=font_size,
                                       color=(255, 255, 255))
    if text_info2:
        text = f"编号：{text_info2['product_sn']} | {text_info2['account_type']}                              kkzhw.com"
        background = add_text_to_image(background, text, (left_margin + 20, bg_height - 120), font_path=font_path,
                                       font_size=font_size,
                                       color=(255, 255, 255))

    # 调整每张小图的大小，并添加圆角和边框
    resized_images = []
    for img in images:
        # 计算图片的宽高比
        img_width, img_height = img.size
        img_aspect_ratio = img_width / img_height

        # 如果宽高比小于目标比例，说明太高了，需要裁剪高度
        if img_aspect_ratio < target_aspect_ratio:
            # 按宽度缩放，保持宽度为 small_img_width
            new_width = small_img_width
            new_height = int(new_width / img_aspect_ratio)
            resized_image = img.resize((new_width, new_height), Image.LANCZOS)

            # 裁剪上下多余部分，使高度符合9:14比例
            top = (new_height - small_img_height) // 2
            bottom = top + small_img_height
            resized_image = resized_image.crop((0, top, new_width, bottom))

        # 如果宽高比大于等于目标比例，说明太宽了，需要裁剪宽度
        else:
            # 按高度缩放，保持高度为 small_img_height
            new_height = small_img_height
            new_width = int(new_height * img_aspect_ratio)
            resized_image = img.resize((new_width, new_height), Image.LANCZOS)

            # 裁剪左右多余部分，使宽度符合9:14比例
            left = (new_width - small_img_width) // 2
            right = left + small_img_width
            resized_image = resized_image.crop((left, 0, right, new_height))

        # 添加圆角和边框
        rounded_img = add_rounded_corners_with_border(resized_image, radius=radius, border_size=border_size,
                                                      border_color=border_color)
        resized_images.append(rounded_img)

    # 拼接图像到背景上（确保有指定的上下左右边距）
    # y_offset = top_margin + 20  # 留出文字空间
    y_offset = top_margin
    for index, img in enumerate(resized_images):
        x = left_margin + (index % images_per_row) * (small_img_width + spacing)
        y = y_offset + (index // images_per_row) * (small_img_height + row_spacing)
        background.paste(img, (x, y), img)

    # 保存结果
    background = background.convert("RGB")  # 去除透明背景（如果需要）
    background.save(output_path)
    print(f'Collage saved at {output_path}')


@lru_cache(maxsize=1)
def get_skin_dict(file_path='all_hero_and_skin.json'):
    with open(file_path, 'r', encoding='utf-8') as f:
        skin_json = json.load(f)
    skin_list = skin_json.get('allSkinConf')
    #
    skin_dict = {skin['skinId']: skin for skin in skin_list}
    return skin_dict


@lru_cache(maxsize=1)
def get_hero_dict(file_path='all_hero_and_skin.json'):
    with open(file_path, 'r', encoding='utf-8') as f:
        hero_json = json.load(f)
    hero_list = hero_json.get('allHeroConf')
    #
    hero_dict = {hero['heroId']: hero for hero in hero_list}
    return hero_dict


def download_skin_img():
    # 获取皮肤字典
    skin_dict = get_skin_dict()

    # 创建存放图片的文件夹
    folder_path = 'skin'
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

    def download_image(key, skin, folder_path):
        img_url = skin['szLargeIcon']
        label_url = skin['classLabel']
        img_name = f"{key}.jpeg"
        img_path = os.path.join(folder_path, img_name)

        if os.path.exists(img_path):
            return

        try:
            # 下载图片
            img_response = requests.get(img_url, timeout=10)  # 设置超时
            img_response.raise_for_status()  # 检查请求是否成功

            label = None
            if label_url and img_url.startswith(('http://', 'https://')):  # 如果有 label 图片
                label_response = requests.get(label_url, timeout=10)
                label_response.raise_for_status()
                try:
                    label = Image.open(BytesIO(label_response.content))
                except Exception as e:
                    print(f"Failed to open label image: {e}")
                    # traceback.print_exc()

            # 打开主图（imgUrl）图片
            img = Image.open(BytesIO(img_response.content))

            # 智能缩放和裁切到目标尺寸 180x280
            def smart_resize_and_crop(image, target_width=180, target_height=280):
                """
                智能缩放和裁切图像到目标尺寸
                如果宽高比不匹配，采用中心裁切策略
                """
                original_width, original_height = image.size
                target_ratio = target_width / target_height
                original_ratio = original_width / original_height

                if original_ratio > target_ratio:
                    # 图像过宽，保持高度，从中心裁切宽度
                    new_height = target_height
                    new_width = int(original_width * (target_height / original_height))
                    resized_img = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # 从中心裁切宽度
                    left = (new_width - target_width) // 2
                    cropped_img = resized_img.crop((left, 0, left + target_width, target_height))
                else:
                    # 图像过高，保持宽度，从中心裁切高度
                    new_width = target_width
                    new_height = int(original_height * (target_width / original_width))
                    resized_img = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # 从中心裁切高度
                    top = (new_height - target_height) // 2
                    cropped_img = resized_img.crop((0, top, target_width, top + target_height))

                return cropped_img

            # 对主图进行智能缩放和裁切
            processed_img = smart_resize_and_crop(img)

            # 将处理后的图片转换为 RGBA，以便支持透明背景
            img_rgba = processed_img.convert("RGBA")

            # 创建一个新的空白图像，使用处理后的尺寸 180x280
            new_img = Image.new('RGBA', (180, 280))

            new_img.paste(img_rgba, (0, 0), mask=img_rgba)

            # 如果有 label 图片，则贴在上面
            if label:
                # 确保 label 是 RGBA 格式
                label = label.convert("RGBA")

                # 将 label 贴在上方，使用新的图像尺寸 180x280
                new_img.paste(label, (180 - label.width - 15, 8), mask=label)

            final_img = new_img.convert("RGB")  # 转换为 RGB 模式以便保存为 JPEG
            final_img.save(img_path, quality=95, optimize=True)  # 高质量 JPEG

            # 上传oss
            oss_util.upload_file_to_oss_with_name(img_path, 'mall/statics/wzry/wzskin/', img_name)

            print(f"成功下载并合成图片: {img_name}")

        except Exception as e:
            print(f"下载图片失败: {img_url} 错误: {e}")
            traceback.print_exc()

    # 使用线程池并发下载
    with ThreadPoolExecutor(max_workers=10) as executor:  # 设置最大线程数

        # 为每个皮肤提交下载任务
        for key, skin in skin_dict.items():
            executor.submit(download_image, key, skin, folder_path)


def download_hero_img():
    # 获取皮肤字典
    hero_dict = get_hero_dict()

    # 创建存放图片的文件夹
    folder_path = 'hero'
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

    def download_image(key, hero, folder_path):
        img_url = hero['heroIcon']
        img_name = f"{key}.jpeg"
        img_path = os.path.join(folder_path, img_name)

        if os.path.exists(img_path):
            return

        try:
            # 下载图片
            img_response = requests.get(img_url, timeout=10)  # 设置超时
            img_response.raise_for_status()  # 检查请求是否成功

            # 打开主图（imgUrl）图片
            img = Image.open(BytesIO(img_response.content))

            final_img = img.convert("RGB")  # 转换为 RGB 模式以便保存为 JPEG
            final_img.save(img_path, quality=95, optimize=True)  # 高质量 JPEG

            print(f"成功下载hero图片: {img_name}")

        except requests.exceptions.RequestException as e:
            print(f"下载图片失败: {img_url} 错误: {e}")
            traceback.print_exc()

    # 使用线程池并发下载
    with ThreadPoolExecutor(max_workers=10) as executor:  # 设置最大线程数

        # 为每个皮肤提交下载任务
        for key, skin in hero_dict.items():
            executor.submit(download_image, key, skin, folder_path)


def create_wzry_cover(image_paths, text_info, text_info2):
    background_path = 'wz_bg2.jpg'
    output_path = 'output_collage.jpg'

    # 提供贵族等级、英雄数、皮肤数、荣耀典藏等信息

    font_path = "SourceHanSerifCN-Bold-2.otf"
    font_size = 60

    create_collage(image_paths, background_path, output_path, images_per_row=7, spacing=20, left_margin=50,
                   right_margin=50, row_spacing=40, radius=15, border_size=5, border_color=(255, 255, 255),
                   text_info=text_info, text_info2=text_info2, font_path=font_path, font_size=font_size)
    oss_object_name = oss_util.upload_one_img_to_oss(output_path)
    return oss_object_name


def create_wzry_cover2(images, text_dict, nums_dict):
    print('script_dir: ', script_dir)
    output_path = script_dir / 'output_cover.jpg'

    print('output_path: ', output_path)

    image_data = wz_cover.create_cover(images, text_dict, nums_dict, output_path)
    oss_object_name = oss_util.upload_one_img_to_oss_by_data(image_data)
    return oss_object_name


def create_wzry_detail(skin_group, text_dict):
    output_path = f'{script_dir}/output_detail.jpg'

    image_data = wz_detail.create_detail_image(skin_group, text_dict, output_path)
    oss_object_name = oss_util.upload_one_img_to_oss_by_data(image_data)
    return oss_object_name


def get_skin_image_path(skin_id, auto_download=True):
    """
    获取皮肤图片路径，支持自动下载
    :param skin_id: 皮肤ID
    :param auto_download: 是否自动下载缺失的图片
    :return: 图片路径，如果图片不存在且下载失败则返回占位图路径
    """
    if auto_download:
        img_path = image_cache_manager.ensure_skin_image(skin_id)
        if img_path:
            return img_path
    else:
        img_path = script_dir / 'skin' / f"{skin_id}.jpeg"
        if img_path.exists():
            return str(img_path)

    # 返回占位图
    placeholder_path = script_dir / '占位图.png'
    return str(placeholder_path) if placeholder_path.exists() else None


def get_hero_image_path(hero_id, auto_download=True):
    """
    获取英雄图片路径，支持自动下载
    :param hero_id: 英雄ID
    :param auto_download: 是否自动下载缺失的图片
    :return: 图片路径，如果图片不存在且下载失败则返回占位图路径
    """
    if auto_download:
        img_path = image_cache_manager.ensure_hero_image(hero_id)
        if img_path:
            return img_path
    else:
        img_path = script_dir / 'hero' / f"{hero_id}.jpeg"
        if img_path.exists():
            return str(img_path)

    # 返回占位图
    placeholder_path = script_dir / '占位图1.png'
    return str(placeholder_path) if placeholder_path.exists() else None


def batch_ensure_images(skin_ids=None, hero_ids=None, max_workers=5):
    """
    批量确保图片存在，用于预加载
    :param skin_ids: 皮肤ID列表
    :param hero_ids: 英雄ID列表
    :param max_workers: 最大并发数
    """
    def download_skin(skin_id):
        return image_cache_manager.ensure_skin_image(skin_id)

    def download_hero(hero_id):
        return image_cache_manager.ensure_hero_image(hero_id)

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []

        if skin_ids:
            for skin_id in skin_ids:
                futures.append(executor.submit(download_skin, skin_id))

        if hero_ids:
            for hero_id in hero_ids:
                futures.append(executor.submit(download_hero, hero_id))

        # 等待所有下载完成
        for future in futures:
            try:
                future.result(timeout=30)  # 30秒超时
            except Exception as e:
                print(f"[ImageCache] 批量下载出错: {e}")


def get_jpeg_images(folder_path):
    """
    获取文件夹下所有的 JPEG 图片路径
    """
    jpeg_images = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg')):  # 支持 .jpg 和 .jpeg 文件
                jpeg_images.append(os.path.join(root, file))
    return jpeg_images


def select_random_images(image_list, num_images=12):
    """
    从图片列表中随机选取指定数量的图片
    """
    if len(image_list) <= num_images:
        return image_list  # 如果图片数量不足，返回所有图片
    return random.sample(image_list, num_images)


def get_skin_id_by_skin_name(skin_name):
    """
    根据皮肤名称获取对应的 skinId
    :param all_skin_conf: 所有皮肤配置列表
    :param skin_name: 皮肤名称(可能包含了英雄名)
    :return: 对应的 skinId，如果未找到则返回 None
    """
    with open('all_hero_and_skin.json', 'r', encoding='utf-8') as file:
        all_skin_conf = json.load(file)
        for skin in all_skin_conf['allSkinConf']:
            if skin.get("skinName").replace("·", "").replace(" ", "") in skin_name and skin.get("heroName") in skin_name:
                return skin.get("skinId")
    return None

def get_skin_id_by_skin_name_hero_name(skin_name, hero_name):
    with open('all_hero_and_skin.json', 'r', encoding='utf-8') as file:
        all_skin_conf = json.load(file)
        for skin in all_skin_conf['allSkinConf']:
            if skin.get("skinName").replace('·','') == skin_name and skin.get("heroName") == hero_name:
                return skin.get("skinId")
        print(f"未找到对应的 skinId，皮肤名称：{skin_name}")
    return None


if __name__ == '__main__':
    start_time = time.time()
    download_skin_img()
    # download_hero_img()
    end_time = time.time()
    print(f"download_skin_img cost {end_time - start_time:.2f} seconds")

    # jpeg_images = get_jpeg_images('skin')  # 获取所有 JPEG 图片
    # selected_images = select_random_images(jpeg_images)  # 随机选取 12 张图片
    #
    # print(f"找到 {len(jpeg_images)} 张 JPEG 图片，随机选取 12 张：")
    # create_wzry_cover2(selected_images, text_info={}, text_info2={})
