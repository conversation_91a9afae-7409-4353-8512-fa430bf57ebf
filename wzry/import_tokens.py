"""
Token导入工具
"""
import logging
from common.mongo_manager import MongoDBClient
from wzry.simple_token_pool import SimpleTokenPool
from common.configs import current_config

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def import_tokens():
    """从配置文件导入Token"""
    mongo = MongoDBClient(uri="******************************************", db_name="luhao-prod")
    token_pool = SimpleTokenPool(mongo, "wzry_token_pool")
    wz_config = current_config.wz_req_config

    imported_count = 0
    for device_id, config_data in wz_config.items():
        if not isinstance(config_data, dict) or 'token' not in config_data:
            continue

        token_data = {
            'token': config_data['token'],
            'user_id': config_data['user_id'],
            'encodeParam': config_data['encodeParam'],
            'gameareaid': config_data.get('gameareaid', ''),
            'gameroleid': config_data.get('gameroleid', ''),
            'gameserverid': config_data.get('gameserverid', ''),
            'gameopenid': config_data.get('gameopenid', ''),
            'kohdimgender': config_data.get('kohdimgender', ''),
            'gameusersex': config_data.get('gameusersex', ''),
            'openid': config_data.get('openid', ''),
            'comment': config_data.get('comment', device_id)
        }

        if token_pool.add_token(token_data):
            imported_count += 1
            logger.info(f"导入: {device_id}")

    logger.info(f"导入完成: {imported_count} 个Token")


def show_status():
    """显示Token池状态"""
    mongo = MongoDBClient(uri="******************************************", db_name="luhao-prod")
    token_pool = SimpleTokenPool(mongo, "wzry_token_pool")
    status = token_pool.get_pool_status()
    logger.info(f"Token池状态: {status}")


if __name__ == "__main__":
    import sys

    import_tokens()
    # if len(sys.argv) > 1 and sys.argv[1] == "import":
    #     import_tokens()
    # else:
    #     show_status()
