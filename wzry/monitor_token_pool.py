"""
Token池监控脚本
用于监控Token池状态，发现异常情况并自动处理
"""
import sys
import os
import time
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.mongo_manager import MongoDBClient
from wzry.simple_token_pool import SimpleTokenPool
from wzry.token_models import TokenStatus


class TokenPoolMonitor:
    """Token池监控器"""
    
    def __init__(self):
        """初始化监控器"""
        self.mongo = MongoDBClient(
            uri="******************************************",
            db_name="luhao-prod"
        )
        self.token_pool = SimpleTokenPool(self.mongo, "wzry_token_pool")
        self.last_status = None
        
    def get_detailed_status(self):
        """获取详细状态信息"""
        status = self.token_pool.get_pool_status()
        
        # 获取IN_USE状态Token的详细信息
        in_use_tokens = self.mongo.find_many(
            "wzry_token_pool", 
            {"status": TokenStatus.IN_USE.value}
        )
        
        # 计算IN_USE Token的时长分布
        now = datetime.now()
        in_use_duration_stats = {
            'less_than_5min': 0,
            '5_to_30min': 0,
            '30min_to_2hour': 0,
            'more_than_2hour': 0
        }
        
        for token in in_use_tokens:
            updated_at = token.get('updated_at')
            if updated_at:
                if isinstance(updated_at, str):
                    # 如果是字符串，尝试解析
                    try:
                        updated_at = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    except:
                        continue
                
                duration = now - updated_at
                duration_minutes = duration.total_seconds() / 60
                
                if duration_minutes < 5:
                    in_use_duration_stats['less_than_5min'] += 1
                elif duration_minutes < 30:
                    in_use_duration_stats['5_to_30min'] += 1
                elif duration_minutes < 120:
                    in_use_duration_stats['30min_to_2hour'] += 1
                else:
                    in_use_duration_stats['more_than_2hour'] += 1
        
        return {
            **status,
            'in_use_duration_stats': in_use_duration_stats,
            'timestamp': now.isoformat()
        }
    
    def check_health(self):
        """检查Token池健康状态"""
        status = self.get_detailed_status()
        issues = []
        
        # 检查可用Token数量
        normal_tokens = status.get('normal_tokens', 0)
        total_tokens = status.get('total_tokens', 0)
        
        if total_tokens == 0:
            issues.append("❌ 严重：Token池为空")
        elif normal_tokens == 0:
            issues.append("⚠️  警告：没有可用的Token")
        elif normal_tokens < 3:
            issues.append(f"⚠️  警告：可用Token数量过少 ({normal_tokens}个)")
        
        # 检查IN_USE Token数量
        in_use_tokens = status.get('in_use_tokens', 0)
        if in_use_tokens > total_tokens * 0.8:
            issues.append(f"⚠️  警告：IN_USE Token比例过高 ({in_use_tokens}/{total_tokens})")
        
        # 检查长时间IN_USE的Token
        duration_stats = status.get('in_use_duration_stats', {})
        long_term_in_use = duration_stats.get('more_than_2hour', 0)
        if long_term_in_use > 0:
            issues.append(f"⚠️  警告：有 {long_term_in_use} 个Token使用超过2小时")
        
        # 检查失效Token比例
        expired_tokens = status.get('expired_tokens', 0)
        limited_tokens = status.get('limited_tokens', 0)
        failed_tokens = expired_tokens + limited_tokens
        
        if total_tokens > 0 and failed_tokens / total_tokens > 0.5:
            issues.append(f"⚠️  警告：失效Token比例过高 ({failed_tokens}/{total_tokens})")
        
        return issues
    
    def auto_cleanup(self, timeout_minutes=30):
        """自动清理超时的IN_USE Token"""
        try:
            cleaned_count = self.token_pool.cleanup_expired_tokens(timeout_minutes)
            if cleaned_count > 0:
                print(f"🧹 自动清理了 {cleaned_count} 个超时Token")
                return cleaned_count
        except Exception as e:
            print(f"❌ 自动清理失败: {e}")
        return 0
    
    def print_status_report(self, status, issues):
        """打印状态报告"""
        timestamp = status.get('timestamp', datetime.now().isoformat())
        print(f"\n=== Token池状态报告 ({timestamp}) ===")
        
        # 基本状态
        print("\n📊 基本状态:")
        print(f"  总Token数: {status.get('total_tokens', 0)}")
        print(f"  可用Token: {status.get('normal_tokens', 0)}")
        print(f"  使用中Token: {status.get('in_use_tokens', 0)}")
        print(f"  受限Token: {status.get('limited_tokens', 0)}")
        print(f"  失效Token: {status.get('expired_tokens', 0)}")
        
        # IN_USE Token时长分布
        duration_stats = status.get('in_use_duration_stats', {})
        if any(duration_stats.values()):
            print("\n⏱️  IN_USE Token时长分布:")
            print(f"  < 5分钟: {duration_stats.get('less_than_5min', 0)}")
            print(f"  5-30分钟: {duration_stats.get('5_to_30min', 0)}")
            print(f"  30分钟-2小时: {duration_stats.get('30min_to_2hour', 0)}")
            print(f"  > 2小时: {duration_stats.get('more_than_2hour', 0)}")
        
        # 健康检查结果
        if issues:
            print("\n🚨 发现的问题:")
            for issue in issues:
                print(f"  {issue}")
        else:
            print("\n✅ Token池状态正常")
    
    def monitor_once(self, auto_cleanup_enabled=True, cleanup_timeout=30):
        """执行一次监控检查"""
        status = self.get_detailed_status()
        issues = self.check_health()
        
        self.print_status_report(status, issues)
        
        # 自动清理
        if auto_cleanup_enabled:
            duration_stats = status.get('in_use_duration_stats', {})
            long_term_count = duration_stats.get('more_than_2hour', 0)
            
            if long_term_count > 0:
                print(f"\n🧹 检测到 {long_term_count} 个长时间IN_USE的Token，执行自动清理...")
                cleaned = self.auto_cleanup(cleanup_timeout)
                if cleaned > 0:
                    # 重新获取状态
                    status = self.get_detailed_status()
                    print(f"✅ 清理完成，当前IN_USE Token数: {status.get('in_use_tokens', 0)}")
        
        self.last_status = status
        return status, issues
    
    def monitor_continuous(self, interval_seconds=300, auto_cleanup_enabled=True):
        """持续监控模式"""
        print(f"🔍 开始持续监控Token池状态 (间隔: {interval_seconds}秒)")
        print("按 Ctrl+C 停止监控")
        
        try:
            while True:
                self.monitor_once(auto_cleanup_enabled)
                print(f"\n⏰ 等待 {interval_seconds} 秒后进行下次检查...")
                time.sleep(interval_seconds)
                
        except KeyboardInterrupt:
            print("\n👋 监控已停止")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Token池监控工具')
    parser.add_argument('--continuous', action='store_true', 
                       help='持续监控模式')
    parser.add_argument('--interval', type=int, default=300, 
                       help='持续监控的检查间隔（秒），默认300秒')
    parser.add_argument('--no-auto-cleanup', action='store_true', 
                       help='禁用自动清理功能')
    parser.add_argument('--cleanup-timeout', type=int, default=30, 
                       help='自动清理的超时时间（分钟），默认30分钟')
    
    args = parser.parse_args()
    
    try:
        monitor = TokenPoolMonitor()
        
        if args.continuous:
            monitor.monitor_continuous(
                interval_seconds=args.interval,
                auto_cleanup_enabled=not args.no_auto_cleanup
            )
        else:
            monitor.monitor_once(
                auto_cleanup_enabled=not args.no_auto_cleanup,
                cleanup_timeout=args.cleanup_timeout
            )
            
    except Exception as e:
        print(f"❌ 监控过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
