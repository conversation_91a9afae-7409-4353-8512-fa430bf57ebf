"""
手动清理IN_USE状态Token的脚本
用于清理因异常终止而未正确释放的Token
"""
import sys
import os
import argparse
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.mongo_manager import MongoDBClient
from wzry.simple_token_pool import SimpleTokenPool
from wzry.token_models import TokenStatus


def cleanup_in_use_tokens(timeout_minutes=0, dry_run=False):
    """
    清理IN_USE状态的Token
    
    Args:
        timeout_minutes: 超时时间（分钟），0表示清理所有IN_USE状态的Token
        dry_run: 是否为试运行模式，只显示将要清理的Token而不实际清理
    """
    print("=== Token池IN_USE状态清理工具 ===")
    
    # 初始化MongoDB连接和Token池
    mongo = MongoDBClient(
        uri="******************************************",
        db_name="luhao-prod"
    )
    token_pool = SimpleTokenPool(mongo, "wzry_token_pool")
    
    # 显示清理前状态
    print("\n清理前Token池状态:")
    initial_status = token_pool.get_pool_status()
    for key, value in initial_status.items():
        print(f"  {key}: {value}")
    
    in_use_count = initial_status.get('in_use_tokens', 0)
    if in_use_count == 0:
        print("\n✅ 没有IN_USE状态的Token需要清理")
        return
    
    print(f"\n发现 {in_use_count} 个IN_USE状态的Token")
    
    if timeout_minutes > 0:
        print(f"将清理超过 {timeout_minutes} 分钟的IN_USE状态Token")
    else:
        print("将清理所有IN_USE状态的Token")
    
    if dry_run:
        print("\n🔍 试运行模式 - 只显示将要清理的Token，不实际执行清理")
        
        # 查询将要清理的Token
        query = {"status": TokenStatus.IN_USE.value}
        if timeout_minutes > 0:
            timeout_time = datetime.now() - timedelta(minutes=timeout_minutes)
            query["updated_at"] = {"$lt": timeout_time}
        
        tokens_to_clean = mongo.find_many("wzry_token_pool", query)
        
        if tokens_to_clean:
            print(f"\n将要清理的Token ({len(tokens_to_clean)}个):")
            for token in tokens_to_clean:
                updated_at = token.get('updated_at', 'Unknown')
                comment = token.get('comment', 'No comment')[:20]
                print(f"  - ID: {token['_id']}")
                print(f"    Comment: {comment}")
                print(f"    Updated: {updated_at}")
                print(f"    Status: {token['status']}")
                print()
        else:
            print("\n✅ 没有符合条件的Token需要清理")
        
        return
    
    # 确认清理操作
    if timeout_minutes == 0:
        confirm = input(f"\n⚠️  确认要清理所有 {in_use_count} 个IN_USE状态的Token吗？(y/N): ")
    else:
        confirm = input(f"\n⚠️  确认要清理超过 {timeout_minutes} 分钟的IN_USE状态Token吗？(y/N): ")
    
    if confirm.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    # 执行清理
    print("\n🧹 开始清理...")
    cleaned_count = token_pool.cleanup_expired_tokens(timeout_minutes=timeout_minutes)
    
    if cleaned_count > 0:
        print(f"✅ 成功清理了 {cleaned_count} 个Token")
    else:
        print("ℹ️  没有Token被清理（可能已经被其他进程清理了）")
    
    # 显示清理后状态
    print("\n清理后Token池状态:")
    final_status = token_pool.get_pool_status()
    for key, value in final_status.items():
        print(f"  {key}: {value}")
    
    # 显示变化
    print("\n状态变化:")
    for key in initial_status:
        initial = initial_status[key]
        final = final_status[key]
        change = final - initial
        if change != 0:
            print(f"  {key}: {initial} → {final} ({change:+d})")


def show_token_details():
    """显示所有Token的详细状态"""
    print("=== Token池详细状态 ===")
    
    mongo = MongoDBClient(
        uri="******************************************",
        db_name="luhao-prod"
    )
    
    # 查询所有Token
    all_tokens = mongo.find_many("wzry_token_pool", {})
    
    if not all_tokens:
        print("没有找到任何Token")
        return
    
    # 按状态分组
    status_groups = {}
    for token in all_tokens:
        status = token.get('status', 'UNKNOWN')
        if status not in status_groups:
            status_groups[status] = []
        status_groups[status].append(token)
    
    print(f"\n总Token数量: {len(all_tokens)}")
    print("\n按状态分组:")
    
    for status, tokens in status_groups.items():
        print(f"\n{status} ({len(tokens)}个):")
        for token in tokens:
            comment = token.get('comment', 'No comment')[:20]
            updated_at = token.get('updated_at', 'Unknown')
            print(f"  - {comment} | 更新时间: {updated_at}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Token池IN_USE状态清理工具')
    parser.add_argument('--timeout', type=int, default=0, 
                       help='超时时间（分钟），0表示清理所有IN_USE状态的Token（默认: 0）')
    parser.add_argument('--dry-run', action='store_true', 
                       help='试运行模式，只显示将要清理的Token而不实际清理')
    parser.add_argument('--details', action='store_true', 
                       help='显示所有Token的详细状态')
    
    args = parser.parse_args()
    
    try:
        if args.details:
            show_token_details()
        else:
            cleanup_in_use_tokens(
                timeout_minutes=args.timeout,
                dry_run=args.dry_run
            )
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
