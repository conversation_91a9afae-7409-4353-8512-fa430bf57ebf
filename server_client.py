import json
import os
import re
import time
import traceback
from functools import lru_cache
from time import sleep

import requests

import configs
import logger_config
import sys_tool

logger = logger_config.setup_logger(os.path.basename(__file__))


class PortalClient(object):

    def __init__(self):
        self.server_url = configs.server_url
        self.token = configs.SERVER_API_TOKEN

    def get_sms_code(self, task_id, timeout=120):
        """
        轮询获取短信验证码
        :param task_id:
        :param timeout: 轮询超时时间
        :return:
        """
        max_retry = int(timeout) // 3
        for i in range(max_retry):
            task_info = self.get_task_info(task_id)
            if task_info is None:
                return None
            sms_code = task_info.get('smsCode')
            if sms_code is not None and sms_code != '':
                return sms_code
            sleep(3)
        return None

    def get_task_info(self, task_id):
        """
        获取任务详情
        :param task_id:
        :return:
        """
        r = self.__request('GET', self.server_url + '/mall-portal/openapi/record/task/' + str(task_id))
        return r

    def get_task_info_by_sn(self, product_sn):
        """
        获取任务详情
        :param product_sn:
        :return:
        """
        r = self.__request('GET', self.server_url + '/mall-portal/openapi/record/task/detail?productSn=' + product_sn)
        return r

    def sync_task_info(self, task):
        """
        同步任务信息
        :param task:
        :return:
        """
        # 如果task有属性 productMeta 并且 值是 'null', 则去掉该属性
        if 'productMeta' in task and task['productMeta'] == 'null':
            del task['productMeta']
        if 'propertyBag' in task and task['propertyBag'] == 'null':
            del task['propertyBag']
        r = self.__request('POST', self.server_url + '/mall-portal/openapi/record/task/update', json=task)
        return r

    def sync_product_info(self, product_sn, pic, raw_pic_url, metadata):
        """
        同步商品信息到服务器
        :param product_sn:
        :param pic: 封面图
        :param raw_pic_url: 详情图片
        :param metadata: 扩展属性
        :return:
        """
        urls = []
        for item in raw_pic_url[:]:
            if item['name'].startswith('头图'):
                raw_pic_url.remove(item)
            else:
                item["url"] = configs.image_server_url + item["value"]
                urls.append(item["url"])
                del item["value"]

        album_pics = ','.join(urls)
        album_pics_json = sys_tool.safe_json_dumps(raw_pic_url)

        meta_dict = {meta['name']: meta for meta in metadata}

        err_msg = ''

        attrs = []

        # 属性处理
        for attr in metadata:
            name = attr['name']

            origin_value = attr.get('values')

            if not origin_value:
                continue

            if name not in meta_dict:
                err_msg += f"1 attr_name【{name}】不存在"
                continue
            if name in ['换绑CD', '转职CD', '转性CD']:
                if origin_value == '是':
                    origin_value = '有'
                elif origin_value == '否':
                    origin_value = '无'

            # 去重
            origin_value = list(set(origin_value))
            # origin_value = 转成字符串
            origin_value = ','.join(str(item) for item in origin_value)

            if name == '国色值':
                origin_value = self.extract_number(origin_value)

            item = {
                "productAttributeId": meta_dict.get(name)['id'],
                "value": origin_value,
                "attriName": meta_dict.get(name)['name'],
                "sort": meta_dict.get(name)['sort'],
                "filterType": meta_dict.get(name)['filterType'],
                "searchType": meta_dict.get(name)['searchType'],
                "type": meta_dict.get(name)['type'],
                "searchSort": meta_dict.get(name)['searchSort'],
            }
            attrs.append(item)

        product_param = {
            "albumPics": album_pics,
            "albumPicsJson": album_pics_json,
            "productAttributeValueList": attrs,
            "brandId": None,
            "productAttributeCategoryId": None,  # 逆水寒手游是 17
            "gameAccountQufu": None,
            "description": None,
            "pic": pic,
            "price": None,
            "originalPrice": None,
            "stock": 9,
            "gameGoodsFangxin": None,
            "gameGoodsBukuan": 0,
            "gameGoodsJiangjia": 0,
            "gameGoodsYijia": None,
            "sort": 1,
            "publishTime": None,
            "publishStatus": 0,
            "gameGoodsYishou": None,
            "gameGoodsYuyue": 0,
            "gameGoodsSaletype": 1,
            "gameCareinfoPhone": None,
            "gameCareinfoTime": None,
            "gameCareinfoVx": None,
            "subjectProductRelationList": [],
            "recommandStatus": 0,
            "productCategoryId": None,
            "productCategoryName": None,
            "brandName": None,
            "productSn": product_sn,
            "verifyStatus": 0,
            "verifyDetail": None,
            "createTime": None,
            "gameSysinfoReadcount": None,
            "gameSysinfoCollectcount": None,
            # "memberId": member_id,
            "pushType": None,
            "pushStatus": 2  # 录号完成
        }
        r = self.__request('POST', self.server_url + '/mall-portal/openapi/record/product/update', json=product_param)

    def sync_product_info2(self, product_sn, cover, album_pics, metadata):
        """
        同步商品信息到服务器
        :param product_sn:
        :param cover: 封面图
        :param album_pics: 详情图片
        :param metadata: 扩展属性
        :return:
        """
        album_pics = ','.join(album_pics)
        album_pics_json = '[]'

        meta_dict = {meta['name']: meta for meta in metadata}
        err_msg = ''

        attrs = []
        # 属性处理
        for attr in metadata:
            name = attr['name']
            origin_value = attr.get('values')
            if not origin_value:
                continue
            if name not in meta_dict:
                err_msg += f"1 attr_name【{name}】不存在"
                continue

            # 去重
            origin_value = list(set(origin_value))
            origin_value = ','.join(str(item) for item in origin_value)

            item = {
                "productAttributeId": meta_dict.get(name)['id'],
                "value": origin_value,
                "attriName": meta_dict.get(name)['name'],
                "sort": meta_dict.get(name)['sort'],
                "filterType": meta_dict.get(name)['filterType'],
                "searchType": meta_dict.get(name)['searchType'],
                "type": meta_dict.get(name)['type'],
                "searchSort": meta_dict.get(name)['searchSort'],
            }
            attrs.append(item)

        product_param = {
            "albumPics": album_pics,
            "albumPicsJson": album_pics_json,
            "productAttributeValueList": attrs,
            "brandId": None,
            "productAttributeCategoryId": None,
            "gameAccountQufu": None,
            "description": None,
            "pic": cover,
            "price": None,
            "originalPrice": None,
            "stock": 9,
            "gameGoodsFangxin": None,
            "gameGoodsBukuan": 0,
            "gameGoodsJiangjia": 0,
            "gameGoodsYijia": None,
            "sort": 1,
            "publishTime": None,
            "publishStatus": 0,
            "gameGoodsYishou": None,
            "gameGoodsYuyue": 0,
            "gameGoodsSaletype": 1,
            "gameCareinfoPhone": None,
            "gameCareinfoTime": None,
            "gameCareinfoVx": None,
            "subjectProductRelationList": [],
            "recommandStatus": 0,
            "productCategoryId": None,
            "productCategoryName": None,
            "brandName": None,
            "productSn": product_sn,
            "verifyStatus": 0,
            "verifyDetail": None,
            "createTime": None,
            "gameSysinfoReadcount": None,
            "gameSysinfoCollectcount": None,
            # "memberId": member_id,
            "pushType": None,
            "pushStatus": 2  # 录号完成
        }
        r = self.__request('POST', self.server_url + '/mall-portal/openapi/record/product/update', json=product_param)

    def product_auto_verify(self, product_id, verify_status, verify_detail):
        """
        自动审核
        """
        if verify_status == 1:
            push_status = 3

        product_param = {
            "id": product_id,
            "verifyStatus": verify_status,
            "verifyDetail": verify_detail
        }
        r = self.__request('POST', self.server_url + '/mall-portal/openapi/record/product/verify', json=product_param)

    def get_new_task_from_kk(self, categoryId):
        """
        从kk获取新任务
        :param categoryId:
        :return:
        """
        task_id_product_sn = self.__request('GET',
                                            self.server_url + f'/mall-portal/openapi/record/product/getTheProductAccountInfo?categoryId={categoryId}')
        return task_id_product_sn

    def get_todo_task_from_kk(self, device_id):
        """
        从kk获取新任务
        :param device_id:
        :return:
        """
        try:
            task = self.__request('GET',
                                  self.server_url + f'/mall-portal/openapi/record/product/getTodoTask?deviceId={device_id}')
        except Exception as e:
            print(e)
            return None
        if task == '':
            return None
        return task

    def cancel_task_and_restart(self, task_id, retries=3, delay=2):
        """
        取消任务，重启
        """
        url = self.server_url + f'/mall-portal/openapi/record/task/cancel'
        for attempt in range(retries):
            try:
                resp = self.__request('POST', url, json={
                    "id": task_id,
                    "restart": True
                })
                return resp
            except requests.exceptions.RequestException as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                if attempt < retries - 1:  # Don't sleep after the last attempt
                    time.sleep(delay)
                else:
                    raise

    def cancel_task(self, task_id, retries=3, delay=2):
        """
        取消任务，重启
        """
        url = self.server_url + f'/mall-portal/openapi/record/task/cancel'
        for attempt in range(retries):
            try:
                resp = self.__request('POST', url, json={
                    "id": task_id,
                    "restart": False
                })
                return resp
            except requests.exceptions.RequestException as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                if attempt < retries - 1:  # Don't sleep after the last attempt
                    time.sleep(delay)
                else:
                    raise

    def resume_task(self, task_id, retries=3, delay=2):
        """
        重新发起任务
        """
        url = self.server_url + f'/mall-portal/openapi/record/task/resume'
        for attempt in range(retries):
            try:
                resp = self.__request('POST', url, json={
                    "taskId": task_id,
                    "needLogin": False,
                    "beginStage": "safe_step_juese"
                })
                return resp
            except requests.exceptions.RequestException as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                if attempt < retries - 1:  # Don't sleep after the last attempt
                    time.sleep(delay)
                else:
                    raise

    def get_product_category_meta(self, categoryId):
        resp = self.__request('GET', self.server_url + '/mall-portal/openapi/record/product/meta?categoryId=' + str(
            categoryId))
        return resp

    def get_product_detail(self, product_id):
        # resp = self.__request('GET', self.server_url + '/mall-portal/product/detail/' + str(product_id))
        resp = self.__request('GET', self.server_url + '/mall-portal/openapi/record/taskDetail/' + str(product_id))
        return resp

    # def send_heartbeat(self, instruction):
    #     """
    #     发送心跳
    #     :return:
    #     """
    #     resp = self.__request('POST', self.server_url + '/mall-portal/openapi/record/device/heartbeat',
    #                           json=instruction)
    #     return resp

    def send_heartbeat(self, instruction, retries=3, delay=2):
        """
        发送心跳 with retry mechanism.
        :param instruction: Heartbeat data to send.
        :param retries: Number of retry attempts.
        :param delay: Delay between retries in seconds.
        :return: Response object from the request.
        """
        url = self.server_url + '/mall-portal/openapi/record/device/heartbeat'
        for attempt in range(retries):
            try:
                resp = self.__request('POST', url, json=instruction)
                return resp
            except requests.exceptions.RequestException as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                if attempt < retries - 1:  # Don't sleep after the last attempt
                    time.sleep(delay)
                else:
                    raise

    def create_product(self, create_product_req):
        """
        创建商品
        :param create_product_req:
        :return: 返回task信息
        """
        resp = self.__request('POST', self.server_url + '/mall-portal/openapi/record/product/create',
                              json=create_product_req)
        return resp

    def create_task(self, create_task_req):
        """
        创建任务
        :param create_task_req:
        :return: 返回task信息
        """
        resp = self.__request('POST', self.server_url + '/mall-portal/openapi/record/task/createByProductSn',
                              json=create_task_req)
        return resp

    def get_attrs_from_sku(self, sku_id):
        "https://api2.kkzhw.com/mall-portal/home/<USER>/listall/17"
        resp = self.__request('GET', self.server_url + '/mall-portal/home/<USER>/listall/' + str(sku_id))
        return resp

    def restart_device(self, device_id):
        timestamp = int(time.time() * 1000)
        propertyBag = {
            'timestamp': timestamp,
            'command': 'DEVICE_RESTART',
            'status': 'TODO',
            'createTime': timestamp,
        }
        device = {
            "id": device_id,
            "propertyBag": sys_tool.safe_json_dumps(propertyBag)
        }
        return self.update_device(device)

    def update_device(self, device):
        resp = self.__request('POST', self.server_url + '/mall-portal/openapi/record/device/update',
                              json=device)
        return resp

    def send_msg(self, member_id, msg):
        data = {
            "fromIm": "kkassistant8",
            "toMemberId": member_id,
            "msg": msg
        }
        resp = self.__request('POST', self.server_url + '/mall-portal/openapi/record/sendMsg',
                              json=data)
        return resp

    def send_sms(self, member_id, tpl_code_desc, tpl_params):
        """
        :param member_id:
        :param tpl_code_desc: 录号失败通知
        :param tpl_params:
            格式如下
            [
                {
                    "key": "productSn",
                    "value": "xxx"
                },
                {
                    "key": "cause",
                    "value": "被顶号"
                }
            ]
        :return:
        """
        data = {
            "toMemberId": member_id,
            "tplCodeDesc": tpl_code_desc,
            "params": tpl_params
        }
        resp = self.__request('POST', self.server_url + '/mall-portal/openapi/record/sendSms',
                              json=data)
        return resp

    def __request(self, method, url, **kwargs):
        """
        发送请求
        :param method:
        :param url:
        :param kwargs:
        :return:
        """
        # 设置 proxies 为空以禁用代理
        proxies = {
            "http": None,
            "https": None,
        }

        r = None
        try:
            r = requests.request(method, url, headers={'X-Token': self.token}, proxies=proxies, **kwargs)
            if r.json().get('code') == 200:
                return r.json()['data']
            else:
                logger.error('request: %s %s %s', method, url, kwargs)
                print(('resp:', r.text))
        except Exception as e:
            logger.exception('request error:')
            logger.error('request: %s %s %s', method, url, kwargs)
            if r:
                logger.error('resp: %s %s', r.status_code, r.text)

    def __request_return_msg(self, method, url, **kwargs):
        """
        发送请求
        :param method:
        :param url:
        :param kwargs:
        :return:
        """
        r = None
        try:
            r = requests.request(method, url, headers={'X-Token': self.token}, **kwargs)
            if r.json().get('code') == 200:
                return r.json()['data']
            else:
                print(('resp:', r.text))
                return r.json().get('message')
        except Exception as e:
            logger.exception('request error:')
            logger.error('request: %s %s %s', method, url, kwargs)
            if r:
                logger.error('resp: %s %s', r.status_code, r.text)

    @staticmethod
    def extract_number(input_string):
        # 使用正则表达式提取数字
        match = re.search(r'\d+', input_string)
        if match:
            return match.group(0)  # 返回找到的第一个数字
        return None

    def get_device_id(self, device_addr):
        result = self.__request_return_msg('GET',
                                           self.server_url + '/mall-portal/openapi/record/device/getId?deviceAddress=' + device_addr)
        if result is None:
            for i in range(3):
                time.sleep(2)
                result = self.__request_return_msg('GET',
                                                   self.server_url + '/mall-portal/openapi/record/device/getId?deviceAddress=' + device_addr)
                if result:
                    break
        return result

    @lru_cache(maxsize=128)
    def get_device_detail(self, device_id):
        result = self.__request_return_msg('GET',
                                           self.server_url + '/mall-portal/openapi/record/device/detail?deviceId=' + str(
                                               device_id))
        if result is None:
            for i in range(3):
                time.sleep(2)
                result = self.__request_return_msg('GET',
                                                   self.server_url + '/mall-portal/openapi/record/device/detail?deviceId=' + str(
                                                       device_id))
                if result:
                    break
        return result

    @lru_cache(maxsize=128)
    def get_error_text(self, category_name):
        try:
            print('get_error_text:', category_name)
            result = self.__request('GET',
                                    self.server_url + '/mall-portal/openapi/record/getErrorText?categoryName=' + category_name)
            if result is None:
                for i in range(3):
                    time.sleep(2)
                    result = self.__request('GET',
                                            self.server_url + '/mall-portal/openapi/record/getErrorText?categoryName=' + category_name)
                    if result:
                        break
            if result is None:
                return configs.ERROR_TEXT
            else:
                return json.loads(result)
        except Exception as e:
            traceback.print_exc()
            return configs.ERROR_TEXT

    @lru_cache(maxsize=128)
    def get_config_value(self, key):
        try:
            print('get_config_value:', key)
            result = self.__request('GET',
                                    self.server_url + '/mall-portal/openapi/record/getConfigValue?key=' + key)
            if result is None:
                for i in range(3):
                    time.sleep(2)
                    result = self.__request('GET',
                                            self.server_url + '/mall-portal/openapi/record/getConfigValue?key=' + key)
                    if result:
                        break
            return result
        except Exception as e:
            traceback.print_exc()

    def get_tss_value(self):
        value = self.get_config_value('逆水寒手游天赏石')
        if value is None:
            return configs.TSS_VALUE
        else:
            return json.loads(value)

    def get_black_attr_value(self, category_name):
        value = self.get_config_value(category_name + '黑话库')
        if value is None:
            return configs.black_attr_value
        else:
            return json.loads(value)

    def init_bot_members(self, batch_size):
        resp = self.__request('POST',
                              self.server_url + '/mall-portal/openapi/negotia/initNegoBotMember?batchSize=' + str(
                                  batch_size),
                              json=None)
        return resp

    def get_bot_members(self, limit):
        resp = self.__request('GET',
                              self.server_url + '/mall-portal/openapi/negotia/getNegoBotMember?limit=' + str(limit))
        return resp

    def get_nego_product_ids(self):
        resp = self.__request('GET',
                              self.server_url + '/mall-portal/openapi/negotia/getAINegoProductIds')
        return resp

    def ai_nego_offer(self, username, product_id):
        params = {
            'username': username,
            'productId': product_id,
        }
        resp = self.__request('GET', self.server_url + '/mall-portal/openapi/negotia/AINegoOffer', params=params)
        return resp


if __name__ == '__main__':
    client = PortalClient()

    # task = {
    #     "id": 116,
    #     "snapshot": "xjiewjfwieofweji"
    # }
    # client.sync_task_info(task)

    # client.sync_product_info("TESTNSH99512726", "xxx", None, None)
    # client.cancel_task_and_restart(1223)
    # client.send_heartbeat(instruction={
    #     'deviceId': 31,
    #     'deviceStatus': 'IDLE',
    #     # 'command':'TASK_CANCEL',
    #     'timestamp': 1727273906134
    # })
    # for i in range(200):
    #     error_text = client.get_black_attr_value('逆水寒手游')
    #     print(error_text)
    # client.send_msg(72, 'test')
    # r = client.get_product_detail("385801")
    r = client.get_product_detail(400390)
    print(r)
