import datetime
import random
import time

from apscheduler.schedulers.blocking import BlockingScheduler

from common.logger_config import setup_logger
from common.server_client import PortalClient

logger = setup_logger("ai_nego_scheduler")

portal_client = PortalClient()
scheduler = BlockingScheduler()  # 将scheduler定义为全局变量


def get_random_member(members):
    """从成员列表中随机选择一个成员"""
    if not members:
        logger.warning("成员列表为空，无法选择随机成员")
        return None
    return random.choice(members)


def negotiate_all_products(product_ids, members):
    """对所有商品进行议价，每次议价随机选择一个用户"""
    if not product_ids:
        logger.info("没有待议价的商品")
        return

    logger.info(f"开始对 {len(product_ids)} 个商品进行议价")

    for product_id in product_ids:
        # 随机延迟，模拟用户操作时间
        delay = random.uniform(1, 5)  # 1到5秒的随机延迟
        logger.info(f"等待 {delay:.2f} 秒后开始议价...")
        time.sleep(delay)

        logger.info(f"正在议价的商品ID: {product_id}")

        # 每次议价都随机选择一个用户
        selected_member = get_random_member(members)
        if not selected_member:
            logger.error("无法选择用户，跳过当前商品")
            continue

        selected_username = selected_member["username"]
        logger.info(f"随机选择的用户: {selected_username}")

        try:
            portal_client.ai_nego_offer(selected_username, product_id)
            logger.info(f"商品ID {product_id} 议价成功")
        except Exception as e:
            logger.error(f"议价过程中发生错误，商品ID: {product_id}, 错误: {e}")


def main():
    """主函数"""
    try:
        # 获取成员列表
        members = portal_client.get_bot_members(100)
        logger.info(f"获取到的机器人数量: {len(members)}")

        # 获取待议价商品ID
        product_ids = portal_client.get_nego_product_ids()
        logger.info(f"待议价的商品ID: {product_ids}")

        # 对所有商品进行议价，每次议价随机选择一个用户
        negotiate_all_products(product_ids, members)
    except Exception as e:
        logger.error(f"主函数执行异常: {e}", exc_info=True)
    finally:
        # 确保无论如何都调度下一次任务
        schedule_next_job()


def schedule_next_job():
    """动态调度下一次任务"""
    # 随机生成下一次任务的执行时间间隔
    next_run_interval = random.randint(30, 60)  # 随机间隔（分钟）
    next_run_time = datetime.datetime.now() + datetime.timedelta(minutes=next_run_interval)

    # 定义允许的时间范围
    start_time = datetime.time(9, 30)
    end_time = datetime.time(23, 59)

    # 检查是否在允许的时间范围内
    current_time = next_run_time.time()
    if current_time > end_time or current_time < start_time:
        # 如果不在允许范围内，延迟到当天的9:30到10:30之间执行
        today = datetime.datetime.now().date()
        # 生成9点30分 + 随机0-60分钟
        random_minutes = random.randint(0, 60)
        next_run_time = datetime.datetime.combine(
            today,
            datetime.time(9, 30)
        ) + datetime.timedelta(minutes=random_minutes)

        logger.info(f"下次执行超出允许范围，已调度到当天 {next_run_time.strftime('%H:%M')}")

    # 添加任务
    scheduler.add_job(
        main,
        'date',
        run_date=next_run_time,
        timezone='Asia/Shanghai'
    )
    logger.info(f"下一次任务将在 {next_run_time} 执行")


def schedule_job():
    """初始化调度任务"""
    # 首次任务立即执行
    scheduler.add_job(
        main,
        'date',
        run_date=datetime.datetime.now(),
        timezone='Asia/Shanghai'
    )

    try:
        logger.info("开始调度任务...")
        scheduler.start()
    except (KeyboardInterrupt, SystemExit):
        logger.info("调度任务已停止")


if __name__ == "__main__":
    schedule_job()
