import atexit
import hashlib
import json
import os
import platform
import re
import shutil
import threading
import time

import psutil
import pyzipper

# 应用程序的进程名
starter_app = 'LootHoarder.exe'
zxsj_starter_app = 'ZXSJGame.exe'
yysls_starter_app = 'launcher.exe'
possible_paths = [
    r'C:\Program Files (x86)\逆水寒手游-MuMu模拟器\LootHoarder.exe',
    r'D:\Program Files (x86)\逆水寒手游-MuMu模拟器\LootHoarder.exe',
    r'E:\Program Files (x86)\逆水寒手游-MuMu模拟器\LootHoarder.exe',
    r'C:\逆水寒手游-MuMu模拟器\LootHoarder.exe',
    r'D:\逆水寒手游-MuMu模拟器\LootHoarder.exe',
    r'E:\逆水寒手游-MuMu模拟器\LootHoarder.exe'
]
nshm_app = 'nshm.exe'
zxsj_app = 'ZhuxianClient-Win64-Shipping.exe'
yysls_app = 'yysls.exe'
# zxsj_app = 'ZXSJBrowser.exe'

nsh_luhao_app = '看看账号网逆水寒手游录号.exe'
nsh_luhao_possible_paths = [
    r'D:\local_luhao\看看账号网逆水寒手游录号.exe',
    r'E:\local_luhao\看看账号网逆水寒手游录号.exe',
    r'F:\local_luhao\看看账号网逆水寒手游录号.exe',
]

mumu_app = 'MuMuPlayer.exe'
mum_possible_paths = [
    r'C:\Program Files\Netease\MuMu Player 12\shell\MuMuPlayer.exe',
    r'D:\Program Files\Netease\MuMu Player 12\shell\MuMuPlayer.exe',
    r'E:\Program Files\Netease\MuMu Player 12\shell\MuMuPlayer.exe',
    r'C:\Netease\MuMu Player 12\shell\MuMuPlayer.exe',
    r'D:\Netease\MuMu Player 12\shell\MuMuPlayer.exe',
    r'E:\Netease\MuMu Player 12\shell\MuMuPlayer.exe',

    r'C:\Program Files\Netease\MuMuPlayer-12.0\shell\MuMuPlayer.exe',
    r'D:\Program Files\Netease\MuMuPlayer-12.0\shell\MuMuPlayer.exe',
    r'E:\Program Files\Netease\MuMuPlayer-12.0\shell\MuMuPlayer.exe',
    r'C:\Netease\MuMuPlayer-12.0\shell\MuMuPlayer.exe',
    r'D:\Netease\MuMuPlayer-12.0\shell\MuMuPlayer.exe',
    r'E:\Netease\MuMuPlayer-12.0\shell\MuMuPlayer.exe',
]


def find_process(process_name):
    """检查指定进程是否在运行"""
    for proc in psutil.process_iter(['name']):
        if proc.info['name'] == process_name:
            return proc
    return None

def trigger_scheduled_task(task_name):
    """触发计划任务"""
    try:
        # 运行计划任务
        cmd = f'schtasks /run /tn "{task_name}"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        # 检查任务触发结果
        if result.returncode == 0:
            print(f"计划任务 '{task_name}' 已成功触发。")
            return True
        else:
            print(f"触发计划任务 '{task_name}' 失败。错误信息：{result.stderr}")
            return False
    except Exception as e:
        print(f"触发计划任务时发生异常: {e}")
        return False

def start_loot_hoarder_1():
    """尝试从多个可能的路径启动 LootHoarder.exe"""
    for path in possible_paths:
        if os.path.exists(path):
            print(f"找到 {starter_app} 路径: {path}，启动中...")

            task_name = 'StartNSH'

            # 触发任务
            trigger_scheduled_task(task_name)

            time.sleep(10)
            starter_app_proc = find_process(starter_app)
            if starter_app_proc:
                print(f"{starter_app} 启动成功。")
            else:
                print(f"{starter_app} 未启动运行，尝试重新启动 {starter_app}...")
                subprocess.Popen([path])
                time.sleep(10)
            return True
    print(f"未找到 {starter_app} 的路径，无法启动。")
    return False


def start_loot_hoarder():
    task_name = 'StartNSH'

    # 触发任务
    trigger_scheduled_task(task_name)

    time.sleep(10)
    starter_app_proc = find_process(starter_app)
    if starter_app_proc:
        print(f"{starter_app} 启动成功。")
        return True
    else:
        print(f"{starter_app} 启动失败。")
        return False

def start_loot_zxsj():
    try:
        task_name = 'StartZXSJ'
        # 触发任务
        trigger_scheduled_task(task_name)
        time.sleep(10)
        starter_app_proc = find_process(zxsj_starter_app)
        if starter_app_proc:
            print(f"{zxsj_starter_app} 启动成功。")
            return True
        else:
            print(f"未找到 {zxsj_starter_app} 的路径，无法启动。")
            return False
    except Exception as e:
        print(f"触发计划任务时发生异常: {e}")
        return False

def start_loot_yysls():
    try:
        task_name = 'StartYYSLS'
        # 触发任务
        trigger_scheduled_task(task_name)
        time.sleep(10)
        starter_app_proc = find_process(yysls_starter_app)
        if starter_app_proc:
            print(f"{yysls_starter_app} 启动成功。")
            return True
        else:
            print(f"未找到 {yysls_starter_app} 的路径，无法启动。。")
            return False
    except Exception as e:
        print(f"触发计划任务时发生异常: {e}")
        return False

def start_mumu():
    for path in mum_possible_paths:
        if os.path.exists(path):
            print(f"找到 {mumu_app} 路径: {path}，启动中...")
            subprocess.Popen([path])
            time.sleep(10)
            starter_app_proc = find_process(mumu_app)
            if starter_app_proc:
                print(f"{mumu_app} 启动成功。")
            else:
                print(f"{mumu_app} 未启动运行，尝试重新启动 {mumu_app}...")
                subprocess.Popen([path])
                time.sleep(10)
        return True
    print(f"未找到 {mumu_app} 的路径，无法启动。")
    return False


def close_nshm():
    """关闭 nshm.exe 进程"""
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == nshm_app:
            print(f"找到并关闭 {proc.info['name']} (PID: {proc.info['pid']})")
            proc.terminate()  # 尝试终止进程
            try:
                proc.wait(timeout=10)  # 等待更长时间以确保进程关闭
                return True
            except psutil.TimeoutExpired:
                print(f"{proc.info['name']} 进程没有及时终止，强制关闭...")
                proc.kill()  # 强制终止进程
                return True
    print(f"未找到 {nshm_app} 进程。")
    return False

def close_zxsj():
    """关闭 zxsj.exe 进程"""
    for proc in psutil.process_iter(['pid', 'name']):
        # print(f"-------------------------------------{proc.info['name']}")
        if proc.info['name'] == zxsj_app:

            print(f"找到并关闭 {proc.info['name']} (PID: {proc.info['pid']})")
            proc.terminate()  # 尝试终止进程
            try:
                proc.wait(timeout=10)  # 等待更长时间以确保进程关闭
                return True
            except psutil.TimeoutExpired:
                print(f"{proc.info['name']} 进程没有及时终止，强制关闭...")
                proc.kill()  # 强制终止进程
                return True
    print(f"未找到 {zxsj_app} 进程。")
    return False

def close_yysls():
    """关闭 zxsj.exe 进程"""
    for proc in psutil.process_iter(['pid', 'name']):
        # print(f"-------------------------------------{proc.info['name']}")
        if proc.info['name'] == yysls_app:

            print(f"找到并关闭 {proc.info['name']} (PID: {proc.info['pid']})")
            proc.terminate()  # 尝试终止进程
            try:
                proc.wait(timeout=10)  # 等待更长时间以确保进程关闭
                return True
            except psutil.TimeoutExpired:
                print(f"{proc.info['name']} 进程没有及时终止，强制关闭...")
                proc.kill()  # 强制终止进程
                return True
    print(f"未找到 {yysls_app} 进程。")
    return False

def close_mumu():
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == mumu_app:
            print(f"找到并关闭 {proc.info['name']} (PID: {proc.info['pid']})")
            proc.terminate()  # 终止进程
            proc.wait(timeout=5)  # 等待进程关闭
            return True
    print(f"未找到 {mumu_app} 进程。")
    return False


def restart_nshm():
    """重启 nshm.exe 进程"""
    close_success = close_nshm()  # 先关闭 nshm.exe
    if close_success:
        time.sleep(5)  # 等待 5 秒以确保所有资源释放
        try:
            start_loot_hoarder()
            print(f"{starter_app} 已重新启动。")
        except Exception as e:
            print(f"重启 {nshm_app} 失败: {e}")
    else:
        print(f"{nshm_app} 未运行，无需关闭，直接启动...")
        start_loot_hoarder()

def restart_zxsj():
    """重启 nshm.exe 进程"""
    close_success = close_zxsj()  # 先关闭 nshm.exe
    if close_success:
        time.sleep(5)  # 等待 5 秒以确保所有资源释放
        try:
            start_loot_zxsj()
            print(f"{zxsj_app} 已重新启动。")
        except Exception as e:
            print(f"重启 {zxsj_app} 失败: {e}")
    else:
        print(f"{zxsj_app} 未运行，无需关闭，直接启动...")
        start_loot_zxsj()

def restart_yysls():
    """重启 nshm.exe 进程"""
    close_success = close_yysls()  # 先关闭 nshm.exe
    if close_success:
        time.sleep(5)  # 等待 5 秒以确保所有资源释放
        try:
            start_loot_yysls()
            print(f"{yysls_app} 已重新启动。")
        except Exception as e:
            print(f"重启 {yysls_app} 失败: {e}")
    else:
        print(f"{yysls_app} 未运行，无需关闭，直接启动...")
        start_loot_yysls()

def restart_mumu():
    """重启 mumu 进程"""
    close_success = close_mumu()
    if close_success:
        try:
            start_mumu()
            print(f"{mumu_app} 已重新启动。")
        except Exception as e:
            print(f"重启 {nshm_app} 失败: {e}")
    else:
        print(f"{mumu_app} 未运行，无需关闭，直接启动...")
        start_mumu()


def close_nsh_luhao():
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == nsh_luhao_app:
            print(f"找到并关闭 {proc.info['name']} (PID: {proc.info['pid']})")
            proc.terminate()  # 终止进程
            proc.wait(timeout=5)  # 等待进程关闭
            return True
    print(f"未找到 {nsh_luhao_app} 进程。")
    return False


def start_nsh_luhao():
    for path in nsh_luhao_possible_paths:
        if os.path.exists(path):
            print(f"找到 {nsh_luhao_app} 路径: {path}，启动中...")
            subprocess.Popen([path])
            # 每秒检查一次，最多检查10秒
            for _ in range(10):
                time.sleep(1)  # 等待1秒
                proc = find_process(nsh_luhao_app)
                if proc:
                    print(f"{nsh_luhao_app} 启动成功。")
                    return True
            print(f"{nsh_luhao_app} 启动失败，未在10秒内找到进程。")
            return False
    print(f"未找到 {nsh_luhao_app} 的路径，无法启动。")
    return False


def restart_nsh_luhao():
    close_success = close_nsh_luhao()
    if close_success:
        try:
            start_success = start_nsh_luhao()
            print(f"{nsh_luhao_app} 已重新启动。")
            return start_success
        except Exception as e:
            print(f"重启 {nsh_luhao_app} 失败: {e}")
            return False
    else:
        print(f"{nsh_luhao_app} 未运行，无需关闭，直接启动...")
        return start_nsh_luhao()


def get_mac_address():
    system_name = platform.system()

    try:
        if system_name == "Windows":
            # Windows 获取 MAC 地址
            output = subprocess.check_output("getmac", shell=True).decode('gbk')
            # 解析输出结果，找到 MAC 地址
            mac_address = re.search(r'([A-F0-9]{2}[:-]){5}([A-F0-9]{2})', output, re.I)
            if mac_address:
                return mac_address.group(0)

        elif system_name == "Linux" or system_name == "Darwin":  # Linux 和 macOS 使用 ifconfig
            output = subprocess.check_output("ifconfig", shell=True).decode()
            # 解析输出结果，找到 MAC 地址
            mac_address = re.search(r'([a-f0-9]{2}:){5}[a-f0-9]{2}', output, re.I)
            if mac_address:
                return mac_address.group(0)

    except Exception as e:
        return "UnknownMAC"


def monitor_and_restart():
    """监控 nshm.exe 进程，并在必要时重启 LootHoarder.exe"""
    while True:
        nshm_proc = find_process(nshm_app)  # 监控 nshm.exe
        if nshm_proc:
            print(f"{nshm_app} 正在运行。")
        else:
            print(f"{nshm_app} 未运行，尝试重新启动 {starter_app}...")
            start_loot_hoarder()
            time.sleep(10)  # 等待10秒查看是否能成功启动 nshm.exe
        time.sleep(5)  # 每隔5秒检查一次进程状态


def get_hardware_info():
    # 获取 MAC 地址
    mac_address = get_mac_address()

    try:
        cpu_id = subprocess.check_output('wmic cpu get ProcessorId', shell=True).decode().split()[1]
    except:
        cpu_id = "UnknownCPU"

    try:
        hdd_serial = subprocess.check_output('wmic diskdrive get serialnumber', shell=True).decode().split()[1]
    except:
        hdd_serial = "UnknownHDD"

    # 组合信息
    hardware_info = f"{mac_address}-{cpu_id}-{hdd_serial}"
    return hardware_info


def generate_device_id(prefix="DEV-NSH"):
    hardware_info = get_hardware_info()
    # 使用 SHA-256 生成哈希值，取前 16 位，并添加固定前缀
    short_device_id = hashlib.sha256(hardware_info.encode()).hexdigest()[:16]
    return f"{prefix}-{short_device_id}"


def safe_json_dumps(data):
    if data is None:
        return None
    return json.dumps(data, ensure_ascii=False)


def zip_and_encrypt_folder(folder_path, zip_name, password):
    with pyzipper.AESZipFile(zip_name, 'w', compression=pyzipper.ZIP_DEFLATED) as zf:
        zf.setpassword(password.encode('utf-8'))
        zf.setencryption(pyzipper.WZ_AES)  # 设置加密模式为 AES
        for root, _, files in os.walk(folder_path):
            for file in files:
                if file.endswith('.jpg'):
                    file_path = os.path.join(root, file)
                    # 使用 arcname 参数保留原文件夹结构
                    arcname = os.path.relpath(file_path, folder_path)
                    zf.write(file_path, arcname=arcname)
    print(f"Folder '{folder_path}' has been zipped and encrypted as '{zip_name}'.")


def zip_folder(folder_path, zip_name):
    # 确保文件夹路径存在
    if not os.path.exists(folder_path):
        print(f"文件夹路径 {folder_path} 不存在")
        return

    # 获取文件夹的父目录和文件夹名称
    parent_dir = os.path.dirname(folder_path)
    folder_name = os.path.basename(folder_path)

    # 创建 ZIP 文件
    shutil.make_archive(zip_name, 'zip', parent_dir, folder_name)
    print(f"文件夹 {folder_path} 已成功打包为 {zip_name}.zip")

def restart_computer():
    os.system("shutdown /r /t 0")  # /r 表示重启，/t 0 表示立即重启

import subprocess

ffmpeg_process = None
max_duration_timer = None  # 计时器对象


# 开始录制，并设置最大录制时长
def start_recording(output_file='recording.mp4', max_duration=1800):  # 默认最大录制时长为 30 分钟（1800 秒）
    global ffmpeg_process, max_duration_timer
    try:
        ffmpeg_process = subprocess.Popen([
            'ffmpeg', '-f', 'gdigrab', '-i', 'desktop', output_file
        ], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print("Recording started...")

        # 设置计时器，在 max_duration 秒后自动停止录制
        max_duration_timer = threading.Timer(max_duration, stop_recording)
        max_duration_timer.start()
        print(f"Recording will automatically stop after {max_duration / 60} minutes.")
    except Exception as e:
        print(f"Failed to start recording: {e}")


# 停止录制
def stop_recording():
    global ffmpeg_process, max_duration_timer
    if ffmpeg_process:
        ffmpeg_process.communicate(input=b'q')  # 通过 stdin 发送 'q' 结束录制
        print("Recording stopped...")
        ffmpeg_process = None
    if max_duration_timer:
        max_duration_timer.cancel()  # 取消计时器
        max_duration_timer = None


atexit.register(stop_recording)

if __name__ == '__main__':
    # 示例：关闭 nshm.exe，启动 LootHoarder.exe，并监控 nshm.exe 的运行
    # start_loot_hoarder()  # 启动 LootHoarder.exe
    # monitor_and_restart()  # 监控并重新启动 nshm.exe

    # 示例：生成设备 ID，自定义前缀
    # print(generate_device_id())
    # restart_nsh_luhao()
    # restart_mumu()
    # start_recording('F:\\recording.mp4')  # 开始录制
    # input("Press Enter to stop recording...")  # 按回车键停止录制
    # stop_recording()  # 停止录制
    folder_path = "/Users/<USER>/workspace/luhao/dict"
    zip_name = "a"
    zip_folder(folder_path, zip_name)