# 使用官方 Python 3.9 镜像作为基础镜像
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/python:3.9.19

# 设置时区为 Asia/Shanghai
ENV TZ=Asia/Shanghai

WORKDIR /app

# 复制 requirements.txt 到容器中
COPY ./bm/requirements.txt .

# 安装依赖项
RUN pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 复制项目文件到容器中
COPY ./common /app/common
COPY ./client /app/client
COPY ./bm /app/bm
COPY cache_util.py /app

# 设置 PYTHONPATH
ENV PYTHONPATH=/app

# 设置默认的命令来运行你的 Python 应用
CMD ["python", "bm/import_scheduler.py"]