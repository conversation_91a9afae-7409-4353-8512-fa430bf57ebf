# -*- encoding=utf8 -*-
__author__ = "kkzhw016"

import io
import traceback
from logging import exception
from os import times
from time import sleep

from airtest.core.android import Android
from airtest.core.api import *


# -*- encoding=utf8 -*-
__author__ = "labugao"

# auto_setup(__file__)

# if 'auto_setup' in globals():
#     auto_setup(__file__)
# else:
#     from airtest.core.api import *
#     from airtest.cli.parser import cli_setup

#     if not cli_setup():
#         auto_setup(__file__, logdir=False, devices=[
#             "Android:///?cap_method=javacap&ori_method=adbori",
#     ])
import os
import sys

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

from airtest.core.api import *
from airtest.aircv import *
from paddleocr import PaddleOCR
import requests
import json
from airtest.aircv.cal_confidence import cal_ccoeff_confidence  
from airtest.core.settings import Settings as ST
import oss2  
import os  
import numpy as np  
import configparser
from PIL import Image, ImageDraw, ImageFont,ImageFilter, ImageChops
import shutil  
import logging  
from urllib.parse import urlparse

import psutil

import sys_tool
from sys_tool import restart_nshm


import configs
import product_util
import logger_config
import nshpc_image_util as  image_util
from server_client import PortalClient
import models
from models import *
from collections import defaultdict
from luhao_models import EventStatus, TaskEvent

#####################################################
# auto_setup(__file__, devices=["Android://127.0.0.1:5037/127.0.0.1:50000?ori_method=adbori"])

# auto_setup(__file__)
# connect_device("Android://127.0.0.1:5037/127.0.0.1:50000?ori_method=adbori")
auto_setup(__file__)
# connect_device("Windows:///?title_re=逆水寒手游模拟器.*")

#########################################################################
import datetime  
import random  
import string  
import re
import pyautogui
#############################################
#############################################################################全局变量
projectPath = "D:\\kkzhw\\airtest_log\\"
snapImgPath = ""
logger = None
logger2 = None
# 设置全局的截图精度为90
ST.SNAPSHOT_QUALITY = 99
# 获取当前设备的屏幕宽度和高度  
width = 1280
height = 756

center_x = 1280//2
center_y = 756//2
product_meta = []
luhao_task = None
luhao_failed = False
pic_url_arrays = []
tianniran_ocr_txt_arrays = set()
ocr_file = 1 #ocr 是否保存识别区域图片 1保存图片 0不保存
touch_fast = True
start_time = datetime.datetime.now()
end_time = datetime.datetime.now()
is_local = False
save_original_pic = False
is_debug = False
logger_queue=None
login_other_count = 0
android = None
tianniran_count = 0
ziran_pic_count = 0
waiguan_set = []
waiguan_taozhuang_set = []
is_fashi_end = False
waiguan_fashi_set = []
waiguan_ziran_fashi_set = []
waiguan_wuqi_set = []
waiguan_zuoqi_set = []
waiguan_xiangrui_set = []
waiguan_beibu_set = []
waiguan_jiaoyin_set = []
waiguan_huanshen_set = []
waiguan_qingong_set = []
waiguan_ziran_set = []
waiguan_ts_jineng_set = []
neigong_lingyun_set = []
has_mumu = False
is_fail_send=False
is_skip_qufu=False
###############################################################################


##########################################
ocr = PaddleOCR(use_angle_cls=False, lang='ch',
                det_model_dir="models\\det\\ch_PP-OCRv4_det_infer",
                rec_model_dir="models\\rec\\ch_PP-OCRv4_rec_infer",
                cls_model_dir="models\\cls\\ch_ppocr_mobile_v2.0_cls_infer"
                )


portal_client = PortalClient()
###########################################################################

def remove_unwanted_chars(s):
    s = re.sub(r'\[[^\]]*\]', '', s) 
    return re.sub(r'[^·\u4e00-\u9fff]', '', s)
  
####################################################
def contains_digit(s):  
    return bool(re.search(r'\d', s)) 
######################################################################
def clear_folder(folder_path):
    # 文件夹路径  
    new_folder_name = f'{folder_path}_' + str(int(time.time()))
    if os.path.exists(folder_path):  
        shutil.move(folder_path, new_folder_name)  
        print(f"文件夹 '{folder_path}' 已重命名为 '{new_folder_name}'")
    os.makedirs(folder_path, exist_ok=True)
    print(f"新的空文件夹 '{folder_path}' 已创建")
##############################################################
def clear_folder2(folder_path):
    if os.path.exists(folder_path):
       return
    os.makedirs(folder_path, exist_ok=True)
    print(f"新的空文件夹 '{folder_path}' 已创建")
######################################################################
  
def generate_oss_path():  
    # 获取当前日期并格式化为 yyyyMMdd  
    today = datetime.datetime.now().strftime("%Y%m%d")
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=6))  
    # 获取当前毫秒时间戳  
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
    # 拼接路径  
    oss_path = f"{today}/"  
    return oss_path
###################################################################
def read_config(filename):  
    config = {}  
    with open(filename, 'r', encoding='utf-8') as file:  
        for line in file:  
            # 去除每行两端的空白字符，并检查是否为空行或注释行（假设以#开头的行为注释）  
            line = line.strip()  
            if not line or line.startswith('#'):  
                continue  
            # 使用等号分割键和值  
            key, value = line.split('=', 1)  
            # 去除值两端的引号（如果有的话）  
            value = value.strip('"').strip("'")  
            config[key] = value  
    return config  
########################################################################

def generate_oss_fileName():  
    # 生成六位随机字符数字串  
    # 注意：这里使用了string.ascii_letters + string.digits来确保生成的是字母和数字的混合  
    # 如果只想要数字，可以去掉string.ascii_letters部分  
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=7))  
      
    # 获取当前毫秒时间戳  
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
      
    # 拼接路径  
    oss_fileName = f"{random_str}_{current_millis}.jpg"  
      
    return oss_fileName
############################################################################


################################################
def resize_image_by_width(input_image_path, output_image_path, target_width):  
    # 打开原始图片  
    with Image.open(input_image_path) as img:  
        # 获取原始图片的宽度和高度  
        original_width, original_height = img.size  
          
        # 计算缩放比例  
        ratio = original_width / float(target_width)  
          
        # 根据缩放比例计算新的高度  
        new_height = int(original_height / ratio)  
          
        # 缩放图片  
        resized_img = img.resize((target_width, new_height), Image.LANCZOS)  
          
        # 保存缩放后的图片  
        resized_img.save(output_image_path)  
##############################################################################


#########################################################################

def upload_kk_img_to_oss(folder_path):
    for i in range(3):
        try:
            image_path_set = set()
            global pic_url_arrays
            pic_url_arrays = []

            bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称
            endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint
            access_key_id = configs.OSS_ACCESS_KEY_ID
            access_key_secret = configs.OSS_ACCESS_KEY_SECRET
            kkLogger_log(f"开始上传文件夹{folder_path} 内容")

            auth = oss2.Auth(access_key_id, access_key_secret)
            bucket = oss2.Bucket(auth, endpoint, bucket_name)
            total_count = count_images(folder_path)
            up_count  = 0
            for filename in os.listdir(folder_path):
                local_file_path = os.path.join(folder_path, filename)
                if filename.endswith('jpg') and "luhao_" not in filename:
                    up_count = up_count + 1
                    kkLogger_log(f"上传图片：{up_count}/{total_count}")
                    oss_file_path = "mall/images2/"+ generate_oss_path()

                    oss_fileName = oss_file_path+generate_oss_fileName()
                    with open(local_file_path, 'rb') as fileobj:
                        bucket.put_object(oss_fileName, fileobj)
                        image_path_set.add(oss_fileName)

                        parts = filename.split('_')
                        kkLogger_log(f"oss 文件:{oss_fileName}")
                        # new_json_object = {"name": parts[0], "value": oss_fileName}
                        if "头图_天赏外观" in filename:
                            new_json_object = {"name": "头图", "value": oss_fileName}
                            pic_url_arrays.append(new_json_object)

                            # new_json_object = {"name": parts[1], "value": oss_fileName}
                            # pic_url_arrays.append(new_json_object)
                        else:
                            new_json_object = {"name": parts[1], "value": oss_fileName}
                            pic_url_arrays.append(new_json_object)

            break
        except Exception as e:
            kkLogger_log(f"upload_kk_img_to_oss error：{traceback.format_exc()},第{i}次")
            sleep(2)
            continue
###############################################################################
def upload_one_img_to_oss(filename):
    try:
        bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称
        endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint
        access_key_id = configs.OSS_ACCESS_KEY_ID
        access_key_secret = configs.OSS_ACCESS_KEY_SECRET
        # print(f"开始上传文件夹{snapImgPath} 内容")

        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)

        local_file_path = os.path.join(snapImgPath, filename)
        oss_file_path = "mall/images2/"+ generate_oss_path()
        oss_fileName = oss_file_path+generate_oss_fileName()
        with open(local_file_path, 'rb') as fileobj:
            bucket.put_object(oss_fileName, fileobj)

        # print(f"oss_filename:{oss_fileName}")
        return oss_fileName
    except Exception as e:
        kkLogger_log(f"upload_one_img_to_oss error：{traceback.format_exc()}")

    #############################################################
def count_images(folder_path):  
    jpg_count = 0  
    png_count = 0  
    # 遍历指定文件夹及其所有子文件夹  
    for root, dirs, files in os.walk(folder_path):  
        for file in files:  
            # 检查文件扩展名  
            if file.lower().endswith('.jpg'):  
                jpg_count += 1  
#             elif file.lower().endswith('.png'):  
#                 png_count += 1  
    # 返回总数  
    total_images = jpg_count + png_count  
    return total_images  
###################################################################


############################################################################
#根据sn查询账号密码
def requestGameAccountInfo(productSn):  
    kk_url = 'https://api2.kkzhw.com/mall-portal/import/product/getProductAccountInfo?productSn='+productSn
    response = requests.get(kk_url)
    # 检查请求是否成功  
    if response.status_code == 200:  
        # 打印返回的内容  
        kkLogger_log(response.text)
        responseObj = json.loads(response.text)
        product_dict = responseObj.get('data')
        return product_dict
    
########################################################################
#根据分类ID获取待录号productSn
def requestCategoryProductSn(categoryId):  
    kk_url = f'http://api2.kkzhw.com/mall-portal/import/product/getTheProductAccountInfo?categoryId={categoryId}'
    response = requests.get(kk_url)
    # 检查请求是否成功  
    if response.status_code == 200:  
        # 打印返回的内容  
#         print(response.text)
        responseObj = json.loads(response.text)
        productSn = responseObj.get('data')
        return productSn
    else:
        return ""
###########################################################################
def requestGameProductCategoryMeta(categoryId): 
    kk_url = f'https://api2.kkzhw.com/mall-portal/openapi/record/product/meta?categoryId={categoryId}'
    response = requests.get(kk_url)
    # 检查请求是否成功
    if response.status_code == 200:  
        # 打印返回的内容
        responseObj = json.loads(response.text)
        product_meta = responseObj.get('data')
        return product_meta
###################################################################根据sn查询账号密码


##OCR TOUCH#########################################ffffffffff##########
def ocr_touch(target_text) :
      # 截屏当前画面
    pic_path=f"{snapImgPath}\\now.png"
    snapshot(pic_path)
    
     # 使用PaddleOCR识别图片文字
    ocr_result = ocr.ocr(pic_path, cls=False)
    
    # 遍历识别结果，找到目标文字的坐标
    target_coords = None
    for line in ocr_result:
        for word_info in line:
            #获取识别结果的文字信息
            textinfo = word_info[1][0]
            kkLogger_log(textinfo)
            if target_text in textinfo:
                # 获取文字的坐标（中心点）
                x1, y1 = word_info[0][0]
                x2, y2 = word_info[0][2]
                target_coords = ((x1 + x2) / 2, (y1 + y2) / 2)
                break
        if target_coords:
            break

    # 使用Airtest..坐标
    if target_coords:
        touch(target_coords)
    else:
        kkLogger_log(f"ocr_touch 未找到目标文字：{target_text}")
#####################################################################


##ocr text####################################################################
def ocr_text_all():
    for i in range(5):
        try:
            sleep(1)
            pic_path=f"{snapImgPath}\\now.png"
            snapshot(pic_path)
            ocr_result = ocr.ocr(pic_path, cls=False)
            target_coords = None
            for line in ocr_result:
                for word_info in line:
                    textinfo = word_info[1][0]
            return ocr_result
        except Exception as e:
            kkLogger_log(f"ocr_text_all 失败：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
############################################################

def find_all_subImg(temp, start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            find_result =  temp.match_all_in(local_screen)

            kkLogger_log(f"find_all_subImg：{find_result}")

            if find_result:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_all_subImg_{current_millis}.png')
            return find_result
        except Exception as e:
            kkLogger_log(f"find_all_subImg error：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
###################################################
def find_subImg(temp, start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            # sleep(0.5)
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            find_result = temp.match_in(local_screen)

            # kkLogger_log(f"find_subImg：{find_result}")

            if ocr_file == 1:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\plog_find_subImg_{current_millis}.png')
            return find_result
        except Exception as e:
            kkLogger_log(f"find_subImg 失败：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
########################################################
def find_subImg_inscreen(temp,local_screen):

    for i in range(5):
        try:
            # sleep(0.5)
            find_result = temp.match_in(local_screen)
            kkLogger_log(f"find_subImg_inscreen：{find_result}")
            if find_result and ocr_file == 1:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_subImg_inscreen_{current_millis}.png')
            return find_result
        except Exception as e:
            kkLogger_log(f"find_subImg_inscreen 失败：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
#########################################################
def find_first_subImg(temp, start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            sleep(0.5)
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            f_result = temp.match_all_in(local_screen)
            kkLogger_log(f"find_first_subImg：{f_result}")

            if f_result:
                #保存为图片
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_first_subImg_{current_millis}.png')

                print(f"f_result:{f_result}")  
                min_y = f_result[0]['result'][1]  # 假设f_result的第一个元素的第二个值是y坐标  
                min_y_item = f_result[0]['result']  

                # 遍历f_result找到y坐标最小的匹配项  
                for item in f_result:  
                    # 假设item是一个包含(x, y, w, h)的元组  
                    current_y = item['result'][1]
                    if current_y < min_y:  
                        min_y = current_y
                        min_y_item = item['result']
                print("Y坐标最小的匹配项:", min_y_item)  
                print("Y坐标:", min_y)  
                return min_y_item
            else:
                return None
        except Exception as e:
            kkLogger_log(f"find_first_subImg 失败：{traceback.format_exc()}")
            sleep(2)
            continue  
    return None
#######################################################################
def ocr_text_inverted(start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()
            if screen is None:
                sleep(0.5)
                screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            cropped_gray_screen_rgb = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            cropped_screen_rgb = cv2.cvtColor(cropped_gray_screen_rgb, cv2.COLOR_BGR2RGB)
            inverted_cropped_screen = 255 - cropped_screen_rgb
            ocr_result = ocr.ocr(inverted_cropped_screen, cls=False)
            if ocr_file == 1:
                pil_img = cv2_2_pil(inverted_cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_now_ocr_{current_millis}.png")

            return ocr_result
        except Exception as e:
            kkLogger_log(f"ocr_text error：{traceback.format_exc()}")
            sleep(2)
            continue
    return None

##############################################################
def ocr_text(start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()
            if screen is None:
                sleep(0.5)
                screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            gray_cropped_screen = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            ocr_result = ocr.ocr(gray_cropped_screen, cls=False)

            if ocr_file == 1:
                pil_img = cv2_2_pil(gray_cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_now_ocr_{current_millis}.png")

            return ocr_result
        except Exception as e:
            kkLogger_log(f"ocr_text error：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
###############################################################################
def ocr_text_all(target_text,start_x,start_y,end_x,end_y) :
    for i in range(5):
        try:
            if touch_fast is False:
                sleep(0.5)
              # 截屏当前画面
            pic_path=f"{snapImgPath}\\now_orc_all.png"
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            pil_img = cv2_2_pil(cropped_screen)
            pil_img.save(f"{snapImgPath}\\now_orc_all.png")

             # 使用PaddleOCR识别图片文字
            ocr_result = ocr.ocr(pic_path, cls=False)
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            target_coords_list = []
            for line in ocr_result:
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"ocr_text_all 识别到{textinfo}")
                    if target_text in textinfo:
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        target_coords_list.append(target_coords)
            kkLogger_log(f"识别到{target_text},数量：{len(target_coords_list)}")
            return target_coords_list
        except Exception as e:
            kkLogger_log(f"ocr_text_all error：{traceback.format_exc()}")
            sleep(2)
            continue
    return None
################################################################################
def ocr_text_target_coords(target_text,start_x,start_y,end_x,end_y,is_wait=True) :
    if touch_fast is False and is_wait is True:
        sleep(0.2)
    for i in range(5):
        try:  
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_target_coords_{target_text}_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=False)
            if ocr_result is None:
                return None
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"识别到文字：{textinfo}")
                    if target_text in textinfo:
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        break
                if target_coords:
                    break

            # 使用Airtest..坐标
            if target_coords:
                return target_coords
            else:
                kkLogger_log(f"ocr_text_target_coords 未找到目标文字：{target_text}")
                return target_coords
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_target_coords error：{traceback.format_exc()}")
            continue
            
    return None
#########################################################################
def calculate_similarity(image1, image2):
    diff = ImageChops.difference(image1, image2)
    diff = diff.convert('L')
    mean_diff = diff.getextrema()[1] / 255.0
    return mean_diff

def long_snapshot(mode_name,start_x,start_y,end_x,end_y,scroll_height,waite_time=1.5,same=0.8):
    window_width = end_x - start_x
    window_height = end_y - start_y
    current_millis = int(round(datetime.datetime.now().timestamp()))
    try:
        images = []
        for i in range(50):
            screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            images.append(screenshot)
            pyautogui.scroll(-scroll_height)
            sleep(waite_time)
            new_screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            similarity = calculate_similarity(new_screenshot, screenshot)
            if (1-similarity) > same:
                break
        total_width = window_width
        total_height = sum(img.height for img in images)
        long_image = Image.new('RGB', (total_width, total_height))

        y_offset = 0
        for img in images:
            long_image.paste(img, (0, y_offset))
            y_offset += img.height

        long_image.save(f'{snapImgPath}\\{mode_name}_其他物品_{current_millis}.jpg')
    except Exception as e:
        kkLogger_log(f"long_snapshot error：{traceback.format_exc()}")
###################################################################################
def long_snapshot3(mode_name,start_x,start_y,end_x,end_y,scroll_height,waite_time=1.5,same=0.8,width_num=1):
    window_width = end_x - start_x
    window_height = end_y - start_y
    current_millis = int(round(datetime.datetime.now().timestamp()))
    try:
        images = []
        for i in range(100):
            screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            images.append(screenshot)
            pyautogui.scroll(-scroll_height)
            sleep(waite_time)
            new_screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            similarity = calculate_similarity(new_screenshot, screenshot)
            if (1-similarity) > same:
                break

        # 计算长图的尺寸：每行3张小图，高度根据图片数量决定（每3张一行）
        rows = len(images)  # 向上取整到最近的行数（使用+2是为了确保即使图片数量不是3的倍数也能正确处理）
        long_img_width = window_width * width_num
        long_img_height = window_height * rows
        # 创建长图
        long_image = Image.new('RGB', (long_img_width, long_img_height))
        # 将小图粘贴到长图上
        y_offset = 0
        x_offset = 0
        for i, img in enumerate(images):
            row = i // width_num
            col = i % width_num
            if col == 0:
                y_offset = row * window_height
                x_offset = 0
            long_image.paste(img, (x_offset, y_offset))
            x_offset += window_width

        long_image.save(f'{snapImgPath}\\{mode_name}_其他物品_{current_millis}.png')
        os.rename(f'{snapImgPath}\\{mode_name}_其他物品_{current_millis}.png', f'{snapImgPath}\\{mode_name}_其他物品_{current_millis}.jpg')

    except Exception as e:
        kkLogger_log(f"long_snapshot error：{traceback.format_exc()}")
##############################################################
def long_snapshot_click(mode_name,start_x,start_y,end_x,end_y,click_position,waite_time=1.5,same=0.8,width_num=1):
    window_width = end_x - start_x
    window_height = end_y - start_y
    current_millis = int(round(datetime.datetime.now().timestamp()))
    try:
        images = []
        sync_current_snapshot(mode_name, None, True)
        for i in range(100):
            screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            images.append(screenshot)

            has_lock = save_cropped_area_screen_and_ocr_add_to_get(mode_name, start_x,start_y,end_x,end_y,[5,4])
            kktouch3(click_position[0],click_position[1],waite_time,"")
            new_screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            similarity = calculate_similarity(new_screenshot, screenshot)
            print(f"{1-similarity}")
            if (1-similarity) > same:
                break

        # 计算长图的尺寸：每行3张小图，高度根据图片数量决定（每3张一行）
        # rows = (len(images) + 2) // width_num  # 向上取整到最近的行数（使用+2是为了确保即使图片数量不是3的倍数也能正确处理）
        # long_img_width = window_width * width_num
        # long_img_height = window_height * rows
        # # 创建长图
        # long_image = Image.new('RGB', (long_img_width, long_img_height))
        # # 将小图粘贴到长图上
        # y_offset = 0
        # x_offset = 0
        # for i, img in enumerate(images):
        #     row = i // width_num
        #     col = i % width_num
        #     if col == 0:
        #         y_offset = row * window_height
        #         x_offset = 0
        #     long_image.paste(img, (x_offset, y_offset))
        #     x_offset += window_width

        # long_image.save(f'{snapImgPath}\\{mode_name}_其他物品_{current_millis}.png')
        # os.rename(f'{snapImgPath}\\{mode_name}_其他物品_{current_millis}.png', f'{snapImgPath}\\{mode_name}_其他物品_{current_millis}.jpg')
    except Exception as e:
        kkLogger_log(f"long_snapshot error：{traceback.format_exc()}")
#########################################################################

###########################################################################
def long_snapshot2(mode_name,start_x,start_y,end_x,end_y,scroll_height,waite_time=1.5,same=0.7):
    window_width = end_x - start_x  # 窗口宽度
    window_height = end_y - start_y  # 窗口高度

    try:
        # 存储截图列表
        images = []
        for i in range(100):
            # 截图
            screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))
            images.append(screenshot)

            # 滚动窗口
            pyautogui.scroll(-scroll_height)  # 向下滚动
            sleep(waite_time)  # 等待滚动完成

            # 再次截图，检查是否有新的内容出现
            new_screenshot = pyautogui.screenshot(region=(start_x, start_y, window_width, window_height))

            # 计算两次截图的相似度
            similarity = calculate_similarity(new_screenshot, screenshot)
            # print(f"===================={1-similarity}")
            if (1 - similarity) > same:
                break

        # 拼接图片
        total_width = window_width
        total_height = sum(img.height for img in images)
        long_image = Image.new('RGB', (total_width, total_height))

        y_offset = 0
        for img in images:
            long_image.paste(img, (0, y_offset))
            y_offset += img.height

        # 保存长截图
        long_image.save(f'{snapImgPath}\\{mode_name}_其他物品.jpg')
    except Exception as e:
        kkLogger_log(f"long_snapshot error：{traceback.format_exc()}")
###############################################################
def ocr_text_target_coords_arrays(target_text,start_x,start_y,end_x,end_y,hit_txts=None,t_positon=0) :
    if touch_fast is False:
        sleep(0.5)
    target_coords_arrays = []
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            gray_cropped_screen = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            if ocr_file == 1:
                pil_img = cv2_2_pil(gray_cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_target_coords_arrays_{target_text}_{current_millis}.png")

            ocr_result = ocr.ocr(gray_cropped_screen, cls=False)
            if ocr_result is None:
                return target_coords_arrays
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"o_t_t_c_a识别到文字：{textinfo}","info")
                    if target_text in textinfo:
                        if hit_txts is not None:
                            hit_txts.append(textinfo.strip())
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        if 0 == t_positon:
                            target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                            target_coords_arrays.append(target_coords)
                        elif -1 == t_positon:
                            target_coords = (start_x + x1, start_y + y1)
                            target_coords_arrays.append(target_coords)
                        elif 1 == t_positon:
                            target_coords = (start_x + x2, start_y + y2)
                            target_coords_arrays.append(target_coords)

            # 使用Airtest..坐标
            if len(target_coords_arrays) > 0:
                return target_coords_arrays
            else:
                kkLogger_log(f"ocr_text_target_coords_arrays 未找到目标文字：{target_text}")
                return target_coords_arrays
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_target_coords_arrays error：{traceback.format_exc()}")
            continue
            
    return target_coords_arrays
#########################################################################
def ocr_text_target_coords_arrays_zuoqi(modename,target_text, start_x, start_y, end_x, end_y, hit_txts=None, t_positon=0):
    if touch_fast is False:
        sleep(0.5)
    target_coords_arrays = []
    for i in range(2):
        try:
            screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            gray_cropped_screen = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            if ocr_file == 1:
                pil_img = cv2_2_pil(gray_cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_target_coords_arrays_{target_text}_{current_millis}.png")

            ocr_result = ocr.ocr(gray_cropped_screen, cls=False)
            if ocr_result is None:
                return target_coords_arrays
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    # 获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    # kkLogger_log(f"o_t_t_c_a识别到文字：{textinfo}", "info")
                    get_ocr_txt = remove_unwanted_chars(textinfo)
                    if target_text in textinfo:
                        if hit_txts is not None:
                            hit_txts.append(textinfo.strip())
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        if 0 == t_positon:
                            target_coords = (start_x + (x1 + x2) / 2, start_y + (y1 + y2) / 2)
                            target_coords_arrays.append(target_coords)
                        elif -1 == t_positon:
                            target_coords = (start_x + x1, start_y + y1)
                            target_coords_arrays.append(target_coords)
                        elif 1 == t_positon:
                            target_coords = (start_x + x2, start_y + y2)
                            target_coords_arrays.append(target_coords)

            # 使用Airtest..坐标
            if len(target_coords_arrays) > 0:
                return target_coords_arrays
            else:
                kkLogger_log(f"ocr_text_target_coords_arrays 未找到目标文字：{target_text}")
                return target_coords_arrays
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_target_coords_arrays error：{traceback.format_exc()}")
            continue

    return target_coords_arrays
###############################################################################
def ocr_text_target_coords_arrays_inverted(target_text, start_x, start_y, end_x, end_y, hit_txts=None):
    if touch_fast is False:
        sleep(0.5)
    target_coords_arrays = []
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            # gray_cropped_screen = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            # cropped_screen_rgb = cv2.cvtColor(gray_cropped_screen, cv2.COLOR_BGR2RGB)

            cropped_screen_rgb = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            inverted_cropped_screen = 255 - cropped_screen_rgb

            if ocr_file == 1:
                pil_img = cv2_2_pil(inverted_cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_target_coords_arrays_{target_text}_{current_millis}.png")

            ocr_result = ocr.ocr(inverted_cropped_screen, cls=False)
            if ocr_result is None:
                return target_coords_arrays
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    # 获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"o_t_t_c_a识别到文字：{textinfo}", "info")
                    if target_text in textinfo:
                        if hit_txts is not None:
                            hit_txts.append(textinfo.strip())
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x + (x1 + x2) / 2, start_y + (y1 + y2) / 2)
                        target_coords_arrays.append(target_coords)

            # 使用Airtest..坐标
            if len(target_coords_arrays) > 0:
                return target_coords_arrays
            else:
                kkLogger_log(f"ocr_text_target_coords_arrays 未找到目标文字：{target_text}")
                return target_coords_arrays
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_target_coords_arrays error：{traceback.format_exc()}")
            continue

    return target_coords_arrays
##ocr text#################################################################
def local_ocr_text_has_txt(start_x, start_y, end_x, end_y):
    if touch_fast is False:
        sleep(0.5)

    for i in range(2):
        try:
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_util.capture_screen(
                f"{snapImgPath}\\local_ocr_text_has_txt_{current_millis}.png",
                region=(start_x, start_y, end_x, end_y))

            ocr_result = ocr.ocr(f"{snapImgPath}\\local_ocr_text_has_txt_{current_millis}.png",
                                 cls=False)

            if ocr_result is None:
                return False

            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    # 获取识别结果的文字信息
                    txt = word_info[1][0]
                    if txt is not None and len(txt) > 0:
                        return True

            return False
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_number error：{traceback.format_exc()}")
            continue

    return False


################################################################################
def ocr_text_has_txt_inverted(start_x, start_y, end_x, end_y):
    if touch_fast is False:
        sleep(0.5)

    for i in range(2):
        try:
            screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            cropped_screen_rgb = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            inverted_cropped_screen = 255 - cropped_screen_rgb

            if ocr_file == 1:
                pil_img = cv2_2_pil(inverted_cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_has_txt_{current_millis}.png")

            ocr_result = ocr.ocr(inverted_cropped_screen, cls=False)
            if ocr_result is None or ocr_result is [None]:
                return False
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    txt = word_info[1][0]
                    if txt is not None and len(txt) > 0:
                        return True
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_txt error：{traceback.format_exc()}")
            continue

    return False
###########################################################################################

def ocr_text_has_txt(start_x, start_y, end_x, end_y):
    if touch_fast is False:
        sleep(0.5)

    for i in range(2):
        try:
            screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            # gray_cropped_screen = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_has_txt_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=False)
            # print(f"==============={ocr_result}")
            if ocr_result is None or ocr_result is [None]:
                return False
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    txt = word_info[1][0]
                    if txt is not None and len(txt) > 0:
                        return True
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_txt error：{traceback.format_exc()}")
            continue

    return False
#################################################
def local_ocr_text_has_number(start_x, start_y, end_x, end_y):
    if touch_fast is False:
        sleep(0.5)

    for i in range(2):
        try:
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_util.capture_screen(
                f"{snapImgPath}\\local_ocr_text_has_number_{current_millis}.png",
                region=(start_x, start_y, end_x, end_y))

            ocr_result = ocr.ocr(f"{snapImgPath}\\local_ocr_text_has_number_{current_millis}.png",
                                 cls=False)

            if ocr_result is None:
                return False

            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    # 获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    if bool(re.search(r'\d', textinfo)):
                        kkLogger_log(f"ocr_text_has_number查找到数字：{textinfo}")
                        return True

            return False
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_number error：{traceback.format_exc()}")
            continue

    return False

############################################
def ocr_text_has_number(start_x,start_y,end_x,end_y) :
    if touch_fast is False:
        sleep(0.5)
    
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_has_number_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=False)
            if ocr_result is None:
                return False
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    if bool(re.search(r'\d', textinfo)):
                        kkLogger_log(f"ocr_text_has_number查找到数字：{textinfo}")
                        return True

            return False
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_number error：{traceback.format_exc()}")
            continue
            
    return False
####################################################
##全屏截图方法，指定区域打码，去掉UUID
def save_cropped_screen_with_blur2(modename,start_x,start_y,end_x,end_y, x1, y1, x2, y2):
    sleep(1)
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    for i in range(5):
        try:
            kkLogger_log(f"全屏截图，部分打码：{modename}")
            blur_region = (x1-start_x, y1-start_y, x2-start_x, y2-start_y)
            screen = G.DEVICE.snapshot()

            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            pil_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(pil_img)

            region_to_blur = pil_img.crop(blur_region)

            region_to_blur = region_to_blur.filter(ImageFilter.GaussianBlur(radius=20))

            pil_img.paste(region_to_blur, blur_region)

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg', 'JPEG')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_screen_with_mulblur error：{traceback.format_exc()}")
            continue

###################################################################
##全屏截图方法，指定区域打码，去掉UUID
def save_cropped_screen_with_blur(modename, x1, y1, x2, y2):  
    sleep(1)
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    for i in range(5):
        try:  
            kkLogger_log(f"全屏截图，部分打码：{modename}")
            blur_region = (x1, y1, x2, y2)
            screen = G.DEVICE.snapshot()  
            
            cropped_screen = aircv.crop_image(screen, (1, 40, width, height))

            pil_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(pil_img)

            region_to_blur = pil_img.crop(blur_region)

            region_to_blur = region_to_blur.filter(ImageFilter.GaussianBlur(radius=20))

            pil_img.paste(region_to_blur, blur_region)

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg', 'JPEG')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_screen_with_mulblur error：{traceback.format_exc()}")
            continue
#########################################################
def kk_keyevent(key,times=1,sleep_time=0.8,msg=None):
    check_nshm_exits()
    for i in range(times):
        keyevent(key)
        kkLogger_log(f"kk_keyevent：{key},msg:{msg}")
        sleep(sleep_time)
################################################################
def kk_scroll(coords,wheel_dist,sleep_time= 0.9,msg=None):
    device().mouse.scroll(coords=coords, wheel_dist=wheel_dist)
    sleep(sleep_time)
    if msg is not None:
        kkLogger_log(f"kk_scroll wheel_dist：{coords},wheel_dist:{wheel_dist},msg:{msg}")
##################################################
is_gonggao_checked = False
is_zhuangyuan_gonggao_checked = False
is_game_update_checked = False
def check_and_try_game_home(attempts=4,step=None,is_check_logother=True):
    kkLogger_log(f"check_and_try_game_home: {attempts}")
    if is_check_logother and check_account_login_other():
        sync_login_fail("被顶号，录号失败")
        print("CATGH 被顶号")
        return False

    #两次归零
    kk_keyevent("{ESC}", 2, 0.1, "回主页")
    #尝试唤出菜单
    kk_keyevent("{ESC}", 1, 0.5, "回主页2")
    for j in range(attempts):
        # if check_account_login_other():
        #     sync_login_fail("被顶号，录号失败")
        #     print("CATGH 被顶号")
        #     return False
        #判断是否菜单界面
        if local_ocr_text_target_coords("设", 655, 323, 944, 620) is not None:
            #关闭菜单
            kk_keyevent("{ESC}", 1, 0.5, "回主页2")
            return True
        else:
            kk_keyevent("{ESC}", 1, 0.5, "回主页2")
            sleep(j+1)
            print(f"CATGH:{j}")
            continue

    return False
###########################################################################
def save_cropped_screen_with_mulblur(modename, blur_regions):  
    sleep(1)
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    for i in range(5):
        try:  
            kkLogger_log(f"全屏截图，部分打码：{modename}")

            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (1, 39, width, height))
            
            pil_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(pil_img)

            # 遍历所有需要模糊的区域  
            for blur_region in blur_regions:  
                x1, y1, x2, y2 = blur_region  
                # 确保区域在图片范围内  
                x1, y1, x2, y2 = max(0, x1), max(0, y1), min(width, x2), min(height, y2)  
                # 裁剪出需要模糊的区域  
                region_to_blur = pil_img.crop((x1, y1, x2, y2))  
                # 应用高斯模糊  
                region_to_blur = region_to_blur.filter(ImageFilter.GaussianBlur(radius=20))  
                # 将模糊后的区域粘贴回原图  
                pil_img.paste(region_to_blur, (x1, y1))  

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg', 'JPEG')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_screen_with_mulblur error：{traceback.format_exc()}")
            continue
##全屏截图方法，去掉UUID
def save_cropped_screen(modename,file_endfix=".jpg"):
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    sleep(0.5)
    for i in range(2):
        try:  
            kkLogger_log(f"全屏截图：{modename}")
            # screen = G.DEVICE.snapshot()
            # if touch_fast is False:
            #     sleep(0.5)
            # if width > 1290:
            #     cropped_screen = aircv.crop_image(screen, (1, 40, 1280, 720))
            # else:
            #     cropped_screen = aircv.crop_image(screen, (1, 40, width, height))
            # pil_img = cv2_2_pil(cropped_screen)
            # # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒
            # current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            #
            # pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}{file_endfix}')

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            if width > 1599:
                image_util.capture_screen(f'{snapImgPath}\\{modename}_{current_millis}{file_endfix}',
                                          region=(10, 40, 1599, 1046))
            else:
                image_util.capture_screen(f'{snapImgPath}\\{modename}_{current_millis}{file_endfix}',
                                          region=(10, 40, width, height))

            return f"{modename}_{current_millis}{file_endfix}"
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_screen error：{traceback.format_exc()},尺寸:{width},{height}")


    
###################################################################
def save_cropped_area_screen(modename,start_x,start_y,end_x,end_y,sleep_time=0.8,file_endfix=".jpg"):
    if sleep_time > 0:
        sleep(sleep_time)
    else:
        sleep(0.1)
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    for i in range(2):
        try:  
            kkLogger_log(f"区域截图：{modename}")
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_util.capture_screen(f'{snapImgPath}\\{modename}_{current_millis}{file_endfix}',
                region=(start_x, start_y, end_x, end_y))
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen error 截图失败：{traceback.format_exc()}")
            continue
#############################################################################################
def save_cropped_area_screen_dazao(modename,start_x,start_y,end_x,end_y):
    # sleep(1)
    for i in range(5):
        try:  
            kkLogger_log(f"区域截图：{modename}")
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            
            tile_img_2 = np.array(pil_img)
            if contains_purple_in_corner(tile_img_2,(55,29)):
                pil_img.save(f'{snapImgPath}\\{modename}_1_{current_millis}.jpg')
            elif contains_gold_in_corner(tile_img_2,(55,29)) and not contains_pink_in_corner(tile_img_2,(55,29)):
                pil_img.save(f'{snapImgPath}\\{modename}_0_{current_millis}.jpg')
            else:
                pil_img.save(f'{snapImgPath}\\{modename}_2_{current_millis}.jpg')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen error：{traceback.format_exc()}")
            continue
###############################################################################
def save_cropped_area_screen_zuoqi(modename, start_x, start_y, end_x, end_y):
    for i in range(1):
        try:
            # kkLogger_log(f"区域截图：{modename}")
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            pil_img = cv2_2_pil(cropped_screen)
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))

            cropped_screen2 = aircv.crop_image(screen, (start_x+65, start_y, end_x-30, end_y-28))
            pil_img_ocr = cv2_2_pil(cropped_screen2)
            tile_img_2_ocr = np.array(pil_img_ocr)
            try:
                result = ocr_screen_add_to_get(modename, tile_img_2_ocr, True)
                if result[0]is False or result[1] is False:
                    continue
            except Exception as e:
                kkLogger_log(f"ocr_screen_add_to_get3 error {e}")
                continue



            # tile_img_2 = np.array(pil_img)
            # if contains_purple_in_corner(tile_img_2, (55, 29)):
            #     pil_img.save(f'{snapImgPath}\\{modename}_1_{current_millis}.jpg')
            # elif contains_gold_in_corner(tile_img_2, (55, 29)) and not contains_pink_in_corner(tile_img_2, (55, 29)):
            #     pil_img.save(f'{snapImgPath}\\{modename}_0_{current_millis}.jpg')
            # else:
            #     pil_img.save(f'{snapImgPath}\\{modename}_2_{current_millis}.jpg')
            pil_img.save(f'{snapImgPath}\\{modename}_2_{current_millis}.png')
            # os.rename(f'{snapImgPath}\\{modename}_2_{current_millis}.png',
            #           f'{snapImgPath}\\{modename}_2_{current_millis}.jpg')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen error：{traceback.format_exc()}")
            continue
######################################################################################
def is_close_match(s1, s2):
    """
    检查两个字符串是否至多有一个字符不同。
    """
    if len(s1) != len(s2) or len(s1) < 3:
        return False

    diff_count = 0
    for c1, c2 in zip(s1, s2):
        if c1 != c2:
            diff_count += 1
            if diff_count > 1:
                return False

    return True

##########################################################
def find_close_match(input_str, string_array):
    """
    在字符串数组中查找与输入字符串至多有一个字符不同的字符串。
    """
    for target in string_array:
        if len(input_str) == len(target) and is_close_match(input_str, target):
            return target
    return None

######################################################################
tianniran_saved_hashes = set()  
def save_cropped_area_screen_tianniran(modename,start_x,start_y,end_x,end_y):
    sleep(1)
    for i in range(5):
        try:  
            kkLogger_log(f"save_cropped_area_screen_tianniran区域截图：{modename}")
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)
            
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen_tianniran error：{e}")
            continue
######################################################################
def save_cropped_area_screen_and_ocr_add_to_get(modename,start_x,start_y,end_x,end_y,split_num=None,temp=None):
    if touch_fast is False:
        sleep(0.5)
    else:
        sleep(0.1)
    has_lock = False
    is_end = False

    for i in range(2):
        try:
            kkLogger_log(f"区域截图：{modename}")

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_util.capture_screen(
                f"{snapImgPath}\\save_cropped_area_screen_and_ocr_add_to_get_{current_millis}.png",
                region=(start_x, start_y, end_x, end_y))
            pil_img = Image.open(f"{snapImgPath}\\save_cropped_area_screen_and_ocr_add_to_get_{current_millis}.png")

            if split_num is not None:
                # 获取图像的尺寸
                img_width, img_height = pil_img.size
                # 计算切割后的小图片尺寸（这里假设均匀切割）
                tile_width = img_width // split_num[1]
                tile_height = img_height // split_num[0]
                # 切割图像并保存
                for row in range(split_num[0]):
                    for col in range(split_num[1]):
                        if is_end:
                            continue

                        left = col * tile_width
                        upper = row * tile_height
                        right = (col + 1) * tile_width
                        lower = (row + 1) * tile_height

                        tile_img = pil_img.crop((left, upper, right, lower))
                        tile_img_ocr = pil_img.crop((left, lower-22, right, lower))
                        tile_img_ocr2 = pil_img.crop((left, lower-22, right, lower))


                        tile_img_2_ocr = np.array(tile_img_ocr)
                        tile_img_2_ocr2 = np.array(tile_img_ocr2)


                        result = (True,False)


                        try:
                            inverted_tile_img_2_ocr = 255 - tile_img_2_ocr
                            inverted_tile_img_2_ocr = np.clip(inverted_tile_img_2_ocr, 0, 255).astype(np.uint8)
                            result = ocr_screen_add_to_get(modename,inverted_tile_img_2_ocr)
                        except Exception as e:
                            kkLogger_log(f"ocr_screen_add_to_get2 error {e}")

                        #是否无锁
                        # if result[0] is False:
                        #     has_lock = True
                        #     is_end = True
                        #     continue

                        try:
                            result = ocr_screen_add_to_get(modename, tile_img_2_ocr2,True)
                        except Exception as e:
                            kkLogger_log(f"ocr_screen_add_to_get3 error {e}")

                        if result[1] is False:
                            continue


                        current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
                        tile_img.save(f'{snapImgPath}\\{modename}_{current_millis}{row}{col}.png')

            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen_and_ocr_add_to_get error：{e}")
            continue
    return has_lock
###############################################################################################################
def contains_pink_in_corner(tile_img_np, region_size):  
    try:  
        height, width, _ = tile_img_np.shape  

        x_end = region_size[0] 
        y_end = region_size[1]  
        corner_img = tile_img_np[22:y_end, 10:x_end]
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  

        lower_gold = np.array([233, 103, 94])  # 金黄色的下限（色调、饱和度、明度）  
        upper_gold = np.array([234, 108, 102])  # 金黄色的上限  

        mask = cv2.inRange(hsv_img, lower_gold, upper_gold)

        return np.any(mask != 0)  
    except Exception as e:  
        return False
########################################################
def contains_gold_in_corner(tile_img_np, region_size):  
    try:  
        height, width, _ = tile_img_np.shape  

        x_end = region_size[0] 
        y_end = region_size[1]  
        corner_img = tile_img_np[22:y_end, 10:x_end]
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  

        lower_gold = np.array([15, 100, 100])  # 金黄色的下限（色调、饱和度、明度）  
        upper_gold = np.array([45, 255, 255])  # 金黄色的上限  

        mask = cv2.inRange(hsv_img, lower_gold, upper_gold)

        return np.any(mask != 0)  
    except Exception as e:  
        return False
###########################################################
def contains_orange_in_corner(tile_img_np, region_size):
    try:
        # 获取图像的高度、宽度和通道数
        height, width, _ = tile_img_np.shape

        # 定义要检查的角落区域的尺寸
        x_end = region_size[0]
        y_end = region_size[1]
        # 从图像的指定角落提取子图像
        corner_img = tile_img_np[22:y_end, 10:x_end]

        # 将图像从RGB颜色空间转换为HSV颜色空间
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)

        # 定义橘黄色的HSV阈值范围
        # 这些值可能需要根据实际的图像情况进行调整
        lower_orange = np.array([10, 100, 100])  # 橘黄色的下限（色调、饱和度、明度）
        upper_orange = np.array([25, 255, 255])  # 橘黄色的上限

        # 创建一个掩码，用于标记橘黄色区域
        mask = cv2.inRange(hsv_img, lower_orange, upper_orange)

        # 检查掩码中是否存在非零像素，即是否包含橘黄色
        return np.any(mask != 0)
    except Exception as e:
        # 如果发生任何异常，返回False
        return False
###############################################################################
def contains_purple_in_corner(tile_img_np, region_size):  
    try:  
        height, width, _ = tile_img_np.shape  

        x_end = region_size[0]  # 左上角区域的右边界  
        y_end = region_size[1]  # 左上角区域的下边界  

        corner_img = tile_img_np[0:y_end, 0:x_end]  
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  

        lower_purple = np.array([120, 30, 150])  # 紫色下限（色调、饱和度、明度）  
        upper_purple = np.array([160, 200, 255])  # 紫色上限（注意：HSV色调是循环的，所以180接近0）  

        # 创建紫色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_purple, upper_purple)  

        return np.any(mask != 0)  
    except Exception as e:  
        return False

###############################################################################
def contains_yellow_in_corner(tile_img_np, region_size):   
    try:
        height, width, _ = tile_img_np.shape  
        # 计算右下角区域的左上角坐标  
        x_start = width-10 - region_size[0]
        y_start = height-10 - region_size[1]
        corner_img = tile_img_np[y_start:y_start+region_size[1], x_start:x_start+region_size[0]]  
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  
        # lower_yellow = np.array([15, 100, 100])
        lower_yellow = np.array([15, 100, 150])
        upper_yellow = np.array([35, 255, 255])

        # 创建黄色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_yellow, upper_yellow)  

        # 检查掩码中是否存在非零像素，以确定是否存在黄色  
        return np.any(mask != 0)  
    except Exception as e:
        return False
#################################################################
def contains_yellow(start_x,start_y,end_x,end_y):
    try:
        screen = G.DEVICE.snapshot()
        cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
        hsv_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2HSV)

        lower_yellow = np.array([15, 100, 100])  
        upper_yellow = np.array([45, 255, 255])


        # 创建黄色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_yellow, upper_yellow)  

        # 检查掩码中是否存在非零像素，以确定是否存在黄色  
        return np.any(mask != 0)  
    except Exception as e:
        print(e)
        return False
###############################################################################
#识别到的名称放入meta中
def add_to_get(ocr_text):
    return ocr_check_and_add(ocr_text)
    
#####################################################################
def add_to_json_array(json_array, name, value):
    new_json_object = {"name": name, "value": value}
    json_array.append(new_json_object)  
    return json_array
################################################

def kktouch(x, y, sleep_time):  
    touch((x,y))
    sleep(sleep_time)
######################################################
def kkpress(key,msg,time=1):
    pyautogui.press(key)
    print(f"pr{msg}")
    sleep(time)

# def kkmove(x,y,duration=0.8,dou=True,stime=1):
#     pyautogui.moveTo(x, y, duration)
#     sleep(stime)
#     if dou:
#         pyautogui.moveTo(x-3, y-3, 0.3)
#         pyautogui.moveTo(x+3, y+3, 0.3)

###################################################
def kkmove(x,y,msg,stime=0.8,duration=0.8,dou=True):
    print(msg)
    pyautogui.moveTo(x, y, duration)
    sleep(stime)
    if dou:
        pyautogui.moveTo(x-3, y-3, 0.3)
        pyautogui.moveTo(x+3, y+3, 0.3)
############################################################################
def kktouch_step(x, y, sleep_time,msg):
    if msg == "外观":
        kk_keyevent("{F12}")
        if ocr_text_target_coords("时", 198, 690, 358, 740) is not None:
            kkLogger_log(f"快捷键前往：{msg}，成功")
            return True
        else:
            kkLogger_log(f"快捷键前往：{msg}，失败，尝试..前往")
            return kktouch2(x, y, sleep_time, msg)
    if msg == "角色":
        return kktouch2(x, y, sleep_time, msg)
    if msg == "打造":
        kk_keyevent("{F10}")
        kktouch2(106,684,1,"快捷打造")
        sync_current_snapshot("开始打造。")
        if ocr_text_target_coords("打", 95, 42,187, 102) is not None:
            kkLogger_log(f"快捷键前往：{msg}，成功")
            return True
        else:
            kkLogger_log(f"快捷键前往：{msg}，失败，尝试..前往")
            return kktouch2(x, y, sleep_time, msg)
####################################################################
step_names = ["菜单","外观","角色","打造","武功","设置","开放世界","宠物","群侠","庄园"]
#################################################################################
def check_nshm_exits():
    return True

    # if is_local:
    #     return True
    #
    # if wait_for_ocr_txt("手游模拟器",20,33,2,145,36):
    #     return True
    # else:
    #     if process_exists("nshm.exe"):
    #         sleep(10)
    #         return True
    #     try:
    #         connect_device("windows:///?title_re=.*Version.*")
    #         G.DEVICE.move((0, 0))
    #         return True
    #     except Exception as e:
    #         sync_current_snapshot("未识别到手游模拟器，开始重启逆水寒手游模拟器")
    #         step_restart_nshm(0)
    #         connect_device("Windows:///?title_re=.*Version.*")
    #         sync_current_snapshot("未识别到手游模拟器，重启逆水寒手游模拟器完成")
    #         # connect_device("Windows:///?title_re=MuMu模拟器12")
    #         while True:
    #             portal_client.cancel_task_and_restart(luhao_task["id"])
    #             kkLogger_log("未识别到手游模拟器，重启逆水寒手游模拟器完成,等待任务重启指令")
    #             sleep(120)


####################################################
def kktouch3(x, y, sleep_time,msg):
    kkLogger_log(f"..3:[{msg}]")
    touch((x, y))
    sleep(sleep_time)
#####################################################################################
def kktouch2(x, y, sleep_time,msg,attempts=4):
    # check_nshm_exits()
    current_step = luhao_task.get("stage","None")
    if current_step.startswith("step_") and current_step not in ["step_init","step_login"]:
        check_account_login_other()
        check_game_alert_note()

    if is_debug:
        save_cropped_area_screen(f"..：{msg}_{x}_{y}", x-30, y-30, x+30, y+30, 0.2,".png")

    global  touch_fast
    if attempts <= 0:
        return False

    if attempts <2:
        kkLogger_log(f"..[{msg}],attempts:{attempts}，转换为普通模式")
        touch_fast = False

    if msg not in step_names and sleep_time < 1.1:
        sleep_time = 0.6

    if msg == "菜单":
        check_and_try_game_home(4,"菜单")
        kk_keyevent("{ESC}",1,2,"菜单")
        return True

    kkLogger_log(f"..[{msg}],attempts:{attempts}")
    touch((x,y))
    sleep(sleep_time)
    if msg == "菜单":
        coords = local_ocr_text_target_coords("外",1010, 129,1210, 305)
        sync_current_snapshot(f"菜单{attempts}")
        if coords is None:
            check_and_try_game_home()
            return kktouch2(1224+10, 32+37, 1, "菜单",attempts-1)
        return True
    if msg == "外观":
        sync_current_snapshot(f"外观{attempts}")
        coords = local_ocr_text_target_coords("时",198, 690,358, 740)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")

            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "角色":
        sync_current_snapshot(f"角色-背包{attempts}")
        coords = local_ocr_text_target_coords("角色",11, 92,143, 347)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 , 32 , 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "打造":
        sync_current_snapshot(f"打造{attempts}")
        coords = local_ocr_text_target_coords("打",95, 42,187, 102)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts - 1)
        return True
    if msg == "武功":
        sync_current_snapshot(f"武功{attempts}")
        coords = local_ocr_text_target_coords("功",12, 92,140, 499)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "设置":
        sync_current_snapshot(f"设置{attempts}")
        coords = local_ocr_text_target_coords("下",8, 196,160, 676)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "开放世界":
        sync_current_snapshot(f"开放世界{attempts}")
        return True
    if msg == "宠物":
        sleep(4)
        kktouch3(1077,128,1,"关闭更新弹框.")
        kktouch3(1077,128,1,"关闭更新弹框..")
        sync_current_snapshot(f"宠物{attempts}")
        coords = local_ocr_text_target_coords("宠物",105, 45,194, 98)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "群侠":
        sync_current_snapshot(f"群侠{attempts}")
        coords = local_ocr_text_target_coords("群",8, 84,158, 344)
        if coords is None:
            return False
        return True
    if msg == "庄园":
        sync_current_snapshot(f"庄园{attempts}")
        kktouch3(766,677,1,"取消引导")
        kktouch3(766,677,1,"取消引导")
        coords = local_ocr_text_target_coords("庄园",98, 45,183, 97)
        if coords is None:
            return False
        return True

    return True
#########################################################
def swipe_to_end(judge_area,swipe_start,swipe_end,judge_blank=False):
    if swipe_start[1] < swipe_end[1]:
        kk_scroll(swipe_start, 20,2,"swipe_to_end")
    else:
        kk_scroll(swipe_start, -20,2,"swipe_to_end")
###############################################################

#########################################################
def swipe_to_end_slow(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and (judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    for i in range(40):
        try:
            start_screen = G.DEVICE.snapshot()
            cropped_start_screen = aircv.crop_image(start_screen, judge_area)
            swipe(swipe_start, swipe_end, duration=2)
            sleep(3)
            end_screen = G.DEVICE.snapshot()
            cropped_end_screen = aircv.crop_image(end_screen, judge_area)
            confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
            kkLogger_log(f"{confidence},times:{i}")
            if confidence > 0.8:
                break
        except Exception as e:
            kkLogger_log(f"swipe_to_end_slow error：{e}")
            continue

#########################################################################
def swipe_and_judge_end(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and ocr_text(judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen,judge_area)

    # swipe(swipe_start, swipe_end,steps=35, duration=7)
    swipe(swipe_start, swipe_end, steps=15, duration=3)
    sleep(2)

    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, judge_area)
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
    kkLogger_log(f"confidence：{confidence}")
    if confidence > 0.8:
        return True
    else:
        return False
##################################################################
def scroll_and_judge_end(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and ocr_text(judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen,judge_area)


    # swipe(swipe_start, swipe_end)
    # sleep(2)
    height = abs(swipe_end[1] - swipe_start[1])
    kkLogger_log(f"================height:{height}")
    scroll_count = height // (575 - 285)
    coords = ( (judge_area[2] + judge_area[0]) //2, (judge_area[3] + judge_area[1]) //2)
    kkLogger_log(f"================scroll_count:{scroll_count}，coords：{coords}")
    if swipe_end[1] > swipe_start[1]:
        kk_scroll(coords,scroll_count,2,"scroll_and_judge_end")
    else:
        kk_scroll(coords, -scroll_count, 2,"scroll_and_judge_end")

    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, judge_area)
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
    kkLogger_log(f"confidence：{confidence}")

    # sleep(3)
    if confidence > 0.8:
        return True
    else:
        return False

#####################################################
def swipe_and_judge_end_fast(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and ocr_text(judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen,judge_area)


    swipe(swipe_start, swipe_end)
    sleep(2)

    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, judge_area)
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
    kkLogger_log(f"confidence：{confidence}")
    if confidence > 0.8:
        return True
    else:
        return False
###########################################################
def init(log_queue=None, fast=False, save_original=False):
    if log_queue:
        global logger_queue
        logger_queue = log_queue
    global touch_fast
    touch_fast = fast

    global save_original_pic
    save_original_pic = save_original
########################################################################################
################################################
def init_local_nsh(l_task):
    global center_x
    global center_y
    global width
    global height
    global start_time
    global snapImgPath
    global projectPath
    global luhao_task
    global logger
    global product_meta
    global is_local
    global tianniran_count
    global ziran_pic_count
    global waiguan_set
    global waiguan_taozhuang_set
    global waiguan_fashi_set
    global waiguan_ziran_fashi_set
    global waiguan_wuqi_set
    global waiguan_zuoqi_set
    global waiguan_xiangrui_set
    global waiguan_qingong_set
    global waiguan_ts_jineng_set
    global neigong_lingyun_set



    waiguan_set = []
    waiguan_taozhuang_set = []
    waiguan_fashi_set = []
    waiguan_ziran_fashi_set = []
    waiguan_wuqi_set = []
    waiguan_zuoqi_set = []
    waiguan_xiangrui_set = []
    waiguan_qingong_set = []
    waiguan_ts_jineng_set = []
    neigong_lingyun_set = []

    tianniran_count = 0
    ziran_pic_count = 0

    is_local = True

    center_x = width // 2
    center_y = height // 2

    luhao_task = l_task
    productSn = luhao_task['productSn']


    nowTime = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    projectPath = "D:\\kkzhw\\airtest_log"
    snapImgPath = f"{projectPath}\\{productSn}_{nowTime}"

    clear_folder2(snapImgPath)

    logging.getLogger("airtest").setLevel(logging.INFO)
    logging.getLogger("ppocr").setLevel(logging.INFO)
    if logger is None:
        logger = logger_config.setup_logger(os.path.basename(__file__),
                                            log_file_name=f'{snapImgPath}\\{productSn}_runtime.log')


    for i in range(120):
        sleep(2)
        try:
            connect_device("windows:///?title_re=.*Version.*")
            width, height = device().get_current_resolution()
            kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST} {(width,height)}")
            if width < 1280:
                print(f"设备错误或非初始界面，请调整，第{i}/{120}次")
                continue
            else:
                G.DEVICE.move((0, 0))
                break
        except Exception as e:
            print(e)
            continue

    print(f"设备屏幕宽：{width} 高：{height}")
    print(f"窗口标题 {G.DEVICE.get_title()}")
    G.DEVICE.move((0, 0))

    center_x = width // 2
    center_y = height // 2

    kkLogger_log(f"屏幕 宽：{width}，高：{height}")
    kkLogger_log(f"图片路径：{snapImgPath}")

    if logger is None:
        logger = logger_config.setup_logger(os.path.basename(__file__),
                                            log_file_name=f'{snapImgPath}\\{productSn}_runtime.log')
        kkLogger_log(f"屏幕 宽：{width}，高：{height}")
        kkLogger_log(f"图片路径：{snapImgPath}")

    product_meta = l_task['product_meta']

    # check_game_gonggao()
###################################################
def init_env():
    global center_x
    global snapImgPath
    global logger
    global logger2
    global width
    global height

    global center_x
    global center_y
    global product_meta
    global luhao_task
    global luhao_failed
    global pic_url_arrays
    global tianniran_ocr_txt_arrays
    global ocr_file
    global touch_fast
    global start_time
    global end_time
    global is_local
    global save_original_pic
    global is_debug
    global login_other_count
    global tianniran_count
    global ziran_pic_count

    global waiguan_set
    global waiguan_taozhuang_set
    global waiguan_fashi_set
    global waiguan_ziran_fashi_set
    global waiguan_wuqi_set
    global waiguan_zuoqi_set
    global waiguan_xiangrui_set
    global waiguan_qingong_set
    global waiguan_ts_jineng_set
    global has_mumu
    global neigong_lingyun_set

    waiguan_set = []
    waiguan_taozhuang_set = []
    waiguan_fashi_set = []
    waiguan_ziran_fashi_set = []
    waiguan_wuqi_set = []
    waiguan_zuoqi_set = []
    waiguan_xiangrui_set = []
    waiguan_qingong_set = []
    waiguan_ts_jineng_set = []
    neigong_lingyun_set = []



    snapImgPath = ""
    logger = None
    logger2 = None
    # 设置全局的截图精度为90
    ST.SNAPSHOT_QUALITY = 99
    # 获取当前设备的屏幕宽度和高度
    width = 1280
    height = 756
    login_other_count = 0

    center_x = 1280 // 2
    center_y = 756 // 2

    product_meta = []
    luhao_task = None
    luhao_failed = False
    pic_url_arrays = []
    tianniran_ocr_txt_arrays = set()
    ocr_file = 0  # ocr 是否保存识别区域图片 1保存图片 0不保存
    start_time = datetime.datetime.now()
    end_time = datetime.datetime.now()
    is_debug = False
    tianniran_count = 0
    ziran_pic_count = 0


    # try:
    #     connect_device(f"Android://127.0.0.1:5037/127.0.0.1:16384?cap_method=JAVACAP")
    #     start_app("com.netease.micro.nshm")
    #     print("启动云逆水寒成功")
    #     has_mumu = True
    #     return True
    # except Exception as e:
    #     kkLogger_log("error 连接mumu模拟器失败，判定设备仅能扫码上号")
    #     has_mumu = False

    return True

#################################################################
def safe_step_gui_task(is_skip = 0):

    for i in range(3):
        try:
            task = portal_client.get_task_info(luhao_task["id"])
            kkLogger_log(f"获取到任务：{task}")
            task_id = task["id"]
            device_id = task["deviceId"]

            connect_device("windows:///?title_re=看看账号网逆水寒手游录号工具.*")

            kktouch2(116, 101, 1, "客服账号输入框")
            keyevent("^a")
            keyevent("{DELETE}")
            kktouch2(116, 101, 1, "客服账号输入框")
            text(f"DID:{device_id}")



            kktouch2(147, 171, 1, "编号输入框")
            keyevent("^a")
            keyevent("{DELETE}")
            kktouch2(147, 171, 1, "编号输入框")
            text(f"TID:{task_id}")
            kktouch2(291, 170, 2, "开始任务")
            connect_device("windows:///?title_re=.*Version.*")
            break
        except Exception as e:
            kkLogger_log(f"safe_step_gui_task, error: {e}")
            sleep(5)
            continue

    for i in range(240): #最多40分钟
        try:
            task = portal_client.get_task_info(luhao_task["id"])
            if task is None:
                sleep(5)
                continue
            task_status = task.get("status", "None")

            global end_time
            end_time = datetime.datetime.now()
            total_time = end_time - start_time
            minutes = total_time.total_seconds() // 60

            kkLogger_log(f"当前任务状态：{task_status}，已执行：{minutes}分钟")
            if task_status == "COMPLETED":
                break
            elif task_status == "FAILED":
                break
            elif task_status == "CANCELLED":
                break
            else:
                sleep(10)
                continue
        except Exception as e:
            kkLogger_log(f"safe_step_gui_task, error: {e}")
            sleep(5)
            continue
#################################################################
def init_nsh(l_task):
    init_success = init_env()
    if not init_success:
        return get_return_taskEvent("step_init", EventStatus.FAILURE, "初始化失败")

    global center_x
    global center_y
    global width
    global height
    global start_time
    global snapImgPath
    global projectPath
    global luhao_task
    global logger
    global product_meta

    luhao_task = l_task
    productSn = luhao_task['productSn']
    projectPath = "D:\\kkzhw\\airtest_log"
    nowTime = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    snapImgPath = f"{projectPath}\\{productSn}_{nowTime}"
    start_time = datetime.datetime.now()
    print(f"snapImgPath: {snapImgPath}")
    clear_folder2(snapImgPath)


    for j in range(5):
        try:
            print("init_nsh 连接逆水寒")
            connect_device("windows:///?title_re=.*Version.*")
            sleep(2)
            print("init_nsh 连接桌面")
            connect_device("windows:///")
            if local_ocr_text_target_coords("Version", 1, 1, 365, 35,False) is not None:
                connect_device("windows:///?title_re=.*Version.*")
                G.DEVICE.move((0, 0))
                width, height = device().get_current_resolution()

                if wait_for_ocr_txt("确定",2,659,537,968,594):
                    kktouch3(724, 562, 5, "确定")

                print("init_nsh 逆水寒PC界面准备就绪")
                break
            else:
                raise Exception("逆水寒客户端不存在")
        except Exception as e:
            print(f"init_nsh error 设备窗口异常。{e}")
            sync_login_fail("设备繁忙，请稍后再试.")
            # step_restart_nshm(0)
            return get_return_taskEvent("step_init", EventStatus.CANCELLED, "初始化完成，退出当前任务.")


    # try:
    #     connect_device("windows:///?title_re=逆水寒手游模拟器")
    #     print(f"设备屏幕宽：{width} 高：{height}")
    #     G.DEVICE.move((0,0))
    # except Exception as e:
    #     return get_return_taskEvent("step_init", EventStatus.MANUAL_REQUIRED, "初始化失败，需人工介入")



    center_x = width // 2
    center_y = height // 2

    nowTime = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    # projectPath = "D:\\kkzhw\\airtest_log"
    # snapImgPath = f"{projectPath}\\{productSn}_{nowTime}"
    #
    # clear_folder2(snapImgPath)

    logging.getLogger("airtest").setLevel(logging.INFO)
    logging.getLogger("ppocr").setLevel(logging.INFO)
    if logger is None:
        logger = logger_config.setup_logger(os.path.basename(__file__), log_file_name=f'{snapImgPath}\\{productSn}_runtime.log')
        kkLogger_log(f"屏幕 宽：{width}，高：{height}")
        kkLogger_log(f"图片路径：{snapImgPath}")


    product_meta = l_task['product_meta']

    kkLogger_log(f"touch_false:{touch_fast},ocr_file:{ocr_file}")

    return get_return_taskEvent("step_init", EventStatus.SUCCESS, "初始化成功")

########################################################################################
def kkLogger():
    global logger
    global logger2
    if logger is None:
        if logger2 is None:
            # logger2 = logger_config.setup_logger(os.path.basename(__file__), log_file_name=f'{projectPath}\\nsh_runtime.log')
            logger2 = logger_config.setup_logger(os.path.basename(__file__), log_file_name=f'{snapImgPath}\\nsh_runtime.log')

        return logger2
    else:
        return logger
########################################################################
def kkLogger_log(msg,level="info"):
    # print(msg)
    global logger_queue
    if ocr_file == 1 or ".." in msg or "error" in msg:
        try:
            if "info" == level:
                kkLogger().info(msg)
                if logger_queue:
                    logger_queue.put(msg)
            elif "error" == level:
                kkLogger().error(msg)
            elif "debug" == level:
                kkLogger().debug(msg)
            else:
                print(msg)
                if logger_queue:
                    logger_queue.put(msg)
        except Exception as e:
            print(f"kkLogger_log {msg} exception：:{e}")
######################################################################
def check_game_home(counts):
    check_and_try_game_home()
    for i in range(counts):
        sleep(2)
        if exists(Template(r"tpl1728385666525.png", record_pos=(0.471, -0.26), resolution=(1278, 720))):
            touch((1240,60))
            sleep(1)
            coords = local_ocr_text_target_coords("角色", 1198, 91, 1280, 236)
            if coords is not None:
                touch((1249,63))
                sleep(1)
                print(f"当前正在游戏主页")
                return True

            print(f"当前不在游戏登录成功角色主页，第{i}次")
            continue
        else:
            print(f"当前不在游戏登录成功角色主页，第{i}次")
            continue
    return False

#########################################################

def get_head_pic():
    pic = None
    for item in pic_url_arrays:
        if item['name'] == "头图":
            pic = item['value']
            break
    if pic is None:
        pic = pic_url_arrays[0]['value']
    return pic
#######################################################################


##################################################################
def ocr_add_to_get(start_x,start_y,end_x,end_y):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    add_to_get(get_ocr_txt)
                    kkLogger_log(f"ocr_add_to_get ocr_get：{get_ocr_txt}")
    else:
        kkLogger_log("ocr nothing")
###########################################################
def ocr_screen_add_to_get(mode_name,cropped_screen,is_record=False):
    try:
        has_text = False
        if cropped_screen is not None:
            ocr_result = ocr.ocr(cropped_screen, cls=False)
            if ocr_file == 1:
                current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
                cropped_screen = Image.fromarray(cropped_screen)
                cropped_screen.save(f"{snapImgPath}\\inverted_{current_millis}.png")

            if ocr_result:
                for line in ocr_result:
                    if line is None:
                        continue
                    for word_info in line:
                        if word_info is None:
                            continue
                        else:
                            get_ocr_txt = word_info[1][0]
                            # get_ocr_txt = remove_unwanted_chars(get_ocr_txt)
                            # print(f"-------------------------------{get_ocr_txt}")
                            has_text = True
                            if any(keyword in get_ocr_txt for keyword in configs.lock_keywords):
                                kkLogger_log(f"ocr_screen_add_to_get 识别到非解锁字符 ：{get_ocr_txt}")
                                return (False,has_text)

                            ocr_check_and_add(get_ocr_txt)

                            value = portal_client.get_error_text('逆水寒手游').get(get_ocr_txt, get_ocr_txt)
                            if "坐骑_祥瑞" == mode_name:
                                if value not in waiguan_zuoqi_set:
                                    waiguan_zuoqi_set.append(value)
                                else:
                                    return (False, has_text)
                            elif "祥瑞_祥瑞" == mode_name:
                                if value not in waiguan_xiangrui_set:
                                    waiguan_xiangrui_set.append(value)
                                else:
                                    return (False, has_text)
                            elif "武器_衣品外观" == mode_name:
                                if value not in waiguan_wuqi_set:
                                    waiguan_wuqi_set.append(value)
                                else:
                                    return (False, has_text)
                            elif "发式_衣品外观" == mode_name:
                                if value not in waiguan_fashi_set:
                                    waiguan_fashi_set.append(value)
                                else:
                                    return (False, has_text)
                            elif "身体_衣品外观" == mode_name:
                                if value not in waiguan_taozhuang_set:
                                    waiguan_taozhuang_set.append(value)
                                else:
                                    return (False, has_text)

                            elif "背部_衣品外观" == mode_name:
                                if value not in waiguan_beibu_set:
                                    waiguan_beibu_set.append(value)
                                else:
                                    return (False, has_text)
                            elif "脚印_衣品外观" == mode_name:
                                if value not in waiguan_jiaoyin_set:
                                    waiguan_jiaoyin_set.append(value)
                                else:
                                    return (False, has_text)
                            elif "环身_衣品外观" == mode_name:
                                if value not in waiguan_huanshen_set:
                                    waiguan_huanshen_set.append(value)
                                else:
                                    return (False, has_text)

                            # kkLogger_log(f"ocr_screen_add_to_get ：{get_ocr_txt}")
                return (True,has_text)
            else:
                kkLogger_log("ocr_screen_add_to_get ocr nothing")
                return (False,has_text)
        else:
            kkLogger_log("ocr_screen_add_to_get cropped_screen is none")
            return (False,has_text)
    except Exception as e:
        kkLogger_log(f"ocr_screen_add_to_get error：{e}")
        return (False,has_text)

##################################################################
def ocr_add_to_get_attr(start_x,start_y,end_x,end_y,attr_names):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    for a_name in attr_names:
                        ocr_check_and_add_attr(get_ocr_txt,a_name)
                    kkLogger_log(f"ocr_add_to_get_attr ocr_get：{get_ocr_txt}")
    else:
        kkLogger_log("ocr nothing")

################################################################################
def ocr_text_inverted_denoised(start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()
            if screen is None:
                sleep(0.5)
                screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            cropped_gray_screen_rgb = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2GRAY)
            cropped_screen_rgb = cv2.cvtColor(cropped_gray_screen_rgb, cv2.COLOR_BGR2RGB)
            inverted_cropped_screen = 255 - cropped_screen_rgb

            denoised_image = cv2.medianBlur(inverted_cropped_screen, 3)
            # denoised_image = cv2.GaussianBlur(inverted_cropped_screen, (5, 5), 0)

            ocr_result = ocr.ocr(denoised_image, cls=False)
            if ocr_file == 1:
                pil_img = cv2_2_pil(denoised_image)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_now_ocr_{current_millis}.png")

            return ocr_result
        except Exception as e:
            kkLogger_log(f"ocr_text error：{traceback.format_exc()}")
            sleep(2)
            continue
    return None

########################################################
def ocr_add_to_get_and_count_tianniran_number(start_x,start_y,end_x,end_y):
    global tianniran_count
    # ocr_result = ocr_text_inverted_denoised(start_x,start_y,end_x,end_y)
    ocr_result = ocr_text_inverted(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    get_ocr_txt = get_ocr_txt.strip()

                    # print(f"-------------------------------------------------->{get_ocr_txt}")

                    if contains_digit(get_ocr_txt):
                        try:
                            match = re.search(r'\d+', get_ocr_txt)
                            value_number = match.group(0) if match else ""
                            kkLogger_log(f"get_ocr_number：{value_number}")
                            tianniran_count = int(value_number)//2
                            # if tianniran_count > 0:
                            #     ocr_check_and_set("天霓染", str(tianniran_count))
                            kkLogger_log(f"ocr_add_to_get_and_count_tianniran_number 天霓染数量 ：{tianniran_count}")
                            break
                        except Exception as e:
                            kkLogger_log(f"ocr_add_to_get_and_count_tianniran_number ：{e}")
                    else:
                        kkLogger_log(f"ocr_add_to_get_and_count_tianniran_number [{get_ocr_txt}]，未识别到自染数量")

    else:
        kkLogger_log("ocr_add_to_get_and_count_tianniran ocr nothing")
    return tianniran_count
##################################################################################
def ocr_add_to_get_and_count_tianniran(start_x,start_y,end_x,end_y,skip_word):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]

                    get_ocr_txt = get_ocr_txt.strip()
                    if(len(get_ocr_txt) == 1):
                        continue
                    if skip_word not in get_ocr_txt and "点" not in get_ocr_txt and "案" not in get_ocr_txt:
                        tianniran_ocr_txt_arrays.add(get_ocr_txt)
                    kkLogger_log(f"ocr_add_to_get_and_count_tianniran ocr_get：{get_ocr_txt}")
    else:
        kkLogger_log("ocr_add_to_get_and_count_tianniran ocr nothing")
    kkLogger_log(f"tianniran_ocr_txt_arrays ：{tianniran_ocr_txt_arrays}")
#################################################################################



#################################################################################
def ocr_check_and_add(ocr_txt):
    global product_meta
    ocr_txt = ocr_txt.strip()
    ocr_txt = remove_unwanted_chars(ocr_txt)

    if len(ocr_txt) == 1 or len(ocr_txt) == 0:
        return
    # ocr_txt2 = configs.ERROR_TEXT.get(ocr_txt, ocr_txt)
    ocr_txt2 = portal_client.get_error_text('逆水寒手游').get(ocr_txt, ocr_txt)

    # ocr_txt2 = configs.black_attr_value.get(ocr_txt2,ocr_txt2)
    ocr_txt2 = portal_client.get_black_attr_value("逆水寒手游").get(ocr_txt2,ocr_txt2)
    hit_item_name_value = None
    for item in product_meta:
        if item["type"] not in [1,2]:
            continue
        if item["inputList"] and item["name"] not in configs.SKIP_ATTRI_NAME:
            input_arrays = item["inputList"].split(',')
            values = item.get("values", [])
            for input_value in input_arrays:
                input_value_2 = remove_unwanted_chars(input_value)
                if ocr_txt2 == input_value_2:
                    hit_item_name_value = item["name"]+":"+ocr_txt2
                    if item["name"] == "灵韵内功":
                        neigong_lingyun_set.append(ocr_txt2)
                    if input_value not in values:
                        values.append(input_value)

            item["values"] = values
    return hit_item_name_value
######################################################################
def ocr_check_and_add2(ocr_txt):
    global product_meta
    ocr_txt = ocr_txt.strip()
    ocr_txt = remove_unwanted_chars(ocr_txt)

    if len(ocr_txt) == 1 or len(ocr_txt) == 0:
        return
    # ocr_txt2 = configs.ERROR_TEXT.get(ocr_txt, ocr_txt)
    ocr_txt2 = portal_client.get_error_text('逆水寒手游').get(ocr_txt, ocr_txt)

    # ocr_txt2 = configs.black_attr_value.get(ocr_txt2,ocr_txt2)
    ocr_txt2 = portal_client.get_black_attr_value("逆水寒手游").get(ocr_txt2,ocr_txt2)
    hit_item_name_value = None
    for item in product_meta:
        if item["type"] not in [1,2]:
            continue
        if item["inputList"] and item["name"] not in configs.SKIP_ATTRI_NAME:
            input_arrays = item["inputList"].split(',')
            values = item.get("values", [])
            for input_value in input_arrays:
                input_value_2 = remove_unwanted_chars(input_value)
                if ocr_txt2 == input_value_2 or is_close_match(ocr_txt2,input_value):
                    hit_item_name_value = item["name"]+":"+ocr_txt2
                    if input_value not in values:
                        values.append(input_value)

            item["values"] = values
    return hit_item_name_value
#######################################################################

################################################################################
def ocr_check_and_add_attr(ocr_txt,attr_name):
    global product_meta
    kkLogger_log(f"ocr_check_and_add_attr ocr_txt:{ocr_txt}")
    ocr_txt = ocr_txt.strip()
    ocr_txt = remove_unwanted_chars(ocr_txt)

    if len(ocr_txt) == 1 or len(ocr_txt) == 0:
        return
    # ocr_txt2 = configs.ERROR_TEXT.get(ocr_txt, ocr_txt)
    ocr_txt2 = portal_client.get_error_text('逆水寒手游').get(ocr_txt, ocr_txt)

    # ocr_txt2 = configs.black_attr_value.get(ocr_txt2,ocr_txt2)
    ocr_txt2 = portal_client.get_black_attr_value("逆水寒手游").get(ocr_txt2,ocr_txt2)

    kkLogger_log(f"ocr_check_and_add ocr_txt2:{ocr_txt}")
    for item in product_meta:
        if item["type"] not in [1,2]:
            continue
        if item["inputList"] and item["name"] == attr_name:
            input_arrays = item["inputList"].split(',')
            values = item.get("values", [])
            for input_value in input_arrays:
                input_value_2 = remove_unwanted_chars(input_value)
                if ocr_txt2 == input_value_2:
                    if input_value not in values:
                        values.append(input_value)
                        kkLogger_log(f"{input_value} append in {values}")
                    else:
                        kkLogger_log(f"{input_value} already in {values}")

            item["values"] = values  # 更新字典中的values键
    return ocr_txt2
#############################################################
def category_meta_item(item_name):
    for item in product_meta:
        if item["name"] == item_name:
            return item
        else:
            return None
##############################################################################
def get_ocr_number(start_x,start_y,end_x,end_y):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    if contains_digit(get_ocr_txt):
                        match = re.search(r'\d+', get_ocr_txt)
                        value_number = match.group(0) if match else ""
                        kkLogger_log(f"get_ocr_number：{value_number}")
                        return value_number
    else:
        kkLogger_log(f"未获取到数字")

    return ""

#########################################################
def get_attr_ocr_number(attr_name,start_x,start_y,end_x,end_y):
    has_yipin = False
    try:
        ocr_result = ocr_text(start_x,start_y,end_x,end_y)
        if ocr_result:
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    if word_info is None:
                        continue
                    else:
                        get_ocr_txt = word_info[1][0]
                        if contains_digit(get_ocr_txt):
                            ocr_check_and_set_number(attr_name,get_ocr_txt)
                            kkLogger_log(f"get_attr_ocr_number {attr_name}：{get_ocr_txt}")
                            has_yipin = True
        else:
            kkLogger_log(f"未获取到{attr_name}")
    except Exception as e:
        print(f"sync_login_success：{e}")
    return has_yipin
#####################################################################
def get_wenyu_attr_ocr_number(attr_name,start_x,start_y,end_x,end_y):
    has_wenyun = 0
    try:
        ocr_result = ocr_text(start_x,start_y,end_x,end_y)
        if ocr_result:
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    if word_info is None:
                        continue
                    else:
                        get_ocr_txt = word_info[1][0]
                        print(f"识别到纹玉：{get_ocr_txt}")
                        # if contains_digit(get_ocr_txt) and "/" in get_ocr_txt:
                        if contains_digit(get_ocr_txt):
                            parts = get_ocr_txt.split('/')
                            print(f"设置限定纹玉额度：{parts[0]}")
                            if parts[0].isdigit():
                                # ocr_check_and_set_number(attr_name,parts[0])
                                kkLogger_log(f"get_wenyu_attr_ocr_number {attr_name}：{get_ocr_txt}")

                                sync_current_snapshot(f"识别到限定纹玉：{parts[0]}", None, True)
                                has_wenyun = int(parts[0])
        else:
            kkLogger_log(f"未获取到{attr_name}")
    except Exception as e:
        print(f"sync_login_success：{e}")
    return has_wenyun

########################################################################
def ocr_check_and_set(attrName,ocr_txt):
    global product_meta
    for item in product_meta:
        if item["name"] == attrName:
            values = item.get("values", [])
            values.append(ocr_txt)
            item["values"] = values
            kkLogger_log(f"ocr_check_and_set: {item}")
            return ocr_txt
######################################################
def ocr_check_and_set_number(attrName,ocr_txt):
    global product_meta
    match = re.search(r'\d+', ocr_txt)
    value_number = match.group(0) if match else ""

    for item in product_meta:
        if item["name"] == attrName:
            values = item.get("values", [])
            values.append(value_number)
            item["values"] = values
            if attrName == "充值金额":
                ocr_check_and_set("充值称号",get_chongzhi_chenghao(value_number))
                print(f"充值称号================：{get_chongzhi_chenghao(value_number)}")
            kkLogger_log(f"ocr_check_and_set_number {item}")
            return ocr_txt
################################################################
def get_chongzhi_chenghao(num):
    sorted_chongzhi_chenghao = sorted(configs.chongzhi_chenghao, key=lambda x: x[1], reverse=True)
    for item in sorted_chongzhi_chenghao:
        if int(num) > item[1]:
            return item[0]
    return ""
################################################################
def get_luhao_snapshot():
    try:
        step_login_pic = save_cropped_screen("luhao_snapshot",".png")
        oss_file = upload_one_img_to_oss(step_login_pic)
        the_state_img = configs.image_server_url+oss_file
        return the_state_img
    except Exception as e:
        print(f"get_luhao_snapshot error {e}")

#######################################################
# def check_game_window(check_login=False):
#     connect_device("windows:///?title_re=逆水寒手游模拟器")
#
#     width2, height2 = device().get_current_resolution()
#     if width2 > 1280:
#         #判断是否存在扫码弹窗
#         coords = local_ocr_text_target_coords("扫码登录", 472, 212, 804, 552,False)
#         if coords is not None:
#             kktouch2(802 , 163 , 1, "关闭登录弹窗")
#             return True
#         # 判断是否存在扫码超时弹窗
#         coords = local_ocr_text_target_coords("登录失败", 472, 212, 804, 552,False)
#         if coords is not None:
#             kktouch2(802 , 163 , 1, "关闭登录弹窗")
#             return True
#         # 判断是否上一个游戏登录状态
#         if check_login and check_and_try_game_home(1,"init"):
#             kk_keyevent("{ESC}", 1, 1, "回主页")
#             if local_ocr_text_target_coords("角色", 1191, 30, 1275, 300,False) is not None:
#                 step_logout(0)
#                 connect_device("windows:///?title_re=逆水寒手游模拟器")
#                 kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
#             return True
#     else:
#         sleep(2)
#         connect_device("windows:///")
#         kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
#         coords = local_ocr_text_target_coords("扫码登录",472,212,804,552,False)
#         if coords is not None:
#             kktouch2(802,163,1,"关闭登录弹窗")
#             return True
#         else:
#             step_restart_nshm(0)
#         return True
###########################################################################################################
def check_mumu_game_ready():
    for i in range(5):
        # sleep(1)
        target = ocr_text_target_coords("详细", 399, 456, 910, 634,False)
        if target is not None:
            kktouch3(467, 579, 1, "同意")
            return True
        #登录状态则退出
        target = ocr_text_target_coords("退出", 872, 20, 1270, 214,False)
        if target is not None:
            kktouch3(target[0], target[1]-10, 1.2, "右上退出")
            kktouch3(746,461-33,1.1,"弹窗退出")

        target = ocr_text_target_coords("其他账号登录", 330, 417, 920, 626,False)
        if target is not None:
            kktouch3(target[0],target[1],1.2,"其他账号登录")

        for j in range(2):
            target = ocr_text_target_coords("详细", 399, 456, 910, 634,False)
            if target is not None:
                kktouch3(467,579,1,"同意")
                return True
            else:
                kktouch3(940,225-36,1.2,"关闭登录弹窗")
                kktouch3(940, 225 - 36, 1.2, "关闭登录弹窗")
                continue

        stop_app("com.netease.micro.nshm")
        sleep(10)
        start_app("com.netease.micro.nshm")
        sleep(10)

    return False

######################################################################################################3
def show_login_qrcode():
    for i in range(600):
        connect_device("windows:///?title_re=.*Version.*")
        if device().get_current_resolution()[0] < 1280:
            kkLogger_log(f"游戏非初始界面:{device().get_current_resolution()}")
            sleep(2)
            continue
        coords = local_ocr_text_target_coords("扫码",688, 306,916, 356,False)
        if coords is not None :
            return True
        else:
            # kktouch3(731, 502, 5, "确定")
            kktouch3(741, 528, 1, "账号")
            kktouch3(725, 642, 1, "二维码")

            kkLogger_log(f"未识别到二维码")
            sleep(1)
            continue
    return False
######################################################
def sync_login_success(msg="登录成功"):
    for i in range(3):
        try:
            end_time = datetime.datetime.now()
            total_time = end_time - start_time
            msg = f"{msg},用时：{total_time}"

            global luhao_task
            luhao_task["snapshot"] = ""
            luhao_task["stage"] = "step_login"
            luhao_task["msg"] = msg
            propertyBag = {
                "state": "login_success",
                "state_img": ""
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            break
        except Exception as e:
            print(f"sync_login_success：{e}")
            sleep(1)
            continue
################################################################
def sync_task_uid(uid):
    try:
        global luhao_failed
        global luhao_task
        luhao_task["msg"] = "同步UID"
        luhao_task["status"] = 'IN_PROGRESS'
        luhao_task["uid"] = uid
        portal_client.sync_task_info(luhao_task)
    except Exception as e:
        print(f"sync_task_uid：{e}")
####################################
def sync_login_fail(msg="登录失败"):
    for i in range(2):
        try:
            global luhao_task
            luhao_task["snapshot"] = get_luhao_snapshot()
            luhao_task["stage"] = "step_login"
            # luhao_task["status"] = "CANCELLED"
            luhao_task["status"] = "IN_PROGRESS"
            luhao_task["msg"] = msg
            propertyBag = {
                "state": "login_fail",
                "state_img": get_luhao_snapshot()
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            break
        except Exception as e:
            print(f"sync_login_fail：{e}")
            sleep(1)
            continue
def sync_task_cancel(msg="任务取消"):
    for i in range(2):
        try:
            global luhao_task
            # luhao_task["snapshot"] = get_luhao_snapshot()
            luhao_task["stage"] = "step_login"
            luhao_task["status"] = "CANCELLED"
            luhao_task["msg"] = msg
            propertyBag = {
                "state": "login_fail",
                "state_img": get_luhao_snapshot()
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            break
        except Exception as e:
            print(f"sync_login_fail：{e}")
            sleep(1)
            continue
#####################################################
def sync_current_snapshot(msg="录号同步",now_status=None,is_snapshot=True):
    try:
        kkLogger_log(msg)

        global luhao_failed
        global luhao_task
        luhao_task["msg"] = msg
        if is_snapshot:
            luhao_task["snapshot"] = get_luhao_snapshot()
        if now_status is not None:
            luhao_task["status"] = now_status
        else:
            luhao_task["status"] = 'IN_PROGRESS'
        portal_client.sync_task_info(luhao_task)

        if "FAILED" == now_status:
            luhao_failed = True

    except Exception as e:
        print(f"sync_current_snapshot：{e}")
###########################################################################
def up_qrcode_pic():

    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
    image_util.capture_screen(f"{snapImgPath}\\luhao_step_{current_millis}.png",region=(686, 312, 934, 580))
    oss_file = upload_one_img_to_oss(f"{snapImgPath}\\luhao_step_{current_millis}.png")
    the_state_img = configs.image_server_url + oss_file
    # if local_ocr_text_target_coords("成功", 476, 180, 818, 614) is not None:
    #     propertyBag = {
    #         "state": "need_show_qrcode_done",
    #         "state_img": the_state_img
    #     }
    #     luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
    #     luhao_task["qrcode"] = the_state_img
    #     luhao_task["status"] = "IN_PROGRESS"
    #     portal_client.sync_task_info(luhao_task)
    # else:
    #     propertyBag = {
    #         "state": "need_show_qrcode",
    #         "state_img": the_state_img
    #     }
    #
    #     luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
    #     luhao_task["qrcode"] = the_state_img
    #     luhao_task["status"] = "PENDING"
    #     portal_client.sync_task_info(luhao_task)
    propertyBag = {
        "state": "need_show_qrcode",
        "state_img": the_state_img
    }

    luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
    luhao_task["qrcode"] = the_state_img
    luhao_task["status"] = "PENDING"
    portal_client.sync_task_info(luhao_task)

#############################################################
def step_mumuclick_login():
    # set_current(0)
    connect_device("Android://127.0.0.1:5037/127.0.0.1:16384?cap_method=JAVACAP")
    width, height = device().get_current_resolution()
    kkLogger_log(f"step_mumuclick_login 设备屏幕宽：{width} 高：{height}")
    for i in range(5):
        target = ocr_text_target_coords("其他", 178, 75, 931, 709)
        if target:
            kktouch3(target[0], target[1] - 84, 1, "授权登录")
            kkLogger_log(f"step_mumuclick_login ..登录")
            return True
        else:
            kkLogger_log(f"未识别到 其他账号，继续等待")
            sleep(2)
            continue
    #不论是否识别到登录，都..这个位置
    kktouch3(651,460-37,1.5,"授权登录。")
    kktouch3(651, 460 - 37, 1.5, "授权登录。")
    return False

def start_login_mumu(l_task):
    if True:
        kkLogger_log("start step_login_mumu")
        luhao_task = l_task
        connect_device("Windows:///?title_re=MuMu模拟器12")
        # set_current(0)
        connect_device("Android://127.0.0.1:5037/127.0.0.1:16384?cap_method=JAVACAP")
        kktouch3(43,117,0.2,"适龄提示")
        kktouch3(1047,176-37,0.2,"关闭公告活动")

        account = luhao_task['gameAccount']
        account_pass = luhao_task['gamePassword']
        if account is None:
            return False

        account = account.replace(" ", "").replace("\t", "").replace("\n", "").replace("\r", "")

        if check_mumu_game_ready() is False:
            return False

        kktouch3(464,578-33,1,"同意条款")
        if '@' in account:
            kktouch3(653, 414, 1, "网易邮箱")
            if ocr_text_target_coords("输入",295,154,1116,674,False) is None:
                kktouch3(464,578-33,1,"同意条款")
                kktouch3(653, 414, 1, "网易邮箱")

            kktouch3(581, 259, 1, "账号输入框")
            kktouch3(614, 680, 1, "账号输入")
            text(account, enter=False)
            # kktouch2(1189, 680, 1, "下一步")

            kktouch3(546, 346, 1, "密码输入框")
            kktouch3(600, 672-33, 1, "密码输入")
            text(account_pass, enter=False)
            # kktouch2(1193, 675, 1, "下一步")
            kktouch3(642, 430, 3, "登录")
            # sleep(5)
            ################################################################
            kktouch3(540,483,1,"取消")
            target = ocr_text_target_coords("取消", 540-200, 483-200, 540+200, 483+200, False)
            if target is not None:
                kktouch3(target[0],target[1],1,"取消2")

            if ocr_text_target_coords("扫码", 872, 20, 1259, 106, False) is None:
                coords = ocr_text_target_coords("网络", 10, 10, 900, 200, False)
                if coords is not None:
                    kkLogger_log(f"需要二步验证")
                    kktouch3(40, 114 - 37, 1.5, "退出安全验证")
                    kktouch3(40, 114 - 37, 1.5, "退出安全验证")
                    kktouch3(369, 218 - 37, 2, "返回登录框")
                    sync_current_snapshot("需要二步验证", None, False)
                    return False
                coords = ocr_text_target_coords("二步", 10, 10, 900, 200,False)
                if coords is not None:
                    kkLogger_log(f"需要二步验证")
                    kktouch3(40, 114 - 37, 1.5, "退出安全验证")
                    kktouch3(40, 114 - 37, 1.5, "退出安全验证")
                    kktouch3(369, 218 - 37, 2, "返回登录框")
                    sync_current_snapshot("需要二步验证",None,False)
                    return False
                coords = ocr_text_target_coords("发送", 338, 531, 1034, 718,False)
                if coords is not None:
                    kkLogger_log(f"需要安全验证")
                    kktouch3(40, 114-37, 1.5, "退出安全验证")
                    kktouch3(40, 114-37, 1.5, "退出安全验证")
                    kktouch3(369, 218-37, 2, "返回登录框")
                    sync_current_snapshot("需要安全验证",None,False)
                    return False
                coords = ocr_text_target_coords("密码", 201, 98, 980, 604,False)
                if coords is not None:
                    kkLogger_log(f"密码错误，停留在登录界面")
                    kktouch3(341, 153, 2, "返回主登录界面")
                    # kktouch2(369, 214 - 33, 2, "退出登录框")
                    sync_current_snapshot("密码错误",None,False)
                    return False

            if is_mumulogin_success():
                return True
            else:
                sync_current_snapshot("is_mumulogin_success:False",None,False)
                return False

        else:
            kktouch3(642,381-36,2,"手机账号")
            if ocr_text_target_coords("输入",295,154,1116,674,False) is None:
                kktouch3(464,578-36,2,"同意条款")
                kktouch3(642,381-36,2,"手机账号")

            kktouch3(614,352-36,2,"请输入手机号码")
            kktouch3(577, 714-36, 2, "输入框")
            text(account)
            kktouch3(1145, 645 - 36, 2, "下一步")


            #判断短信是否用完
            if ocr_text_target_coords("发送",400,415,883,617,False) is not None:
                kktouch3(885,226-37,1.5,"关闭短信发送次数限制弹窗")
                kktouch3(941,223-37,3,"关闭登录弹窗")
                kktouch3(941, 226 - 37, 2, "关闭登录弹窗")
                kktouch3(640, 559 - 36, 2, "其他账号登录")

                kkLogger_log("短信次数达到上限，取消本次录号，开始恢复初始页面")
                sync_current_snapshot("短信验证码次数达到上限",None,False)
                return False

            sync_need_smscode()
            sleep(5)

            kktouch3(460, 395-36, 1, "验证码")
            kktouch3(577, 714-36, 1, "短信输入框")

            smscode = portal_client.get_sms_code(task_id=luhao_task['id'], timeout=90)

            if smscode is None:
                kkLogger_log("未获取到短信验证码，开始恢复初始页面")
                kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch3(640, 559 - 36, 1, "其他账号登录")
                return False

            if len(smscode) > 6:
                smscode = smscode[:6]

            if is_six_digit_code(smscode):
                text(smscode)
                sleep(3)
                kktouch3(980,291,1,"空白")
                # 判断验证码是否正确，不正确或者超时则要求重新输入
                if wait_for_ocr_txt("手机", 2, 380, 188, 927, 348):
                    sync_current_snapshot("验证码错误", None, False)
                    kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                    kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                    kktouch3(640, 559 - 36, 1, "其他账号登录")
                    return False
                if is_mumulogin_success():
                    kkLogger_log("短信登录成功")
                    return True
                else:
                    sync_current_snapshot("验证码验证异常", None, False)
                    kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                    kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                    kktouch3(640, 559 - 36, 1, "其他账号登录")
                    return False
            else:
                sync_current_snapshot("短信验证码异常，开始恢复初始页面", None, False)
                kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch3(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch3(640, 559 - 36, 1, "其他账号登录")
                return False

############################################################
def is_chinese_mobile_number(phone_number):
    pattern = re.compile(r'^1[3-9]\d{9}$')
    return pattern.match(phone_number) is not None
####################################
def is_six_digit_code(code):
    if len(code) == 6 and code.isdigit():
        return True
    return False
##########################################
def sync_need_smscode(is_snap=False):
    step_login_pic = save_cropped_screen("luhao_step")
    oss_file = upload_one_img_to_oss(step_login_pic)
    the_state_img = configs.image_server_url + oss_file
    propertyBag = {
        "state": "need_sms_code",
        "state_img": the_state_img
    }
    luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
    portal_client.sync_task_info(luhao_task)
############################################################
def step_mumu_login():
    is_mumu_login_success = start_login_mumu(luhao_task)

    if is_mumu_login_success is not True:
        # account = luhao_task.get('gameAccount', "None")
        kkLogger_log("云逆水寒登录失败，开始启动用户扫码登录")
        sync_current_snapshot("登录失败，开始启动用户扫码登录", None, True)
        return step_qrcode_login()
    else:
        sync_login_success("云逆水寒登录成功")
        kkLogger_log("云逆水寒登录成功并开启扫码")

    connect_device("Windows:///?title_re=.*Version.*")
    (width2, height2) = device().get_current_resolution()
    kkLogger_log(f"尝试连接逆水寒手游模拟器:{(width2, height2)}")

    show_login_qrcode()

    is_scan = wait_qrcode_scan_desk(24)
    if not is_scan:
        kktouch3(802, 202, 1.1, "关闭扫码弹窗")
        sync_login_fail("等待扫码登录失败")
        return get_return_taskEvent("step_login", EventStatus.CANCELLED, "登录失败")

    step_mumuclick_login()

    is_ok = wait_qrcode_ok(20)
    if is_ok:
        for i in range(120):
            check_game_login_tiaokuan(2)
            check_game_gonggao()
            coords = local_ocr_text_target_coords("接受", 389, 555, 1000, 718,False)
            # coords2 = local_ocr_text_target_coords("江湖", 389, 555, 952, 627,False)
            coords2 = local_ocr_text_target_coords("闯荡", 389, 555, 952, 627,False)
            if coords is not None:
                #登录条款
                kktouch3(coords[0], coords[1], 1, "同意.")
                break
            elif coords2 is not None:
                break
            else:
                print(f"没有发现同意按钮，继续等待 {i}")
                sleep(1)
                connect_device("windows:///?title_re=.*Version.*")
                continue
    else:
        sync_login_fail("扫码超时，录号终止")
        kktouch3(802,202,1.1,"关闭扫码弹窗")
        return get_return_taskEvent("step_login", EventStatus.CANCELLED, "登录扫码超时")

    if is_login_success():
        return get_return_taskEvent("step_login", EventStatus.SUCCESS, "逆水寒模拟器登录成功")
    else:
        kktouch3(802, 202, 1.1, "关闭扫码弹窗")
        sync_login_fail("逆水寒模拟器登录失败，终止录号")
        return get_return_taskEvent("step_login", EventStatus.CANCELLED, "逆水寒模拟器登录失败")
#############################################################
def step_qrcode_login():
    connect_device("windows:///?title_re=.*Version.*")
    if show_login_qrcode() is False:
        return get_return_taskEvent("step_login",EventStatus.MANUAL_REQUIRED,"登录界面错误")
    else:
        up_qrcode_pic()
        sleep(1)
        sync_current_snapshot("需要扫码")
        print("需要扫码")

        is_scan = wait_qrcode_scan_desk(24)
        if not is_scan:
            return get_return_taskEvent("step_login", EventStatus.CANCELLED, "登录失败")
        else:
            sync_login_success("登录成功")
            # up_qrcode_pic_ok()
            if luhao_task.get("memberId", None):
                sn = luhao_task.get("productSn")
                portal_client.send_msg(luhao_task.get("memberId", None),
                                       f'亲爱的小主，您的商品编号【{sn}】，开始录号。【注意】30分钟内请勿上号，被顶号将导致录号失败！')

            return get_return_taskEvent("step_login", EventStatus.SUCCESS, "登录成功")

################################################################################################
def up_qrcode_pic_ok():
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
    image_util.capture_screen(f"{snapImgPath}\\luhao_step_{current_millis}.png",region=(476, 239, 818, 614))
    oss_file = upload_one_img_to_oss(f"{snapImgPath}\\luhao_step_{current_millis}.png")
    the_state_img = configs.image_server_url + oss_file
    propertyBag = {
        "state": "need_show_qrcode_done",
        "state_img": the_state_img
    }
    luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
    luhao_task["qrcode"] = the_state_img
    luhao_task["status"] = "IN_PROGRESS"
    portal_client.sync_task_info(luhao_task)

####################################################
def step_login(is_skip):
    if is_skip == 0:
        login_type = luhao_task.get("loginType", "None")
        account = luhao_task.get('gameAccount',"None")
        source = luhao_task.get('source',"WEB")

        # if "None" == account or len(account) < 4:
        #     sync_login_fail(f"账号异常:{account}")
        #     return get_return_taskEvent("step_login", EventStatus.CANCELLED, "账号异常")
        # elif is_chinese_mobile_number(account) is False or has_mumu is False or "WEB" == source or is_between_1_and_9():
        #     return step_qrcode_login()
        # else:
        #     return step_mumu_login()

        return step_qrcode_login()

#######################################################
def check_game_doubleCheck():
    if local_ocr_text_target_coords("手机号发送", 50, 60, 1000, 600, False) is not None:
        sync_login_fail("需要二次确认，取消录号，请联系客服人工录号")

        if luhao_task.get("memberId", None):
            sn = luhao_task.get("productSn")
            portal_client.send_msg(luhao_task.get("memberId", None),
                                   f'亲爱的小主，您的商品编号【{sn}】，因需要手机二次验证，录号终止，请联系客服人工录号')

        step_restart_nshm(0)
        connect_device("Windows:///?title_re=.*Version.*")
        sync_current_snapshot("需要二次确认，取消录号，重启客户端完成")
        sync_task_cancel("需要二次验证，取消录号")
        # connect_device("Windows:///?title_re=MuMu模拟器12")


        while True:
            portal_client.cancel_task_and_restart(luhao_task["id"])
            kkLogger_log("需要二次确认，重启后，等待任务取消指令")
            sleep(120)
    elif local_ocr_text_target_coords("验证", 50, 60, 1000, 600, False) is not None:
        sync_login_fail("需要二次验证,中止录号，请联系客服人工录号")

        if luhao_task.get("memberId", None):
            sn = luhao_task.get("productSn")
            portal_client.send_msg(luhao_task.get("memberId", None),
                                   f'亲爱的小主，您的商品编号【{sn}】，因需要二次验证，录号终止，请联系客服人工录号')

        step_restart_nshm(0)
        connect_device("Windows:///?title_re=.*Version.*")
        sync_current_snapshot("扫码后识别到需要二次验证，重启客户端完成")
        sync_task_cancel("需要二次验证，取消录号")



        # connect_device("Windows:///?title_re=MuMu模拟器12")
        while True:
            portal_client.cancel_task_and_restart(luhao_task["id"])
            kkLogger_log("二次验证重启后，等待任务取消指令")
            sleep(120)

###############################################################
def wait_qrcode_scan_desk(wait_times):
    global is_skip_qufu
    for i in range(wait_times):
        try:
            connect_device("Windows:///")
            width,height = device().get_current_resolution()
            kkLogger_log(f"wait_qrcode_scan_desk 等待扫码，窗口尺寸: {width},{height}")

            if local_ocr_text_target_coords("开始",1444, 861, 1589, 1030) is not None:
                kkLogger_log("扫码成功。。。。。")
                sync_login_success("登录成功")
                return True
            elif local_ocr_text_target_coords("确认是否", 589, 431, 997, 673, True) is not None:
                kktouch3(722,581,3,"确定")
                sleep(5)
                if wait_for_ocr_txt("顶号失败", 2,589, 431, 997, 673):
                    target = wait_for_ocr_txt("取消", 5, 589, 431, 997, 673)
                    if target:
                        kktouch3(target[0], target[1], 4, "取消")

                    if luhao_task.get("memberId", None):
                        sn = luhao_task.get("productSn")
                        portal_client.send_msg(luhao_task.get("memberId", None),
                                               f'亲爱的小主，您的商品编号【{sn}】,因顶号失败终止录号。')
                    return False
                else:
                    is_skip_qufu = True
                    if check_and_try_game_home(60,None,False):
                        return True
                    else:
                        continue
            elif local_ocr_text_target_coords("关于防止", 589, 431, 1004, 730, True) is not None:
                target = wait_for_ocr_txt("取消", 5, 589, 431, 1004, 730,)
                if target:
                    kktouch3(target[0], target[1], 4, "取消")

                if luhao_task.get("memberId", None):
                    sn = luhao_task.get("productSn")
                    portal_client.send_msg(luhao_task.get("memberId", None),
                                           f'亲爱的小主，您的商品编号【{sn}】,因未实名终止录号。')
                return False
            elif local_ocr_text_target_coords("机", 939, 912, 1475, 1034, False) is not None:
                sync_current_snapshot("区服错误",None,True)
                kktouch3(47, 71, 3, "返回")
                if luhao_task.get("memberId", None):
                    sn = luhao_task.get("productSn")
                    portal_client.send_msg(luhao_task.get("memberId", None),
                                           f'亲爱的小主，您的商品编号【{sn}】,因区服错误，终止录号。如需继续，请重新提交录号。')
                return False
            elif local_ocr_text_target_coords("分钟", 589, 431, 997, 673, True) is not None:
                for i in range(6):
                    sleep(5)
                    if wait_for_ocr_txt("分钟", 2,589, 431, 997, 673):
                        print(f"排队等待：{i}")
                        continue
                    else:
                        break

                if local_ocr_text_target_coords("分钟", 589, 431, 997, 673, True) is not None:
                    sync_current_snapshot("需排队", None, True)
                    kktouch3(1012, 432, 3, "返回")
                    if luhao_task.get("memberId", None):
                        sn = luhao_task.get("productSn")
                        portal_client.send_msg(luhao_task.get("memberId", None),
                                               f'亲爱的小主，您的商品编号【{sn}】,因排队过久，终止录号。')
                    return False
            elif local_ocr_text_target_coords("禁止顶号", 589, 431, 997, 673, True) is not None:
                sync_current_snapshot("禁止顶号", None, True)
                target = wait_for_ocr_txt("取消",5,589, 431, 997, 673)
                if target:
                    kktouch3(target[0],target[1],4,"取消")

                if luhao_task.get("memberId", None):
                    sn = luhao_task.get("productSn")
                    portal_client.send_msg(luhao_task.get("memberId", None),
                                           f'亲爱的小主，您的商品编号【{sn}】,因所在副本禁止顶号，终止录号。')
                return False
            elif local_ocr_text_target_coords("顶号失败", 589, 431, 997, 673, True) is not None:
                target = wait_for_ocr_txt("取消",5,589, 431, 997, 673)
                if target:
                    kktouch3(target[0],target[1],4,"取消")

                if luhao_task.get("memberId", None):
                    sn = luhao_task.get("productSn")
                    portal_client.send_msg(luhao_task.get("memberId", None),
                                           f'亲爱的小主，您的商品编号【{sn}】,因顶号失败终止录号。')
                return False
            else:
                kkLogger_log(f"wait_qrcode_scan_desk 等待扫码,第{i}/{wait_times}次")
                sleep(3)
                kk_keyevent("{ESC}",1,3,"")
                continue
        except Exception as e:
            kkLogger_log(f"wait_qrcode_scan error: {e}")
            sleep(5)
            continue

    if luhao_task.get("memberId", None):
        sn = luhao_task.get("productSn")
        portal_client.send_msg(luhao_task.get("memberId", None),
                               f'亲爱的小主，您的商品编号【{sn}】,因未扫码，终止录号。如需继续，请重新提交录号。')

    sync_current_snapshot("扫码超时",None,False)
    kkLogger_log(f"未完成扫码,共计等待{wait_times}次")
    return False
#######################################################
def wait_qrcode_ok(wait_seconde):
    for i in range(wait_seconde):
        try:
            # connect_device("Windows:///?title_re=.*Version.*")
            # kkLogger_log(f"wait_qrcode_ok 逆水寒手游模拟器：等待扫码，窗口尺寸: {device().get_current_resolution()}")
            # check_game_login_face()
            # check_game_login_tiaokuan(2)
            # check_game_doubleCheck()
            # if device().get_current_resolution()[0] >= 1280:
            if local_ocr_text_target_coords("开始", 1444, 861, 1589, 1030, False) is not None:
                sync_current_snapshot("wait_qrcode_ok ok")
                return True
            else:
                sleep(3)
                continue
        except Exception as e:
            print(f"error {e}")
            sleep(3)
            continue

    return False
###################################################################
def local_ocr_text_target_coords(target_text,start_x,start_y,end_x,end_y,is_wait=True,t_position=0):
    for i in range(5):
        try:
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_region = image_util.capture_screen(f"{snapImgPath}\\plog_local_ocr_text_target_coords_{target_text}_{current_millis}.png", region=(start_x, start_y, end_x, end_y))
            if touch_fast and is_wait is True:
                sleep(0.5)
            ocr_result = ocr.ocr(f"{snapImgPath}\\plog_local_ocr_text_target_coords_{target_text}_{current_millis}.png", cls=False)
            if ocr_result is None:
                return None
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    if target_text in textinfo:
                        kkLogger_log(f"l_o_t_t_c：{textinfo},命中:{target_text}")
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        if 0 == t_position:
                            target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        elif -1 == t_position:
                            target_coords = (start_x+x1,start_y+y1)
                        elif 1 == t_position:
                            target_coords = (start_x + x2, start_y + y2)
                        break
                    else:
                        kkLogger_log(f"l_o_t_t_c：{textinfo},未命中:{target_text}")
                if target_coords:
                    break

            # 使用Airtest..坐标
            if target_coords:
                return target_coords
            else:
                return target_coords
        except Exception as e:
            sleep(2)
            kkLogger_log(f"l_o_t_t_c error：{e}")
            continue

    return None
##############################
def is_login_success():
    for i in range(20):
        sleep(1)
        connect_device("Windows:///?title_re=.*Version.*")
        # if local_ocr_text_target_coords("江湖",466,476,847,639,False) is not None:
        if local_ocr_text_target_coords("闯荡",466,476,847,639,False) is not None:
            # step_login_pic = save_cropped_screen("luhao_step")
            # oss_file = upload_one_img_to_oss(step_login_pic)
            # the_state_img = configs.image_server_url + oss_file
            propertyBag = {
                "state":"login_success",
                "state_img":"the_state_img"
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            kkLogger_log("is_login_success 登录成功")
            return True
        else:
            check_game_login_tiaokuan()
            check_game_gonggao()
            check_yueka_jiazeng(1)
            check_game_update(1)

            kkLogger_log("未发现 闯荡江湖")
            continue

    propertyBag = {
        "state":"login_timeout",
        "state_img":"the_state_img"
    }
    luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
    portal_client.sync_task_info(luhao_task)
    print("登录超时")
    return False
###########################################################################################
def is_mumulogin_success():
    for i in range(30):
        # set_current(0)
        connect_device("Android://127.0.0.1:5037/127.0.0.1:16384?cap_method=JAVACAP")
        target = ocr_text_target_coords("取消", 540 - 200, 483 - 200, 540 + 200, 483 + 200, False)
        if target is not None:
            kktouch3(target[0], target[1], 1, "取消。")

        # kktouch3(479, 625, 1, "关闭实名弹窗")
        target = ocr_text_target_coords("关闭", 479 - 100, 625 - 100, 479 + 100, 625 + 50, False)
        if target is not None:
            kktouch3(target[0], target[1], 1, "关闭实名弹窗2")

        target = ocr_text_target_coords("扫码", 872, 20, 1259, 106,False)
        if target is not None:

            sync_login_success(f"云逆水寒登录成功")

            kktouch3(target[0], target[1] - 10, 2, "扫码")
            if local_ocr_text_target_coords("二",369,563,911,710,False) is None:
                kkLogger_log("没有识别到取景框")
                sleep(1)
                continue
            else:
                kkLogger_log("识别到取景框")

            kkLogger_log("连接桌面")
            connect_device("windows:///")
            if local_ocr_text_target_coords("二", 493, 302, 783, 511) is not None:
                kktouch3(635,460,1,"摄像头")
                # kktouch2(635, 392, 1, "桌面")
                return True
            else:
                kkLogger_log("未识别到摄像头选择弹框")
                continue
        else:
            kkLogger_log("未发现 扫码按钮")
            continue

    kkLogger_log("登录超时")
    return False
###########################################
#############################################################################
def scan_qrcode():
    # sleep(3)
    coords = ocr_text_target_coords("扫码", 872, 20, 1259, 106)
    if coords is not None:
        kktouch3(coords[0], coords[1]-10, 1, "扫码")
        sleep(1)
        for i in range(3):
            try:
                kkLogger_log("连接MuMu模拟器")
                connect_device("Windows:///?title_re=MuMu模拟器12")
                kkLogger_log("连接桌面")
                kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")

                dev2 = connect_device("windows:///")
                kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
                sleep(1)
                break
            except Exception as e:
                kkLogger_log(f"连接桌面异常：{e}")
                sleep(2)
                continue

        kktouch3(604, 467, 1, "摄像头")
        return True
    else:
        return False


####################################################################
def check_game_gonggao():
    try:
        global is_gonggao_checked
        global is_zhuangyuan_gonggao_checked
        if is_local:
            return
        coords = local_ocr_text_target_coords("本期上新",10,10,280,422,False)
        if coords is not None and is_zhuangyuan_gonggao_checked is False:
            kktouch3(1089, 138, 2, "关闭庄园公告")
            # is_zhuangyuan_gonggao_checked = True

        coords = local_ocr_text_target_coords("月", 10, 10, 80, 653,False)
        if coords is not None and is_gonggao_checked is False:
            kktouch3(1199, 144, 2, "关闭公告.")
            kktouch3(1199, 149, 2, "关闭公告.")
            # is_gonggao_checked = True

    except Exception as e:
        print(f"没有公告:{e}")

####################################
##########################################
def get_return_taskEvent(now_stage,now_status,now_msg=None):
    return TaskEvent(task_id=luhao_task['id'],
                             stage=now_stage,
                             status=now_status,
                             snapshot=get_luhao_snapshot(),
                             data=product_meta,
                             msg=now_msg)

#######################################
def luhao_health_check():
    for i in range(3):
        try:
            screen = G.DEVICE.snapshot()
            if touch_fast:
                sleep(0.5)
            pil_img = cv2_2_pil(screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))

            pil_img.save(f'{projectPath}\\device_health\\luhao_health_{current_millis}.jpg')

            filename = f'luhao_health_{current_millis}.jpg'
            snapImgPath = f'{projectPath}\\device_health'

            image_path_set = set()
            bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称
            endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint
            access_key_id = configs.OSS_ACCESS_KEY_ID
            access_key_secret = configs.OSS_ACCESS_KEY_SECRET

            auth = oss2.Auth(access_key_id, access_key_secret)
            bucket = oss2.Bucket(auth, endpoint, bucket_name)

            local_file_path = os.path.join(snapImgPath, filename)
            oss_file_path = "mall/images2/"+ generate_oss_path()
            oss_fileName = oss_file_path+generate_oss_fileName()
            with open(local_file_path, 'rb') as fileobj:
                bucket.put_object(oss_fileName, fileobj)

            print(f"oss_filename:{oss_fileName}")

            return True
        except Exception as e:
            continue

    return False

###########################################
def wait_for_ocr_txt(ocr_txt,timeout,start_x,start_y,end_x,end_y,t_position=0):
    for i in range(timeout):
        coords = local_ocr_text_target_coords(ocr_txt,start_x, start_y,end_x, end_y,False,t_position)
        if coords is not None:
            kkLogger_log(f"w_ft_识别到文字：{ocr_txt},时间：{i}")
            return coords
        else:
            if timeout > 1:
                sleep(1)
    kkLogger_log(f"没有找到文字：{ocr_txt},超时时间：{timeout}")
    return False

######################################################################
def check_huodong():
    if is_local:
        return
    kkLogger_log(f"检查是否有活动弹窗")
    coords = ocr_text_target_coords("参与",427, 300,1233, 734)
    if coords is not None:
        kktouch3(1150, 149,1.5,"关闭活动")


#######################################################################
def check_yueka_jiazeng(timeout=5):
    global is_jiazheng_checked
    global jiazheng_check_count

    if is_local:
        return

    coords = ocr_text_target_coords("赠送",1091, 644,1266, 736,False)
    if coords:
        kktouch3(coords[0],coords[1],2,"赠送")
        target= wait_for_ocr_txt("确定",2,100, 100,1266, 736)
        if target:
            kktouch3(target[0],target[1],1,"赠送")

    coords = ocr_text_target_coords("领取",627, 400,1233, 734,False)
    if coords:
        kktouch3(coords[0], coords[1],2,"领取")
        kktouch3(coords[0], coords[1],2,"领取")
        kktouch3(coords[0], coords[1], 2, "领取")
        kktouch3(coords[0], coords[1], 2, "领取")
        is_jiazheng_checked = True
        return

#######################################################################

#########################################################################
def step_qufu(is_skip):
    # try:
    #     connect_device("windows:///?title_re=.*Version.*")
    # except Exception as e:
    #     print(f"step_qufu error: {e}")
    global is_skip_qufu
    if is_skip == 0:
        if is_skip_qufu:
            sleep(5)
            kktouch3(1509, 979, 10, "开始游戏")
            return get_return_taskEvent("step_qufu", EventStatus.SUCCESS, "跳过角色选择")
        elif wait_for_ocr_txt("开始",120, 1444, 861, 1589, 1030):
            kktouch3(1509,979,10,"开始游戏")
            check_and_try_game_home(100)
            return get_return_taskEvent("step_qufu", EventStatus.SUCCESS,"角色选择完成")
        else:
            return get_return_taskEvent("step_qufu", EventStatus.FAILURE, "角色界面错误")

###################################################################################################################################
#########################################################################################################################################
def safe_step_gameset(is_skip):
    return step_gameset(is_skip)
########################################################################
def check_game_login_face():
    kkLogger_log(f"大神人脸确认")
    sleep(1)
    coords = local_ocr_text_target_coords("人脸",50, 60, 1000, 700,False)
    if coords is not None:
        connect_device("windows:///")
        kkLogger_log(f"连接桌面,尺寸:{device().get_current_resolution()}")
        sync_current_snapshot("处理大神弹窗")
        kktouch3(1116, 99,2,"关闭大神弹窗")
        connect_device("windows:///?title_re=.*Version.*")
        kkLogger_log(f"连接逆水寒手游模拟器,尺寸:{device().get_current_resolution()}")
#######################################################################
def check_game_login_tiaokuan(timeout=5):
    connect_device("windows:///")
    kkLogger_log(f"check_game_login_tiaokuan 连接桌面,尺寸:{device().get_current_resolution()}")

    # wait_for_ocr_txt("拒绝",timeout,288, 426,999, 648)
    coords = local_ocr_text_target_coords("接受",288, 426,1200, 648,False)
    if coords is not None:
        sync_current_snapshot("处理登录条款")
        kktouch3(coords[0], coords[1],2,"接受")

    connect_device("windows:///?title_re=.*Version.*")
    kkLogger_log(f"连接逆水寒手游模拟器,尺寸:{device().get_current_resolution()}")
#######################################################################
def check_game_update(timeout=5):
    return
    # if is_local:
    #     return
    # kkLogger_log(f"check_game_update")
    # wait_for_ocr_txt("过旧",timeout,337, 206,909, 479)
    # coords = ocr_text_target_coords("确定",337, 206,909, 479)
    # if coords is not None:
    #     sync_current_snapshot("提示版本过旧，需要升级客户端","FAILED")
    #     kktouch2(coords[0], coords[1],5,"确定")
#######################################################################
def check_game_alert_note():
    if is_local:
        return
    kkLogger_log(f"检查是否有提示弹窗")
    cancel_target = local_ocr_text_target_coords("取消",239, 150,1200, 700,False)
    if cancel_target:
        kktouch2(cancel_target[0],cancel_target[1],1,"check_game_alert_note 取消")

#######################################################################

####################################################
def check_account_login_other():
    account_logout = False
    if local_ocr_text_target_coords("顶号",653, 427,966, 475,False) is not None:
        sync_current_snapshot("被顶号，录号中断.","FAILED",True)
        kktouch3(728,496,2,"顶号：关闭登录框")
        if wait_for_ocr_txt("返回",60,669,653,957,710):
            kktouch3(872,679,1,"返回")
        account_logout = True

    if local_ocr_text_target_coords("顶号",669, 303,926, 359,False) is not None:
        sync_current_snapshot("被顶号，录号中断..","FAILED",True)
        kktouch3(871,679,2,"顶号：关闭二维码")
        account_logout = True

    global is_fail_send
    if account_logout and is_fail_send is False:
        if luhao_task.get("memberId", None):
            is_fail_send = True
            sn = luhao_task.get("productSn")
            portal_client.send_msg(luhao_task.get("memberId", None),
                                   f'亲爱的小主，您的商品编号【{sn}】，因被顶号，终止录号，如需继续请重新提交官方录号！')

    return account_logout

###################################################################################
def safe_step_fuben_in(is_skip):
    if is_skip == 0:
        try:
            kkLogger_log(f"开始进入副本")
            check_and_try_game_home(4)
            kk_keyevent("{ESC}",1,1,"菜单")
            kktouch3(1027,364,2,"副本")

            kktouch3(689,432,3,"千机")

            kktouch3(1104,80,3,"秋岚画院")

            kktouch3(937,567,3,"单双人")

            kktouch3(1148,688,4,"开始挑战")

            kktouch3(1030,614,10,"就位")

            if check_and_try_game_home(4,"fuben"):
                sync_current_snapshot("进入副本成功")
                return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS,"进入副本完成")
            else:
                sync_current_snapshot("进入副本失败")
                return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS, "进入副本失败")
        except Exception as e:
            kkLogger_log(f"进入副本异常 {e}")
###########################################################################
# def safe_step_fuben_out(is_skip):
#     if is_skip == 0:
#         try:
#             kkLogger_log(f"开始退出副本")
#             check_and_try_game_home(4)
#             kktouch2(947,177,2,"退出副本")
#             if ocr_text_target_coords("确认",1043,343,1250,458,) is not None:
#                 kktouch2(1157,386,10,"确认")
#
#             sync_current_snapshot("退出副本")
#             if check_and_try_game_home(4):
#                 return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS, "退出副本完成")
#             else:
#                 return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS, "退出副本失败")
#         except Exception as e:
#             kkLogger_log(f"退出副本异常 {e}")
########################################################################
def step_gameset(step_gameset):
    if step_gameset == 0:
        connect_device("windows:///?title_re=.*Version.*")
        check_yueka_jiazeng(1)
        check_game_update(1)

        sync_current_snapshot("游戏设置开始",None,False)
        for i in range(6):
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            target = local_ocr_text_target_coords("设置", 1214, 236, 1278, 621)
            if target:
                kktouch2(target[0], target[1],2,"设置")

                kktouch2(77+10, 445+37, 1, "视角")
                kktouch2(747+10, 126+37, 1, "2.5D")

                kktouch2(76, 359,1,"左侧画面")
                kktouch2(805, 319, 1, "流畅")



                kktouch2(70, 73,1,"左上退出游戏设定")
                kk_keyevent("{ESC}",2,0.5,"退出游戏设定")
                sync_current_snapshot("游戏设置完成")


                safe_step_fuben_in(0)
                return get_return_taskEvent("step_gameset", EventStatus.SUCCESS,"游戏设置完成回到主页")
            else:
                check_and_try_game_home(4)
                continue
        return get_return_taskEvent("step_gameset", EventStatus.SUCCESS,"游戏设置完成回到主页")
##################################################################################
def safe_step_juese(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游模拟器")
            return step_juese(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_juese error,{e}")
            check_and_try_game_home(4)

    return get_return_taskEvent("step_juese", EventStatus.FAILURE)

#####
def step_juese(is_skip):
    if is_skip == 0:

        kktouch2(1224 + 10, 32 + 37, 2, "菜单")
        kktouch3(1248, 275, 2, "背包")
        sync_current_snapshot("背包", None, True)
        kktouch3(1002, 60, 2, "纹玉")
        sync_current_snapshot("背包-纹玉", None, True)
        wenyu_count = 0
        if ocr_text_target_coords("兑换", 705, 40, 1250, 700):
            save_cropped_area_screen("纹玉兑换_面板属性", 705, 40, 1250, 700, 0.1)
            wenyu_count = get_wenyu_attr_ocr_number("限定纹玉额度", 927, 347, 1201, 403)

            kktouch3(909, 60, 2, "纹玉1-2")
            sync_current_snapshot("背包-纹玉1-2", None, True)
            target = ocr_text_target_coords("限", 1018, 230, 1259, 268)
            if ocr_text_target_coords("兑换", 705, 40, 1250, 700) and target:
                kktouch3(target[0], target[1], 1, "限定")
                save_cropped_area_screen("纹玉兑换_面板属性", 705, 40, 1250, 700, 0.1)
                wenyu_count = wenyu_count + get_wenyu_attr_ocr_number("限定纹玉额度", 1001, 448, 1130, 503)
        else:
            kktouch3(925, 56, 2, "纹玉2")
            sync_current_snapshot("背包-纹玉2", None, True)
            target = ocr_text_target_coords("限", 1018, 230, 1259, 268)
            if ocr_text_target_coords("兑换", 705, 40, 1250, 700) and target:
                kktouch3(target[0], target[1], 1, "限定")
                save_cropped_area_screen("纹玉兑换_面板属性", 705, 40, 1250, 700, 0.1)
                wenyu_count = get_wenyu_attr_ocr_number("限定纹玉额度", 1001, 448, 1130, 503)

        print(f"限定纹玉总额 {wenyu_count}")
        ocr_check_and_set_number("限定纹玉额度", str(wenyu_count))
        sync_current_snapshot(f"计算到限定纹玉额度总额为：{wenyu_count}", None, False)




        kktouch2(1224 + 10, 32 + 37, 2, "菜单")
        kktouch2(1250, 187, 1.5, "主页")
        if ocr_text_target_coords("个人", 92, 40, 252, 113) is not None:
            sleep(5)
            save_cropped_screen_with_mulblur("主页_其他物品", [(17, 49, 317, 357)])
            kk_keyevent("{ESC}", 1, 1, "退出主页")


        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        # if kktouch_step(1247, 124, 2, "角色") is False:
        if kktouch2(1247, 124, 2, "角色") is False:
            return get_return_taskEvent("step_juese", EventStatus.SUCCESS, "未执行")
        kktouch2(68+10, 128+37,2,"左侧角色")

        uid = get_ocr_number(949,684,1068,729)
        sync_task_uid(uid)

        save_cropped_screen_with_mulblur("角色头图_面板属性",[(49, 41-40,449, 95),(866, 6,1261+10, 239+40),(884, 652,1101+10, 695+40),(515,6,788,186)])


        get_attr_ocr_number("评分",229, 103,441, 182)

        kktouch2(277+10, 257+37,1,"..角色-装备1")
        save_cropped_area_screen("角色-装备1_面板属性",347+10, 37+40,700+10, 634+40,0.1)

        kktouch2(323+10, 296+37,1,"..角色-装备2")
        save_cropped_area_screen("角色-装备2_面板属性",388+10, 32+40,742, 631+40,0.1)

        kktouch2(341+10, 425+37,1,"..角色-装备3")
        save_cropped_area_screen("角色-装备3_面板属性",436+10, 112+40,787, 712+40,0.1)

        kktouch2(284+10, 512+37,1,"..角色-装备4")
        save_cropped_area_screen("角色-装备4_面板属性",346+10, 115+40,698, 709+40,0.1)

        kktouch2(286+10, 598+37,1,"..角色-装备5")
        save_cropped_area_screen("角色-装备5_面板属性",346+10, 113+40,700, 710+40,0.1)

        kktouch2(256+10, 296+37,1,"..角色-装备6")
        save_cropped_area_screen("角色-装备6_面板属性",301+10, 34+40,655, 632+40,0.1)

        kktouch2(290+10, 343+37,1,"..角色-装备7")
        save_cropped_area_screen("角色-装备7_面板属性",346+10, 56+40,700, 658+40,0.1)

        kktouch2(254+10, 385+37,1,"..角色-装备8")
        save_cropped_area_screen("角色-装备8_面板属性",302+10, 98+40,656, 701+40,0.1)

        kktouch2(254+10, 467+37,1,"..角色-装备9")
        save_cropped_area_screen("角色-装备9_面板属性",301+10, 112+40,655, 710+40,0.1)

        kktouch2(256+10, 556+37,1,"..角色-装备10")
        save_cropped_area_screen("角色-装备10_面板属性",304+10, 112+40,656, 713+40,0.1)

        kktouch2(215+10, 344+37,1,"..角色-装备11")
        save_cropped_area_screen("角色-装备11_面板属性",259+10, 59+40,613, 655+40,0.1)

        kktouch2(212+10, 508+37,1,"..角色-装备12")
        save_cropped_area_screen("角色-装备12_面板属性",259+10, 113+40,616+10, 709+40,0.1)

        #############################################################
        kkLogger_log("======旅途截图开始")
        ############开始旅途
        kktouch3(75, 286, 2, "..左侧旅途")
        save_cropped_area_screen("纹玉兑换_面板属性", 864, 80, 1251, 687)
        # save_cropped_screen("旅途")
        kktouch3(67, 354, 2, "..左侧修行")
        save_cropped_screen("修行")

        #####################################################
        kktouch2(73+10, 194+37,2,"左侧背包")
        kktouch2(869, 716,2,"底部仓库")

        swipe_to_end((832, 175, 1170, 287),(1058, 191),(934, 523))
        save_cropped_area_screen("角色-背包_其他物品", 834, 124, 1181, 626, 0.2)

        long_snapshot("角色-背包_其他物品", 834, 629, 1181, 683, 24,1.5)

        kkLogger_log(f'背包截图结束,相关材料在{snapImgPath}')
        #############################################################
        kktouch3(869, 716,2,"底部仓库")
        kktouch3(52,169,1,"常规")
        findcangkuResult = find_all_subImg(Template(r"tpl1727872265286.png", threshold=0.4),191, 105,405, 160)
        cangkuCount = 0
        if findcangkuResult is None:
            cangkuCount = 0
        else:
            for line in findcangkuResult:
                kkLogger_log(line)
                if line['confidence'] > 0.4:
                    cangkuCount = cangkuCount +1
                    kktouch2(line['result'][0]+191,line['result'][1]+105,1,f"仓库第{cangkuCount}个包")
                    save_cropped_area_screen("仓库_其他物品", 105, 118, 450, 460)
            if cangkuCount >= 4:
                kktouch3(425,135,1,"最右边仓库")
                if (ocr_text_target_coords("是否", 408, 348, 871, 433)
                        or ocr_text_target_coords("取消", 77, 361, 169, 420)):
                    kktouch2(67,390,1,"取消")
                else:
                    save_cropped_area_screen("仓库_其他物品", 105, 118, 450, 460)
        print(f"=====仓库数量===={cangkuCount}")

        kk_keyevent("{ESC}",1,1,"退出仓库")
        ###################################

        # kkLogger_log("======旅途截图开始")
        # ############开始旅途
        # kktouch3(75, 286,2,"..左侧旅途")
        # save_cropped_area_screen("纹玉兑换_面板属性",864, 80,1251, 687)
        # # save_cropped_screen("旅途")
        # kktouch3(67, 354,2,"..左侧修行")
        # save_cropped_screen("修行")
        step_2 =1
        # kk_keyevent("{ESC}",1,0.2)
        return get_return_taskEvent("step_juese", EventStatus.SUCCESS,"角色信息完成")
    else:
        return get_return_taskEvent("step_juese", EventStatus.SUCCESS,"未执行")
###############################################################
def safe_step_jueji(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游模拟器")
            return step_jueji(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_jueji error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_jueji", EventStatus.FAILURE)
###################
def step_jueji(step_jueji):
    if step_jueji == 0:
        kkLogger_log("======武功-绝技截图开始")
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1065, 193,3,"武功") is False:
            return get_return_taskEvent("step_jueji", EventStatus.SUCCESS, "绝技未执行")
        kktouch2(64+10, 121+37,1,"左侧技能")
        kktouch2(68+10, 245+37,1,"绝技")
        save_cropped_screen("绝技_其他物品")

        if ocr_text_target_coords("升",877, 678,1056, 719) is not None:
            kktouch2(933,700,1,"绝技-升阶")
            sleep(2)
            save_cropped_screen("绝技升阶_其他物品")

        kk_keyevent("{ESC}",2,0.2)

        kkLogger_log("======武功-绝技截图完成")
        return get_return_taskEvent("step_jueji", EventStatus.SUCCESS,"绝技完成")
####################################################
def safe_step_neigong(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游模拟器")
            return step_neigong(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_neigong error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_neigong", EventStatus.FAILURE)
###################

def step_neigong(is_skip):
    if is_skip == 0:
        kkLogger_log("======内功截图开始")
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1065, 193,3,"武功") is False:
            return get_return_taskEvent("step_neigong", EventStatus.SUCCESS, "内功未执行")

        kktouch2(75+10, 123+37,1,"技能")
        kktouch2(75+10, 434+37,1,"内功")

        kktouch2(346+10, 658+37,1,"..详细属性加成")
        # if ocr_text_has_number(399, 47, 878, 662):
        if ocr_text_target_coords("加成",399, 47,878, 662) is not None:
            save_cropped_area_screen("内功详细属性加成_打造内功",399, 47,878, 715,0)
            kktouch2(270+10, 699+37,1,"取消详细属性加成")


        for j in range(3):
            kktouch2(1001 + 10, 653 + 37, 1.5, "全部内功")
            kktouch2(143+10, 107+37,1,"筛选")
            # kk_scroll((1008, 587), -5, 1, "词条搜索")
            # kktouch2(1054, 546,1.5,"词条")

            kktouch2(1059, 658,1,"词条")
            kktouch2(440, 219,1,"灵韵")
            kktouch2(1169, 701,1.2,"确定")
            save_cropped_screen("lingyunall_打造内功")
            # if ocr_text_target_coords("中",96,110,200,159) is not None:
            if True:
                start_x = 109
                start_y = 170
                end_x = 823
                end_y = 286
                loop_height = 127
                lingyun_count = 0
                for i in range(4):
                    find_lingyun_result = find_all_subImg(Template(r"tpl1726237406954.png", threshold=0.6), start_x,
                                                          start_y, end_x, end_y)
                    neigong_count = 0
                    if find_lingyun_result is None:
                        break
                    # elif ocr_text_has_txt(start_x, start_y, end_x, end_y) is False:
                    #     break
                    else:
                        kkLogger_log(f"第{i + 1}行，find_lingyun_result数量：{len(find_lingyun_result)}")
                        for line in find_lingyun_result:
                            neigong_count = neigong_count + 1
                            if line['confidence'] > 0.5:
                                x1 = line['result'][0]
                                y1 = line['result'][1]
                                kktouch2(x1 + start_x, y1 + start_y, 1, f"第{neigong_count}个灵韵内功")

                                # swipe((1055, 505), (1055, 450),duration=0.2,steps=2)
                                kk_scroll((1055, 505), -3, 1, "灵韵")


                                # find_lingyun_result = find_subImg(Template(r"tpl1726830336235.png"), 868, 407, 1263,663)

                                # screen = G.DEVICE.snapshot()
                                # cropped_screen = aircv.crop_image(screen, (868, 485, 1263, 663))
                                # pil_img = cv2_2_pil(cropped_screen)
                                # tile_img_2 = np.array(pil_img)

                                # if find_lingyun_result is not None or local_ocr_text_target_coords("灵韵",868, 407, 1263,663) is not None:
                                # if contains_orange_in_corner(tile_img_2, (1263 - 868, 663 - 485)):

                                if local_ocr_text_target_coords("等级",868, 407, 1263,663) is not None:
                                    save_cropped_area_screen("灵韵_打造内功_0", 857, 59, 1266, 669, 0)
                                    ocr_add_to_get(857, 59, 1129, 105)
                                    lingyun_count = lingyun_count + 1
                                    print(f"灵韵数量:{lingyun_count}")
                                else:
                                    save_cropped_area_screen("灵韵_打造内功_1", 857, 59, 1266, 669, 0)
                        start_y = start_y + loop_height
                        end_y = end_y + loop_height

                ocr_check_and_set("灵韵数量", str(lingyun_count))
                sync_current_snapshot(f"识别到灵韵数量：{lingyun_count}:{neigong_lingyun_set}",None,True)
                break

        kk_keyevent("{ESC}", 2, int(0.2))
    return get_return_taskEvent("step_neigong", EventStatus.SUCCESS,"内功完成")
####################################################
def safe_step_dazao(is_skip):
    for i in range(3):
        try:
            return step_dazao(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_dazao error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_dazao", EventStatus.FAILURE)
###################
def step_dazao(is_skip):
    if is_skip == 0:
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1172 , 329 , 1, "打造") is False:
            return get_return_taskEvent("step_dazao", EventStatus.SUCCESS, "无打造，未执行")
        kktouch2(77,160,1,"左侧强化")
        save_cropped_screen("打造强化_打造内功")

        kktouch3(71,224,2,"左侧打造")

        sync_current_snapshot("打造强化")

        loop = 80
        start_x = 221
        start_y = 167
        for i in range(6):
            kktouch2(start_x,start_y,0.5,f"第{i+1}个打造")

            # if ocr_text_target_coords("已",1201,268,1270,421) is None and ocr_text_target_coords("有",1201,268,1270,421) is None:
            #     sleep(1)
            #     start_y = start_y + loop
            #     continue

            kktouch3(1238, 336,2,"已有特技库")
            kktouch3(1263, 340,2,"已有特技库")

            if local_ocr_text_target_coords("打造", 92, 43, 194, 101) is not None:
                sync_current_snapshot(f"无法进入已有特技库 {i}",None,True)
                start_y = start_y + loop
                continue

            kk_scroll((1032, 632), -10, 1.5, "打造滚动底部")
            kktouch2(872 + 10, 643 + 37, 1, "..最后一个")
            kk_scroll((1032, 632), 10, 1.5, "打造滚动顶部")

            target_arrays = ocr_text_target_coords_arrays("激活", 732, 98, 1266, 726)
            if target_arrays is not None:
                for item in target_arrays:
                    save_cropped_area_screen_dazao("打造属性_打造内功", item[0] - 384, item[1] - 40,(item[0] - 384) + 444, (item[1] - 40) + 81)
            else:
                target_arrays = ocr_text_target_coords_arrays("提升",732, 98,1266, 726)
                if target_arrays is not None:
                    for item in target_arrays:
                        save_cropped_area_screen_dazao("打造属性_打造内功",item[0]-121, item[1]-57,(item[0]-121)+444, (item[1]-57)+81)

            if target_arrays is not None and len(target_arrays) == 5:
                for i in range(5):
                    pyautogui.scroll(-21)
                    sleep(2)
                    target_arrays_single = ocr_text_target_coords_arrays("激活", 622,601,1230,741)
                    if target_arrays_single is not None and len(target_arrays_single) == 1:
                        for item in target_arrays_single:
                            save_cropped_area_screen_dazao("打造属性_打造内功", item[0] - 384, item[1] - 40,
                                                           (item[0] - 384) + 444, (item[1] - 40) + 81)
                    else:
                        break


            kktouch2(71+10, 40+37,1,"左上返回")
            start_y = start_y + loop

        # kktouch2(71+10, 36+37,1,"左上返回")
        kk_keyevent("{ESC}", 1, 0.2)
        kkLogger_log("打造结束")
    return get_return_taskEvent("step_dazao", EventStatus.SUCCESS,"打造完成")
###########################################################################################################

#####################################################################################################################
def safe_step_waiguan(is_skip):
    for i in range(2):
        try:
            return step_waiguan(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_waiguan error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_waiguan", EventStatus.FAILURE)
##############################################################################
def waiguan_substep_guancang1025(is_skip):
    if is_skip == 0:
        global tianniran_count
        global waiguan_ziran_fashi_set
        kktouch2(1094, 123, 2, "衣品")
        sync_current_snapshot("外观-衣品")
        save_cropped_area_screen("衣品", 835, 81, 1245, 651)
        has_yipin = get_attr_ocr_number("衣品", 1009, 84, 1254, 125)
        kk_keyevent("{ESC}", 1, 2, "退出衣品")


        kktouch2(1217, 124,5,"馆藏")
        # target = ocr_text_target_coords("跳过", 954, 54, 1273, 177)
        target = wait_for_ocr_txt("跳过",3, 954, 54, 1273, 177)
        if target:
            sleep(5)
            kktouch3(target[0],target[1],2,"跳过")
            sleep(3)

        connect_device("Windows:///?title_re=.*Version.*")
        save_cropped_screen(f"馆藏总等级_其他物品")
        sync_current_snapshot("馆藏")


        kktouch2(640,616,1.5,"时鉴赏")
        kktouch2(67, 313,1,"时鉴赏一期")
        save_cropped_screen(f"时鉴赏一期_其他物品")
        kktouch2(67, 374,1,"时鉴赏二期")
        save_cropped_screen("时鉴赏二期_其他物品")
        kktouch2(67, 444, 1, "时鉴赏三期")
        save_cropped_screen("时鉴赏三期_其他物品")
        kk_keyevent("{ESC}", 1, 1.5, "退出时鉴赏")

        kktouch2(374, 464,2,"天赏")
        kktouch2(89, 242, 2, "青丝馆霓")


        # kktouch3(998,72,1.5,"自染分享")
        # # save_cropped_area_screen("头图_天赏外观", 112, 95, 1116, 642)
        # save_cropped_screen_with_blur2("头图_天赏外观", 112, 95, 1116, 667, 112, 642, 1116, 667)
        # kktouch3(1219, 72, 1.5, "退出自染分享")


        target = local_ocr_text_target_coords("暂无", 528, 558, 832, 618)
        if target is None:
            kktouch3(990, 68, 1.5, "自染分享")
            save_cropped_area_screen("头图_天赏外观", 112, 95, 1116, 642)
            kktouch3(1219, 72, 1.5, "退出自染分享")

            save_cropped_screen("自染_天赏外观")
            kktouch2(334,140,1,"分类展示")

            ocr_add_to_get_and_count_tianniran_number(1091, 127, 1216, 167)
            sleep(1)
            if tianniran_count == 0:
                ocr_add_to_get_and_count_tianniran_number(1156, 109, 1272, 215)

            sync_current_snapshot(f"识别到自染数量:{tianniran_count}",None,True)

            if tianniran_count < 10:
                tianniran_count = 163

            if tianniran_count > 4:
                start_x = 176
                start_y = 174
                end_x = 295
                end_y = 372
                save_ziran_pic(start_x,start_y,end_x,end_y)
                for i in range(20):
                    start_x = 176
                    start_y = 174
                    end_x = 295
                    end_y = 372

                    swipe_and_judge_end((165, 310, 544, 372), (418, 571), (418, 335))
                    save_ziran_pic(start_x,start_y,end_x,end_y)

                    # print(f"==========剩余数量：{(tianniran_count - ziran_pic_count)}")
                    # if ((tianniran_count == 163 and ocr_text_has_txt_inverted(418, 574, 501, 653  ) is False)
                    if ((tianniran_count == 163 and ocr_text_has_txt_inverted(173, 574, 501, 653  ) is False)
                            or (tianniran_count - ziran_pic_count) <= 6):
                        break
                #识别第二行
                save_ziran_pic(176, 375, 293, 573)
                kk_scroll((341, 662), -10, 1, "自染滚动底部")
                # 识别倒数第二行
                save_ziran_pic(176, 315, 291, 511)
                #识别最后一行
                save_ziran_pic(176, 518, 295, 715)

                if tianniran_count == 163 and ziran_pic_count > 0:
                    ocr_check_and_set("天霓染", str(ziran_pic_count))
                elif 0 < tianniran_count < 100:
                    ocr_check_and_set("天霓染", str(tianniran_count))

                sync_current_snapshot(f"自染:{ziran_pic_count}:{waiguan_ziran_fashi_set}", None, True)
        else:
            # pass
            kktouch3(990, 68, 1.5, "自染分享")
            save_cropped_screen_with_blur2("头图_天赏外观", 112, 95, 1116, 667, 112, 642, 1116, 667)
            kktouch3(1219, 72, 1.5, "退出自染分享")

            save_cropped_screen("自染_天赏外观")

        kk_keyevent("{ESC}", 1, 1.5, "退出天赏")

        kktouch2(143, 689,1.2,"国色")
        save_cropped_screen(f"国色_其他物品")
        get_attr_ocr_number("国色值",843, 47,1130, 95)
        kk_keyevent("{ESC}", 1, 1.5, "退出国色")

        kkLogger_log("馆藏结束")

#################################################################################
def save_ziran_pic(start_x,start_y,end_x,end_y):
    global ziran_pic_count
    global tianniran_count
    global waiguan_ziran_fashi_set
    loop_x = 299 - 175
    count = 0
    for j in range(3):
        if ziran_pic_count < tianniran_count:
            if tianniran_count == 163 and ocr_text_has_txt(start_x,end_y-37,end_x,end_y) is False:
                print(f"未发现自染头 {start_x},{end_y-37}")
                start_x = start_x + loop_x
                end_x = end_x + loop_x
                continue

            kktouch2(start_x + 50, start_y + 50, 0.5, "左侧自染头")
            # save_cropped_area_screen(f"自染头_天赏外观", start_x, start_y, end_x, end_y, 0)
            pic_width = 149
            pic_height = 115

            hit_txt_arrays = []
            ocr_text_target_coords_arrays_inverted("点", start_x-20, start_y-20, start_x+50, start_y+50, hit_txt_arrays)
            if len(hit_txt_arrays) == 0:
                sleep(1)
                ocr_text_target_coords_arrays("点", start_x-20, start_y-20, start_x+50, start_y+50, hit_txt_arrays)

            if len(hit_txt_arrays) > 0 or ocr_text_has_txt(start_x,end_y-37,end_x,end_y) is not False:

                ziran_fashi = get_first_ocr_txt(start_x,end_y-40,end_x,end_y+5)

                # waiguan_ziran_fashi_set.append(ziran_fashi)

                print(f"识别到：{ziran_fashi}")

                if ziran_fashi is not None and ziran_fashi in waiguan_ziran_fashi_set and len(ziran_fashi) > 1:
                    start_x = start_x + loop_x
                    end_x = end_x + loop_x
                    continue

                if ziran_fashi is not None:
                    waiguan_ziran_fashi_set.append(ziran_fashi)

                save_cropped_area_screen(f"自染头_天赏外观", start_x, start_y, end_x, end_y, 0)

                item = "2点"
                if len(hit_txt_arrays) > 0:
                    item = hit_txt_arrays[0]

                if "6" in item:
                    # tianniran_count = tianniran_count - 2
                    ziran_pic_count = ziran_pic_count + 2
                    save_cropped_area_screen(f"自染方案_天赏外观", 823, 154-50, 823 + pic_width, 154-50 + pic_height, 0)
                    save_cropped_area_screen(f"自染方案_天赏外观", 717, 327-50, 717 + pic_width, 327-50 + pic_height, 0)
                    save_cropped_area_screen(f"自染方案_天赏外观", 862, 510-50, 862 + pic_width, 510-50 + pic_height, 0)
                elif "4" in item:
                    # tianniran_count = tianniran_count -1
                    ziran_pic_count = ziran_pic_count + 1
                    save_cropped_area_screen(f"自染方案_天赏外观", 823, 154, 823 + pic_width, 154 + pic_height, 0)
                    save_cropped_area_screen(f"自染方案_天赏外观", 717, 327, 717 + pic_width, 327 + pic_height, 0)
                else:
                    save_cropped_area_screen(f"自染方案_天赏外观", 845, 242, 845 + pic_width, 242 + pic_height, 0)

                ziran_pic_count = ziran_pic_count + 1

            else:
                print("未识别到自染")

            start_x = start_x + loop_x
            end_x = end_x + loop_x
            count = count + 1

            # ziran_pic_count = ziran_pic_count + 1

    print(f"发现{count}个自染")
    return count
##################################################################################
def waiguan_substep_guancang(is_skip):
    if is_skip == 0:
        kktouch2(1223+10, 86+37,2,"馆藏")
        kktouch2(1013, 84,2,"馆藏奖励")
        kktouch2(71+10, 131+37,2,"馆藏总等级")
        save_cropped_screen(f"馆藏总等级_其他物品")

        sync_current_snapshot("馆藏等级")

        kktouch2(68+10, 199+37,1,"时鉴赏一期")
        save_cropped_screen(f"时鉴赏一期_其他物品")

        kktouch2(70+10, 272+37,1,"时鉴赏二期")
        save_cropped_screen("时鉴赏二期_其他物品")

        # kktouch2(1220+10, 79+37,2,"右侧X退出时鉴赏")
        kk_keyevent("{ESC}",1,2,"馆藏奖励")

        kktouch2(486+10, 39+37,2,"衣品")
        sync_current_snapshot("外观-衣品")

        # save_cropped_area_screen("衣品",835, 81,1245, 651)
        save_cropped_screen("衣品")

        has_yipin = get_attr_ocr_number("衣品",1009, 84,1254, 125)
        # kktouch2(1238+10, 35+37,1,"退出衣品")
        if has_yipin:
            kk_keyevent("{ESC}",1,2,"退出衣品")

        kktouch2(542,688,2,"天赏主题")

        kktouch2(642+10, 346+37,2,"青丝管霓")
        sync_current_snapshot("外观-自染")

        save_cropped_screen("自染_天赏外观")

        tianniran_count = ocr_add_to_get_and_count_tianniran_number(106, 653,229, 720)
        if tianniran_count == 0:
            tianniran_count = ocr_add_to_get_and_count_tianniran_number(106, 653, 229, 720)

        if tianniran_count == 0 and ocr_text_target_coords("暂无", 542, 345, 741, 389) is None:
            tianniran_count = 16

        if tianniran_count > 0:
            hit_txt_arrays = []
            targets = ocr_text_target_coords_arrays("点", 44, 101, 568, 184,hit_txt_arrays)
            snapshot_ziran(targets, hit_txt_arrays)

        if tianniran_count > 4:
            hit_txt_arrays = []
            targets = ocr_text_target_coords_arrays("点", 53, 314, 554, 387,hit_txt_arrays)
            snapshot_ziran(targets, hit_txt_arrays)

        ######################################################################
        if tianniran_count > 8:
            # swipe_to_end((54, 67, 554, 193), (302, 431), (302, 182),True)
            kk_scroll((302, 431), -10, 1, "自染滚动底部")
        #######################################################################
        if tianniran_count > 8:
            hit_txt_arrays = []
            targets = ocr_text_target_coords_arrays("点", 49, 234, 559, 295, hit_txt_arrays)
            snapshot_ziran(targets,hit_txt_arrays)

        if tianniran_count > 12:
            hit_txt_arrays = []
            targets = ocr_text_target_coords_arrays("点", 51, 434, 559, 494, hit_txt_arrays)
            snapshot_ziran(targets, hit_txt_arrays)


        for item in tianniran_ocr_txt_arrays:
            ocr_check_and_add_attr(item,"热门女号自染")
            ocr_check_and_add_attr(item,"热门男号自染")

        kktouch2(67+10, 42+37,1,"退出青丝管霓")

        kktouch2(143+10, 659+37,1,"国色")
        save_cropped_screen(f"国色_其他物品")
        #获取国色值
        get_attr_ocr_number("国色值",843, 47,1130, 95)


        # kktouch2(65+10, 35+37,1,"左侧退出国色")
        kk_keyevent("{ESC}", 1, 0.2,"左侧退出国色")
        kkLogger_log("馆藏结束")
#######################################################################################################################
def snapshot_ziran(targets,hit_txt_arrays):
    i = 0
    for item in targets:
        kktouch2(item[0], item[1], 1, "方案")
        save_cropped_area_screen(f"自染头_天赏外观", item[0] - 77, item[1] - 154, item[0] + 43, item[1] + 45)
        kkLogger_log(f"hit_txt_arrays:{hit_txt_arrays[i]}")
        pic_width=149
        pic_height=115
        if "2点" == hit_txt_arrays[i]:
            save_cropped_area_screen(f"自染方案_天赏外观", 845, 242, 845+pic_width, 242+pic_height)
        elif "4点" == hit_txt_arrays[i]:
            save_cropped_area_screen(f"自染方案_天赏外观", 845, 242, 845+pic_width, 242+pic_height)
            save_cropped_area_screen(f"自染方案_天赏外观", 700, 427, 962+pic_width, 352+pic_height)
        else:
            save_cropped_area_screen(f"超过4点自染方案_天赏外观", 664, 104, 664+pic_width, 104+pic_height)
        i = i + 1

# def snapshot_ziran(targets,hit_txt_arrays):
#     i = 0
#     for item in targets:
#         kktouch2(item[0], item[1], 1, "点")
#         save_cropped_area_screen(f"自染头_天赏外观", item[0] - 28, item[1] - 20, item[0] + 93, item[1] + 177)
#         kkLogger_log(f"hit_txt_arrays:{hit_txt_arrays[i]}")
#         pic_width=149
#         pic_height=115
#         if "2点" == hit_txt_arrays[i]:
#             save_cropped_area_screen(f"自染方案_天赏外观", 845, 242, 845+pic_width, 242+pic_height)
#         elif "4点" == hit_txt_arrays[i]:
#             save_cropped_area_screen(f"自染方案_天赏外观", 845, 242, 845+pic_width, 242+pic_height)
#             save_cropped_area_screen(f"自染方案_天赏外观", 700, 427, 962+pic_width, 352+pic_height)
#         else:
#             save_cropped_area_screen(f"超过4点自染方案_天赏外观", 664, 104, 664+pic_width, 104+pic_height)
#         i = i + 1
############################################################################
def waiguan_substep_shizhuang(is_skip):
    if is_skip == 0:
        # kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        # if kktouch2(1156, 193, 1, "外观") is False:
        #     return
        # connect_device("Windows:///?title_re=逆水寒手游模拟器")

        if local_ocr_text_target_coords("点",12,530,142,619) is not None:
            sync_current_snapshot("发现引导",None,True)
            kk_keyevent("{ESC}",1,1,"取消引导")
            check_game_alert_note()


        kkLogger_log("开始时装")
        kktouch3(258, 718,1,"底部时装")
        kkLogger_log("开始套装")
        kktouch3(61+10, 124+37,1,"套装")
        kktouch3(451, 653,2,"套装回到顶部")
        swipe_to_end((108, 217, 478, 615),(294, 233),(294, 537))

        kktouch3(405, 296,2,"右上第一个套装")

        sync_current_snapshot("外观-套装")

        split_start_x = 109
        split_start_y = 217
        split_end_x = 478
        split_end_y = 615
        for i in range(60):

            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),split_start_x, split_start_y,split_end_x, split_end_y)

            if find_lock_result is None:
                kkLogger_log(f"套装第{i}页")

                if i % 3 == 0:
                    kktouch3(370, 630,0.02,"右下套装")
                elif i % 3 == 1:
                    kktouch3(277, 630,0.02,"底中套装")
                else:
                    kktouch3(163, 630, 0.02, "底左套装")

                if save_cropped_area_screen_and_ocr_add_to_get("套装",split_start_x, split_start_y,split_end_x, split_end_y,[2,3]):
                    sync_current_snapshot("套装结束。")
                    kktouch2(204, 76, 1, "套装重置")
                    kktouch2(1161, 389, 1, "确认套装重置")
                    break

                kktouch3(940, 715,0.1,"底部周边")
                kktouch3(258, 718,1,"底部时装")
            else:
                kkLogger_log(f"套装识别到锁")
                sleep(2)
                save_cropped_area_screen_and_ocr_add_to_get("套装",split_start_x, split_start_y,split_end_x, split_end_y,[2,3],Template(r"tpl1727075488904.png", threshold=0.7))

                sync_current_snapshot("套装结束")

                kktouch2(204, 76,1,"重置")
                kktouch2(1161, 389,1,"确认重置")
                break
        # sync_current_snapshot(f"{waiguan_set}",None,False)
####################################################
def waiguan_substep_fashi(is_skip):
    if is_skip == 0:
        kkLogger_log("开始时装-发式")
        kktouch3(62+10, 406,1.2,"左侧发式")
        # kktouch3(62 + 10, 366 + 37, 1.2, "左侧发式")
        sync_current_snapshot("外观-发式")
        #滚动到顶部
        kktouch3(451, 653,2,"回到顶部")
        swipe_to_end((108, 217, 478, 615),(294, 233),(294, 537))
        kktouch3(451, 653,2,"回到顶部")
        kktouch3(405, 296,2,"右上第一个")

        split_start_x = 109
        split_start_y = 217
        split_end_x = 478
        split_end_y = 615
        for i in range(60):
            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),split_start_x, split_start_y,split_end_x, split_end_y)
            if find_lock_result is None:
                kkLogger_log(f"发式第{i}页")

                if i % 2 == 0:
                    kktouch3(277, 630,0.02,"底中")
                else:
                    kktouch3(367, 630,0.02,"底右")

                # if save_cropped_area_screen_and_ocr_add_to_get("发式",split_start_x, split_start_y,split_end_x, split_end_y,[2,3],Template(r"tpl1727075488904.png", threshold=0.75)):
                if save_cropped_area_screen_and_ocr_add_to_get("发式",split_start_x, split_start_y,split_end_x, split_end_y,[2,3]):
                    sync_current_snapshot("发式结束.")

                    kktouch2(204, 76, 1, "重置")
                    kktouch2(1161, 389, 1, "确认重置")
                    break

                # if i % 2 == 0:
                #     kktouch2(413 + 10, 563 + 37, 0.1, "右下发式")
                # else:
                #     kktouch2(281 + 10, 563 + 37, 0.1, "底中发式")

                kktouch3(67+10, 125+37,0.5,"套装")
                kktouch3(56+10, 372+37,0.5,"发式")
            else:
                kkLogger_log(f"发式识别到锁")
                sleep(2)
                save_cropped_area_screen_and_ocr_add_to_get("发式",split_start_x, split_start_y,split_end_x, split_end_y,[2,3],Template(r"tpl1727075488904.png", threshold=0.7))

                sync_current_snapshot("发式结束")

                kktouch2(204, 76, 1, "重置")
                kktouch2(1161, 389, 1, "确认重置")
                break
        # sync_current_snapshot(f"{waiguan_set}", None, False)
####################################################
def waiguan_substep_wuqi(is_skip):
    if is_skip == 0:
        kktouch3(536+10, 676+37,1,"底部武器")#..底部武器
        kktouch3(536+10, 676+37,1,"底部武器")#..底部武器
        kktouch3(66+10, 121+37,1,"分块")
        kktouch3(66+10, 121+37,1,"分块")
        kktouch3(56+10, 486+37,1,"套装")


        sync_current_snapshot("外观-武器")

        kkLogger_log("开始武器")
        kktouch3(430+10, 618+37,2,"武器回到顶部")
        swipe_to_end((110, 178,466, 604),(287, 200),(287, 500))
        for i in range(11):
            find_lock_result = find_subImg(Template(r"tpl1727007667521.png", threshold=0.7),108, 206,462, 624)

            if find_lock_result is None:
                kkLogger_log(f"武器第{i}页")
                save_cropped_area_screen_and_ocr_add_to_get("武器",110, 178,472, 604,[3,2],Template(r"tpl1727007667521.png", threshold=0.7))

                swipe_and_judge_end((110, 178, 466, 604), (287, 656), (287, 199))

                # if swipe_and_judge_end((110, 178,466, 604),(287, 656),(287, 199)):
                #     sync_current_snapshot("武器结束。")
                #     kktouch2(204, 76, 1, "套装重置")
                #     kktouch2(1161, 389, 1, "确认套装重置")
                #     break
                sleep(1)
            else:
                kkLogger_log(f"武器识别到锁")
                sleep(2)
                save_cropped_area_screen_and_ocr_add_to_get("武器",110, 178,472, 604,[3,2],Template(r"tpl1727007667521.png", threshold=0.7))

                sync_current_snapshot("武器结束")

                kktouch2(204, 76, 1, "重置")
                kktouch2(1161, 389, 1, "确认重置")
                break

        # sync_current_snapshot(f"{waiguan_set}", None, False)
####################################################
def waiguan_substep_zhuoqi(is_skip):
    if is_skip == 0:
        kktouch3(671+10, 679+37,1,"底部坐骑")#..底部坐骑
        kktouch3(671+10, 679+37,1,"底部坐骑")#..底部坐骑
        kktouch3(65+10, 126+37,1,"左侧坐骑")#..坐骑
        kktouch3(65+10, 126+37,1,"左侧坐骑")#..坐骑

        sync_current_snapshot("外观-坐骑")
        kkLogger_log("开始坐骑")
        kktouch3(430+10, 616+37,2,"坐骑回到顶部")
        swipe_to_end((110, 215,470, 615),(230, 215+30),(230, 500))
        start_x = 114
        start_y = 215
        end_x = 477
        end_y = 615
        for i in range(40):

            find_lock_result = find_subImg(Template(r"tpl1726951381717.png", threshold=0.75),102, 258,467, 630)

            if find_lock_result is None:
                kkLogger_log(f"坐骑第{i}页")
                kktouch3(365, 637,0.1,"右下坐骑")
                if save_cropped_area_screen_and_ocr_add_to_get("坐骑",start_x, start_y,end_x, end_y,[2,3]):
                    sync_current_snapshot("坐骑结束。")
                    kktouch2(204, 76, 1, "套装重置")
                    kktouch2(1161, 389, 1, "确认套装重置")
                    break

                kktouch3(63, 237,0.1,"祥瑞")
                kktouch3(67, 161,0.1,"坐骑")
            else:
                kkLogger_log(f"坐骑识别到锁")
                sleep(2)
                save_cropped_area_screen_and_ocr_add_to_get("坐骑",start_x, start_y,end_x, end_y,[2,3],Template(r"tpl1726951381717.png", threshold=0.7))

                sync_current_snapshot("坐骑结束")
                kktouch2(204, 76, 1, "重置")
                kktouch2(1161, 389, 1, "确认重置")
                break
        # sync_current_snapshot(f"{waiguan_set}", None, False)
########################################
def waiguan_substep_xiangrui(is_skip):
    if is_skip == 0:
        kktouch3(671+10, 679+37,1,"底部坐骑")#..底部坐骑
        kktouch3(671+10, 679+37,1,"底部坐骑")#..底部坐骑
        kktouch3(61+10, 210+37,1,"左侧祥瑞")#..坐骑
        kktouch3(61+10, 210+37,1,"左侧祥瑞")#..坐骑
        kkLogger_log("开始祥瑞")
        sync_current_snapshot("外观-祥瑞")
        kktouch3(431+10, 619+37,2,"祥瑞回到顶部")
        swipe_to_end((110, 215,470, 615),(230, 215+30),(230, 615))
        kktouch3(410+10, 290+37,2,"右上第一个祥瑞")

        start_x = 114
        start_y = 215
        end_x = 477
        end_y = 615
        for i in range(40):

            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),102, 186,472, 598)

            if find_lock_result is None:
                kkLogger_log(f"祥瑞第{i}页")
                kktouch3(308, 629,0.1,"右下祥瑞")
                save_cropped_area_screen_and_ocr_add_to_get("祥瑞",start_x, start_y,end_x, end_y,[2,3])
                kktouch3(67, 161,0.1,"坐骑")
                kktouch3(63, 237,0.1,"祥瑞")
            else:
                kkLogger_log(f"祥瑞识别到锁")
                sleep(2)
                save_cropped_area_screen_and_ocr_add_to_get("祥瑞",start_x, start_y,end_x, end_y,[2,3],Template(r"tpl1727075488904.png", threshold=0.75))

                sync_current_snapshot("祥瑞结束")

                kktouch2(204, 76, 1, "重置")
                kktouch2(1161, 389, 1, "确认重置")
                break
        # sync_current_snapshot(f"{waiguan_set}", None, False)
#########################################################
def waiguan_substep_jueji(is_skip):
    if is_skip == 0:
        start_x = 1066
        start_y = 183
        end_x = 1243
        end_y = 288
        kktouch3(802+10, 687+37,1,"底部武功")#..底部武功
        kktouch3(67+10, 240+37,1,"左侧绝技")#..绝技

        sync_current_snapshot("外观-绝技")

        kktouch2(201, 223,1,"冰火绝灭")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技冰火绝灭_天赏外观", start_x, start_y, end_x, end_y)
            kkLogger_log("识别到 冰火绝灭")
            add_to_get("明河曙天·长鲸天斗")
            waiguan_ts_jineng_set.append("明河曙天·长鲸天斗")



        kktouch3(297, 223,1,"残心三绝剑")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技残心三绝剑_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 残心三绝剑")
            add_to_get("天曦四象·朱雀")
            waiguan_ts_jineng_set.append("天曦四象·朱雀")



        kktouch3(196, 343,1,"剑破乾坤")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技剑破乾坤_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 剑破乾坤")
            add_to_get("日曜八荒·轩辕剑")
            waiguan_ts_jineng_set.append("日曜八荒·轩辕剑")



        kktouch3(295, 346,1,"万剑绝")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技万剑绝_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 万剑绝")
            add_to_get("山海祠神·阳升羲和")
            waiguan_ts_jineng_set.append("山海祠神·阳升羲和")


        kktouch3(287, 468,1,"长歌献君")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            # if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技长歌献君_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 长歌献君")
            add_to_get("重蝶化梦")
            waiguan_ts_jineng_set.append("重蝶化梦")


        kktouch3(180, 590,1,"狂发一怒")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技狂发一怒_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 狂发一怒")
            add_to_get("青丘雪")
            waiguan_ts_jineng_set.append("青丘雪")


        kktouch3(292, 590,1,"红莲焚夜")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技红莲焚夜_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 红莲焚夜")
            add_to_get("天曦四象·白虎")
            waiguan_ts_jineng_set.append("天曦四象·白虎")

        # 滚动窗口
        pyautogui.scroll(-20)  # 向下滚动
        sleep(5)

        kktouch3(192, 585,1,"繁花一梦")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技繁花一梦_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 繁花一梦")
            add_to_get("天曦四象·青龙")
            waiguan_ts_jineng_set.append("天曦四象·青龙")


        kktouch3(288, 585,1,"九天雷引")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技九天雷引_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 九天雷引")
            add_to_get("岁星行渡·千重焰")
            waiguan_ts_jineng_set.append("岁星行渡·千重焰")


        swipe_to_end((142, 181, 353, 403), (244, 601), (244, 258))
        sleep(5)



        kktouch3(190, 225,1,"法天象地")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技法天象地_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 法天象地")
            add_to_get("降魔破邪·金刚明王")
            waiguan_ts_jineng_set.append("降魔破邪·金刚明王")


        kktouch3(293, 225,1,"相夷太剑")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技相夷太剑_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 相夷太剑")
            add_to_get("明河曙天·醉卧西海")
            waiguan_ts_jineng_set.append("明河曙天·醉卧西海")

        kktouch3(191, 351, 1, "剑霄飞流")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技剑霄飞流_天赏外观", start_x, start_y, end_x, end_y)
            kkLogger_log("识别到 剑霄飞流")
            add_to_get("剑鸣天寰")
            waiguan_ts_jineng_set.append("剑鸣天寰")


        kktouch3(287, 349,1,"星火漫天")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技星火漫天_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 星火漫天")
            add_to_get("妖影寻灵·狐舞青丘")
            waiguan_ts_jineng_set.append("妖影寻灵·狐舞青丘")


        kktouch3(291, 472,1,"剑魂冲销")

        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技剑魂冲销_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 剑魂冲销")
            add_to_get("丹青纵横·龙潭墨云")
            waiguan_ts_jineng_set.append("丹青纵横·龙潭墨云")

        kktouch3(242, 596, 1, "万想鹰扬")
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            save_cropped_area_screen("武功绝技万想鹰扬_天赏外观", start_x, start_y, end_x, end_y)
            kkLogger_log("识别到 万想鹰扬")
            add_to_get("百鬼问道·冥幽狼影")
            waiguan_ts_jineng_set.append("百鬼问道·冥幽狼影")


        sync_current_snapshot(f"天赏技能：{waiguan_ts_jineng_set}", None, False)
        ##########################################################################

        kktouch3(65+10, 334+37,4,"左侧江湖")#..江湖
        sleep(5)

        kktouch3(285, 466, 1, "辉雪寒瑛")
        coords = ocr_text_target_coords("雪",117, 420,380, 663)
        if coords is not None:
            touch(coords)
            kkLogger_log(".. 辉雪寒瑛")
            sleep(1)

        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            kkLogger_log("识别到 辉雪寒瑛")
            save_cropped_area_screen("武功江湖辉雪寒瑛_天赏外观",start_x, start_y,end_x, end_y)
            add_to_get("山海祠神·赤凰重明")
            waiguan_ts_jineng_set.append("山海祠神·赤凰重明")


        kktouch3(191, 597, 1, "芳心妙愈")
        coords = ocr_text_target_coords("心",117, 420,380, 663)
        if coords is not None:
            touch(coords)
            kkLogger_log("..芳心妙愈")
            sleep(1)
        if local_ocr_text_target_coords("石", 861, 614, 1237, 663) is None:
            kkLogger_log("识别到 芳心妙愈")
            save_cropped_area_screen("武功江湖芳心妙愈_天赏外观",start_x, start_y,end_x, end_y)
            add_to_get("新世华霓·喵到病除")
            waiguan_ts_jineng_set.append("新世华霓·喵到病除")

    ###############################################################
        kktouch3(65+10, 425+37,1,"左侧轻功")#..轻功
#         save_cropped_screen(f"轻功")
        kkLogger_log("开始轻功")

        swipe_to_end((876, 181,1238, 511),(1055, 179),(1055, 492))
        # sync_current_snapshot("外观-轻功1")
        save_cropped_area_screen_and_ocr_add_to_get("武功-轻功",876, 181,1238, 511,[3,2],Template(r"tpl1727017209837.png", threshold=0.6))

        swipe_to_end((876, 181,1238, 511),(1055, 492),(1055, 179))
        # sync_current_snapshot("外观-轻功2")
        save_cropped_area_screen_and_ocr_add_to_get("武功-轻功",876, 275,1240, 608,[3,2],Template(r"tpl1727017209837.png", threshold=0.6))

    # sync_current_snapshot(f"{waiguan_set}", None, False)

################################################
def step_waiguan(is_skip):
    if is_skip == 0:
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1156 , 193 , 1, "外观") is False:
            return

        waiguan_substep_shizhuang(0)
        sleep(3)
        try:
            sync_current_snapshot(f"套装：{waiguan_taozhuang_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)

        check_account_login_other()
        waiguan_substep_fashi(0)
        sleep(3)
        try:
            sync_current_snapshot(f"发式：{waiguan_fashi_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)

        check_account_login_other()
        waiguan_substep_wuqi(0)
        sleep(3)
        try:
            sync_current_snapshot(f"武器：{waiguan_wuqi_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)

        check_account_login_other()
        waiguan_substep_zhuoqi(0)
        sleep(3)
        try:
            sync_current_snapshot(f"坐骑：{waiguan_zuoqi_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)

        check_account_login_other()
        waiguan_substep_xiangrui(0)
        sleep(3)
        try:
            sync_current_snapshot(f"祥瑞：{waiguan_xiangrui_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)

        check_account_login_other()
        waiguan_substep_jueji(0)
        sleep(3)
        try:
            sync_current_snapshot(f"轻功：{waiguan_qingong_set}", None, False)
        except Exception as e:
            sync_current_snapshot(f"sync_current_snapshot error {e}",None,False)


        check_and_try_game_home()
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1156, 193, 1, "外观") is False:
            return

        try:
            waiguan_substep_guancang1025(0)
        except Exception as e:
            sync_current_snapshot(f"waiguan_substep_guancang1025 error {e}",None,False)

        kk_keyevent("{ESC}", 1, 0.5, "左上退出外观")
    return get_return_taskEvent("step_waiguan", EventStatus.SUCCESS,"外观完成")
################################################

######################################################
#################################################
def safe_step_kaifang_shijie(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游模拟器")
            return step_kaifang_shijie(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_kaifangshijie error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_kaifang_shijie", EventStatus.FAILURE)
###################
def step_kaifang_shijie(step_kaifang_shijie):
    if step_kaifang_shijie == 0:
        kktouch2(1231+10, 33+37,2,"菜单")#..菜单
        if kktouch2(1161, 496,5,"开放世界") is False:
            return

        kktouch2(284, 621,1,"地名小三角")#..小三角

        swipe_to_end((220, 60, 465, 385),(302, 58),(302, 536))
        save_cropped_screen("开放世界")

        kk_keyevent("{ESC}", 1, 0.2, "退出开放世界")

        kkLogger_log("开放世界结束")
    return get_return_taskEvent("step_kaifang_shijie", EventStatus.SUCCESS)
##########################################################
def safe_step_qunxia(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游模拟器")
            return step_qunxia(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_qunxia error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_qunxia", EventStatus.FAILURE)
##########################################################
def step_qunxia(step_qunxia):
    if step_qunxia == 0:
        kktouch2(1231+10, 33+37,2,"菜单")#..菜单

        if kktouch2(894, 541,1,"群侠") is False:
            return

        kktouch2(64+10, 126+37,1,"左侧群侠")#..左侧群侠
        kktouch2(240+10, 353+37,1,"左侧第一个人")#..第一个人物
        kktouch2(279+10, 395+37,1,"左侧展开箭头")#..展开
        save_cropped_screen("群侠")
        for i in range(4):
            find_full_qunxia_result = find_all_subImg(Template(r"tpl1727019178804.png", threshold=0.89),37, 127,702, 668)
            full_qunxia_count = 0
            if find_full_qunxia_result is None:
                full_qunxia_count = 0
            else:
                for line in find_full_qunxia_result:
                    kkLogger_log(line)
                    if line['confidence'] > 0.89:
                        full_qunxia_count = full_qunxia_count +1
                        kktouch3(line['result'][0]+37,line['result'][1]+127,1,f"第{full_qunxia_count}个满级群侠")
                        ocr_add_to_get(96, 45,225, 110)

            is_end = swipe_and_judge_end_fast((37, 87,708, 262),(258, 529), (254, 109))
            if is_end:
                break

        kk_keyevent("{ESC}", 2, 0.2, "退出群侠")

        kkLogger_log("群侠结束")
    return get_return_taskEvent("step_qunxia", EventStatus.SUCCESS,"群侠完成")
##########################################################
#################################################
def safe_step_zhuangyuan(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游模拟器")
            return step_zhuangyuan(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_zhuangyuan error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_zhuangyuan", EventStatus.FAILURE)
###################
def step_zhuangyuan(step_zhuangyuan):
    if step_zhuangyuan == 0:
        kktouch2(1231+10, 33+37,1,"菜单")#..菜单
        if kktouch2(892, 181,2,"庄园") is False:
            return

        save_cropped_screen_with_blur("庄园",230, 80, 680, 504)
        # kktouch2(67+10, 38+37,1,"左上退出")
        kk_keyevent("{ESC}", 1, 0.2, "退出庄园")

        kkLogger_log("庄园结束")
    return get_return_taskEvent("step_zhuangyuan", EventStatus.SUCCESS,"庄园完成")
#######################################################################
#################################################
def safe_step_lingcong(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=逆水寒手游模拟器")
            return step_lingcong(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_lingcong error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_lingcong", EventStatus.FAILURE)
#########################################################################
def step_lingcong(step_lingcong):
    if step_lingcong == 0:
        kktouch2(1231+10, 33+37,1,"菜单")
        if kktouch2(1246, 333,2,"宠物") is False:
            return get_return_taskEvent("step_lingcong", EventStatus.SUCCESS,"没有宠物")

        if ocr_text_target_coords("跳过",1124-100,10,1124+100,63+100) is not None:
            kk_keyevent("{ESC}", 1, 1, "跳过教学")
            # kktouch2(1124,63,1.2,"跳过教学")
            kktouch2(796, 470, 1.2, "确定")
            # kk_keyevent("{ESC}", 1, 0.2, "退出灵宠")


        if ocr_text_target_coords("跳过",1124-100,10,1124+100,63+100) is not None:
            # kktouch2(1124,63,1.2,"跳过教学")
            kk_keyevent("{ESC}", 1, 1, "跳过教学")
            kktouch2(796, 470, 1.2, "确定")

        kktouch2(696, 615, 1, "空白，跳过引导")
        kktouch2(696, 615, 1, "空白，跳过引导")
        kktouch2(451, 257, 2, "灵宠")
        kktouch2(696, 615, 1, "空白，跳过引导")
        kktouch2(696, 615, 1, "空白，跳过引导")

        find_lingcong_result = find_subImg(Template(r"tpl1727025466427.png"),1136, 41,1269, 111)
        if find_lingcong_result:
            save_cropped_screen_with_mulblur("灵宠_天赏外观",
                                             [ (56,253-40,184,277-40),
                                               (56,383-40,184,416-40),
                                               (56,519-40,184,553-40),
                                               (56,658-40,184,688-40),
                                               (425, 121-40, 691, 173-40)])
            loop = 136
            start_x = 118
            start_y = 195
            for j in range(3):
                kktouch2(start_x,start_y,1.2,"第一个灵宠")
                if ocr_text_target_coords("天狼",600,80,1000,400) is not None:
                    ocr_check_and_add_attr("灵宠·天狼星","天赏宠物")
                elif ocr_text_target_coords("天鹰",600,80,1000,400) is not None:
                    ocr_check_and_add_attr("灵宠·天鹰座", "天赏宠物")
                elif ocr_text_target_coords("月",600,80,1000,400) is not None:
                    ocr_check_and_add_attr("灵宠·心月狐", "天赏宠物")
                start_y = start_y + loop


        kk_keyevent("{ESC}", 1, 0.2, "退出灵宠")
        kkLogger_log("宠物结束")

    step_tianshangshi(0)
    step_chongzhi(0)
    step_shenqi(0)
    get_zhiye(0)
    if "踏歌" in waiguan_fashi_set:
        ocr_check_and_set("性别", "男")
    elif "俏也" in waiguan_fashi_set:
        ocr_check_and_set("性别", "女")
    #-------------------------------------------------------------------------------------
    try:
        check_and_try_game_home(4, "logout")
        kktouch2(1227 + 10, 35 + 37, 1, "菜单")
        kktouch3(1248, 466, 2, "右侧设置")
        kktouch3(73, 163, 2, "左侧系统")

        if local_ocr_text_target_coords("切换",224,293,442,345):
            kktouch3(585, 316, 2, "设置中切换账号")
            ocr_result = local_ocr_text_target_coords("@", 575, 286, 806, 344, False)
            if ocr_result is not None:
                ocr_check_and_set("账号类型", "邮箱账号")
                sync_current_snapshot("账号信息:邮箱账号", None, True)
                print("识别为 邮箱账号")
            else:
                ocr_check_and_set("账号类型", "手机账号")
                print("识别为 手机账号")
                sync_current_snapshot("账号信息：手机账号", None, True)

            kktouch3(802, 157, 2, "关闭切换账号")
            kk_keyevent("{ESC}", 1, 1, "回到主页")
        elif luhao_task.get("gameAccount",None):
            sync_current_snapshot("未识别到账号信息", None, True)
            account = luhao_task['gameAccount']
            if "@" in account:
                ocr_check_and_set("账号类型", "邮箱账号")
            else:
                ocr_check_and_set("账号类型", "手机账号")

    except Exception as e:
        print(e)
    #-------------------------------------------------------------------------------
    baili_chuanyin()

    return get_return_taskEvent("step_lingcong", EventStatus.SUCCESS,"灵宠完成")
##################################################################################
def baili_chuanyin():
    try:
        is_ok = False
        product_info = luhao_task.get("product_info",None)
        if product_info is None:
            return

        for item in product_info["productAttributeValueList"]:
            if item['value'] == "同意传音上架看看":
                is_ok = True
                break

        if not is_ok:
            return

        check_and_try_game_home()
        kktouch3(268,687,1,"进入")
        # if ocr_text_target_coords("传",534,557,592,683) is None:
        #     return
        kktouch3(563,619,1,"传音")
        kktouch3(563,619,1,"传音")

        if ocr_text_target_coords("百",772,271,955,335) is None:
            return
        sync_current_snapshot("传音开始", None, False)

        kktouch3(856,390,1,"百里传音")
        kktouch3(876,598,1,"空白")

        number = get_ocr_number(1017,275,1150,331)
        if "" != number and int(number) > 1:
            kktouch3(779,497,1,"喊话输入框")
            text(f"脱坑，已挂看看zh，{luhao_task['productSn']}，可刀")
            sync_current_snapshot("传音", None, True)
            kktouch3(1154, 596, 1, "发送喊话")
    except Exception as e:
        print(e)
        print("传音失败")

###################################
def get_product_meta_attr_inputList_array(attr_name):
    try:
        for item in product_meta:
            if attr_name == item["name"]:
                return item["inputList"].split(',')
    except Exception as e:
        print(f"get_product_meta_attr_inputList_array error: {e}")
        return []
    return []
#####################################
def get_product_meta_attr_value(attr_name):
    try:
        for item in product_meta:
            if attr_name == item["name"]:
                return item.get("value",None)
    except Exception as e:
        print(f"get_product_meta_attr_value error: {e}")
        return None
    return None
##############################################################
def get_product_meta_qufu(ocr_txt):
    for item in product_meta:
        if "区服" == item["name"]:
            data = json.loads(item["inputList"])
            for it in data:
                parent_name = it["parent_name"]
                if ocr_txt in it["childList"]:
                    return f"{parent_name}|{ocr_txt}"

    return ""
#######################################
def get_zhiye(is_skip):
    try:
        if is_skip == 0:
            check_and_try_game_home()
            # kktouch3(1541, 1024,1,"交友")
            kkpress("o","交友",1)
            if wait_for_ocr_txt("对手",3,1313,314,1523,383) is False:
                sync_current_snapshot("进入交友失败",None,True)
                return
            kktouch3(1351, 268,1,"头像")

            sync_current_snapshot(f"角色", None, True)
            # get_attr_ocr_number("角色等级", 585, 637, 682, 675)
            ocr_result_zhiye = ocr_text(222, 413, 296, 442)

            if ocr_result_zhiye:
                for line in ocr_result_zhiye:
                    if line is None:
                        continue
                    for word_info in line:
                        if word_info is None:
                            continue
                        else:
                            get_ocr_txt = word_info[1][0]
                            get_ocr_txt = get_ocr_txt.strip()

                            if len(get_ocr_txt) > 1 and get_ocr_txt in get_product_meta_attr_inputList_array("职业"):
                                print(f"识别到职业：{get_ocr_txt}")
                                ocr_check_and_set("职业", get_ocr_txt)
                                sync_current_snapshot(f"识别职业：【{get_ocr_txt}】", None, False)
                                break
                            else:
                                kkLogger_log(f"step_zhiye [{get_ocr_txt}]，未识别到职业")
            else:
                kkLogger_log(f"step_zhiye ，未识别到职业")
            #-------------------------------------------------------

    except Exception as e:
        kkLogger_log(f"step_zhiye ，error {e}")
#######################################################################
def get_first_ocr_txt(start_x,start_y,end_x,end_y):
    first_txt = "None"
    try:
        ocr_result = ocr_text(start_x, start_y, end_x, end_y)
        if ocr_result:
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    if word_info is None:
                        continue
                    else:
                        get_ocr_txt = word_info[1][0]
                        get_ocr_txt = get_ocr_txt.strip()
                        if len(get_ocr_txt) > 0:
                            first_txt = get_ocr_txt
                            print(f"识别到：{first_txt}")
                            return first_txt
        else:
            print(f"未识别到文字")
            return first_txt
    except Exception as e:
        return first_txt
##########################################################
def get_qufu(is_skip):
    qufu_tp = ""
    try:
        if is_skip == 0:
            ocr_result = ocr_text(398, 307, 563, 342)
            if ocr_result:
                for line in ocr_result:
                    if line is None:
                        continue
                    for word_info in line:
                        if word_info is None:
                            continue
                        else:
                            get_ocr_txt = word_info[1][0]
                            get_ocr_txt = get_ocr_txt.strip()
                            if len(get_ocr_txt) > 1:
                                print(f"识别到区服：{get_ocr_txt}")
                                qufu_tp = get_product_meta_qufu(get_ocr_txt)
                                if "|" in qufu_tp:
                                    ocr_check_and_set("区服", qufu_tp)
                                return qufu_tp
                            else:
                                kkLogger_log(f"get_qufu [{get_ocr_txt}]，未识别到区服")
            else:
                kkLogger_log(f"get_qufu ，未识别到区服")
                return qufu_tp
        return False
    except Exception as e:
        kkLogger_log(f"get_qufu ，error {e}")
        return qufu_tp
######################################################
def step_shenqi(is_skip):
    try:
        if is_skip == 0:
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            kktouch2(1114, 331,0.5,"千机玄甲")
            sleep(1)
            kktouch2(618,661,1.2,"我知道啦")
            save_cropped_screen("神器")

            kk_keyevent("{ESC}", 1, 0.2, "神器")

        return get_return_taskEvent("step_shenqi", EventStatus.SUCCESS)
    except Exception as e:
        kkLogger_log(f"step_shenqi 失败：{e}")
        kk_keyevent("{ESC}")
        return get_return_taskEvent("step_shenqi", EventStatus.FAILURE)
#######################################################################
def step_chongzhi(is_skip):
    try:
        if is_skip == 0:
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            kktouch2(1167, 722,3,"攻略")

            for i in range(2):
                connect_device("windows:///?title_re=.*Version.*")

                # kktouch3(1216,87,1,"关闭弹窗")

                kktouch2(505+10, 639+37,2,"输入框")
                text("累计充值")
                kktouch2(1090+10, 639+37,2,"发送")
                sleep(1)
                coords = ocr_text_target_coords("元",263, 25,1219, 599)
                if coords is not None:
                    get_attr_ocr_number("充值金额",283, 12,1230, 527)
                    sync_current_snapshot("充值金额", None, True)
                    break

            for i in range(2):
                kktouch2(505+10, 639+37,2,"输入框")
                text("赠礼额度")
                kktouch3(1090+10, 639+37,3,"发送")
                coords = ocr_text_target_coords("赠礼额度查询",296, 269,1219, 599)
                if coords is not None:
                    kktouch3(coords[0],coords[1],3,"赠礼额度查询")
                    save_cropped_screen("赠礼额度")
                    break

            # kktouch2(37, 72,2,"退出小暖问答")
            kk_keyevent("{ESC}", 1, 0.2, "退出小暖问答")
            kkLogger_log("充值查询结束")
        return get_return_taskEvent("step_chongzhi", EventStatus.SUCCESS)
    except Exception as e:
        kkLogger_log(f"step_chongzhi 失败：{e}")
        kktouch2(37+10, 32+37,2,"退出小暖问答")
        return get_return_taskEvent("step_chongzhi", EventStatus.FAILURE)
###################################################################################
def step_zengli_edu(is_skip):
    try:
        if is_skip == 0:
            kktouch2(1231+10, 33+37,1,"菜单")
            kktouch2(1143+10, 680+37,2,"攻略")
            for i in range(2):
                kktouch2(505+10, 639+37,2,"输入框")
                text("赠礼额度")
                kktouch2(1090+10, 639+37,1,"发送")
                sleep(5)
                coords = ocr_text_target_coords("赠礼额度查询",296, 269,1219, 599)
                if coords is not None:
                    kktouch3(coords[0],coords[1],2,"赠礼额度查询")
                    save_cropped_screen("赠礼额度_其他物品")
                    break

            kktouch2(37, 72,2,"退出小暖问答")
            kkLogger_log("赠礼额度查询结束")
        return get_return_taskEvent("step_chongzhi", EventStatus.SUCCESS)
    except Exception as e:
        kkLogger_log(f"step_zengli_edu 失败：{e}")
        kktouch2(37+10, 32,2,"退出小暖问答")
        return get_return_taskEvent("step_zengli_edu", EventStatus.FAILURE)
##############################################################################
def sort_and_remove_number(arr):
    # 首先根据数字大小进行倒序排序
    sorted_arr = sorted(arr, key=lambda x: int(x.split(':')[-1]), reverse=True)
    # 然后遍历排序后的数组，移除冒号以及后面的数字
    cleaned_arr = [item.split(':')[0] for item in sorted_arr]
    return cleaned_arr
#######################################
def sort_by_number_desc(arr):
    # 使用lambda函数和sorted函数进行排序
    # lambda函数提取字符串中最后一个冒号后面的数字，并转换为整数
    # sorted函数的key参数使用上述lambda函数，reverse=True表示倒序
    sorted_arr = sorted(arr, key=lambda x: int(x.split(':')[-1]), reverse=True)
    return sorted_arr

#########################################################################
def step_tianshangshi(step_tianshangshi):
    try:
        if step_tianshangshi == 0:
            tss_value = 0
            tss_item = []
            for item in product_meta:
                if item["type"] not in [1,2]:
                    continue
                else:
                    item_values = item.get("values", [])
                    for item_value in item_values:
                        item_value_2 = remove_unwanted_chars(item_value)
                        # add_value = configs.TSS_VALUE.get(item_value_2, 0)
                        add_value = portal_client.get_tss_value().get(item_value_2, 0)
                        tss_value = tss_value + add_value

                        if add_value > 0:
                            tss_item.append(f"{item_value_2}:{add_value}")

            # kkLogger_log(f"已使用天赏石：{tss_value}")
            ocr_check_and_set_number("已使用天赏石",str(tss_value))
            sync_current_snapshot(f"已使用天赏石：{sort_by_number_desc(tss_item)}：{tss_value}",None,False)

        return get_return_taskEvent("step_tianshangshi", EventStatus.SUCCESS)
    except Exception as e:
        kkLogger_log(f"step_tianshangshi 失败：{e}")
        return get_return_taskEvent("step_tianshangshi", EventStatus.FAILURE)
########################################################################
def step_img_upload(is_skip):
    if is_skip == 0:
        sleep(1)
        for i in range(3):
            try:
                kkLogger_log("开始处理图片")
                image_util.collage(f'{snapImgPath}', save_original=save_original_pic)

                kkLogger_log(f'开始上传')

                upload_kk_img_to_oss(f'{snapImgPath}')

                kkLogger_log(f'上传结束 pic_url_arrays:{pic_url_arrays}')
                return get_return_taskEvent("step_img_upload", EventStatus.SUCCESS,"图片上传完成")
            except Exception as e:
                kkLogger_log(f"图片上传异常 error,{e}")
                sleep(2)
                continue

    return get_return_taskEvent("step_img_upload", EventStatus.FAILURE,"图片上传失败")
###########################################################
def safe_step_meta_upload(is_skip):
    for i in range(3):
        try:
            return step_meta_upload(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_meta_upload error,{e}")
            sleep(2)
            continue

    return get_return_taskEvent("step_meta_upload", EventStatus.FAILURE,"录号数据提交异常")
#################################################################
def step_meta_upload(step_meta_upload):
    global product_meta
    if step_meta_upload == 0:
        kkLogger_log(f"product_meta_json:{product_meta}")
        # kkLogger_log(f"pic_url_arrays:{pic_url_arrays}")
        portal_client.sync_product_info(luhao_task['productSn'], configs.image_server_url + get_head_pic(), pic_url_arrays, product_meta)

    global end_time
    end_time = datetime.datetime.now()
    total_time = end_time - start_time
    minutes = total_time.total_seconds() // 60
    kkLogger_log(f"录号执行了 {minutes} 分钟。")

    return get_return_taskEvent("step_meta_upload", EventStatus.SUCCESS,f"录号数据提交成功，共执行 {minutes} 分钟")
#########################################################################
def step_exit_game(is_skip):
    if is_skip == 0:
        kktouch2(1261,20,2,"关闭游戏")
        kktouch2(1261 , 20 , 2, "关闭游戏")
        kktouch2(1154+10,391+37,1,"确认")
    return get_return_taskEvent("step_meta_upload", EventStatus.SUCCESS,"录号数据提交成功")
#########################################################################
def step_restart_mumu():
    connect_device("windows:///")
    print(f"连接桌面:{device().get_current_resolution()}")
    sys_tool.restart_mumu()
    for i in range(30000):
        sleep(5)
        try:
            if local_ocr_text_target_coords("MuMu模拟器",10,10,1000,700) is None:
                continue
            if local_ocr_text_target_coords("逆水寒",10,10,1000,700) is None:
                continue

            connect_device("Windows:///?title_re=MuMu模拟器12")
            print(f"尝试MuMu模拟器12:{device().get_current_resolution()}")
            if 1288 == device().get_current_resolution()[0]:
                G.DEVICE.move((0, 0))
                if wait_for_ocr_txt("MuMu模拟器", 10,33,2,145,36):
                    sleep(15)
                    break
            else:
                continue
        except Exception as e:
            print(e)
            continue
###############################################
def process_exists(process_name):
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == process_name:
            print(f"{process_name} exist")
            return True
    return False
##################################
def is_between_1_and_9():
    # 获取当前时间
    now = datetime.datetime.now()
    # 获取当前时间的小时部分
    current_hour = now.time().hour

    # 判断当前小时是否在 1 到 10 之间
    if 1 <= current_hour <= 10:
        return True
    else:
        return False

#######################################
def step_restart_nshm(is_skip):
    global width
    global height
    global snapImgPath
    global projectPath
    projectPath = "D:\\kkzhw\\airtest_log"
    snapImgPath = f"{projectPath}"

    if is_skip == 0:
        connect_device("windows:///")
        (screen_width,screen_height) = device().get_current_resolution()
        print(f"连接桌面，尺寸:{(screen_width,screen_height)}")
        sys_tool.restart_nshm()
        sleep(10)
        for i in range(300000):
            sleep(5)
            try:
                connect_device("Windows:///?title_re=逆水寒手游-MuMu模拟器")
                (width,height) = device().get_current_resolution()
                print(f"尝试逆水寒手游-MuMu模拟器:{(width,height)}")
                if width > 1000:
                    G.DEVICE.move((0, 0))
                    wait_for_ocr_txt("启动游戏",120,280,304,1106,650)
                    touch((640,546))

                    sleep(15)
                    #判断客户端是否启动
                    for i in range(300):
                        # sleep(5)
                        try:
                            target = wait_for_ocr_txt("设备", 5, screen_width // 2, 100, screen_width, screen_height)
                            if target is False:
                                continue

                            if target and local_ocr_text_target_coords("选择服务器",target[0]-825,target[1],target[0],target[1]+400) is None:
                                kktouch3(target[0]-445,target[1]-242+(202-162),1,"关闭扫码弹窗")
                                continue

                            connect_device("Windows:///?title_re=.*Version.*")
                            (width2, height2) = device().get_current_resolution()
                            print(f"尝试连接逆水寒手游模拟器 {i}:{(width2, height2)}")
                            if width2 > 1200:
                                print(f"连接逆水寒手游模拟器成功:{(width2, height2)}")
                                G.DEVICE.move((0, 0))
                                sleep(1)
                                connect_device("Windows:///")
                                if local_ocr_text_target_coords("手游", 1, 1, 147, 35) is not None:
                                    print("逆水寒手游模拟器客户端准备就绪")
                                    break
                                else:
                                    continue
                            else:
                                continue
                        except Exception as e:
                            print(e)
                            continue
                    #判断是否界面准备好
                    sleep(1)
                    for i in range(120):
                        # sleep(5)
                        print("判断是否界面准备好")
                        # connect_device("Windows:///")
                        touch((802,202))
                        sleep(1)
                        if local_ocr_text_target_coords("选择服务器",499,456,783,538) is not None:
                            connect_device("Windows:///?title_re=.*Version.*")
                            print("逆水寒手游模拟器界面准备就绪")
                            break
                        else:
                            continue
                    break
                else:
                    continue
            except Exception as e:
                print(e)
                continue
    return True
#####################################################
def step_mumu_logout():
    if has_mumu is False:
        sync_current_snapshot("无mumu模拟器，无需退登",None,False)
        return True

    kkLogger_log("开始退登mumu模拟器云逆水寒手游")
    connect_device("Windows:///?title_re=MuMu模拟器12")
    sleep(1)
    connect_device("Android://127.0.0.1:5037/127.0.0.1:16384?cap_method=JAVACAP")
    for i in range(3):
        # set_current(0)
        target = ocr_text_target_coords("关闭", 479 - 100, 625 - 100, 479 + 100, 625 + 50, False)
        if target is not None:
            kktouch3(target[0], target[1], 1, "关闭实名弹窗2")

        # 登录状态则退出
        target = ocr_text_target_coords("退出", 872, 20, 1270, 214)
        if target is not None:
            kktouch3(target[0], target[1] - 10, 1.2, "右上退出")
            kktouch3(746, 461 - 33, 1.1, "弹窗退出")

        target = ocr_text_target_coords("其他账号登录", 330, 417, 920, 626)
        if target is not None:
            kktouch3(target[0], target[1], 1.2, "其他账号登录")

        target = ocr_text_target_coords("详细", 399, 456, 910, 634)
        if target is not None:
            sync_current_snapshot("云逆水寒退登成功")
            return True
        else:
            stop_app("com.netease.micro.nshm")
            print("停止云逆水寒")
            sleep(3)
            start_app("com.netease.micro.nshm")
            print("启动云逆水寒")
            sleep(10)
    return True

#######################################
def step_logout(is_skip):
    if is_skip == 0:
        global end_time
        end_time = datetime.datetime.now()
        total_time = end_time - start_time
        minutes = total_time.total_seconds() // 60
        for i in range(2):
            try:
                connect_device("windows:///?title_re=.*Version.*")

                if wait_for_ocr_txt("开始", 3, 1444, 861, 1589, 1030):
                    kktouch3(59,80,5,"左上返回")
                    if wait_for_ocr_txt("大神", 5 * 60, 687, 303, 922, 358):
                        kktouch3(871, 677, 1, "返回")

                        if luhao_task.get("memberId", None):
                            sn = luhao_task.get("productSn")
                            portal_client.send_msg(luhao_task.get("memberId", None),
                                                   f'亲爱的小主，您的商品编号【{sn}】，录号已完成，请等待客服审核。通过后请核对信息，如有问题请联系客服！')

                        return get_return_taskEvent("step_logout", EventStatus.SUCCESS,f"录号完成，退登成功，共录制了{minutes}分钟")
                else:
                    check_and_try_game_home(4, "logout")
                    kk_keyevent("{ESC}", 1, 2, "菜单")

                    kktouch3(872,482,1,"重新登录")
                    kktouch3(732,497,1,"确认")

                    if wait_for_ocr_txt("大神",5*60,687,303,922,358):
                        kktouch3(871, 677, 1, "返回")

                        if luhao_task.get("memberId", None):
                            sn = luhao_task.get("productSn")
                            portal_client.send_msg(luhao_task.get("memberId", None),
                                                   f'亲爱的小主，您的商品编号【{sn}】，录号已完成，请等待客服审核。通过后请核对信息，如有问题请联系客服！')

                        return get_return_taskEvent("step_logout", EventStatus.SUCCESS,
                                                    f"录号完成，退登成功，共录制了{minutes}分钟")
                    else:
                        sync_current_snapshot("逆水寒模拟器退登失败，开始重启逆水寒客户端")

                        return get_return_taskEvent("step_logout", EventStatus.FAILURE,
                                                    f"录号完成，退登失败，共录制了{minutes}分钟")

            except Exception as e:
                print(e)
                return get_return_taskEvent("step_logout", EventStatus.FAILURE,
                                            f"录号完成，退登失败")

##############################################################################
def save_zhuangbei_pic(x,y,sleep,msg):
    kktouch3(x, y, sleep, msg)
    target_1 = wait_for_ocr_txt("等级要求", 2, 30, 175, 905, 1050, -1)
    target_2 = wait_for_ocr_txt("装备评分", 2, 30, 175, 905, 1050, -1)
    if target_1 and target_2:
        save_cropped_area_screen(f"角色-{msg}_装备", target_1[0] - 18, target_1[1] - 80, target_1[0] - 18 + 300, target_2[1] + 20, 0.1)
    else:
        print(f"{msg} 定位失败")


#################################################################
def nshpc_step_mianban():

    check_and_try_game_home()
    #
    for i in range(2):
        # kktouch3(1247,1024,1,"角色")
        kkpress("p", "角色")
        if wait_for_ocr_txt("人", 3,44,248,127,420) is False:
            sync_current_snapshot("进入角色失败",None,True)
            continue
        else:
            break

    get_attr_ocr_number("战力",341,771,434,802)

    kktouch3(79, 465, 1, "基本")

    if ocr_text_target_coords("详细", 630, 246, 842, 290) is not None:
        kktouch3(679, 268, 1, "详细属性")
    kkmove(319, 782, "总评分",2, False)
    save_cropped_screen_with_blur2("面板_属性", 43, 232, 1016, 1000, 204, 251, 394, 286)

    save_zhuangbei_pic(277, 496, 1, "装备01_装备")
    save_zhuangbei_pic(425, 387, 1, "装备02_装备")
    save_zhuangbei_pic(353, 423, 1, "装备03_装备")
    save_zhuangbei_pic(355, 576, 1, "装备04_装备")
    save_zhuangbei_pic(502, 549, 1, "装备05_装备")
    save_zhuangbei_pic(452, 600, 1, "装备06_装备")
    save_zhuangbei_pic(402, 646, 1, "装备07_装备")
    save_zhuangbei_pic(348, 700, 1, "装备08_装备")
    save_zhuangbei_pic(205, 534, 1, "装备09_装备")
    save_zhuangbei_pic(238, 569, 1, "装备10_装备")
    save_zhuangbei_pic(279, 609, 1, "装备11_装备")
    save_zhuangbei_pic(308, 647, 1, "装备12_装备")
    save_zhuangbei_pic(469, 421, 1, "装备13_装备")
    save_zhuangbei_pic(503, 456, 1, "装备14_装备")

    # 惊鸿华云
    kktouch3(535, 337, 1, "风华")
    save_cropped_area_screen("惊鸿华韵_属性", 346, 332, 1296, 799)
    kk_keyevent("{ESC}", 1, 0.5, "退出惊鸿华韵")

    ##称号
    kktouch3(550, 268, 1, "称号")
    kkmove(553, 445, "称号",2, False)
    pyautogui.scroll(-120)
    sleep(1)
    save_cropped_area_screen("面板称号_属性", 332, 268, 1045, 831)
    kk_keyevent("{ESC}", 1, 0.5, "退出称号")

    ##旅途
    kktouch3(80, 518, 1, "旅途")
    save_cropped_screen_with_blur2("面板旅途_属性", 40, 239, 593, 827, 191, 385, 358, 422)
    kk_keyevent("{ESC}", 1, 0.5, "退出旅途")

#
# ###################################
def nshpc_step_fuzhi():
    # 副职
    check_and_try_game_home()

    for i in range(2):
        # kkmove(1249, 1024, "角色", 2, 0.5, False)
        # kktouch3(1242, 956, 1, "副职")
        pyautogui.hotkey("ctrl","l")
        sleep(1)
        if wait_for_ocr_txt("切换", 3,507,735,695,834) is False:
            sync_current_snapshot("进入副职失败",None,True)
            continue
        else:
            break

    save_cropped_screen_with_blur2("副职_属性", 277, 233, 958, 912, 719, 260, 922, 363)
    kk_keyevent("{ESC}", 1, 0.5, "退出副职")
##########################
def nshpc_step_jineng():
    ##技能
    check_and_try_game_home()
    for i in range(2):
        # kktouch3(1376, 1021, 1, "技能")
        kkpress("k", "技能", 1)
        if wait_for_ocr_txt("轻功", 3,313,302,386,461) is False:
            sync_current_snapshot("进入技能失败",None,True)
            continue
        else:
            break

    save_cropped_area_screen("技能_属性", 271, 260, 1300, 800)
    kk_keyevent("{ESC}", 1, 0.5, "退出技能")
##########################
def nshpc_step_zhuangyuan():
    # 庄园
    check_and_try_game_home()
    # kktouch3(1416, 1024, 1, "庄园")
    pyautogui.hotkey("ctrl", "z")
    if wait_for_ocr_txt("确认", 3, 633, 398, 974, 551):
        kktouch3(799,510,1,"确认")
        sync_current_snapshot("进入庄园失败", None, True)
        return
    else:
        save_cropped_screen_with_blur2("庄园_其他物品", 285, 247, 1186, 749, 475, 290, 637, 319)
        kk_keyevent("{ESC}", 1, 0.5, "退出庄园")
##########################
def nshpc_step_chognzhi():
    # 充值进度
    check_and_try_game_home()

    target = wait_for_ocr_txt("开服", 4, 1063, 28, 1407, 88)
    if target is False:
        sync_current_snapshot("进入福利失败", None, True)
        return
    kktouch3(target[0] + 84, target[1], 1, "福利")

    # kktouch3(1291, 48, 1, "福利")
    if wait_for_ocr_txt("福利", 3,435,460,632,520) is False:
        sync_current_snapshot("进入福利失败",None,True)
        return

    kktouch3(526, 483, 1, "充值福利")
    kkmove(725, 416, "查看进度", 2, 0.5, False)
    save_cropped_area_screen("充值进度", 362, 259, 1199, 752)
    kk_keyevent("{ESC}", 1, 0.5, "退出福利")

# #################################################
def kkscroll_top(scroll_null=50):
    for i in range(scroll_null):
        pyautogui.scroll(200)
        sleep(0.1)

    tname = ""
    for i in range(100):
        kktouch3(372, 323, 1, "1")
        ttname = get_first_ocr_txt(581, 246, 739, 282)
        if ttname == tname:
            break
        else:
            tname = ttname
        pyautogui.scroll(120)
        sleep(0.1)
###############################################
def kkscroll2(times=6,wait=0.1):
    for i in range(times):
        pyautogui.scroll(-120)
        sleep(wait)
#############################################
def nshpc_step_xiangrui_zuoqi():
    global waiguan_xiangrui_set
    global waiguan_zuoqi_set
    # 祥瑞坐骑
    check_and_try_game_home()

    for i in range(2):
        # kkmove(1249, 1024, "角色", 2, 0.5, False)
        # kktouch3(1233, 910, 1, "角色坐骑")
        kkpress("u","坐骑",1)
        if wait_for_ocr_txt("陆地", 3,276,240,482,279) is False:
            sync_current_snapshot("坐骑失败",None,True)
            continue
        else:
            break

    kktouch3(229, 654, 1, "珍稀祥瑞")
    kkmove(410, 365, "祥瑞", 2, 0.5, False)
    kkscroll_top()

    for i in range(50):
        target_arrays = ocr_text_target_coords_arrays_zuoqi("祥瑞_祥瑞", "等级", 284, 313, 529, 780, None, -1)
        if len(target_arrays) < 6:
            target_arrays2 = ocr_text_target_coords_arrays_zuoqi("祥瑞_祥瑞", "等级", 284, 313, 529, 780, None, -1)
            if len(target_arrays2) > len(target_arrays):
                target_arrays = target_arrays2


        if target_arrays is not None and len(target_arrays) > 0:
            waiguan_xiangrui_set_len = len(waiguan_xiangrui_set)
            for item in target_arrays:
                save_cropped_area_screen_zuoqi("祥瑞_祥瑞", item[0]-72, item[1]-38, (item[0]-72)+236,(item[1]-38)+68)
            if len(waiguan_xiangrui_set) - waiguan_xiangrui_set_len < 3:
                break
        else:
            break
        kkscroll2(6)
    sync_current_snapshot(f"祥瑞：{waiguan_xiangrui_set}", None, True)

##############################################################################################################

    kktouch3(230, 582, 1, "坐骑")
    kkmove(410, 365, "坐骑", 2, 0.5, False)
    kkscroll_top()
    for i in range(50):
        target_arrays = ocr_text_target_coords_arrays_zuoqi("坐骑_祥瑞", "等级", 284, 313, 529, 780, None, -1)
        if len(target_arrays) < 6:
            target_arrays2 = ocr_text_target_coords_arrays_zuoqi("坐骑_祥瑞", "等级", 284, 313, 529, 780, None, -1)
            if len(target_arrays2) > len(target_arrays):
                target_arrays = target_arrays2



        if target_arrays is not None and len(target_arrays) > 0:
            waiguan_zuoqi_set_len = len(waiguan_zuoqi_set)
            for item in target_arrays:
                save_cropped_area_screen_zuoqi("坐骑_祥瑞", item[0] - 72, item[1] - 38, (item[0] - 72) + 236, (item[1] - 38) + 68)
            if len(waiguan_zuoqi_set) - waiguan_zuoqi_set_len < 3:
                break
        else:
            break
        kkscroll2(6)
    sync_current_snapshot(f"坐骑：{waiguan_zuoqi_set}", None, True)


    kk_keyevent("{ESC}", 1, 0.5, "退出坐骑")
##########################
def nshpc_step_waiguan():
    # 外观
    check_and_try_game_home()
    # kktouch3(1459, 1021, 1, "外观")
    kkpress("f12", "时装", 1)
    if wait_for_ocr_txt("有", 3,1149,97,1333,130) is False:
        sync_current_snapshot("进入外观失败",None,True)
        return


    kktouch3(1176, 117, 1, "已拥有")
    kktouch3(1121, 165, 1, "发型")
    target = (1523, 1033)

    long_snapshot_click("发式_衣品外观", 1162, 159, 1578, 1010, target, 2, 0.7, 3)
    sync_current_snapshot(f"发型：{waiguan_fashi_set}", None, False)
        # sleep(3000)
        # kktouch3(1416,113,1,"筛选")
        # kktouch3(904,296,1,"已染稀有")
        # kktouch3(953,493,1,"确定")

    kktouch3(1119, 239, 1, "身体")
    long_snapshot_click("身体_衣品外观", 1162, 159, 1578, 1010, target, 2, 0.7, 3)
    sync_current_snapshot(f"身体：{waiguan_taozhuang_set}", None, False)

    kktouch3(1119, 867, 1, "背部")
    long_snapshot_click("背部_衣品外观", 1162, 159, 1578, 1010, target, 2, 0.7, 3)
    sync_current_snapshot(f"背部：{waiguan_beibu_set}", None, False)

    kktouch3(1119, 797, 1, "武器")
    long_snapshot_click("武器_衣品外观", 1162, 159, 1578, 1010, target, 2, 0.7, 3)
    sync_current_snapshot(f"武器：{waiguan_wuqi_set}", None, False)

    kktouch3(1119, 865, 1, "脚印")
    long_snapshot_click("脚印_衣品外观", 1162, 159, 1578, 1010, target, 2, 0.7, 3)
    sync_current_snapshot(f"脚印：{waiguan_jiaoyin_set}", None, False)

    kktouch3(1119, 936, 1, "环身")
    long_snapshot_click("环身_衣品外观", 1162, 159, 1578, 1010, target, 2, 0.7, 3)
    sync_current_snapshot(f"环身：{waiguan_huanshen_set}", None, False)


    print("开始技能外观")
    kktouch3(1365, 58, 1, "珍宝阁")
    kktouch3(680, 261, 1, "外观")
    kktouch3(387, 648, 1, "技能外观")
    save_cropped_area_screen("技能外观_衣品外观", 201, 216, 1375, 869)
    kk_keyevent("{ESC}", 1, 0.5, "退出珍宝阁")
    kk_keyevent("{ESC}", 1, 0.5, "退出外观")

    kktouch3(1371, 1021, 1, "技能")
    kktouch3(340, 423, 1, "轻功")
    save_cropped_area_screen("轻功_衣品外观", 266, 256, 1309, 822)
    kk_keyevent("{ESC}", 1, 0.5, "退出轻功")
##########################
def nshpc_step_beibao():
    check_and_try_game_home()
    # kktouch3(1286, 1025, 1, "背包")
    kkpress("b","包裹",1)

    if wait_for_ocr_txt("密码",2,636,443,939,575):
        beibao_pass = None
        try:
            product_info = luhao_task.get("product_info", None)
            for item in product_info["productAttributeValueList"]:
                if item['productAttributeName'] == "背包锁密码":
                    beibao_pass = item.get("value","")
                    break
        except Exception as e:
            print(f"get beibao_pass error: {e}")


        if beibao_pass is not None and beibao_pass != "":
            beibao_pass = beibao_pass.replace(" ", "").replace("\t", "").replace("\n", "").replace("\r", "")
            kktouch3(783,525,1,"输入框")
            text(beibao_pass)
            sleep(1)
            kktouch3(799,601,2,"确定")
            if wait_for_ocr_txt("密码", 2, 636, 443, 939, 575):
                kktouch3(945,457,1,"关闭密码验证")
                sync_current_snapshot("背包密码错误", None, True)
                print("背包密码错误")
                return
        else:
            kktouch3(945, 457, 1, "关闭密码验证")
            sync_current_snapshot("未提供背包密码", None, True)
            print("未提供背包密码")
            return


    kktouch3(828, 482, 1, "包裹")
    target = wait_for_ocr_txt("去仓库", 3, 945, 592, 1570, 982, -1)
    if target:
        save_cropped_area_screen("包裹", target[0] - 297, target[1] - 586, target[0] + 198, target[1] - 50)

        kktouch3(target[0] + 5, target[1] + 3, 15, "去仓库")
        target = wait_for_ocr_txt("打开仓库", 2 * 60, 376, 270, 1593, 871)
        if target:
            kktouch3(target[0], target[1], 1, "打开仓库")
            loop_x = 45
            for i in range(5):
                kktouch3(595 + i * loop_x, 379, 1, f"仓库{i+1}")
                if wait_for_ocr_txt("取消", 1, 637, 476, 969, 559):
                    kktouch3(872, 513, 1, "取消")
                    break
                else:
                    save_cropped_area_screen(f"仓库{i + 1}", 556, 364, 1035, 834-50)
        else:
            sync_current_snapshot("超时未找到仓库", None, True)
    else:
        sync_current_snapshot("进入背包失败", None, True)
###############################
def nshpc_step_toutu():
    get_zhiye(0)

    check_and_try_game_home()

    # kktouch3(1205,48,1,"开服盛典")
    target = wait_for_ocr_txt("开服", 4, 1063, 28, 1407, 88)
    if target is False:
        sync_current_snapshot("进入开服盛典失败", None, True)
    else:
        kktouch3(target[0], target[1], 1, "开服盛典")
        if wait_for_ocr_txt("充值", 3, 405, 550, 546, 608) is False:
            sync_current_snapshot("进入开服盛典失败", None, True)
        else:
            kktouch3(478, 576, 1, "充值返利")
            save_cropped_area_screen_tianniran("00充值返利_属性",361,280,1300,832)

            kktouch3(478,634,1,"外观继承")
            save_cropped_area_screen_tianniran("外观继承_属性", 361, 280, 1300, 832)

    check_and_try_game_home()
    kk_keyevent("{ESC}", 1, 0.1, "菜单")
    kktouch3(874, 443, 1, "切换角色")
    sleep(10)
    wait_for_ocr_txt("开始", 20, 1444, 861, 1589, 1030)
    sleep(10)
    save_cropped_area_screen_tianniran("头图_天赏外观",380,347,1260,951)


    if "双双" in waiguan_fashi_set:
        ocr_check_and_set("性别", "女")
    else:
        ocr_check_and_set("性别", "男")

    # kktouch3(60,84,1,"左上返回")

########################################
def step_record_all(is_skip):
    if is_skip == 0:
        if check_account_login_other():
            return get_return_taskEvent("step_record_all", EventStatus.CANCELLED, "被顶号，录号失败")
        nshpc_step_mianban()

        if check_account_login_other():
            return get_return_taskEvent("step_record_all", EventStatus.CANCELLED, "被顶号，录号失败")
        nshpc_step_fuzhi()

        if check_account_login_other():
            return get_return_taskEvent("step_record_all", EventStatus.CANCELLED, "被顶号，录号失败")
        nshpc_step_jineng()

        if check_account_login_other():
            return get_return_taskEvent("step_record_all", EventStatus.CANCELLED, "被顶号，录号失败")
        nshpc_step_zhuangyuan()

        if check_account_login_other():
            return get_return_taskEvent("step_record_all", EventStatus.CANCELLED, "被顶号，录号失败")
        nshpc_step_chognzhi()

        if check_account_login_other():
            return get_return_taskEvent("step_record_all", EventStatus.CANCELLED, "被顶号，录号失败")
        nshpc_step_xiangrui_zuoqi()

        if check_account_login_other():
            return get_return_taskEvent("step_record_all", EventStatus.CANCELLED, "被顶号，录号失败")
        nshpc_step_waiguan()

        if check_account_login_other():
            return get_return_taskEvent("step_record_all", EventStatus.CANCELLED, "被顶号，录号失败")
        nshpc_step_beibao()

        if check_account_login_other():
            return get_return_taskEvent("step_record_all", EventStatus.CANCELLED, "被顶号，录号失败")
        nshpc_step_toutu()
##################################################
if __name__ == '__main__':
    # 记录程序开始执行的时间  
    accounts = ["***********"]
    # accounts = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
    # accounts = ["***********"]
    # password = ["wodejia","Jun135790","Jun135790.","Jun135790."]
    password = ["Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790."]

    i = 0

    for item in accounts:
        luhao_task = {
            #     'gameAccount':'***********',
            'gameAccount': item,
            # 'gamePassword': "Jun135790",
            'gamePassword': password[i],
            "id": 18678,
            "status": "IN_PROGRESS",
            # "loginType":"QR_CODE",
            # "loginType":"PASSWORD",
            "loginType":"QR_CODE",
            "productSn": "NHTYBU",
            "product_meta": requestGameProductCategoryMeta(214),
            "product_info": {
                "product": {
                    "productCategoryId": 214,
                    "productSn": "NHTYBU",
                    "productCategoryName": "逆水寒黄金服",
                    "updateTime": "2024-09-29T06:36:15.000+00:00",
                    "createTime": "2024-09-28T22:49:57.000+00:00",
                    "gameActiveFlag": 0,
                    "gameAccountQufu": ""
                }
            }
        }
        i = i +1

        start_time = datetime.datetime.now()
        # step_restart_nshm(0)
        # step_restart_mumu()

        # print(is_chinese_mobile_number("***********"))
        # print(is_chinese_mobile_number("**********"))
        # print(is_chinese_mobile_number("**********"))
        # sleep(30)

        init_nsh(luhao_task)

        # save_cropped_area_screen("角色-背包_其他物品", 868, 263, 1267, 610, 0.2)
        # long_snapshot("角色-背包_其他物品", 868, 610, 1267, 650, 120, 1.5)
        # save_cropped_area_screen("角色-背包_其他物品", 868, 650, 1267, 885, 0.2)

        # step_login(0)
        # step_qufu(0)
        # step_record_all(0)

        # target = wait_for_ocr_txt("开服", 4, 1060, 28, 1407, 88)
        # kktouch3(target[0] + 84, target[1], 1, "福利")


        # kkpress("u", "面板")
        # sleep(1)
        # pyautogui.hotkey("ctrl","l")

        # kkpress("f12", "时装", 1)
        # nshpc_step_waiguan()

        # target = wait_for_ocr_txt("奇珍",3,1052,28,1407,88)
        # nshpc_step_toutu()
        # step_logout(0)

        # nshpc_step_beibao()

        print("end")

        step_img_upload(0)
        # step_meta_upload(0)

        sleep(3000)


        end_time = datetime.datetime.now()
        total_time = end_time - start_time
        minutes = total_time.total_seconds()
        print(f"录号执行了 {minutes} 秒。")



        break

