import copy
import json
import multiprocessing
import os
import queue
import sys
import threading
import time
import traceback
from contextlib import contextmanager

import configs
import logger_config
import sys_tool
from luhao_models import TaskEvent, EventStatus
from server_client import PortalClient

# 获取当前脚本文件的绝对路径
current_dir = os.path.dirname(os.path.abspath(__file__))

# 使用相对路径，将 nsh_mumu.air 文件夹所在目录加入 sys.path
nsh_mumu_air_dir = os.path.join(current_dir, 'nsh_mumu.air')
sys.path.append(nsh_mumu_air_dir)

log_widget = None


# Worker类，用于执行录号任务，并通过队列接收指令
class Worker(multiprocessing.Process):
    def __init__(self, task, result_queue, params, is_fast_record=False, save_original=True, log_queue=None,
                 need_logout=False):
        super().__init__()
        self.result_queue = result_queue  # 用于返回结果。 任务进度通过result_queue返回master，master进入后续的处理
        # 清空result_queue
        while not self.result_queue.empty():
            self.result_queue.get()
        self.current_task = task  # 存储当前任务的信息
        self.params = params

        self.portal_client = PortalClient()
        self.is_fast_record = is_fast_record
        self.log_queue = log_queue
        self.daemon = True  # 守护进程
        self.save_original = save_original
        self.need_logout = need_logout

    def run(self):
        # self.log_queue.put("【worker】任务开始")
        try:
            result = self.execute_long_task()
            if result:
                self.result_queue.put(
                    TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FINISHED))
            else:
                self.result_queue.put(
                    TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FAILURE))
        except Exception as e:
            # 将异常信息和堆栈信息放入 log_queue
            error_message = f"【worker】任务出错: {e}\n{traceback.format_exc()}"
            # self.log_queue.put(error_message)
            traceback.print_exc()
            self.result_queue.put(
                TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FAILURE))
        # self.log_queue.put("【worker】任务完成")
        self.current_task = None  # 任务完成后清除任务信息

    # 耗时任务的具体实现
    def execute_long_task(self):
        metadata = self.portal_client.get_product_category_meta(75)
        self.current_task['product_meta'] = metadata
        product = self.portal_client.get_product_detail(self.current_task['productId'])
        self.current_task['product_info'] = product

        begin_stage = None
        need_login = False
        if self.params:
            params = json.loads(self.params)
            begin_stage = params.get('beginStage')
            need_login = params.get('needLogin')

        # self.log_queue.put('[worker] task info: ', task)

        nsh = None

        # 重定向标准输出和标准错误
        sys.stdout = open(os.devnull, 'w')
        sys.stderr = open(os.devnull, 'w')

        # 这里写需要屏蔽打印信息的代码
        print("这个信息不会被打印出来")
        import nsh_mumu_login as nsh

        # 恢复标准输出和标准错误
        sys.stdout = sys.__stdout__
        sys.stderr = sys.__stderr__

        # nsh.init(log_widget, fast=self.is_fast_record)
        nsh.init(log_queue=None, fast=self.is_fast_record, save_original=self.save_original)

        if need_login:
            init_steps = [
                (nsh.init_nsh, 'init_nsh'),
                (nsh.step_login, 'step_login'),
                (nsh.step_qufu, 'step_qufu'),
            ]
        else:
            init_steps = [
                (nsh.init_local_nsh, 'init_local_nsh'),
            ]

        for step_function, stage in init_steps:
            # self.status_queue.put(
            #     {'status': EventStatus.IN_PROGRESS, 'stage': stage, 'product_sn': task['productSn']})
            success = self.execute_task_stage(step_function, stage, self.current_task)
            if not success:
                return False  # 如果任务失败，提前返回

        steps = [
            # (nsh.init_nsh, 'init_nsh', task),
            # (nsh.step_logout, 'step_logout', 0),
            # (nsh.step_login, 'step_login', 0),
            # (nsh.step_qufu, 'step_qufu', 0),
            # (nsh.safe_step_gameset, 'safe_step_gameset', 0),
            (nsh.safe_step_juese, 'safe_step_juese'),
            (nsh.safe_step_jueji, 'safe_step_jueji'),
            (nsh.safe_step_neigong, 'safe_step_neigong'),
            (nsh.safe_step_dazao, 'safe_step_dazao'),
            (nsh.safe_step_waiguan, 'safe_step_waiguan'),
            (nsh.safe_step_kaifang_shijie, 'safe_step_kaifang_shijie'),
            (nsh.safe_step_qunxia, 'safe_step_qunxia'),
            (nsh.safe_step_zhuangyuan, 'safe_step_zhuangyuan'),
            (nsh.safe_step_lingcong, 'safe_step_lingcong'),
            (nsh.step_img_upload, 'step_img_upload'),
            (nsh.step_meta_upload, 'step_meta_upload'),
            # (nsh.step_logout, 'step_logout', 0)
        ]

        start_execution = begin_stage is None

        # 逐个执行任务
        for step_function, stage in steps:
            if not start_execution:
                if stage == begin_stage:
                    start_execution = True  # 达到指定阶段，开始执行后续步骤
                else:
                    continue  # 跳过之前的步骤

            # 执行当前阶段的任务
            success = self.execute_task_stage(step_function, stage)
            if not success:
                return False  # 如果任务失败，提前返回

        if self.need_logout:
            post_steps = [
                (nsh.step_logout, 'step_logout'),
            ]

            for step_function, stage in post_steps:
                success = self.execute_task_stage(step_function, stage)
                if not success:
                    return False  # 如果任务失败，提前返回

        return True

    def execute_task_stage(self, stage_function, stage, param=0):
        with self.time_logger(stage):  # 开始计时

            try:
                self.result_queue.put(TaskEvent(task_id=self.current_task['id'],
                                                stage=stage,
                                                status=EventStatus.IN_PROGRESS,
                                                snapshot=None,
                                                data=None,
                                                msg=None))

                stage_event = stage_function(param)
                self.result_queue.put(stage_event)
            except Exception as e:
                # self.log_queue.put(f"【worker】任务出错: {e}")
                self.handle_failure(self.current_task, stage)
                traceback.print_exc()
                return False  # 表示失败
        return True  # 表示成功

    def handle_failure(self, task, stage):
        step_login_event = TaskEvent(task['id'], stage=stage, status=EventStatus.FAILURE)
        self.result_queue.put(step_login_event)

    @contextmanager
    def time_logger(self, stage):
        start_time = time.time()  # 记录开始时间
        # self.log_queue.put(f"开始执行阶段: {stage}")
        try:
            yield  # 执行阶段操作
        finally:
            end_time = time.time()  # 记录结束时间
            duration = end_time - start_time  # 计算耗时
            # self.log_queue.put(f"阶段 {stage} 耗时: {duration:.2f} 秒")


# Master类，发送任务指令，管理Worker，并处理心跳
class Master:
    def __init__(self, logger, status_queue, is_fast_record, save_original, device_addr=None, app_version=None):
        self.task_queue = multiprocessing.Queue()
        self.result_queue = multiprocessing.Queue()
        # 创建日志队列
        # self.log_queue = multiprocessing.Queue()

        self.worker = None
        self.control_lock = multiprocessing.Lock()  # 用于保护共享资源
        self.running = True
        self.portal_client = PortalClient()
        self.last_instruction = None
        self.test_queue = queue.Queue()  # 测试队列

        self.gui_queue = queue.Queue()
        self.status_queue = status_queue
        self.logger = logger
        self.is_fast_record = is_fast_record
        self.save_original = save_original
        self.device_id = self.get_device_id(device_addr)
        self.app_version = app_version

        self.log_queue = multiprocessing.Queue()

    def process_log_messages(self):
        """处理日志消息并记录到日志文件"""
        while True:
            while not self.log_queue.empty():
                message = self.log_queue.get()
                self.logger.info(message)  # 记录到文件和控制台
            time.sleep(0.1)  # 每100毫秒检查一次队列

    def get_device_id(self, device_addr):
        result_msg = self.portal_client.get_device_id(device_addr)
        if result_msg and isinstance(result_msg, int):
            self.device_id = result_msg
            return self.device_id
        else:
            return None

    # 启动Worker
    def start_worker(self, task, need_logout):
        if self.worker is None or not self.worker.is_alive():
            self.logger.info("【master】启动新任务...")
            self.worker = Worker(task, self.result_queue, None,
                                 self.is_fast_record, self.save_original, log_queue=None,
                                 need_logout=need_logout)
            self.worker.start()

    def stop_worker_now(self, is_canceled=False):
        if self.worker is not None:
            self.logger.info("【master】停止worker...")
            try:
                task_id = self.worker.current_task['id']
                self.worker.terminate()  # 立即停止
                time.sleep(2)
                if self.worker:
                    self.worker.kill()
                self.worker = None
                self.logger.info("【master】停止worker成功")
                self.status_queue.put({'status': '任务已停止'})
                if is_canceled:
                    task = {
                        'id': task_id,
                        'status': 'CANCELLED',
                        'msg': '取消任务'
                    }
                    self.portal_client.sync_task_info(task)
            except Exception as e:
                error_message = f"【worker】停止任务出错: {e}\n{traceback.format_exc()}"
                self.logger.error(error_message)

    def process_worker_logs(self):
        while True:
            while not self.log_queue.empty():
                message = self.log_queue.get()
                self.logger.info(message)  # 记录到文件和控制台
            time.sleep(1)  # 每100毫秒检查一次队列

    def heartbeat(self):
        while self.running:
            # self.logger.info("heartbeat... ")
            time.sleep(5)
            if self.worker is not None:
                self.send_heartbeat(device_status='IN_PROGRESS')
            else:
                self.send_heartbeat(device_status='IDLE')

    def check_worker_result(self):
        # 检查任务状态, 如果worker在执行中，才检查任务状态
        while self.running:
            try:
                if self.worker is None:
                    time.sleep(5)
                    continue
                result = self.result_queue.get_nowait()
                current_task = self.worker.current_task
                if result is None:
                    continue
                if result.status == EventStatus.FINISHED:
                    self.logger.info('【master】同步录号任务信息...FINISHED')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'COMPLETED',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    self.status_queue.put({'status': result.status, 'stage': result.stage})
                    self.stop_worker_now()
                if result.status == EventStatus.SUCCESS:
                    self.logger.info('【master】同步录号任务信息...SUCCESS')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'IN_PROGRESS',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    product_sn = None
                    if current_task:
                        product_sn = current_task['productSn']
                    self.status_queue.put({'status': result.status, 'stage': result.stage,
                                           'product_sn': product_sn})
                if result.status == EventStatus.FAILURE:
                    self.logger.info('【master】同步录号任务信息...FAILURE')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'FAILED',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    self.status_queue.put(
                        {'status': result.status, 'stage': result.stage, 'product_sn': current_task['productSn']})
                    self.stop_worker_now()
                if result.status == EventStatus.MANUAL_REQUIRED:
                    self.logger.info('【master】同步录号任务信息...MANUAL_REQUIRED')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'MANUAL_REQUIRED',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    self.status_queue.put(
                        {'status': result.status, 'stage': result.stage, 'product_sn': current_task['product.Sn']})
                if result.status == EventStatus.IN_PROGRESS:
                    product_sn = None
                    if current_task:
                        product_sn = current_task['productSn']
                    self.status_queue.put({'status': result.status, 'stage': result.stage,
                                           'product_sn': product_sn})
            except queue.Empty:
                time.sleep(5)

    def check_new_task_from_ui(self):
        while self.running:
            try:
                # gui_queue gui点击了开始任务
                product_params = self.gui_queue.get_nowait()
                # ui提交了任务。 先检查work是不是正在执行中
                if self.worker is not None:
                    self.status_queue.put(
                        {'status': 'ERROR:录号中，请勿点击开始任务。若要结束录号, 请先点击停止任务', 'stage': None})
                    continue

                if product_params is None:
                    continue

                need_logout = False
                if product_params.get('productSn') is None or product_params.get('productSn') == '':
                    #  创建商品和任务
                    user_name = product_params['username']
                    create_product_req = copy.copy(configs.create_product_req)
                    create_product_req['username'] = user_name
                    create_product_req['gameAccountQufu'] = product_params['gameAccountQufu']
                    create_product_req['gameCareinfoPhone'] = user_name
                    create_product_req['gameCareinfoVx'] = product_params['gameCareinfoVx']
                    create_product_req['price'] = product_params['price']
                    create_product_req['originalPrice'] = product_params['originalPrice']
                    create_product_req['operateMan'] = product_params['operateMan']

                    for item in create_product_req['productAttributeValueList']:
                        if item['attriName'] == '职业':
                            item['value'] = product_params['job']
                        elif item['attriName'] == '已使用天赏石':
                            item['value'] = product_params['tianshang']
                        elif item['attriName'] == '账号类型':
                            item['value'] = product_params['accountType']
                        elif item['attriName'] == '区服':
                            item['value'] = product_params['gameAccountQufu']
                        elif item['attriName'] == '游戏账号':
                            item['value'] = product_params['gameAccount']
                    task = self.portal_client.create_product(create_product_req)  # 创建product
                elif product_params['productSn'].startswith('TID:'):
                    tid = product_params['productSn'].replace('TID:', '')
                    task = self.portal_client.get_task_info(tid)
                    need_logout = True
                else:
                    create_task_req = {
                        'productSn': product_params['productSn'],
                        'operateMan': product_params.get('operateMan')
                    }
                    task = self.portal_client.create_task(create_task_req)

                if task is None:
                    self.status_queue.put({'status': 'ERROR:任务不存在', 'stage': None})
                    continue
                # if task['status'] == 'COMPLETED' or task['status'] == 'IN_PROGRESS':
                #     self.status_queue.put({'status': 'ERROR:任务已完成或正在录号中', 'stage': None})
                #     continue
                # else:
                #     # 任务正常，可以正常启动worker开始录号
                #     product_sn = task['productSn']
                #     self.logger.info(f"【master】有新任务: {product_sn}")
                #     # 更新设备id
                #     task['deviceId'] = self.device_id
                #     self.portal_client.sync_task_info(task)
                #     self.start_worker(task)
                #     self.status_queue.put(
                #         {'status': EventStatus.IN_PROGRESS, 'stage': None, 'product_sn': product_sn})
                product_sn = task['productSn']
                self.logger.info(f"【master】有新任务: {product_sn}")
                # 更新设备id
                task['deviceId'] = self.device_id
                self.portal_client.sync_task_info(task)
                self.start_worker(task, need_logout)
                self.status_queue.put(
                    {'status': EventStatus.IN_PROGRESS, 'stage': None, 'product_sn': product_sn})
            except queue.Empty:
                time.sleep(1)
            except Exception as e:
                self.logger.error(f"获取任务错误: {e}")

    def send_heartbeat(self, device_status='IDLE'):
        description = {
            'app_version': self.app_version
        }
        instruction = {
            'deviceId': self.device_id,
            'deviceStatus': device_status,
            'timestamp': int(round(time.time() * 1000)),
            'description': sys_tool.safe_json_dumps(description)
        }
        return self.portal_client.send_heartbeat(instruction)

    # 关闭Master
    def shutdown(self):
        self.stop_worker_now()
        self.running = False
        self.send_heartbeat(device_status='OFFLINE')
        self.logger.info("Master 关闭")


# 主程序
def main(text_widget=None, status_queue=None, is_fast_record=False, save_original=True, device_addr=None,
         app_version=None):
    global log_widget
    log_widget = text_widget
    logger = logger_config.setup_logger(os.path.basename(__file__), text_widget=text_widget)

    logger.info(f"【master】device_addr: {device_addr}")

    master = Master(logger, status_queue, is_fast_record, save_original, device_addr, app_version)

    if master.device_id is None:
        logger.error(f"【master】device_id is None, exit")
        return None

    # 启动心跳线程
    heartbeat_thread = threading.Thread(target=master.heartbeat, daemon=True)
    heartbeat_thread.start()

    # log_process_thread = threading.Thread(target=master.process_worker_logs, daemon=True)
    # log_process_thread.start()

    check_task_from_ui_thread = threading.Thread(target=master.check_new_task_from_ui, daemon=True)
    check_task_from_ui_thread.start()

    # 检查worker执行结果
    check_worker_result_thread = threading.Thread(target=master.check_worker_result, daemon=True)
    check_worker_result_thread.start()
    return master


if __name__ == "__main__":
    main()
