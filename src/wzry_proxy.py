import http.server
import json
import multiprocessing
import traceback
import urllib.parse
from datetime import datetime

import requests

from cache_util import Simple<PERSON><PERSON>
from luhao_models import TaskEvent, EventStatus
from utils.crypto_util import AESCryptoUtil
from utils.shelve_util import ShelveDB

cache = SimpleCache(maxsize=100, ttl=600)
single_cache = SimpleCache(maxsize=1, ttl=600)

proxies = {
    "http": None,
    "https": None,
}

db = ShelveDB('ww_account')

class HeroSkinReporter:
    def __init__(self):
        self.aes_util = AESCryptoUtil("aEUAzI8eCEgdo02pfkLY+w==")
        pass

    def get_hero_list(self, token, user_id, role_id):
        print(f"[INFO] Fetching hero list for roleId: {role_id}, userId: {user_id}")
        try:
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "token": token,
                "userId": user_id,
            }
            data = f"roleId={role_id}&cClientVersionName=8.93.0605&token={token}&userId={user_id}"
            response = requests.post("https://ssl.kohsocialapp.qq.com:10001/play/h5getherolist", data=data,
                                     headers=headers, proxies=proxies)
            print("[INFO] Hero list successfully fetched.")
            return response.json()
        except Exception as error:
            print(f"[ERROR] Error fetching hero skin list: {error}")
            raise error

    def save_to_json_file(self, data, file_path):
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"[INFO] Data successfully written to file: {file_path}")
        except Exception as error:
            print(f"[ERROR] Error saving data to file: {error}")

    def do_report(self, headers, body, skin, result_queue):
        token = headers.get('token')
        user_id = headers.get('userid')
        parsed_body = json.loads(body)
        role_id = parsed_body.get('roleId')

        print(f"[INFO] Starting hero and skin report for roleId: {role_id}, userId: {user_id}")

        try:
            res = self.get_hero_list(token, user_id, role_id)
            obj = {
                "heroList": [],
                "skinList": [],
            }
            print(json.dumps(res, ensure_ascii=False))
            hero_list = res.get('data', {}).get('heroList', [])

            all_hero = {str(hero['heroId']): hero for hero in hero_list}

            for hero in hero_list:
                if not hero.get('notOwnedFlag'):
                    obj['heroList'].append({
                        'heroId': hero['heroId'],
                        'heroType': hero['heroType'],
                        'name': hero['name'],
                    })

            skin_data = skin.get('skinData', [])
            all_skin_conf = skin.get('allSkinConf', [])
            all_hero_conf = skin.get('allHeroConf', [])
            skin_num_obj = {}

            all_hero_and_skin = {
                "allSkinConf" : all_skin_conf,
                "allHeroConf" : all_hero_conf
            }
            self.save_to_json_file(all_hero_and_skin, "all_hero_and_skin.json")

            skin_conf_by_id = {}
            for skin in all_skin_conf:
                if skin.get('isHidden') != 1:
                    skin_conf_by_id[skin['skinId']] = skin
                    hero_name = skin.get('heroName')
                    skin_num_obj[hero_name] = skin_num_obj.get(hero_name, 0) + 1

            for skin in skin_data:
                skin_id = skin.get('skinId')
                find_it = skin_conf_by_id.get(skin_id)
                if find_it:
                    hero_id = str(find_it['heroId'])
                    print('[INFO] Found skin, hero_id:', hero_id, ', type:', type(hero_id))
                    obj['skinList'].append({
                        'skinId': find_it['skinId'],
                        'skinName': find_it['skinName'],
                        'heroId': hero_id,
                        'heroName': find_it['heroName'],
                        'classTypeName': find_it['classTypeName'],
                        'heroType': all_hero[hero_id]['heroType'],
                        'accessWay': find_it['accessWay'],
                        'skinNum': skin_num_obj[find_it['heroName']],
                    })
                else:
                    print(f"[WARN] Skin with skinId {skin_id} not found in configuration.")

            user_info = cache.get(role_id)
            if user_info:
                obj['roleJobName'] = user_info.get('roleJobName')
                obj['userId'] = user_info.get('userId')
                obj['encrypted_user_id'] = self.aes_util.encrypt(obj.get("userId"))

            self.add_wzry_detail(obj)

            now = datetime.now()
            filename = f"E:\\result-{now.strftime('%Y%m%d-%H%M%S')}.json"
            self.save_to_json_file(obj, filename)
            #     todo 返回worker结果
            result_queue.put(TaskEvent(task_id='',
                                       stage='',
                                       status=EventStatus.SUCCESS,
                                       data=obj))
            print(f"[INFO] Hero and skin report for roleId: {role_id}, userId: {user_id} completed.")

        except Exception as e:
            traceback.print_exc()
            print(f"[ERROR] Failed to fetch hero skin list: {e}")
            result_queue.put(TaskEvent(task_id='',
                                       stage='',
                                       status=EventStatus.FAILURE,
                                       data=None))

    def do_ww_report(self, headers, body, skin, result_queue):
        account_info = {}

        data = skin['data']
        agent = skin['agent']
        skin = skin['skin']
        spray = skin['spray']
        card = skin['card']
        charm = skin['charm']




        token = headers.get('token')
        user_id = headers.get('userid')
        parsed_body = json.loads(body)
        role_id = parsed_body.get('roleId')

        print(f"[INFO] Starting hero and skin report for roleId: {role_id}, userId: {user_id}")

        try:
            res = self.get_hero_list(token, user_id, role_id)
            obj = {
                "heroList": [],
                "skinList": [],
            }
            print(json.dumps(res, ensure_ascii=False))
            hero_list = res.get('data', {}).get('heroList', [])

            all_hero = {str(hero['heroId']): hero for hero in hero_list}

            for hero in hero_list:
                if not hero.get('notOwnedFlag'):
                    obj['heroList'].append({
                        'heroId': hero['heroId'],
                        'heroType': hero['heroType'],
                        'name': hero['name'],
                    })

            skin_data = skin.get('skinData', [])
            all_skin_conf = skin.get('allSkinConf', [])
            all_hero_conf = skin.get('allHeroConf', [])
            skin_num_obj = {}

            all_hero_and_skin = {
                "allSkinConf" : all_skin_conf,
                "allHeroConf" : all_hero_conf
            }
            self.save_to_json_file(all_hero_and_skin, "all_hero_and_skin.json")

            skin_conf_by_id = {}
            for skin in all_skin_conf:
                if skin.get('isHidden') != 1:
                    skin_conf_by_id[skin['skinId']] = skin
                    hero_name = skin.get('heroName')
                    skin_num_obj[hero_name] = skin_num_obj.get(hero_name, 0) + 1

            for skin in skin_data:
                skin_id = skin.get('skinId')
                find_it = skin_conf_by_id.get(skin_id)
                if find_it:
                    hero_id = str(find_it['heroId'])
                    print('[INFO] Found skin, hero_id:', hero_id, ', type:', type(hero_id))
                    obj['skinList'].append({
                        'skinId': find_it['skinId'],
                        'skinName': find_it['skinName'],
                        'heroId': hero_id,
                        'heroName': find_it['heroName'],
                        'classTypeName': find_it['classTypeName'],
                        'heroType': all_hero[hero_id]['heroType'],
                        'accessWay': find_it['accessWay'],
                        'skinNum': skin_num_obj[find_it['heroName']],
                    })
                else:
                    print(f"[WARN] Skin with skinId {skin_id} not found in configuration.")

            user_info = cache.get(role_id)
            if user_info:
                obj['roleJobName'] = user_info.get('roleJobName')
                obj['userId'] = user_info.get('userId')
                obj['encrypted_user_id'] = self.aes_util.encrypt(obj.get("userId"))

            self.add_wzry_detail(obj)

            now = datetime.now()
            filename = f"E:\\result-{now.strftime('%Y%m%d-%H%M%S')}.json"
            self.save_to_json_file(obj, filename)
            #     todo 返回worker结果
            result_queue.put(TaskEvent(task_id='',
                                       stage='',
                                       status=EventStatus.SUCCESS,
                                       data=obj))
            print(f"[INFO] Hero and skin report for roleId: {role_id}, userId: {user_id} completed.")

        except Exception as e:
            traceback.print_exc()
            print(f"[ERROR] Failed to fetch hero skin list: {e}")
            result_queue.put(TaskEvent(task_id='',
                                       stage='',
                                       status=EventStatus.FAILURE,
                                       data=None))

    def add_wzry_detail(self, obj):
        print(f"[INFO] Sending report to external service for userId: {obj.get('userId')}")
        try:
            url = f"http://api.yyd8.com/mall-portal/import/product/addWzryDetail?wzryId={obj.get('encrypted_user_id')}"
            response = requests.post(url, json=obj, proxies=proxies)
            print("[INFO] Report successfully sent.")
        except Exception as err:
            traceback.print_exc()
            print(f"[ERROR] Failed to send report: {err}")


class RequestHandler(http.server.BaseHTTPRequestHandler):
    def __init__(self, result_queue, request, client_address, server):
        self.result_queue = result_queue
        self.reporter = HeroSkinReporter()
        # super().__init__(*args, **kwargs)
        super().__init__(request, client_address, server)

    def do_GET(self):
        # 解析当前请求的URL
        parsed_url = urllib.parse.urlparse(self.path)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        target_url = query_params.get('targetUrl', [None])[0]

        if not target_url:
            self.send_response(400)
            self.end_headers()
            self.wfile.write(b"Missing targetUrl in query parameters")
            return

        # 重新构建查询字符串，移除所有 targetHost 参数
        cleaned_query_params = {k: v for k, v in query_params.items() if k != 'targetUrl'}
        new_query_string = urllib.parse.urlencode(cleaned_query_params, doseq=True)

        # 构建完整的目标 URL
        # target_url = f"https://{host}{parsed_url.path}"
        # if new_query_string:
        #     target_url = f"{target_url}?{new_query_string}"

        # 打印或者使用 target_url
        print(f"Forwarding GET request to: {target_url}")

        # 调用转发方法
        self.forward_get_request(target_url, new_query_string)

    def forward_get_request(self, target_url, query_string):
        headers = dict(self.headers)
        headers['Host'] = 'xui.ptlogin2.qq.com'

        options = {
            'url': f"{target_url}?{query_string}",
            'headers': headers,
            'proxies': proxies
        }

        print(options)

        if target_url == 'https://xui.ptlogin2.qq.com/ssl/ptqrshow':
            response = requests.get(**options)
            print(target_url)

            output_image_path = 'qrcode.png'
            if response.status_code == 200:
                # 将响应内容写入 PNG 文件
                with open(output_image_path, 'wb') as f:
                    f.write(response.content)
                print(f"Image saved as {output_image_path}")
            else:
                print(f"Failed to fetch image. Status code: {response.status_code}")

        print('resp headers:')

        self.send_response(response.status_code)
        for key, value in response.headers.items():
            print(key, value)
            if key.lower() == 'set-cookie':
                self.send_header(key, value)

        self.end_headers()
        self.wfile.write(response.content)
        pass

    def do_POST(self):
        parsed_url = urllib.parse.urlparse(self.path)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        target_url = query_params.get('targetUrl', [None])[0]

        if not target_url:
            self.send_response(400)
            self.end_headers()
            self.wfile.write(b"Missing targetUrl in query parameters")
            return

        body = self.rfile.read(int(self.headers.get('Content-Length'))).decode('utf-8')

        if target_url == "https://kohcamp.qq.com/game/koh/profile":
            self.reporter.profile = {}

        self.forward_request(target_url, body)

    def forward_request(self, target_url, body):
        parsed_target_url = urllib.parse.urlparse(target_url)
        headers = dict(self.headers)
        options = {
            'url': target_url,
            'headers': headers,
            'data': body,
            'proxies': proxies
        }
        try:
            # 根据 target_url 区分不同的处理逻辑
            if target_url == "https://kohcamp.qq.com/game/koh/profile":
                response = requests.post(**options)
                try:
                    # self.reporter.profile = response.json()
                    profile = response.json()
                    target_role_id = profile['data']['targetRoleId']

                    for item in profile['data']['roleList']:
                        if item['roleId'] == target_role_id:
                            obj = {
                                'roleJobName': item.get('roleJobName'),
                                'userId': item.get('userId'),
                            }
                            cache.set(target_role_id, obj)

                    print("[INFO] Profile fetched successfully.")
                except Exception as e:
                    print(f"[ERROR] Failed to parse profile: {e}")
            elif target_url == "https://kohcamp.qq.com/game/itempage/skinlist":
                response = requests.post(**options)
                self.reporter.do_report(self.headers, body, response.json(), self.result_queue)
            elif target_url == "https://app.mval.qq.com/go/user_profile/query/user":
                # 账号信息
                response = requests.post(**options)
                try:
                    resp = response.json()
                    uid = resp['data'][0]['appNum']
                    print('### uid:' + uid)

                    scene = resp['data'][0]['gameInfoList'][0]['scene']
                    single_cache.set(scene, {})

                    # with open('wwqy_tmp.json', 'w', encoding='utf-8') as f:
                    #     json.dump(resp, f, ensure_ascii=False, indent=2)
                    #
                    # heros = resp['data']['agent_list']
                    #
                    # for hero in heros:
                    #     print(hero)
                    #
                    # print("[INFO] Profile fetched successfully.")
                except Exception as e:
                    print(f"[ERROR] Failed to parse profile: {e}")
            elif target_url == 'https://app.mval.qq.com/go/agame/asset/agent?':
                # 英雄
                response = requests.post(**options)

                heros = []
                try:
                    resp = response.json()
                    # with open('wwqy_heros.json', 'w', encoding='utf-8') as f:
                    #     json.dump(resp, f, ensure_ascii=False, indent=2)

                    heros = resp['data']['agent_list']

                    for hero in heros:
                        print(hero)
                        if hero['lock_status'] == 0:
                            heros.append({
                                "id": hero['id'],
                                "name": hero['name'],
                                "avatar": hero['avatar'],
                                "icon": hero['icon'],
                                "position_name": "先锋",
                                "type_value": "先锋"
                            })

                    request_params = options.get('data')
                    scene = json.loads(request_params).get('scene')
                    account_info = single_cache.get(scene)
                    if account_info is None:
                        account_info = {
                            'heros': heros
                        }

                    single_cache.set(scene, account_info)

                    print("[INFO] Profile fetched successfully.")
                except Exception as e:
                    print(f"[ERROR] Failed to parse profile: {e}")
            elif target_url == 'https://app.mval.qq.com/go/agame/asset/gun_skin?':
                response = requests.post(**options)
                try:
                    # self.reporter.profile = response.json()
                    resp = response.json()
                    with open('wwqy_gnu_skins.json', 'w', encoding='utf-8') as f:
                        json.dump(resp, f, ensure_ascii=False, indent=2)

                    gun_skins = resp['data']['gun_list']

                    for skin in gun_skins:
                        print(skin)

                    print("[INFO] Profile fetched successfully.")
                except Exception as e:
                    print(f"[ERROR] Failed to parse profile: {e}")

            elif target_url == 'https://app.mval.qq.com/go/agame/asset/all?':
                response = requests.post(**options)
                request_params = options.get('data')
                scene = json.loads(request_params).get('scene')
                # self.reporter.do_ww_report(self.headers, body, response.json(), self.result_queue)
                # try:
                #     # self.reporter.profile = response.json()
                #     resp = response.json()
                #     with open('wwqy_user_assets1.json', 'w', encoding='utf-8') as f:
                #         json.dump(resp, f, ensure_ascii=False, indent=2)
                #
                #     gun_skins = resp['data']['gun_list']
                #
                #     for skin in gun_skins:
                #         print(skin)
                #
                #     print("[INFO] Profile fetched successfully.")
                # except Exception as e:
                #     print(f"[ERROR] Failed to parse profile: {e}")
            else:
                response = requests.post(**options)
                print(target_url)
                response = requests.post(**options)
                resp = response.json()
                print(resp)
                pass


            self.send_response(response.status_code)
            self.end_headers()
            self.wfile.write(response.content)

        except Exception as e:
            traceback.print_exc()
            self.send_response(500)
            self.end_headers()
            self.wfile.write(b"Internal Server Error")
            print(f"[ERROR] Error forwarding request: {e}")


def run_server(result_queue, server_class=http.server.HTTPServer, handler_class=RequestHandler, port=9001):
    # def handler(request, client_address, server):
    #     handler_class(result_queue, request, client_address, server)
    def handler(*args, **kwargs):
        handler_class(result_queue, *args, **kwargs)

    server_address = ('', port)
    httpd = server_class(server_address, handler)
    print(f"[INFO] Server is running on port {port}")
    httpd.serve_forever()


if __name__ == '__main__':
    run_server(multiprocessing.Queue())
