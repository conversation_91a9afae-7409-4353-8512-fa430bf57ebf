/* Generated by Cython 3.0.12 */

/* BEGIN: Cython Metadata
{
    "distutils": {
        "name": "configs",
        "sources": [
            "configs.py"
        ]
    },
    "module_name": "configs"
}
END: Cython Metadata */

#ifndef PY_SSIZE_T_CLEAN
#define PY_SSIZE_T_CLEAN
#endif /* PY_SSIZE_T_CLEAN */
#if defined(CYTHON_LIMITED_API) && 0
  #ifndef Py_LIMITED_API
    #if CYTHON_LIMITED_API+0 > 0x03030000
      #define Py_LIMITED_API CYTHON_LIMITED_API
    #else
      #define Py_LIMITED_API 0x03030000
    #endif
  #endif
#endif

#include "Python.h"
#ifndef Py_PYTHON_H
    #error Python headers needed to compile C extensions, please install development version of Python.
#elif PY_VERSION_HEX < 0x02070000 || (0x03000000 <= PY_VERSION_HEX && PY_VERSION_HEX < 0x03030000)
    #error Cython requires Python 2.7+ or Python 3.3+.
#else
#if defined(CYTHON_LIMITED_API) && CYTHON_LIMITED_API
#define __PYX_EXTRA_ABI_MODULE_NAME "limited"
#else
#define __PYX_EXTRA_ABI_MODULE_NAME ""
#endif
#define CYTHON_ABI "3_0_12" __PYX_EXTRA_ABI_MODULE_NAME
#define __PYX_ABI_MODULE_NAME "_cython_" CYTHON_ABI
#define __PYX_TYPE_MODULE_PREFIX __PYX_ABI_MODULE_NAME "."
#define CYTHON_HEX_VERSION 0x03000CF0
#define CYTHON_FUTURE_DIVISION 1
#include <stddef.h>
#ifndef offsetof
  #define offsetof(type, member) ( (size_t) & ((type*)0) -> member )
#endif
#if !defined(_WIN32) && !defined(WIN32) && !defined(MS_WINDOWS)
  #ifndef __stdcall
    #define __stdcall
  #endif
  #ifndef __cdecl
    #define __cdecl
  #endif
  #ifndef __fastcall
    #define __fastcall
  #endif
#endif
#ifndef DL_IMPORT
  #define DL_IMPORT(t) t
#endif
#ifndef DL_EXPORT
  #define DL_EXPORT(t) t
#endif
#define __PYX_COMMA ,
#ifndef HAVE_LONG_LONG
  #define HAVE_LONG_LONG
#endif
#ifndef PY_LONG_LONG
  #define PY_LONG_LONG LONG_LONG
#endif
#ifndef Py_HUGE_VAL
  #define Py_HUGE_VAL HUGE_VAL
#endif
#define __PYX_LIMITED_VERSION_HEX PY_VERSION_HEX
#if defined(GRAALVM_PYTHON)
  /* For very preliminary testing purposes. Most variables are set the same as PyPy.
     The existence of this section does not imply that anything works or is even tested */
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_LIMITED_API 0
  #define CYTHON_COMPILING_IN_GRAAL 1
  #define CYTHON_COMPILING_IN_NOGIL 0
  #undef CYTHON_USE_TYPE_SLOTS
  #define CYTHON_USE_TYPE_SLOTS 0
  #undef CYTHON_USE_TYPE_SPECS
  #define CYTHON_USE_TYPE_SPECS 0
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #if PY_VERSION_HEX < 0x03050000
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #undef CYTHON_USE_UNICODE_INTERNALS
  #define CYTHON_USE_UNICODE_INTERNALS 0
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #undef CYTHON_AVOID_BORROWED_REFS
  #define CYTHON_AVOID_BORROWED_REFS 1
  #undef CYTHON_ASSUME_SAFE_MACROS
  #define CYTHON_ASSUME_SAFE_MACROS 0
  #undef CYTHON_UNPACK_METHODS
  #define CYTHON_UNPACK_METHODS 0
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_GIL
  #define CYTHON_FAST_GIL 0
  #undef CYTHON_METH_FASTCALL
  #define CYTHON_METH_FASTCALL 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #ifndef CYTHON_PEP487_INIT_SUBCLASS
    #define CYTHON_PEP487_INIT_SUBCLASS (PY_MAJOR_VERSION >= 3)
  #endif
  #undef CYTHON_PEP489_MULTI_PHASE_INIT
  #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #undef CYTHON_USE_MODULE_STATE
  #define CYTHON_USE_MODULE_STATE 0
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE 0
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0
  #endif
  #undef CYTHON_USE_FREELISTS
  #define CYTHON_USE_FREELISTS 0
#elif defined(PYPY_VERSION)
  #define CYTHON_COMPILING_IN_PYPY 1
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_LIMITED_API 0
  #define CYTHON_COMPILING_IN_GRAAL 0
  #define CYTHON_COMPILING_IN_NOGIL 0
  #undef CYTHON_USE_TYPE_SLOTS
  #define CYTHON_USE_TYPE_SLOTS 0
  #ifndef CYTHON_USE_TYPE_SPECS
    #define CYTHON_USE_TYPE_SPECS 0
  #endif
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #if PY_VERSION_HEX < 0x03050000
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #undef CYTHON_USE_UNICODE_INTERNALS
  #define CYTHON_USE_UNICODE_INTERNALS 0
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #undef CYTHON_AVOID_BORROWED_REFS
  #define CYTHON_AVOID_BORROWED_REFS 1
  #undef CYTHON_ASSUME_SAFE_MACROS
  #define CYTHON_ASSUME_SAFE_MACROS 0
  #undef CYTHON_UNPACK_METHODS
  #define CYTHON_UNPACK_METHODS 0
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_GIL
  #define CYTHON_FAST_GIL 0
  #undef CYTHON_METH_FASTCALL
  #define CYTHON_METH_FASTCALL 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #ifndef CYTHON_PEP487_INIT_SUBCLASS
    #define CYTHON_PEP487_INIT_SUBCLASS (PY_MAJOR_VERSION >= 3)
  #endif
  #if PY_VERSION_HEX < 0x03090000
    #undef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #elif !defined(CYTHON_PEP489_MULTI_PHASE_INIT)
    #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #endif
  #undef CYTHON_USE_MODULE_STATE
  #define CYTHON_USE_MODULE_STATE 0
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE (PY_VERSION_HEX >= 0x030400a1 && PYPY_VERSION_NUM >= 0x07030C00)
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0
  #endif
  #undef CYTHON_USE_FREELISTS
  #define CYTHON_USE_FREELISTS 0
#elif defined(CYTHON_LIMITED_API)
  #ifdef Py_LIMITED_API
    #undef __PYX_LIMITED_VERSION_HEX
    #define __PYX_LIMITED_VERSION_HEX Py_LIMITED_API
  #endif
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_LIMITED_API 1
  #define CYTHON_COMPILING_IN_GRAAL 0
  #define CYTHON_COMPILING_IN_NOGIL 0
  #undef CYTHON_CLINE_IN_TRACEBACK
  #define CYTHON_CLINE_IN_TRACEBACK 0
  #undef CYTHON_USE_TYPE_SLOTS
  #define CYTHON_USE_TYPE_SLOTS 0
  #undef CYTHON_USE_TYPE_SPECS
  #define CYTHON_USE_TYPE_SPECS 1
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #undef CYTHON_USE_ASYNC_SLOTS
  #define CYTHON_USE_ASYNC_SLOTS 0
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #undef CYTHON_USE_UNICODE_INTERNALS
  #define CYTHON_USE_UNICODE_INTERNALS 0
  #ifndef CYTHON_USE_UNICODE_WRITER
    #define CYTHON_USE_UNICODE_WRITER 0
  #endif
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #undef CYTHON_ASSUME_SAFE_MACROS
  #define CYTHON_ASSUME_SAFE_MACROS 0
  #undef CYTHON_UNPACK_METHODS
  #define CYTHON_UNPACK_METHODS 0
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_GIL
  #define CYTHON_FAST_GIL 0
  #undef CYTHON_METH_FASTCALL
  #define CYTHON_METH_FASTCALL 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #ifndef CYTHON_PEP487_INIT_SUBCLASS
    #define CYTHON_PEP487_INIT_SUBCLASS 1
  #endif
  #undef CYTHON_PEP489_MULTI_PHASE_INIT
  #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #undef CYTHON_USE_MODULE_STATE
  #define CYTHON_USE_MODULE_STATE 1
  #ifndef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE 0
  #endif
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0
  #endif
  #undef CYTHON_USE_FREELISTS
  #define CYTHON_USE_FREELISTS 0
#elif defined(Py_GIL_DISABLED) || defined(Py_NOGIL)
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_LIMITED_API 0
  #define CYTHON_COMPILING_IN_GRAAL 0
  #define CYTHON_COMPILING_IN_NOGIL 1
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #ifndef CYTHON_USE_TYPE_SPECS
    #define CYTHON_USE_TYPE_SPECS 0
  #endif
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #ifndef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #ifndef CYTHON_USE_PYLONG_INTERNALS
    #define CYTHON_USE_PYLONG_INTERNALS 0
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_GIL
  #define CYTHON_FAST_GIL 0
  #ifndef CYTHON_METH_FASTCALL
    #define CYTHON_METH_FASTCALL 1
  #endif
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #ifndef CYTHON_PEP487_INIT_SUBCLASS
    #define CYTHON_PEP487_INIT_SUBCLASS 1
  #endif
  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #endif
  #ifndef CYTHON_USE_MODULE_STATE
    #define CYTHON_USE_MODULE_STATE 0
  #endif
  #ifndef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE 1
  #endif
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 1
  #endif
  #ifndef CYTHON_USE_FREELISTS
    #define CYTHON_USE_FREELISTS 0
  #endif
#else
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_CPYTHON 1
  #define CYTHON_COMPILING_IN_LIMITED_API 0
  #define CYTHON_COMPILING_IN_GRAAL 0
  #define CYTHON_COMPILING_IN_NOGIL 0
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #ifndef CYTHON_USE_TYPE_SPECS
    #define CYTHON_USE_TYPE_SPECS 0
  #endif
  #ifndef CYTHON_USE_PYTYPE_LOOKUP
    #define CYTHON_USE_PYTYPE_LOOKUP 1
  #endif
  #if PY_MAJOR_VERSION < 3
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #ifndef CYTHON_USE_PYLONG_INTERNALS
    #define CYTHON_USE_PYLONG_INTERNALS 1
  #endif
  #ifndef CYTHON_USE_PYLIST_INTERNALS
    #define CYTHON_USE_PYLIST_INTERNALS 1
  #endif
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #if PY_VERSION_HEX < 0x030300F0 || PY_VERSION_HEX >= 0x030B00A2
    #undef CYTHON_USE_UNICODE_WRITER
    #define CYTHON_USE_UNICODE_WRITER 0
  #elif !defined(CYTHON_USE_UNICODE_WRITER)
    #define CYTHON_USE_UNICODE_WRITER 1
  #endif
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #ifndef CYTHON_FAST_THREAD_STATE
    #define CYTHON_FAST_THREAD_STATE 1
  #endif
  #ifndef CYTHON_FAST_GIL
    #define CYTHON_FAST_GIL (PY_MAJOR_VERSION < 3 || PY_VERSION_HEX >= 0x03060000 && PY_VERSION_HEX < 0x030C00A6)
  #endif
  #ifndef CYTHON_METH_FASTCALL
    #define CYTHON_METH_FASTCALL (PY_VERSION_HEX >= 0x030700A1)
  #endif
  #ifndef CYTHON_FAST_PYCALL
    #define CYTHON_FAST_PYCALL 1
  #endif
  #ifndef CYTHON_PEP487_INIT_SUBCLASS
    #define CYTHON_PEP487_INIT_SUBCLASS 1
  #endif
  #if PY_VERSION_HEX < 0x03050000
    #undef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #elif !defined(CYTHON_PEP489_MULTI_PHASE_INIT)
    #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #endif
  #ifndef CYTHON_USE_MODULE_STATE
    #define CYTHON_USE_MODULE_STATE 0
  #endif
  #if PY_VERSION_HEX < 0x030400a1
    #undef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE 0
  #elif !defined(CYTHON_USE_TP_FINALIZE)
    #define CYTHON_USE_TP_FINALIZE 1
  #endif
  #if PY_VERSION_HEX < 0x030600B1
    #undef CYTHON_USE_DICT_VERSIONS
    #define CYTHON_USE_DICT_VERSIONS 0
  #elif !defined(CYTHON_USE_DICT_VERSIONS)
    #define CYTHON_USE_DICT_VERSIONS  (PY_VERSION_HEX < 0x030C00A5)
  #endif
  #if PY_VERSION_HEX < 0x030700A3
    #undef CYTHON_USE_EXC_INFO_STACK
    #define CYTHON_USE_EXC_INFO_STACK 0
  #elif !defined(CYTHON_USE_EXC_INFO_STACK)
    #define CYTHON_USE_EXC_INFO_STACK 1
  #endif
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 1
  #endif
  #ifndef CYTHON_USE_FREELISTS
    #define CYTHON_USE_FREELISTS 1
  #endif
#endif
#if !defined(CYTHON_FAST_PYCCALL)
#define CYTHON_FAST_PYCCALL  (CYTHON_FAST_PYCALL && PY_VERSION_HEX >= 0x030600B1)
#endif
#if !defined(CYTHON_VECTORCALL)
#define CYTHON_VECTORCALL  (CYTHON_FAST_PYCCALL && PY_VERSION_HEX >= 0x030800B1)
#endif
#define CYTHON_BACKPORT_VECTORCALL (CYTHON_METH_FASTCALL && PY_VERSION_HEX < 0x030800B1)
#if CYTHON_USE_PYLONG_INTERNALS
  #if PY_MAJOR_VERSION < 3
    #include "longintrepr.h"
  #endif
  #undef SHIFT
  #undef BASE
  #undef MASK
  #ifdef SIZEOF_VOID_P
    enum { __pyx_check_sizeof_voidp = 1 / (int)(SIZEOF_VOID_P == sizeof(void*)) };
  #endif
#endif
#ifndef __has_attribute
  #define __has_attribute(x) 0
#endif
#ifndef __has_cpp_attribute
  #define __has_cpp_attribute(x) 0
#endif
#ifndef CYTHON_RESTRICT
  #if defined(__GNUC__)
    #define CYTHON_RESTRICT __restrict__
  #elif defined(_MSC_VER) && _MSC_VER >= 1400
    #define CYTHON_RESTRICT __restrict
  #elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define CYTHON_RESTRICT restrict
  #else
    #define CYTHON_RESTRICT
  #endif
#endif
#ifndef CYTHON_UNUSED
  #if defined(__cplusplus)
    /* for clang __has_cpp_attribute(maybe_unused) is true even before C++17
     * but leads to warnings with -pedantic, since it is a C++17 feature */
    #if ((defined(_MSVC_LANG) && _MSVC_LANG >= 201703L) || __cplusplus >= 201703L)
      #if __has_cpp_attribute(maybe_unused)
        #define CYTHON_UNUSED [[maybe_unused]]
      #endif
    #endif
  #endif
#endif
#ifndef CYTHON_UNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define CYTHON_UNUSED __attribute__ ((__unused__))
#   else
#     define CYTHON_UNUSED
#   endif
# elif defined(__ICC) || (defined(__INTEL_COMPILER) && !defined(_MSC_VER))
#   define CYTHON_UNUSED __attribute__ ((__unused__))
# else
#   define CYTHON_UNUSED
# endif
#endif
#ifndef CYTHON_UNUSED_VAR
#  if defined(__cplusplus)
     template<class T> void CYTHON_UNUSED_VAR( const T& ) { }
#  else
#    define CYTHON_UNUSED_VAR(x) (void)(x)
#  endif
#endif
#ifndef CYTHON_MAYBE_UNUSED_VAR
  #define CYTHON_MAYBE_UNUSED_VAR(x) CYTHON_UNUSED_VAR(x)
#endif
#ifndef CYTHON_NCP_UNUSED
# if CYTHON_COMPILING_IN_CPYTHON
#  define CYTHON_NCP_UNUSED
# else
#  define CYTHON_NCP_UNUSED CYTHON_UNUSED
# endif
#endif
#ifndef CYTHON_USE_CPP_STD_MOVE
  #if defined(__cplusplus) && (\
    __cplusplus >= 201103L || (defined(_MSC_VER) && _MSC_VER >= 1600))
    #define CYTHON_USE_CPP_STD_MOVE 1
  #else
    #define CYTHON_USE_CPP_STD_MOVE 0
  #endif
#endif
#define __Pyx_void_to_None(void_result) ((void)(void_result), Py_INCREF(Py_None), Py_None)
#ifdef _MSC_VER
    #ifndef _MSC_STDINT_H_
        #if _MSC_VER < 1300
            typedef unsigned char     uint8_t;
            typedef unsigned short    uint16_t;
            typedef unsigned int      uint32_t;
        #else
            typedef unsigned __int8   uint8_t;
            typedef unsigned __int16  uint16_t;
            typedef unsigned __int32  uint32_t;
        #endif
    #endif
    #if _MSC_VER < 1300
        #ifdef _WIN64
            typedef unsigned long long  __pyx_uintptr_t;
        #else
            typedef unsigned int        __pyx_uintptr_t;
        #endif
    #else
        #ifdef _WIN64
            typedef unsigned __int64    __pyx_uintptr_t;
        #else
            typedef unsigned __int32    __pyx_uintptr_t;
        #endif
    #endif
#else
    #include <stdint.h>
    typedef uintptr_t  __pyx_uintptr_t;
#endif
#ifndef CYTHON_FALLTHROUGH
  #if defined(__cplusplus)
    /* for clang __has_cpp_attribute(fallthrough) is true even before C++17
     * but leads to warnings with -pedantic, since it is a C++17 feature */
    #if ((defined(_MSVC_LANG) && _MSVC_LANG >= 201703L) || __cplusplus >= 201703L)
      #if __has_cpp_attribute(fallthrough)
        #define CYTHON_FALLTHROUGH [[fallthrough]]
      #endif
    #endif
    #ifndef CYTHON_FALLTHROUGH
      #if __has_cpp_attribute(clang::fallthrough)
        #define CYTHON_FALLTHROUGH [[clang::fallthrough]]
      #elif __has_cpp_attribute(gnu::fallthrough)
        #define CYTHON_FALLTHROUGH [[gnu::fallthrough]]
      #endif
    #endif
  #endif
  #ifndef CYTHON_FALLTHROUGH
    #if __has_attribute(fallthrough)
      #define CYTHON_FALLTHROUGH __attribute__((fallthrough))
    #else
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
  #if defined(__clang__) && defined(__apple_build_version__)
    #if __apple_build_version__ < 7000000
      #undef  CYTHON_FALLTHROUGH
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
#endif
#ifdef __cplusplus
  template <typename T>
  struct __PYX_IS_UNSIGNED_IMPL {static const bool value = T(0) < T(-1);};
  #define __PYX_IS_UNSIGNED(type) (__PYX_IS_UNSIGNED_IMPL<type>::value)
#else
  #define __PYX_IS_UNSIGNED(type) (((type)-1) > 0)
#endif
#if CYTHON_COMPILING_IN_PYPY == 1
  #define __PYX_NEED_TP_PRINT_SLOT  (PY_VERSION_HEX >= 0x030800b4 && PY_VERSION_HEX < 0x030A0000)
#else
  #define __PYX_NEED_TP_PRINT_SLOT  (PY_VERSION_HEX >= 0x030800b4 && PY_VERSION_HEX < 0x03090000)
#endif
#define __PYX_REINTERPRET_FUNCION(func_pointer, other_pointer) ((func_pointer)(void(*)(void))(other_pointer))

#ifndef CYTHON_INLINE
  #if defined(__clang__)
    #define CYTHON_INLINE __inline__ __attribute__ ((__unused__))
  #elif defined(__GNUC__)
    #define CYTHON_INLINE __inline__
  #elif defined(_MSC_VER)
    #define CYTHON_INLINE __inline
  #elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define CYTHON_INLINE inline
  #else
    #define CYTHON_INLINE
  #endif
#endif

#define __PYX_BUILD_PY_SSIZE_T "n"
#define CYTHON_FORMAT_SSIZE_T "z"
#if PY_MAJOR_VERSION < 3
  #define __Pyx_BUILTIN_MODULE_NAME "__builtin__"
  #define __Pyx_DefaultClassType PyClass_Type
  #define __Pyx_PyCode_New(a, p, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a+k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
#else
  #define __Pyx_BUILTIN_MODULE_NAME "builtins"
  #define __Pyx_DefaultClassType PyType_Type
#if CYTHON_COMPILING_IN_LIMITED_API
    static CYTHON_INLINE PyObject* __Pyx_PyCode_New(int a, int p, int k, int l, int s, int f,
                                                    PyObject *code, PyObject *c, PyObject* n, PyObject *v,
                                                    PyObject *fv, PyObject *cell, PyObject* fn,
                                                    PyObject *name, int fline, PyObject *lnos) {
        PyObject *exception_table = NULL;
        PyObject *types_module=NULL, *code_type=NULL, *result=NULL;
        #if __PYX_LIMITED_VERSION_HEX < 0x030B0000
        PyObject *version_info;
        PyObject *py_minor_version = NULL;
        #endif
        long minor_version = 0;
        PyObject *type, *value, *traceback;
        PyErr_Fetch(&type, &value, &traceback);
        #if __PYX_LIMITED_VERSION_HEX >= 0x030B0000
        minor_version = 11;
        #else
        if (!(version_info = PySys_GetObject("version_info"))) goto end;
        if (!(py_minor_version = PySequence_GetItem(version_info, 1))) goto end;
        minor_version = PyLong_AsLong(py_minor_version);
        Py_DECREF(py_minor_version);
        if (minor_version == -1 && PyErr_Occurred()) goto end;
        #endif
        if (!(types_module = PyImport_ImportModule("types"))) goto end;
        if (!(code_type = PyObject_GetAttrString(types_module, "CodeType"))) goto end;
        if (minor_version <= 7) {
            (void)p;
            result = PyObject_CallFunction(code_type, "iiiiiOOOOOOiOO", a, k, l, s, f, code,
                          c, n, v, fn, name, fline, lnos, fv, cell);
        } else if (minor_version <= 10) {
            result = PyObject_CallFunction(code_type, "iiiiiiOOOOOOiOO", a,p, k, l, s, f, code,
                          c, n, v, fn, name, fline, lnos, fv, cell);
        } else {
            if (!(exception_table = PyBytes_FromStringAndSize(NULL, 0))) goto end;
            result = PyObject_CallFunction(code_type, "iiiiiiOOOOOOOiOO", a,p, k, l, s, f, code,
                          c, n, v, fn, name, name, fline, lnos, exception_table, fv, cell);
        }
    end:
        Py_XDECREF(code_type);
        Py_XDECREF(exception_table);
        Py_XDECREF(types_module);
        if (type) {
            PyErr_Restore(type, value, traceback);
        }
        return result;
    }
    #ifndef CO_OPTIMIZED
    #define CO_OPTIMIZED 0x0001
    #endif
    #ifndef CO_NEWLOCALS
    #define CO_NEWLOCALS 0x0002
    #endif
    #ifndef CO_VARARGS
    #define CO_VARARGS 0x0004
    #endif
    #ifndef CO_VARKEYWORDS
    #define CO_VARKEYWORDS 0x0008
    #endif
    #ifndef CO_ASYNC_GENERATOR
    #define CO_ASYNC_GENERATOR 0x0200
    #endif
    #ifndef CO_GENERATOR
    #define CO_GENERATOR 0x0020
    #endif
    #ifndef CO_COROUTINE
    #define CO_COROUTINE 0x0080
    #endif
#elif PY_VERSION_HEX >= 0x030B0000
  static CYTHON_INLINE PyCodeObject* __Pyx_PyCode_New(int a, int p, int k, int l, int s, int f,
                                                    PyObject *code, PyObject *c, PyObject* n, PyObject *v,
                                                    PyObject *fv, PyObject *cell, PyObject* fn,
                                                    PyObject *name, int fline, PyObject *lnos) {
    PyCodeObject *result;
    PyObject *empty_bytes = PyBytes_FromStringAndSize("", 0);
    if (!empty_bytes) return NULL;
    result =
      #if PY_VERSION_HEX >= 0x030C0000
        PyUnstable_Code_NewWithPosOnlyArgs
      #else
        PyCode_NewWithPosOnlyArgs
      #endif
        (a, p, k, l, s, f, code, c, n, v, fv, cell, fn, name, name, fline, lnos, empty_bytes);
    Py_DECREF(empty_bytes);
    return result;
  }
#elif PY_VERSION_HEX >= 0x030800B2 && !CYTHON_COMPILING_IN_PYPY
  #define __Pyx_PyCode_New(a, p, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_NewWithPosOnlyArgs(a, p, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
#else
  #define __Pyx_PyCode_New(a, p, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
#endif
#endif
#if PY_VERSION_HEX >= 0x030900A4 || defined(Py_IS_TYPE)
  #define __Pyx_IS_TYPE(ob, type) Py_IS_TYPE(ob, type)
#else
  #define __Pyx_IS_TYPE(ob, type) (((const PyObject*)ob)->ob_type == (type))
#endif
#if PY_VERSION_HEX >= 0x030A00B1 || defined(Py_Is)
  #define __Pyx_Py_Is(x, y)  Py_Is(x, y)
#else
  #define __Pyx_Py_Is(x, y) ((x) == (y))
#endif
#if PY_VERSION_HEX >= 0x030A00B1 || defined(Py_IsNone)
  #define __Pyx_Py_IsNone(ob) Py_IsNone(ob)
#else
  #define __Pyx_Py_IsNone(ob) __Pyx_Py_Is((ob), Py_None)
#endif
#if PY_VERSION_HEX >= 0x030A00B1 || defined(Py_IsTrue)
  #define __Pyx_Py_IsTrue(ob) Py_IsTrue(ob)
#else
  #define __Pyx_Py_IsTrue(ob) __Pyx_Py_Is((ob), Py_True)
#endif
#if PY_VERSION_HEX >= 0x030A00B1 || defined(Py_IsFalse)
  #define __Pyx_Py_IsFalse(ob) Py_IsFalse(ob)
#else
  #define __Pyx_Py_IsFalse(ob) __Pyx_Py_Is((ob), Py_False)
#endif
#define __Pyx_NoneAsNull(obj)  (__Pyx_Py_IsNone(obj) ? NULL : (obj))
#if PY_VERSION_HEX >= 0x030900F0 && !CYTHON_COMPILING_IN_PYPY
  #define __Pyx_PyObject_GC_IsFinalized(o) PyObject_GC_IsFinalized(o)
#else
  #define __Pyx_PyObject_GC_IsFinalized(o) _PyGC_FINALIZED(o)
#endif
#ifndef CO_COROUTINE
  #define CO_COROUTINE 0x80
#endif
#ifndef CO_ASYNC_GENERATOR
  #define CO_ASYNC_GENERATOR 0x200
#endif
#ifndef Py_TPFLAGS_CHECKTYPES
  #define Py_TPFLAGS_CHECKTYPES 0
#endif
#ifndef Py_TPFLAGS_HAVE_INDEX
  #define Py_TPFLAGS_HAVE_INDEX 0
#endif
#ifndef Py_TPFLAGS_HAVE_NEWBUFFER
  #define Py_TPFLAGS_HAVE_NEWBUFFER 0
#endif
#ifndef Py_TPFLAGS_HAVE_FINALIZE
  #define Py_TPFLAGS_HAVE_FINALIZE 0
#endif
#ifndef Py_TPFLAGS_SEQUENCE
  #define Py_TPFLAGS_SEQUENCE 0
#endif
#ifndef Py_TPFLAGS_MAPPING
  #define Py_TPFLAGS_MAPPING 0
#endif
#ifndef METH_STACKLESS
  #define METH_STACKLESS 0
#endif
#if PY_VERSION_HEX <= 0x030700A3 || !defined(METH_FASTCALL)
  #ifndef METH_FASTCALL
     #define METH_FASTCALL 0x80
  #endif
  typedef PyObject *(*__Pyx_PyCFunctionFast) (PyObject *self, PyObject *const *args, Py_ssize_t nargs);
  typedef PyObject *(*__Pyx_PyCFunctionFastWithKeywords) (PyObject *self, PyObject *const *args,
                                                          Py_ssize_t nargs, PyObject *kwnames);
#else
  #if PY_VERSION_HEX >= 0x030d00A4
  #  define __Pyx_PyCFunctionFast PyCFunctionFast
  #  define __Pyx_PyCFunctionFastWithKeywords PyCFunctionFastWithKeywords
  #else
  #  define __Pyx_PyCFunctionFast _PyCFunctionFast
  #  define __Pyx_PyCFunctionFastWithKeywords _PyCFunctionFastWithKeywords
  #endif
#endif
#if CYTHON_METH_FASTCALL
  #define __Pyx_METH_FASTCALL METH_FASTCALL
  #define __Pyx_PyCFunction_FastCall __Pyx_PyCFunctionFast
  #define __Pyx_PyCFunction_FastCallWithKeywords __Pyx_PyCFunctionFastWithKeywords
#else
  #define __Pyx_METH_FASTCALL METH_VARARGS
  #define __Pyx_PyCFunction_FastCall PyCFunction
  #define __Pyx_PyCFunction_FastCallWithKeywords PyCFunctionWithKeywords
#endif
#if CYTHON_VECTORCALL
  #define __pyx_vectorcallfunc vectorcallfunc
  #define __Pyx_PY_VECTORCALL_ARGUMENTS_OFFSET  PY_VECTORCALL_ARGUMENTS_OFFSET
  #define __Pyx_PyVectorcall_NARGS(n)  PyVectorcall_NARGS((size_t)(n))
#elif CYTHON_BACKPORT_VECTORCALL
  typedef PyObject *(*__pyx_vectorcallfunc)(PyObject *callable, PyObject *const *args,
                                            size_t nargsf, PyObject *kwnames);
  #define __Pyx_PY_VECTORCALL_ARGUMENTS_OFFSET  ((size_t)1 << (8 * sizeof(size_t) - 1))
  #define __Pyx_PyVectorcall_NARGS(n)  ((Py_ssize_t)(((size_t)(n)) & ~__Pyx_PY_VECTORCALL_ARGUMENTS_OFFSET))
#else
  #define __Pyx_PY_VECTORCALL_ARGUMENTS_OFFSET  0
  #define __Pyx_PyVectorcall_NARGS(n)  ((Py_ssize_t)(n))
#endif
#if PY_MAJOR_VERSION >= 0x030900B1
#define __Pyx_PyCFunction_CheckExact(func)  PyCFunction_CheckExact(func)
#else
#define __Pyx_PyCFunction_CheckExact(func)  PyCFunction_Check(func)
#endif
#define __Pyx_CyOrPyCFunction_Check(func)  PyCFunction_Check(func)
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_CyOrPyCFunction_GET_FUNCTION(func)  (((PyCFunctionObject*)(func))->m_ml->ml_meth)
#elif !CYTHON_COMPILING_IN_LIMITED_API
#define __Pyx_CyOrPyCFunction_GET_FUNCTION(func)  PyCFunction_GET_FUNCTION(func)
#endif
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_CyOrPyCFunction_GET_FLAGS(func)  (((PyCFunctionObject*)(func))->m_ml->ml_flags)
static CYTHON_INLINE PyObject* __Pyx_CyOrPyCFunction_GET_SELF(PyObject *func) {
    return (__Pyx_CyOrPyCFunction_GET_FLAGS(func) & METH_STATIC) ? NULL : ((PyCFunctionObject*)func)->m_self;
}
#endif
static CYTHON_INLINE int __Pyx__IsSameCFunction(PyObject *func, void *cfunc) {
#if CYTHON_COMPILING_IN_LIMITED_API
    return PyCFunction_Check(func) && PyCFunction_GetFunction(func) == (PyCFunction) cfunc;
#else
    return PyCFunction_Check(func) && PyCFunction_GET_FUNCTION(func) == (PyCFunction) cfunc;
#endif
}
#define __Pyx_IsSameCFunction(func, cfunc)   __Pyx__IsSameCFunction(func, cfunc)
#if __PYX_LIMITED_VERSION_HEX < 0x030900B1
  #define __Pyx_PyType_FromModuleAndSpec(m, s, b)  ((void)m, PyType_FromSpecWithBases(s, b))
  typedef PyObject *(*__Pyx_PyCMethod)(PyObject *, PyTypeObject *, PyObject *const *, size_t, PyObject *);
#else
  #define __Pyx_PyType_FromModuleAndSpec(m, s, b)  PyType_FromModuleAndSpec(m, s, b)
  #define __Pyx_PyCMethod  PyCMethod
#endif
#ifndef METH_METHOD
  #define METH_METHOD 0x200
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Malloc)
  #define PyObject_Malloc(s)   PyMem_Malloc(s)
  #define PyObject_Free(p)     PyMem_Free(p)
  #define PyObject_Realloc(p)  PyMem_Realloc(p)
#endif
#if CYTHON_COMPILING_IN_LIMITED_API
  #define __Pyx_PyCode_HasFreeVars(co)  (PyCode_GetNumFree(co) > 0)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno)
#else
  #define __Pyx_PyCode_HasFreeVars(co)  (PyCode_GetNumFree(co) > 0)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno)  (frame)->f_lineno = (lineno)
#endif
#if CYTHON_COMPILING_IN_LIMITED_API
  #define __Pyx_PyThreadState_Current PyThreadState_Get()
#elif !CYTHON_FAST_THREAD_STATE
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#elif PY_VERSION_HEX >= 0x030d00A1
  #define __Pyx_PyThreadState_Current PyThreadState_GetUnchecked()
#elif PY_VERSION_HEX >= 0x03060000
  #define __Pyx_PyThreadState_Current _PyThreadState_UncheckedGet()
#elif PY_VERSION_HEX >= 0x03000000
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#else
  #define __Pyx_PyThreadState_Current _PyThreadState_Current
#endif
#if CYTHON_COMPILING_IN_LIMITED_API
static CYTHON_INLINE void *__Pyx_PyModule_GetState(PyObject *op)
{
    void *result;
    result = PyModule_GetState(op);
    if (!result)
        Py_FatalError("Couldn't find the module state");
    return result;
}
#endif
#define __Pyx_PyObject_GetSlot(obj, name, func_ctype)  __Pyx_PyType_GetSlot(Py_TYPE(obj), name, func_ctype)
#if CYTHON_COMPILING_IN_LIMITED_API
  #define __Pyx_PyType_GetSlot(type, name, func_ctype)  ((func_ctype) PyType_GetSlot((type), Py_##name))
#else
  #define __Pyx_PyType_GetSlot(type, name, func_ctype)  ((type)->name)
#endif
#if PY_VERSION_HEX < 0x030700A2 && !defined(PyThread_tss_create) && !defined(Py_tss_NEEDS_INIT)
#include "pythread.h"
#define Py_tss_NEEDS_INIT 0
typedef int Py_tss_t;
static CYTHON_INLINE int PyThread_tss_create(Py_tss_t *key) {
  *key = PyThread_create_key();
  return 0;
}
static CYTHON_INLINE Py_tss_t * PyThread_tss_alloc(void) {
  Py_tss_t *key = (Py_tss_t *)PyObject_Malloc(sizeof(Py_tss_t));
  *key = Py_tss_NEEDS_INIT;
  return key;
}
static CYTHON_INLINE void PyThread_tss_free(Py_tss_t *key) {
  PyObject_Free(key);
}
static CYTHON_INLINE int PyThread_tss_is_created(Py_tss_t *key) {
  return *key != Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE void PyThread_tss_delete(Py_tss_t *key) {
  PyThread_delete_key(*key);
  *key = Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE int PyThread_tss_set(Py_tss_t *key, void *value) {
  return PyThread_set_key_value(*key, value);
}
static CYTHON_INLINE void * PyThread_tss_get(Py_tss_t *key) {
  return PyThread_get_key_value(*key);
}
#endif
#if PY_MAJOR_VERSION < 3
    #if CYTHON_COMPILING_IN_PYPY
        #if PYPY_VERSION_NUM < 0x07030600
            #if defined(__cplusplus) && __cplusplus >= 201402L
                [[deprecated("`with nogil:` inside a nogil function will not release the GIL in PyPy2 < 7.3.6")]]
            #elif defined(__GNUC__) || defined(__clang__)
                __attribute__ ((__deprecated__("`with nogil:` inside a nogil function will not release the GIL in PyPy2 < 7.3.6")))
            #elif defined(_MSC_VER)
                __declspec(deprecated("`with nogil:` inside a nogil function will not release the GIL in PyPy2 < 7.3.6"))
            #endif
            static CYTHON_INLINE int PyGILState_Check(void) {
                return 0;
            }
        #else  // PYPY_VERSION_NUM < 0x07030600
        #endif  // PYPY_VERSION_NUM < 0x07030600
    #else
        static CYTHON_INLINE int PyGILState_Check(void) {
            PyThreadState * tstate = _PyThreadState_Current;
            return tstate && (tstate == PyGILState_GetThisThreadState());
        }
    #endif
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030d0000 || defined(_PyDict_NewPresized)
#define __Pyx_PyDict_NewPresized(n)  ((n <= 8) ? PyDict_New() : _PyDict_NewPresized(n))
#else
#define __Pyx_PyDict_NewPresized(n)  PyDict_New()
#endif
#if PY_MAJOR_VERSION >= 3 || CYTHON_FUTURE_DIVISION
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_TrueDivide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceTrueDivide(x,y)
#else
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_Divide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceDivide(x,y)
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX > 0x030600B4 && PY_VERSION_HEX < 0x030d0000 && CYTHON_USE_UNICODE_INTERNALS
#define __Pyx_PyDict_GetItemStrWithError(dict, name)  _PyDict_GetItem_KnownHash(dict, name, ((PyASCIIObject *) name)->hash)
static CYTHON_INLINE PyObject * __Pyx_PyDict_GetItemStr(PyObject *dict, PyObject *name) {
    PyObject *res = __Pyx_PyDict_GetItemStrWithError(dict, name);
    if (res == NULL) PyErr_Clear();
    return res;
}
#elif PY_MAJOR_VERSION >= 3 && (!CYTHON_COMPILING_IN_PYPY || PYPY_VERSION_NUM >= 0x07020000)
#define __Pyx_PyDict_GetItemStrWithError  PyDict_GetItemWithError
#define __Pyx_PyDict_GetItemStr           PyDict_GetItem
#else
static CYTHON_INLINE PyObject * __Pyx_PyDict_GetItemStrWithError(PyObject *dict, PyObject *name) {
#if CYTHON_COMPILING_IN_PYPY
    return PyDict_GetItem(dict, name);
#else
    PyDictEntry *ep;
    PyDictObject *mp = (PyDictObject*) dict;
    long hash = ((PyStringObject *) name)->ob_shash;
    assert(hash != -1);
    ep = (mp->ma_lookup)(mp, name, hash);
    if (ep == NULL) {
        return NULL;
    }
    return ep->me_value;
#endif
}
#define __Pyx_PyDict_GetItemStr           PyDict_GetItem
#endif
#if CYTHON_USE_TYPE_SLOTS
  #define __Pyx_PyType_GetFlags(tp)   (((PyTypeObject *)tp)->tp_flags)
  #define __Pyx_PyType_HasFeature(type, feature)  ((__Pyx_PyType_GetFlags(type) & (feature)) != 0)
  #define __Pyx_PyObject_GetIterNextFunc(obj)  (Py_TYPE(obj)->tp_iternext)
#else
  #define __Pyx_PyType_GetFlags(tp)   (PyType_GetFlags((PyTypeObject *)tp))
  #define __Pyx_PyType_HasFeature(type, feature)  PyType_HasFeature(type, feature)
  #define __Pyx_PyObject_GetIterNextFunc(obj)  PyIter_Next
#endif
#if CYTHON_COMPILING_IN_LIMITED_API
  #define __Pyx_SetItemOnTypeDict(tp, k, v) PyObject_GenericSetAttr((PyObject*)tp, k, v)
#else
  #define __Pyx_SetItemOnTypeDict(tp, k, v) PyDict_SetItem(tp->tp_dict, k, v)
#endif
#if CYTHON_USE_TYPE_SPECS && PY_VERSION_HEX >= 0x03080000
#define __Pyx_PyHeapTypeObject_GC_Del(obj)  {\
    PyTypeObject *type = Py_TYPE((PyObject*)obj);\
    assert(__Pyx_PyType_HasFeature(type, Py_TPFLAGS_HEAPTYPE));\
    PyObject_GC_Del(obj);\
    Py_DECREF(type);\
}
#else
#define __Pyx_PyHeapTypeObject_GC_Del(obj)  PyObject_GC_Del(obj)
#endif
#if CYTHON_COMPILING_IN_LIMITED_API
  #define CYTHON_PEP393_ENABLED 1
  #define __Pyx_PyUnicode_READY(op)       (0)
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GetLength(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) PyUnicode_ReadChar(u, i)
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   ((void)u, 1114111U)
  #define __Pyx_PyUnicode_KIND(u)         ((void)u, (0))
  #define __Pyx_PyUnicode_DATA(u)         ((void*)u)
  #define __Pyx_PyUnicode_READ(k, d, i)   ((void)k, PyUnicode_ReadChar((PyObject*)(d), i))
  #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GetLength(u))
#elif PY_VERSION_HEX > 0x03030000 && defined(PyUnicode_KIND)
  #define CYTHON_PEP393_ENABLED 1
  #if PY_VERSION_HEX >= 0x030C0000
    #define __Pyx_PyUnicode_READY(op)       (0)
  #else
    #define __Pyx_PyUnicode_READY(op)       (likely(PyUnicode_IS_READY(op)) ?\
                                                0 : _PyUnicode_Ready((PyObject *)(op)))
  #endif
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_LENGTH(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) PyUnicode_READ_CHAR(u, i)
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   PyUnicode_MAX_CHAR_VALUE(u)
  #define __Pyx_PyUnicode_KIND(u)         ((int)PyUnicode_KIND(u))
  #define __Pyx_PyUnicode_DATA(u)         PyUnicode_DATA(u)
  #define __Pyx_PyUnicode_READ(k, d, i)   PyUnicode_READ(k, d, i)
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  PyUnicode_WRITE(k, d, i, (Py_UCS4) ch)
  #if PY_VERSION_HEX >= 0x030C0000
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_LENGTH(u))
  #else
    #if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x03090000
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : ((PyCompactUnicodeObject *)(u))->wstr_length))
    #else
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : PyUnicode_GET_SIZE(u)))
    #endif
  #endif
#else
  #define CYTHON_PEP393_ENABLED 0
  #define PyUnicode_1BYTE_KIND  1
  #define PyUnicode_2BYTE_KIND  2
  #define PyUnicode_4BYTE_KIND  4
  #define __Pyx_PyUnicode_READY(op)       (0)
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_SIZE(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) ((Py_UCS4)(PyUnicode_AS_UNICODE(u)[i]))
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   ((sizeof(Py_UNICODE) == 2) ? 65535U : 1114111U)
  #define __Pyx_PyUnicode_KIND(u)         ((int)sizeof(Py_UNICODE))
  #define __Pyx_PyUnicode_DATA(u)         ((void*)PyUnicode_AS_UNICODE(u))
  #define __Pyx_PyUnicode_READ(k, d, i)   ((void)(k), (Py_UCS4)(((Py_UNICODE*)d)[i]))
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  (((void)(k)), ((Py_UNICODE*)d)[i] = (Py_UNICODE) ch)
  #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_SIZE(u))
#endif
#if CYTHON_COMPILING_IN_PYPY
  #define __Pyx_PyUnicode_Concat(a, b)      PyNumber_Add(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  PyNumber_Add(a, b)
#else
  #define __Pyx_PyUnicode_Concat(a, b)      PyUnicode_Concat(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  ((unlikely((a) == Py_None) || unlikely((b) == Py_None)) ?\
      PyNumber_Add(a, b) : __Pyx_PyUnicode_Concat(a, b))
#endif
#if CYTHON_COMPILING_IN_PYPY
  #if !defined(PyUnicode_DecodeUnicodeEscape)
    #define PyUnicode_DecodeUnicodeEscape(s, size, errors)  PyUnicode_Decode(s, size, "unicode_escape", errors)
  #endif
  #if !defined(PyUnicode_Contains) || (PY_MAJOR_VERSION == 2 && PYPY_VERSION_NUM < 0x07030500)
    #undef PyUnicode_Contains
    #define PyUnicode_Contains(u, s)  PySequence_Contains(u, s)
  #endif
  #if !defined(PyByteArray_Check)
    #define PyByteArray_Check(obj)  PyObject_TypeCheck(obj, &PyByteArray_Type)
  #endif
  #if !defined(PyObject_Format)
    #define PyObject_Format(obj, fmt)  PyObject_CallMethod(obj, "__format__", "O", fmt)
  #endif
#endif
#define __Pyx_PyString_FormatSafe(a, b)   ((unlikely((a) == Py_None || (PyString_Check(b) && !PyString_CheckExact(b)))) ? PyNumber_Remainder(a, b) : __Pyx_PyString_Format(a, b))
#define __Pyx_PyUnicode_FormatSafe(a, b)  ((unlikely((a) == Py_None || (PyUnicode_Check(b) && !PyUnicode_CheckExact(b)))) ? PyNumber_Remainder(a, b) : PyUnicode_Format(a, b))
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyString_Format(a, b)  PyUnicode_Format(a, b)
#else
  #define __Pyx_PyString_Format(a, b)  PyString_Format(a, b)
#endif
#if PY_MAJOR_VERSION < 3 && !defined(PyObject_ASCII)
  #define PyObject_ASCII(o)            PyObject_Repr(o)
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBaseString_Type            PyUnicode_Type
  #define PyStringObject               PyUnicodeObject
  #define PyString_Type                PyUnicode_Type
  #define PyString_Check               PyUnicode_Check
  #define PyString_CheckExact          PyUnicode_CheckExact
#ifndef PyObject_Unicode
  #define PyObject_Unicode             PyObject_Str
#endif
#endif
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyBaseString_Check(obj) PyUnicode_Check(obj)
  #define __Pyx_PyBaseString_CheckExact(obj) PyUnicode_CheckExact(obj)
#else
  #define __Pyx_PyBaseString_Check(obj) (PyString_Check(obj) || PyUnicode_Check(obj))
  #define __Pyx_PyBaseString_CheckExact(obj) (PyString_CheckExact(obj) || PyUnicode_CheckExact(obj))
#endif
#if CYTHON_COMPILING_IN_CPYTHON
  #define __Pyx_PySequence_ListKeepNew(obj)\
    (likely(PyList_CheckExact(obj) && Py_REFCNT(obj) == 1) ? __Pyx_NewRef(obj) : PySequence_List(obj))
#else
  #define __Pyx_PySequence_ListKeepNew(obj)  PySequence_List(obj)
#endif
#ifndef PySet_CheckExact
  #define PySet_CheckExact(obj)        __Pyx_IS_TYPE(obj, &PySet_Type)
#endif
#if PY_VERSION_HEX >= 0x030900A4
  #define __Pyx_SET_REFCNT(obj, refcnt) Py_SET_REFCNT(obj, refcnt)
  #define __Pyx_SET_SIZE(obj, size) Py_SET_SIZE(obj, size)
#else
  #define __Pyx_SET_REFCNT(obj, refcnt) Py_REFCNT(obj) = (refcnt)
  #define __Pyx_SET_SIZE(obj, size) Py_SIZE(obj) = (size)
#endif
#if CYTHON_ASSUME_SAFE_MACROS
  #define __Pyx_PySequence_ITEM(o, i) PySequence_ITEM(o, i)
  #define __Pyx_PySequence_SIZE(seq)  Py_SIZE(seq)
  #define __Pyx_PyTuple_SET_ITEM(o, i, v) (PyTuple_SET_ITEM(o, i, v), (0))
  #define __Pyx_PyList_SET_ITEM(o, i, v) (PyList_SET_ITEM(o, i, v), (0))
  #define __Pyx_PyTuple_GET_SIZE(o) PyTuple_GET_SIZE(o)
  #define __Pyx_PyList_GET_SIZE(o) PyList_GET_SIZE(o)
  #define __Pyx_PySet_GET_SIZE(o) PySet_GET_SIZE(o)
  #define __Pyx_PyBytes_GET_SIZE(o) PyBytes_GET_SIZE(o)
  #define __Pyx_PyByteArray_GET_SIZE(o) PyByteArray_GET_SIZE(o)
#else
  #define __Pyx_PySequence_ITEM(o, i) PySequence_GetItem(o, i)
  #define __Pyx_PySequence_SIZE(seq)  PySequence_Size(seq)
  #define __Pyx_PyTuple_SET_ITEM(o, i, v) PyTuple_SetItem(o, i, v)
  #define __Pyx_PyList_SET_ITEM(o, i, v) PyList_SetItem(o, i, v)
  #define __Pyx_PyTuple_GET_SIZE(o) PyTuple_Size(o)
  #define __Pyx_PyList_GET_SIZE(o) PyList_Size(o)
  #define __Pyx_PySet_GET_SIZE(o) PySet_Size(o)
  #define __Pyx_PyBytes_GET_SIZE(o) PyBytes_Size(o)
  #define __Pyx_PyByteArray_GET_SIZE(o) PyByteArray_Size(o)
#endif
#if __PYX_LIMITED_VERSION_HEX >= 0x030d00A1
  #define __Pyx_PyImport_AddModuleRef(name) PyImport_AddModuleRef(name)
#else
  static CYTHON_INLINE PyObject *__Pyx_PyImport_AddModuleRef(const char *name) {
      PyObject *module = PyImport_AddModule(name);
      Py_XINCREF(module);
      return module;
  }
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyIntObject                  PyLongObject
  #define PyInt_Type                   PyLong_Type
  #define PyInt_Check(op)              PyLong_Check(op)
  #define PyInt_CheckExact(op)         PyLong_CheckExact(op)
  #define __Pyx_Py3Int_Check(op)       PyLong_Check(op)
  #define __Pyx_Py3Int_CheckExact(op)  PyLong_CheckExact(op)
  #define PyInt_FromString             PyLong_FromString
  #define PyInt_FromUnicode            PyLong_FromUnicode
  #define PyInt_FromLong               PyLong_FromLong
  #define PyInt_FromSize_t             PyLong_FromSize_t
  #define PyInt_FromSsize_t            PyLong_FromSsize_t
  #define PyInt_AsLong                 PyLong_AsLong
  #define PyInt_AS_LONG                PyLong_AS_LONG
  #define PyInt_AsSsize_t              PyLong_AsSsize_t
  #define PyInt_AsUnsignedLongMask     PyLong_AsUnsignedLongMask
  #define PyInt_AsUnsignedLongLongMask PyLong_AsUnsignedLongLongMask
  #define PyNumber_Int                 PyNumber_Long
#else
  #define __Pyx_Py3Int_Check(op)       (PyLong_Check(op) || PyInt_Check(op))
  #define __Pyx_Py3Int_CheckExact(op)  (PyLong_CheckExact(op) || PyInt_CheckExact(op))
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBoolObject                 PyLongObject
#endif
#if PY_MAJOR_VERSION >= 3 && CYTHON_COMPILING_IN_PYPY
  #ifndef PyUnicode_InternFromString
    #define PyUnicode_InternFromString(s) PyUnicode_FromString(s)
  #endif
#endif
#if PY_VERSION_HEX < 0x030200A4
  typedef long Py_hash_t;
  #define __Pyx_PyInt_FromHash_t PyInt_FromLong
  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsHash_t
#else
  #define __Pyx_PyInt_FromHash_t PyInt_FromSsize_t
  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsSsize_t
#endif
#if CYTHON_USE_ASYNC_SLOTS
  #if PY_VERSION_HEX >= 0x030500B1
    #define __Pyx_PyAsyncMethodsStruct PyAsyncMethods
    #define __Pyx_PyType_AsAsync(obj) (Py_TYPE(obj)->tp_as_async)
  #else
    #define __Pyx_PyType_AsAsync(obj) ((__Pyx_PyAsyncMethodsStruct*) (Py_TYPE(obj)->tp_reserved))
  #endif
#else
  #define __Pyx_PyType_AsAsync(obj) NULL
#endif
#ifndef __Pyx_PyAsyncMethodsStruct
    typedef struct {
        unaryfunc am_await;
        unaryfunc am_aiter;
        unaryfunc am_anext;
    } __Pyx_PyAsyncMethodsStruct;
#endif

#if defined(_WIN32) || defined(WIN32) || defined(MS_WINDOWS)
  #if !defined(_USE_MATH_DEFINES)
    #define _USE_MATH_DEFINES
  #endif
#endif
#include <math.h>
#ifdef NAN
#define __PYX_NAN() ((float) NAN)
#else
static CYTHON_INLINE float __PYX_NAN() {
  float value;
  memset(&value, 0xFF, sizeof(value));
  return value;
}
#endif
#if defined(__CYGWIN__) && defined(_LDBL_EQ_DBL)
#define __Pyx_truncl trunc
#else
#define __Pyx_truncl truncl
#endif

#define __PYX_MARK_ERR_POS(f_index, lineno) \
    { __pyx_filename = __pyx_f[f_index]; (void)__pyx_filename; __pyx_lineno = lineno; (void)__pyx_lineno; __pyx_clineno = __LINE__;  (void)__pyx_clineno; }
#define __PYX_ERR(f_index, lineno, Ln_error) \
    { __PYX_MARK_ERR_POS(f_index, lineno) goto Ln_error; }

#ifdef CYTHON_EXTERN_C
    #undef __PYX_EXTERN_C
    #define __PYX_EXTERN_C CYTHON_EXTERN_C
#elif defined(__PYX_EXTERN_C)
    #ifdef _MSC_VER
    #pragma message ("Please do not define the '__PYX_EXTERN_C' macro externally. Use 'CYTHON_EXTERN_C' instead.")
    #else
    #warning Please do not define the '__PYX_EXTERN_C' macro externally. Use 'CYTHON_EXTERN_C' instead.
    #endif
#else
  #ifdef __cplusplus
    #define __PYX_EXTERN_C extern "C"
  #else
    #define __PYX_EXTERN_C extern
  #endif
#endif

#define __PYX_HAVE__configs
#define __PYX_HAVE_API__configs
/* Early includes */
#ifdef _OPENMP
#include <omp.h>
#endif /* _OPENMP */

#if defined(PYREX_WITHOUT_ASSERTIONS) && !defined(CYTHON_WITHOUT_ASSERTIONS)
#define CYTHON_WITHOUT_ASSERTIONS
#endif

typedef struct {PyObject **p; const char *s; const Py_ssize_t n; const char* encoding;
                const char is_unicode; const char is_str; const char intern; } __Pyx_StringTabEntry;

#define __PYX_DEFAULT_STRING_ENCODING_IS_ASCII 0
#define __PYX_DEFAULT_STRING_ENCODING_IS_UTF8 0
#define __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT (PY_MAJOR_VERSION >= 3 && __PYX_DEFAULT_STRING_ENCODING_IS_UTF8)
#define __PYX_DEFAULT_STRING_ENCODING ""
#define __Pyx_PyObject_FromString __Pyx_PyBytes_FromString
#define __Pyx_PyObject_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#define __Pyx_uchar_cast(c) ((unsigned char)c)
#define __Pyx_long_cast(x) ((long)x)
#define __Pyx_fits_Py_ssize_t(v, type, is_signed)  (\
    (sizeof(type) < sizeof(Py_ssize_t))  ||\
    (sizeof(type) > sizeof(Py_ssize_t) &&\
          likely(v < (type)PY_SSIZE_T_MAX ||\
                 v == (type)PY_SSIZE_T_MAX)  &&\
          (!is_signed || likely(v > (type)PY_SSIZE_T_MIN ||\
                                v == (type)PY_SSIZE_T_MIN)))  ||\
    (sizeof(type) == sizeof(Py_ssize_t) &&\
          (is_signed || likely(v < (type)PY_SSIZE_T_MAX ||\
                               v == (type)PY_SSIZE_T_MAX)))  )
static CYTHON_INLINE int __Pyx_is_valid_index(Py_ssize_t i, Py_ssize_t limit) {
    return (size_t) i < (size_t) limit;
}
#if defined (__cplusplus) && __cplusplus >= 201103L
    #include <cstdlib>
    #define __Pyx_sst_abs(value) std::abs(value)
#elif SIZEOF_INT >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) abs(value)
#elif SIZEOF_LONG >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) labs(value)
#elif defined (_MSC_VER)
    #define __Pyx_sst_abs(value) ((Py_ssize_t)_abs64(value))
#elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define __Pyx_sst_abs(value) llabs(value)
#elif defined (__GNUC__)
    #define __Pyx_sst_abs(value) __builtin_llabs(value)
#else
    #define __Pyx_sst_abs(value) ((value<0) ? -value : value)
#endif
static CYTHON_INLINE Py_ssize_t __Pyx_ssize_strlen(const char *s);
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject*);
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject*, Py_ssize_t* length);
static CYTHON_INLINE PyObject* __Pyx_PyByteArray_FromString(const char*);
#define __Pyx_PyByteArray_FromStringAndSize(s, l) PyByteArray_FromStringAndSize((const char*)s, l)
#define __Pyx_PyBytes_FromString        PyBytes_FromString
#define __Pyx_PyBytes_FromStringAndSize PyBytes_FromStringAndSize
static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char*);
#if PY_MAJOR_VERSION < 3
    #define __Pyx_PyStr_FromString        __Pyx_PyBytes_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#else
    #define __Pyx_PyStr_FromString        __Pyx_PyUnicode_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyUnicode_FromStringAndSize
#endif
#define __Pyx_PyBytes_AsWritableString(s)     ((char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableSString(s)    ((signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableUString(s)    ((unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsString(s)     ((const char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsSString(s)    ((const signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsUString(s)    ((const unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyObject_AsWritableString(s)    ((char*)(__pyx_uintptr_t) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableSString(s)    ((signed char*)(__pyx_uintptr_t) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableUString(s)    ((unsigned char*)(__pyx_uintptr_t) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsSString(s)    ((const signed char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsUString(s)    ((const unsigned char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_FromCString(s)  __Pyx_PyObject_FromString((const char*)s)
#define __Pyx_PyBytes_FromCString(s)   __Pyx_PyBytes_FromString((const char*)s)
#define __Pyx_PyByteArray_FromCString(s)   __Pyx_PyByteArray_FromString((const char*)s)
#define __Pyx_PyStr_FromCString(s)     __Pyx_PyStr_FromString((const char*)s)
#define __Pyx_PyUnicode_FromCString(s) __Pyx_PyUnicode_FromString((const char*)s)
#define __Pyx_PyUnicode_FromOrdinal(o)       PyUnicode_FromOrdinal((int)o)
#define __Pyx_PyUnicode_AsUnicode            PyUnicode_AsUnicode
#define __Pyx_NewRef(obj) (Py_INCREF(obj), obj)
#define __Pyx_Owned_Py_None(b) __Pyx_NewRef(Py_None)
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b);
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject*);
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject*);
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x);
#define __Pyx_PySequence_Tuple(obj)\
    (likely(PyTuple_CheckExact(obj)) ? __Pyx_NewRef(obj) : PySequence_Tuple(obj))
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject*);
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t);
static CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject*);
#if CYTHON_ASSUME_SAFE_MACROS
#define __pyx_PyFloat_AsDouble(x) (PyFloat_CheckExact(x) ? PyFloat_AS_DOUBLE(x) : PyFloat_AsDouble(x))
#else
#define __pyx_PyFloat_AsDouble(x) PyFloat_AsDouble(x)
#endif
#define __pyx_PyFloat_AsFloat(x) ((float) __pyx_PyFloat_AsDouble(x))
#if PY_MAJOR_VERSION >= 3
#define __Pyx_PyNumber_Int(x) (PyLong_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Long(x))
#else
#define __Pyx_PyNumber_Int(x) (PyInt_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Int(x))
#endif
#if CYTHON_USE_PYLONG_INTERNALS
  #if PY_VERSION_HEX >= 0x030C00A7
  #ifndef _PyLong_SIGN_MASK
    #define _PyLong_SIGN_MASK 3
  #endif
  #ifndef _PyLong_NON_SIZE_BITS
    #define _PyLong_NON_SIZE_BITS 3
  #endif
  #define __Pyx_PyLong_Sign(x)  (((PyLongObject*)x)->long_value.lv_tag & _PyLong_SIGN_MASK)
  #define __Pyx_PyLong_IsNeg(x)  ((__Pyx_PyLong_Sign(x) & 2) != 0)
  #define __Pyx_PyLong_IsNonNeg(x)  (!__Pyx_PyLong_IsNeg(x))
  #define __Pyx_PyLong_IsZero(x)  (__Pyx_PyLong_Sign(x) & 1)
  #define __Pyx_PyLong_IsPos(x)  (__Pyx_PyLong_Sign(x) == 0)
  #define __Pyx_PyLong_CompactValueUnsigned(x)  (__Pyx_PyLong_Digits(x)[0])
  #define __Pyx_PyLong_DigitCount(x)  ((Py_ssize_t) (((PyLongObject*)x)->long_value.lv_tag >> _PyLong_NON_SIZE_BITS))
  #define __Pyx_PyLong_SignedDigitCount(x)\
        ((1 - (Py_ssize_t) __Pyx_PyLong_Sign(x)) * __Pyx_PyLong_DigitCount(x))
  #if defined(PyUnstable_Long_IsCompact) && defined(PyUnstable_Long_CompactValue)
    #define __Pyx_PyLong_IsCompact(x)     PyUnstable_Long_IsCompact((PyLongObject*) x)
    #define __Pyx_PyLong_CompactValue(x)  PyUnstable_Long_CompactValue((PyLongObject*) x)
  #else
    #define __Pyx_PyLong_IsCompact(x)     (((PyLongObject*)x)->long_value.lv_tag < (2 << _PyLong_NON_SIZE_BITS))
    #define __Pyx_PyLong_CompactValue(x)  ((1 - (Py_ssize_t) __Pyx_PyLong_Sign(x)) * (Py_ssize_t) __Pyx_PyLong_Digits(x)[0])
  #endif
  typedef Py_ssize_t  __Pyx_compact_pylong;
  typedef size_t  __Pyx_compact_upylong;
  #else
  #define __Pyx_PyLong_IsNeg(x)  (Py_SIZE(x) < 0)
  #define __Pyx_PyLong_IsNonNeg(x)  (Py_SIZE(x) >= 0)
  #define __Pyx_PyLong_IsZero(x)  (Py_SIZE(x) == 0)
  #define __Pyx_PyLong_IsPos(x)  (Py_SIZE(x) > 0)
  #define __Pyx_PyLong_CompactValueUnsigned(x)  ((Py_SIZE(x) == 0) ? 0 : __Pyx_PyLong_Digits(x)[0])
  #define __Pyx_PyLong_DigitCount(x)  __Pyx_sst_abs(Py_SIZE(x))
  #define __Pyx_PyLong_SignedDigitCount(x)  Py_SIZE(x)
  #define __Pyx_PyLong_IsCompact(x)  (Py_SIZE(x) == 0 || Py_SIZE(x) == 1 || Py_SIZE(x) == -1)
  #define __Pyx_PyLong_CompactValue(x)\
        ((Py_SIZE(x) == 0) ? (sdigit) 0 : ((Py_SIZE(x) < 0) ? -(sdigit)__Pyx_PyLong_Digits(x)[0] : (sdigit)__Pyx_PyLong_Digits(x)[0]))
  typedef sdigit  __Pyx_compact_pylong;
  typedef digit  __Pyx_compact_upylong;
  #endif
  #if PY_VERSION_HEX >= 0x030C00A5
  #define __Pyx_PyLong_Digits(x)  (((PyLongObject*)x)->long_value.ob_digit)
  #else
  #define __Pyx_PyLong_Digits(x)  (((PyLongObject*)x)->ob_digit)
  #endif
#endif
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
#include <string.h>
static int __Pyx_sys_getdefaultencoding_not_ascii;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    PyObject* ascii_chars_u = NULL;
    PyObject* ascii_chars_b = NULL;
    const char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    if (strcmp(default_encoding_c, "ascii") == 0) {
        __Pyx_sys_getdefaultencoding_not_ascii = 0;
    } else {
        char ascii_chars[128];
        int c;
        for (c = 0; c < 128; c++) {
            ascii_chars[c] = (char) c;
        }
        __Pyx_sys_getdefaultencoding_not_ascii = 1;
        ascii_chars_u = PyUnicode_DecodeASCII(ascii_chars, 128, NULL);
        if (!ascii_chars_u) goto bad;
        ascii_chars_b = PyUnicode_AsEncodedString(ascii_chars_u, default_encoding_c, NULL);
        if (!ascii_chars_b || !PyBytes_Check(ascii_chars_b) || memcmp(ascii_chars, PyBytes_AS_STRING(ascii_chars_b), 128) != 0) {
            PyErr_Format(
                PyExc_ValueError,
                "This module compiled with c_string_encoding=ascii, but default encoding '%.200s' is not a superset of ascii.",
                default_encoding_c);
            goto bad;
        }
        Py_DECREF(ascii_chars_u);
        Py_DECREF(ascii_chars_b);
    }
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    Py_XDECREF(ascii_chars_u);
    Py_XDECREF(ascii_chars_b);
    return -1;
}
#endif
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT && PY_MAJOR_VERSION >= 3
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_DecodeUTF8(c_str, size, NULL)
#else
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_Decode(c_str, size, __PYX_DEFAULT_STRING_ENCODING, NULL)
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
#include <string.h>
static char* __PYX_DEFAULT_STRING_ENCODING;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) (const char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    __PYX_DEFAULT_STRING_ENCODING = (char*) malloc(strlen(default_encoding_c) + 1);
    if (!__PYX_DEFAULT_STRING_ENCODING) goto bad;
    strcpy(__PYX_DEFAULT_STRING_ENCODING, default_encoding_c);
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    return -1;
}
#endif
#endif


/* Test for GCC > 2.95 */
#if defined(__GNUC__)     && (__GNUC__ > 2 || (__GNUC__ == 2 && (__GNUC_MINOR__ > 95)))
  #define likely(x)   __builtin_expect(!!(x), 1)
  #define unlikely(x) __builtin_expect(!!(x), 0)
#else /* !__GNUC__ or GCC < 2.95 */
  #define likely(x)   (x)
  #define unlikely(x) (x)
#endif /* __GNUC__ */
static CYTHON_INLINE void __Pyx_pretend_to_initialize(void* ptr) { (void)ptr; }

#if !CYTHON_USE_MODULE_STATE
static PyObject *__pyx_m = NULL;
#endif
static int __pyx_lineno;
static int __pyx_clineno = 0;
static const char * __pyx_cfilenm = __FILE__;
static const char *__pyx_filename;

/* #### Code section: filename_table ### */

static const char *__pyx_f[] = {
  "configs.py",
};
/* #### Code section: utility_code_proto_before_types ### */
/* #### Code section: numeric_typedefs ### */
/* #### Code section: complex_type_declarations ### */
/* #### Code section: type_declarations ### */

/*--- Type declarations ---*/
/* #### Code section: utility_code_proto ### */

/* --- Runtime support code (head) --- */
/* Refnanny.proto */
#ifndef CYTHON_REFNANNY
  #define CYTHON_REFNANNY 0
#endif
#if CYTHON_REFNANNY
  typedef struct {
    void (*INCREF)(void*, PyObject*, Py_ssize_t);
    void (*DECREF)(void*, PyObject*, Py_ssize_t);
    void (*GOTREF)(void*, PyObject*, Py_ssize_t);
    void (*GIVEREF)(void*, PyObject*, Py_ssize_t);
    void* (*SetupContext)(const char*, Py_ssize_t, const char*);
    void (*FinishContext)(void**);
  } __Pyx_RefNannyAPIStruct;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNanny = NULL;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname);
  #define __Pyx_RefNannyDeclarations void *__pyx_refnanny = NULL;
#ifdef WITH_THREAD
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          if (acquire_gil) {\
              PyGILState_STATE __pyx_gilstate_save = PyGILState_Ensure();\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), (__LINE__), (__FILE__));\
              PyGILState_Release(__pyx_gilstate_save);\
          } else {\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), (__LINE__), (__FILE__));\
          }
  #define __Pyx_RefNannyFinishContextNogil() {\
              PyGILState_STATE __pyx_gilstate_save = PyGILState_Ensure();\
              __Pyx_RefNannyFinishContext();\
              PyGILState_Release(__pyx_gilstate_save);\
          }
#else
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), (__LINE__), (__FILE__))
  #define __Pyx_RefNannyFinishContextNogil() __Pyx_RefNannyFinishContext()
#endif
  #define __Pyx_RefNannyFinishContextNogil() {\
              PyGILState_STATE __pyx_gilstate_save = PyGILState_Ensure();\
              __Pyx_RefNannyFinishContext();\
              PyGILState_Release(__pyx_gilstate_save);\
          }
  #define __Pyx_RefNannyFinishContext()\
          __Pyx_RefNanny->FinishContext(&__pyx_refnanny)
  #define __Pyx_INCREF(r)  __Pyx_RefNanny->INCREF(__pyx_refnanny, (PyObject *)(r), (__LINE__))
  #define __Pyx_DECREF(r)  __Pyx_RefNanny->DECREF(__pyx_refnanny, (PyObject *)(r), (__LINE__))
  #define __Pyx_GOTREF(r)  __Pyx_RefNanny->GOTREF(__pyx_refnanny, (PyObject *)(r), (__LINE__))
  #define __Pyx_GIVEREF(r) __Pyx_RefNanny->GIVEREF(__pyx_refnanny, (PyObject *)(r), (__LINE__))
  #define __Pyx_XINCREF(r)  do { if((r) == NULL); else {__Pyx_INCREF(r); }} while(0)
  #define __Pyx_XDECREF(r)  do { if((r) == NULL); else {__Pyx_DECREF(r); }} while(0)
  #define __Pyx_XGOTREF(r)  do { if((r) == NULL); else {__Pyx_GOTREF(r); }} while(0)
  #define __Pyx_XGIVEREF(r) do { if((r) == NULL); else {__Pyx_GIVEREF(r);}} while(0)
#else
  #define __Pyx_RefNannyDeclarations
  #define __Pyx_RefNannySetupContext(name, acquire_gil)
  #define __Pyx_RefNannyFinishContextNogil()
  #define __Pyx_RefNannyFinishContext()
  #define __Pyx_INCREF(r) Py_INCREF(r)
  #define __Pyx_DECREF(r) Py_DECREF(r)
  #define __Pyx_GOTREF(r)
  #define __Pyx_GIVEREF(r)
  #define __Pyx_XINCREF(r) Py_XINCREF(r)
  #define __Pyx_XDECREF(r) Py_XDECREF(r)
  #define __Pyx_XGOTREF(r)
  #define __Pyx_XGIVEREF(r)
#endif
#define __Pyx_Py_XDECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; Py_XDECREF(tmp);\
    } while (0)
#define __Pyx_XDECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_XDECREF(tmp);\
    } while (0)
#define __Pyx_DECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_DECREF(tmp);\
    } while (0)
#define __Pyx_CLEAR(r)    do { PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);} while(0)
#define __Pyx_XCLEAR(r)   do { if((r) != NULL) {PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);}} while(0)

/* PyDictVersioning.proto */
#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS
#define __PYX_DICT_VERSION_INIT  ((PY_UINT64_T) -1)
#define __PYX_GET_DICT_VERSION(dict)  (((PyDictObject*)(dict))->ma_version_tag)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)\
    (version_var) = __PYX_GET_DICT_VERSION(dict);\
    (cache_var) = (value);
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP) {\
    static PY_UINT64_T __pyx_dict_version = 0;\
    static PyObject *__pyx_dict_cached_value = NULL;\
    if (likely(__PYX_GET_DICT_VERSION(DICT) == __pyx_dict_version)) {\
        (VAR) = __pyx_dict_cached_value;\
    } else {\
        (VAR) = __pyx_dict_cached_value = (LOOKUP);\
        __pyx_dict_version = __PYX_GET_DICT_VERSION(DICT);\
    }\
}
static CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj);
static CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj);
static CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version);
#else
#define __PYX_GET_DICT_VERSION(dict)  (0)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP)  (VAR) = (LOOKUP);
#endif

/* PyErrExceptionMatches.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyErr_ExceptionMatches(err) __Pyx_PyErr_ExceptionMatchesInState(__pyx_tstate, err)
static CYTHON_INLINE int __Pyx_PyErr_ExceptionMatchesInState(PyThreadState* tstate, PyObject* err);
#else
#define __Pyx_PyErr_ExceptionMatches(err)  PyErr_ExceptionMatches(err)
#endif

/* PyThreadStateGet.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyThreadState_declare  PyThreadState *__pyx_tstate;
#define __Pyx_PyThreadState_assign  __pyx_tstate = __Pyx_PyThreadState_Current;
#if PY_VERSION_HEX >= 0x030C00A6
#define __Pyx_PyErr_Occurred()  (__pyx_tstate->current_exception != NULL)
#define __Pyx_PyErr_CurrentExceptionType()  (__pyx_tstate->current_exception ? (PyObject*) Py_TYPE(__pyx_tstate->current_exception) : (PyObject*) NULL)
#else
#define __Pyx_PyErr_Occurred()  (__pyx_tstate->curexc_type != NULL)
#define __Pyx_PyErr_CurrentExceptionType()  (__pyx_tstate->curexc_type)
#endif
#else
#define __Pyx_PyThreadState_declare
#define __Pyx_PyThreadState_assign
#define __Pyx_PyErr_Occurred()  (PyErr_Occurred() != NULL)
#define __Pyx_PyErr_CurrentExceptionType()  PyErr_Occurred()
#endif

/* PyErrFetchRestore.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyErr_Clear() __Pyx_ErrRestore(NULL, NULL, NULL)
#define __Pyx_ErrRestoreWithState(type, value, tb)  __Pyx_ErrRestoreInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)    __Pyx_ErrFetchInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  __Pyx_ErrRestoreInState(__pyx_tstate, type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)    __Pyx_ErrFetchInState(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb);
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A6
#define __Pyx_PyErr_SetNone(exc) (Py_INCREF(exc), __Pyx_ErrRestore((exc), NULL, NULL))
#else
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#endif
#else
#define __Pyx_PyErr_Clear() PyErr_Clear()
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#define __Pyx_ErrRestoreWithState(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestoreInState(tstate, type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchInState(tstate, type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)  PyErr_Fetch(type, value, tb)
#endif

/* PyObjectGetAttrStr.proto */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name);
#else
#define __Pyx_PyObject_GetAttrStr(o,n) PyObject_GetAttr(o,n)
#endif

/* PyObjectGetAttrStrNoError.proto */
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStrNoError(PyObject* obj, PyObject* attr_name);

/* CLineInTraceback.proto */
#ifdef CYTHON_CLINE_IN_TRACEBACK
#define __Pyx_CLineForTraceback(tstate, c_line)  (((CYTHON_CLINE_IN_TRACEBACK)) ? c_line : 0)
#else
static int __Pyx_CLineForTraceback(PyThreadState *tstate, int c_line);
#endif

/* CodeObjectCache.proto */
#if !CYTHON_COMPILING_IN_LIMITED_API
typedef struct {
    PyCodeObject* code_object;
    int code_line;
} __Pyx_CodeObjectCacheEntry;
struct __Pyx_CodeObjectCache {
    int count;
    int max_count;
    __Pyx_CodeObjectCacheEntry* entries;
};
static struct __Pyx_CodeObjectCache __pyx_code_cache = {0,0,NULL};
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line);
static PyCodeObject *__pyx_find_code_object(int code_line);
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object);
#endif

/* AddTraceback.proto */
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename);

/* FormatTypeName.proto */
#if CYTHON_COMPILING_IN_LIMITED_API
typedef PyObject *__Pyx_TypeName;
#define __Pyx_FMT_TYPENAME "%U"
static __Pyx_TypeName __Pyx_PyType_GetName(PyTypeObject* tp);
#define __Pyx_DECREF_TypeName(obj) Py_XDECREF(obj)
#else
typedef const char *__Pyx_TypeName;
#define __Pyx_FMT_TYPENAME "%.200s"
#define __Pyx_PyType_GetName(tp) ((tp)->tp_name)
#define __Pyx_DECREF_TypeName(obj)
#endif

/* GCCDiagnostics.proto */
#if !defined(__INTEL_COMPILER) && defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 6))
#define __Pyx_HAS_GCC_DIAGNOSTIC
#endif

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value);

/* CIntFromPy.proto */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *);

/* CIntFromPy.proto */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *);

/* FastTypeChecks.proto */
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_TypeCheck(obj, type) __Pyx_IsSubtype(Py_TYPE(obj), (PyTypeObject *)type)
#define __Pyx_TypeCheck2(obj, type1, type2) __Pyx_IsAnySubtype2(Py_TYPE(obj), (PyTypeObject *)type1, (PyTypeObject *)type2)
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b);
static CYTHON_INLINE int __Pyx_IsAnySubtype2(PyTypeObject *cls, PyTypeObject *a, PyTypeObject *b);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject *type);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *type1, PyObject *type2);
#else
#define __Pyx_TypeCheck(obj, type) PyObject_TypeCheck(obj, (PyTypeObject *)type)
#define __Pyx_TypeCheck2(obj, type1, type2) (PyObject_TypeCheck(obj, (PyTypeObject *)type1) || PyObject_TypeCheck(obj, (PyTypeObject *)type2))
#define __Pyx_PyErr_GivenExceptionMatches(err, type) PyErr_GivenExceptionMatches(err, type)
#define __Pyx_PyErr_GivenExceptionMatches2(err, type1, type2) (PyErr_GivenExceptionMatches(err, type1) || PyErr_GivenExceptionMatches(err, type2))
#endif
#define __Pyx_PyErr_ExceptionMatches2(err1, err2)  __Pyx_PyErr_GivenExceptionMatches2(__Pyx_PyErr_CurrentExceptionType(), err1, err2)
#define __Pyx_PyException_Check(obj) __Pyx_TypeCheck(obj, PyExc_Exception)

/* CheckBinaryVersion.proto */
static unsigned long __Pyx_get_runtime_version(void);
static int __Pyx_check_binary_version(unsigned long ct_version, unsigned long rt_version, int allow_newer);

/* InitStrings.proto */
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t);

/* #### Code section: module_declarations ### */

/* Module declarations from "configs" */
/* #### Code section: typeinfo ### */
/* #### Code section: before_global_var ### */
#define __Pyx_MODULE_NAME "configs"
extern int __pyx_module_is_main_configs;
int __pyx_module_is_main_configs = 0;

/* Implementation of "configs" */
/* #### Code section: global_var ### */
/* #### Code section: string_decls ### */
static const char __pyx_k_[] = "\347\273\207\346\230\237\350\257\200";
static const char __pyx_k_17[] = "17";
static const char __pyx_k_5M[] = "5M";
static const char __pyx_k_75[] = "75";
static const char __pyx_k_CD[] = "\346\215\242\347\273\221CD";
static const char __pyx_k__2[] = "\347\273\207\346\230\237\350\242\202";
static const char __pyx_k__3[] = "\351\203\216";
static const char __pyx_k__4[] = "\345\222\251\351\203\216";
static const char __pyx_k__5[] = "\345\244\251\346\233\246\345\233\233\350\261\241\302\267\346\234\261\351\233\200";
static const char __pyx_k__6[] = "\345\244\251\346\233\246\345\233\233\350\261\241\346\234\261\351\233\200";
static const char __pyx_k__7[] = "\345\244\251\346\233\246\345\233\233\350\261\241\302\267\351\235\222\351\276\231";
static const char __pyx_k__8[] = "\345\244\251\346\233\246\345\233\233\350\261\241\351\235\222\351\276\231";
static const char __pyx_k__9[] = "\345\244\251\346\233\246\345\233\233\350\261\241\302\267\347\231\275\350\231\216";
static const char __pyx_k_150[] = "150\344\270\207\345\205\205\345\200\274\347\247\260\345\217\267";
static const char __pyx_k__10[] = "\345\244\251\346\233\246\345\233\233\350\261\241\347\231\275\350\231\216";
static const char __pyx_k__11[] = "\345\215\203\346\210\210\347\216\211\345\270\233";
static const char __pyx_k__12[] = "\345\271\262\346\210\210\347\216\211\345\270\233";
static const char __pyx_k__13[] = "\346\265\201\345\205\211\346\271\226";
static const char __pyx_k__14[] = "\346\265\201\345\205\211\346\272\257";
static const char __pyx_k__15[] = "\347\201\265\345\200\252\350\241\227\345\250\201\345\205\270";
static const char __pyx_k__16[] = "\347\201\265\345\200\252\350\241\224\345\250\201\350\210\206";
static const char __pyx_k__17[] = "\347\201\265\345\200\252\350\241\224\345\250\201\345\205\270";
static const char __pyx_k__18[] = "\347\201\265\350\262\214\350\241\224\345\250\201\350\210\206";
static const char __pyx_k__19[] = "\345\216\213\347\203\237\345\207\255\350\231\232\351\230\200";
static const char __pyx_k__20[] = "\350\234\203\347\203\237\345\207\255\350\231\232\351\230\231";
static const char __pyx_k__21[] = "\345\271\275\345\270\230\350\220\275\346\242\246\350\215\244";
static const char __pyx_k__22[] = "\345\271\275\345\270\230\350\220\275\346\242\246\350\276\207";
static const char __pyx_k__23[] = "\345\271\273\350\231\271\350\262\214";
static const char __pyx_k__24[] = "\345\271\273\350\231\271\347\214\212";
static const char __pyx_k__25[] = "\345\271\273\350\231\271\345\200\252";
static const char __pyx_k__26[] = "\345\271\273\350\231\271\345\205\234";
static const char __pyx_k__27[] = "\345\206\245\345\244\234\350\262\214";
static const char __pyx_k__28[] = "\345\206\245\345\244\234\347\214\212";
static const char __pyx_k__29[] = "\347\202\271\346\230\216\347\202\211";
static const char __pyx_k__30[] = "\347\202\271\345\220\215\347\202\211";
static const char __pyx_k__31[] = "\351\224\200\345\271\262\346\210\210";
static const char __pyx_k__32[] = "\351\224\200\345\215\203\346\210\210";
static const char __pyx_k__33[] = "\351\206\222\347\213\256\350\265\267";
static const char __pyx_k__34[] = "\351\206\222\347\213\256";
static const char __pyx_k__35[] = "\351\206\222\347\213\256\302\267\350\265\267";
static const char __pyx_k__36[] = "\346\217\275\345\215\216\351\234\204\350\265\267";
static const char __pyx_k__37[] = "\346\217\275\345\215\216\351\234\204";
static const char __pyx_k__38[] = "\346\217\275\345\215\216\351\234\204\302\267\350\265\267";
static const char __pyx_k__39[] = "\346\217\275\345\215\216\345\256\265\302\267\350\265\267";
static const char __pyx_k__40[] = "\346\217\275\345\215\216\345\256\265\357\274\232\350\265\267";
static const char __pyx_k__41[] = "\346\217\275\345\215\216\345\256\265:\350\265\267";
static const char __pyx_k__42[] = "\346\217\275\345\215\216\345\256\265\350\265\267";
static const char __pyx_k__43[] = "\345\276\241\345\211\221\350\265\267";
static const char __pyx_k__44[] = "\345\276\241\345\211\221";
static const char __pyx_k__45[] = "\345\276\241\345\211\221\302\267\350\265\267";
static const char __pyx_k__46[] = "\345\276\241\351\276\231\350\265\267";
static const char __pyx_k__47[] = "\345\276\241\351\276\231";
static const char __pyx_k__48[] = "\345\276\241\351\276\231\302\267\350\265\267";
static const char __pyx_k__49[] = "\345\242\250\351\237\265\350\265\267";
static const char __pyx_k__50[] = "\345\242\250\351\237\265";
static const char __pyx_k__51[] = "\345\242\250\351\237\265\302\267\350\265\267";
static const char __pyx_k__52[] = "\344\274\236\346\242\246\350\265\267";
static const char __pyx_k__53[] = "\344\274\236\346\242\246";
static const char __pyx_k__54[] = "\344\274\236\346\242\246\302\267\350\265\267";
static const char __pyx_k__55[] = "\345\244\251\351\251\254\302\267\350\265\267";
static const char __pyx_k__56[] = "\345\244\251\351\251\254";
static const char __pyx_k__57[] = "\345\244\251\351\251\254\350\265\267";
static const char __pyx_k__58[] = "\346\210\220\345\217\214\302\267\350\265\267";
static const char __pyx_k__59[] = "\346\210\220\345\217\214";
static const char __pyx_k__60[] = "\346\210\220\345\217\214\350\265\267";
static const char __pyx_k__61[] = "\345\215\203\345\217\230\302\267\350\265\267";
static const char __pyx_k__62[] = "\345\215\203\345\217\230";
static const char __pyx_k__63[] = "\345\215\203\345\217\230\350\265\267";
static const char __pyx_k__64[] = "\346\211\266\346\221\207\302\267\350\265\267";
static const char __pyx_k__65[] = "\346\211\266\346\221\207";
static const char __pyx_k__66[] = "\346\211\266\346\221\207\350\265\267";
static const char __pyx_k__67[] = "\345\271\275\345\270\230\350\220\275\346\242\246\350\276\210";
static const char __pyx_k__68[] = "\347\221\266\345\270\230\347\203\237\345\205\211\350\276\210";
static const char __pyx_k__69[] = "\347\221\266\345\270\230\347\203\237\345\205\211\350\276\207";
static const char __pyx_k__70[] = "\347\221\266\345\270\230\347\203\237\345\205\211\350\215\244";
static const char __pyx_k__71[] = "\350\213\215\351\233\267\347\240\264\351\232\234";
static const char __pyx_k__72[] = "\350\213\215\351\233\267\347\240\264\347\230\264";
static const char __pyx_k__73[] = "\350\234\203\346\265\267\350\223\254\345\243\266\351\230\200";
static const char __pyx_k__74[] = "\350\234\203\346\265\267\350\223\254\345\243\266\351\230\231";
static const char __pyx_k__75[] = "\350\234\203\347\203\237\345\207\255\350\231\232\351\230\200";
static const char __pyx_k__76[] = "\351\251\260\345\260\275\345\207\214\346\227\245";
static const char __pyx_k__77[] = "\351\251\260\346\230\274\345\207\214\346\227\245";
static const char __pyx_k__78[] = "\347\201\265\351\271\277\346\241\203\345\244\251\351\251\276";
static const char __pyx_k__79[] = "\347\201\265\351\271\277\346\241\203\345\244\255\351\251\276";
static const char __pyx_k__80[] = "\345\215\267\347\272\242\345\260\230";
static const char __pyx_k__81[] = "\347\273\273\347\272\242\345\260\230";
static const char __pyx_k__82[] = "\345\226\265\345\267\253\302\267\351\255\224\345\270\275\347\201\265";
static const char __pyx_k__83[] = "\345\226\265\345\267\253\351\255\224\345\270\275\347\201\265";
static const char __pyx_k__84[] = "\346\236\201\302\267\351\251\260\345\260\275\347\201\265\346\247\216";
static const char __pyx_k__85[] = "\346\236\201\302\267\351\251\260\346\230\274\347\201\265\346\247\216";
static const char __pyx_k__86[] = "\344\277\256\347\275\227\344\274\217\351\255\224";
static const char __pyx_k__87[] = "\345\256\210\345\277\203\351\225\207\351\255\224";
static const char __pyx_k__88[] = "\346\266\205\346\247\203\346\227\240\345\260\230";
static const char __pyx_k__89[] = "\345\210\271\351\202\243\345\271\273\347\251\272";
static const char __pyx_k__90[] = "\351\241\273\345\274\245\344\270\207\350\261\241";
static const char __pyx_k__91[] = "\350\216\262\345\277\203\350\217\251\346\217\220";
static const char __pyx_k__92[] = "\350\207\252\345\234\250\345\215\201\346\226\271";
static const char __pyx_k__93[] = "\346\227\240\351\207\217\351\207\221\345\210\232";
static const char __pyx_k__94[] = "\347\246\205\350\212\261\346\227\240\347\225\214";
static const char __pyx_k__95[] = "\346\270\205\346\263\242\351\234\207\346\272\237";
static const char __pyx_k__96[] = "\346\260\264\346\255\246";
static const char __pyx_k__97[] = "\346\270\205\346\263\242\345\274\225\345\225\206";
static const char __pyx_k__98[] = "\346\270\205\346\263\242\345\225\270\347\251\272";
static const char __pyx_k__99[] = "\346\270\205\346\263\242\347\205\247\346\242\246";
static const char __pyx_k_adb[] = "adb";
static const char __pyx_k_pic[] = "pic";
static const char __pyx_k_2200[] = "2200";
static const char __pyx_k_CD_2[] = "\350\275\254\350\201\214CD";
static const char __pyx_k_CD_3[] = "\350\275\254\346\200\247CD";
static const char __pyx_k_HOST[] = "HOST";
static const char __pyx_k__100[] = "\346\270\205\346\263\242\347\273\275\351\234\262";
static const char __pyx_k__101[] = "\346\270\205\346\263\242\347\240\264\351\230\265";
static const char __pyx_k__102[] = "\346\270\205\346\263\242\351\202\200\346\234\210";
static const char __pyx_k__103[] = "\346\270\205\346\263\242\345\214\226\347\276\275";
static const char __pyx_k__104[] = "\351\233\252\345\260\201\347\226\206";
static const char __pyx_k__105[] = "\345\206\260\346\255\246";
static const char __pyx_k__106[] = "\351\233\252\351\230\265\346\233\262";
static const char __pyx_k__107[] = "\351\233\252\345\207\214\351\243\216";
static const char __pyx_k__108[] = "\351\233\252\347\201\265\350\257\255";
static const char __pyx_k__109[] = "\351\233\252\347\264\240\345\277\203";
static const char __pyx_k__110[] = "\351\233\252\345\206\260\346\262\263";
static const char __pyx_k__111[] = "\351\233\252\346\227\240\347\227\225";
static const char __pyx_k__112[] = "\351\233\252\345\207\235\346\236\242";
static const char __pyx_k__113[] = "\350\276\260\347\201\253\344\270\207\350\261\241";
static const char __pyx_k__114[] = "\347\201\253\346\255\246";
static const char __pyx_k__115[] = "\347\203\275\347\201\253\347\207\216\345\216\237";
static const char __pyx_k__116[] = "\347\226\276\347\201\253\350\200\200\346\227\245";
static const char __pyx_k__117[] = "\347\273\235\347\201\253\347\213\254\346\230\216";
static const char __pyx_k__118[] = "\347\203\237\347\201\253\344\274\274\344\272\221";
static const char __pyx_k__119[] = "\344\270\232\347\201\253\347\233\233\350\212\261";
static const char __pyx_k__120[] = "\345\244\251\347\201\253\345\233\236\351\243\216";
static const char __pyx_k__121[] = "\347\203\210\347\201\253\347\207\203\345\244\251";
static const char __pyx_k__122[] = "\346\200\222\351\233\267\346\221\247\345\237\216";
static const char __pyx_k__123[] = "\351\233\267\346\255\246";
static const char __pyx_k__124[] = "\351\227\273\351\233\267\350\257\233\351\202\252";
static const char __pyx_k__125[] = "\351\235\222\351\233\267\351\234\207\345\262\263";
static const char __pyx_k__126[] = "\346\214\275\351\233\267\346\216\243\346\234\210";
static const char __pyx_k__127[] = "\346\203\212\351\233\267\346\226\255\345\261\261";
static const char __pyx_k__128[] = "\347\226\276\351\233\267\346\270\270\345\244\234";
static const char __pyx_k__129[] = "\345\271\275\351\233\267\351\231\244\345\216\204";
static const char __pyx_k__130[] = "\351\227\262\344\272\221\350\207\252\345\234\250";
static const char __pyx_k__132[] = "\346\224\276\346\255\214\346\270\205\345\256\265";
static const char __pyx_k__134[] = "\345\207\255\346\240\217\345\220\254\351\233\250";
static const char __pyx_k__136[] = "\346\212\212\351\205\222\344\270\264\351\243\216";
static const char __pyx_k__138[] = "\345\215\247\347\234\213\345\215\203\345\261\261";
static const char __pyx_k__140[] = "\344\270\200\351\206\211\350\275\273\347\216\213\344\276\257";
static const char __pyx_k__142[] = "\347\247\213\346\260\264\344\270\215\346\237\223\345\260\230";
static const char __pyx_k__144[] = "\344\271\230\351\243\216\347\231\273\347\216\211\344\272\254";
static const char __pyx_k__146[] = "\347\274\245\347\274\210\344\272\221\346\265\201\351\271\244\344\270\212\344\273\231";
static const char __pyx_k__149[] = "\344\273\273\345\212\241";
static const char __pyx_k__150[] = "\347\245\236\347\237\263";
static const char __pyx_k__151[] = "\345\225\206\345\237\216";
static const char __pyx_k__152[] = "\345\205\266\344\273\226";
static const char __pyx_k__153[] = "\345\237\272\344\273\226";
static const char __pyx_k__154[] = "\345\205\266\345\256\203";
static const char __pyx_k__155[] = "\345\237\272\345\256\203";
static const char __pyx_k__156[] = "\345\205\221\346\215\242";
static const char __pyx_k__157[] = "\345\211\257\346\234\254";
static const char __pyx_k__158[] = "\344\273\273";
static const char __pyx_k__159[] = "\345\212\241";
static const char __pyx_k__160[] = "\346\264\273\345\212\250";
static const char __pyx_k__161[] = "\345\267\262";
static const char __pyx_k__162[] = "\346\213\245";
static const char __pyx_k__163[] = "\346\234\211";
static const char __pyx_k__164[] = "\347\216\204\351\276\231\350\200\200\344\270\226";
static const char __pyx_k__165[] = "\351\223\266\346\262\263\346\230\237\347\272\261";
static const char __pyx_k__166[] = "\347\220\274\345\214\226\347\231\275\347\276\275";
static const char __pyx_k__167[] = "\345\244\251\346\236\242\302\267\346\226\227\346\230\237\347\247\273";
static const char __pyx_k__168[] = "\351\243\236\347\204\260\346\265\201\346\263\242";
static const char __pyx_k__169[] = "\346\267\267\346\262\214";
static const char __pyx_k__170[] = "\347\231\275\345\244\234\346\213\202\351\233\252";
static const char __pyx_k__171[] = "\346\230\237\345\211\221\345\210\206\351\207\216";
static const char __pyx_k__172[] = "\346\230\237\350\220\275\346\234\210\347\232\223";
static const char __pyx_k__173[] = "\347\264\253\345\256\277\345\256\270\346\236\201";
static const char __pyx_k__174[] = "\347\273\235\345\237\237\345\255\244\347\237\263";
static const char __pyx_k__175[] = "\345\206\245\345\256\231\346\230\237\347\206\240";
static const char __pyx_k__176[] = "\347\220\274\345\215\216\346\270\205\351\234\262";
static const char __pyx_k__177[] = "\351\223\266\346\262\263\347\217\215\344\270\235";
static const char __pyx_k__178[] = "\347\220\274\345\215\216\347\231\275\347\276\275";
static const char __pyx_k__179[] = "\347\245\236\345\205\265\346\210\222\346\214\207";
static const char __pyx_k__180[] = "\346\233\246\345\205\211\347\222\236\347\216\211";
static const char __pyx_k__181[] = "\346\230\274\345\256\207\347\201\265\346\231\226";
static const char __pyx_k__182[] = "\345\215\203\347\247\213";
static const char __pyx_k__183[] = "\344\271\235\351\234\204";
static const char __pyx_k__184[] = "\351\233\252\347\211\265\346\203\205";
static const char __pyx_k__185[] = "\346\234\235\351\271\277\351\270\243";
static const char __pyx_k__186[] = "\347\231\275\351\271\277\350\260\243";
static const char __pyx_k__187[] = "\346\265\256\344\272\221\345\261\261";
static const char __pyx_k__188[] = "\347\201\265\350\224\223";
static const char __pyx_k__189[] = "\351\234\201\351\233\252";
static const char __pyx_k__190[] = "\347\273\233\347\203\237";
static const char __pyx_k__191[] = "\347\213\202\346\255\214";
static const char __pyx_k__192[] = "\346\263\275\346\230\216";
static const char __pyx_k__193[] = "\351\243\216\345\215\216";
static const char __pyx_k__194[] = "\345\244\251\347\245\210";
static const char __pyx_k__195[] = "\345\244\251\351\222\247";
static const char __pyx_k__196[] = "\351\276\231\350\215\222";
static const char __pyx_k__197[] = "\347\202\275\346\265\252";
static const char __pyx_k__198[] = "\351\207\221\346\235\257\351\224\231";
static const char __pyx_k__199[] = "\346\214\245\344\272\221";
static const char __pyx_k__200[] = "\351\207\215\347\203\237\346\260\264";
static const char __pyx_k__201[] = "\347\264\253\345\256\270";
static const char __pyx_k__202[] = "\345\220\253\346\233\234";
static const char __pyx_k__203[] = "\347\277\240\347\276\275\345\220\237";
static const char __pyx_k__204[] = "\347\202\263\350\200\200\347\216\211\344\270\235";
static const char __pyx_k__205[] = "\346\262\211\345\243\201\350\207\273\347\213\220";
static const char __pyx_k__206[] = "\346\241\200\351\252\234";
static const char __pyx_k__207[] = "\347\220\274\346\265\267";
static const char __pyx_k__208[] = "\346\227\251\346\250\261";
static const char __pyx_k__209[] = "\345\244\251\345\210\221";
static const char __pyx_k__210[] = "\347\206\231\346\230\216";
static const char __pyx_k__211[] = "\346\230\274\345\244\234\346\255\214";
static const char __pyx_k__212[] = "\351\263\236\345\267\235";
static const char __pyx_k__213[] = "\347\242\216\351\234\204";
static const char __pyx_k__214[] = "\345\226\265\350\257\255";
static const char __pyx_k__215[] = "\345\271\273\347\276\275";
static const char __pyx_k__216[] = "\350\216\271\347\277\216";
static const char __pyx_k__217[] = "\347\201\274\345\246\226";
static const char __pyx_k__218[] = "\347\222\203\345\205\211";
static const char __pyx_k__219[] = "\347\220\242\351\233\252";
static const char __pyx_k__220[] = "\347\272\242\347\202\216";
static const char __pyx_k__221[] = "\344\270\215\345\244\234";
static const char __pyx_k__222[] = "\346\242\246\345\211\215\345\260\230";
static const char __pyx_k__223[] = "\346\270\205\345\256\265";
static const char __pyx_k__224[] = "\347\202\216\345\206\245\350\265\253\350\265\253";
static const char __pyx_k__225[] = "\351\235\222\345\206\245\346\233\231\346\231\226";
static const char __pyx_k__226[] = "\347\245\236\351\231\250";
static const char __pyx_k__227[] = "\351\201\245\346\234\210";
static const char __pyx_k__228[] = "\344\272\221\351\227\264\345\242\250";
static const char __pyx_k__229[] = "\347\273\207\347\203\237";
static const char __pyx_k__230[] = "\346\227\213\346\202\240\345\205\211";
static const char __pyx_k__231[] = "\351\225\277\351\243\216\347\203\210";
static const char __pyx_k__232[] = "\347\213\202\345\244\234\347\221\260\350\257\255";
static const char __pyx_k__233[] = "\346\227\240\345\217\214";
static const char __pyx_k__234[] = "\345\257\222\346\242\246";
static const char __pyx_k__235[] = "\351\234\234\345\271\264";
static const char __pyx_k__236[] = "\351\224\246\345\244\234\350\241\214";
static const char __pyx_k__237[] = "\347\265\256\347\262\211";
static const char __pyx_k__238[] = "\344\272\221\347\273\222";
static const char __pyx_k__239[] = "\347\273\257\346\233\234";
static const char __pyx_k__240[] = "\345\242\250\347\213\251";
static const char __pyx_k__241[] = "\347\213\202\346\254\242";
static const char __pyx_k__242[] = "\351\243\230\345\226\265\350\220\246\346\242\246";
static const char __pyx_k__243[] = "\345\226\265\350\266\243\344\272\221\347\277\224";
static const char __pyx_k__244[] = "\346\212\230\346\230\237\345\275\261";
static const char __pyx_k__245[] = "\347\201\265\347\214\212\350\241\224\345\250\201\350\210\206";
static const char __pyx_k__246[] = "\347\204\232\345\244\251\351\230\231";
static const char __pyx_k__247[] = "\347\216\204\351\271\277\345\236\202\346\230\237\351\251\276";
static const char __pyx_k__248[] = "\344\273\231\351\271\277\350\277\216\346\233\246\351\251\276";
static const char __pyx_k__249[] = "\345\245\224\351\233\267\345\212\250";
static const char __pyx_k__250[] = "\345\257\222\351\234\234\345\207\233";
static const char __pyx_k__251[] = "\345\244\234\346\221\247\346\242\246";
static const char __pyx_k__252[] = "\347\234\240\351\262\244\345\256\277\346\234\210\344\272\255";
static const char __pyx_k__253[] = "\347\255\226\347\216\204\346\210\210";
static const char __pyx_k__254[] = "\345\244\234\346\262\211\346\242\246";
static const char __pyx_k__255[] = "\345\215\227\345\261\261\351\233\252";
static const char __pyx_k__256[] = "\347\216\211\344\272\254\344\273\231";
static const char __pyx_k__257[] = "\350\264\257\346\227\245\347\214\212";
static const char __pyx_k__258[] = "\347\242\247\346\234\210\347\214\212";
static const char __pyx_k__259[] = "\350\275\273\345\206\245\345\271\273\345\244\234\350\275\251";
static const char __pyx_k__260[] = "\346\234\210\345\267\235\347\201\265";
static const char __pyx_k__261[] = "\351\235\222\344\270\230\347\251\272";
static const char __pyx_k__262[] = "\351\223\266\346\261\211\346\270\272";
static const char __pyx_k__263[] = "\345\206\260\346\262\263\345\207\214";
static const char __pyx_k__264[] = "\346\272\257\346\265\256\347\224\237";
static const char __pyx_k__265[] = "\350\277\267\351\207\215\345\267\235";
static const char __pyx_k__266[] = "\347\273\210\345\256\265\346\242\246";
static const char __pyx_k__267[] = "\345\226\265\345\267\253\346\236\253\345\217\266\346\251\230";
static const char __pyx_k__268[] = "\345\226\265\345\267\253\350\247\243\346\242\246\350\257\255";
static const char __pyx_k__269[] = "\345\274\225\345\215\216\346\266\237";
static const char __pyx_k__270[] = "\347\205\247\347\216\211\345\240\202";
static const char __pyx_k__271[] = "\351\200\220\346\232\227\346\262\270\345\244\234";
static const char __pyx_k__272[] = "\345\275\274\345\262\270\350\212\263\345\215\216";
static const char __pyx_k__273[] = "\345\275\274\345\262\270\350\265\244\345\215\216";
static const char __pyx_k__274[] = "\345\226\265\345\267\253\347\245\220\345\244\234\347\234\240";
static const char __pyx_k__275[] = "\346\236\201\302\267\351\200\220\346\232\227\345\244\251\346\242\255";
static const char __pyx_k__276[] = "\347\221\266\345\217\260\347\221\276\345\205\224\345\200\232\346\241\202";
static const char __pyx_k__277[] = "\347\221\266\345\217\260\347\221\276\345\205\224\347\201\274\346\241\203";
static const char __pyx_k__278[] = "\346\230\216\346\262\263\346\233\231\345\244\251\302\267\351\206\211\345\215\247\350\245\277\346\265\267";
static const char __pyx_k__279[] = "\345\261\261\346\265\267\347\245\240\347\245\236\302\267\350\265\244\345\207\260\351\207\215\346\230\216";
static const char __pyx_k__280[] = "\351\207\215\350\235\266\345\214\226\346\242\246";
static const char __pyx_k__281[] = "\346\227\245\346\233\234\345\205\253\350\215\222\302\267\350\275\251\350\276\225\345\211\221";
static const char __pyx_k__282[] = "\351\235\222\344\270\230\351\233\252";
static const char __pyx_k__283[] = "\345\262\201\346\230\237\350\241\214\346\270\241\302\267\345\215\203\351\207\215\347\204\260";
static const char __pyx_k__284[] = "\346\230\216\346\262\263\346\233\231\345\244\251\302\267\351\225\277\351\262\270\345\244\251\346\226\227";
static const char __pyx_k__285[] = "\345\261\261\346\265\267\347\245\240\347\245\236\302\267\351\230\263\345\215\207\347\276\262\345\222\214";
static const char __pyx_k__286[] = "\347\201\265\345\256\240\302\267\345\277\203\346\234\210\347\213\220";
static const char __pyx_k__287[] = "\345\244\251\347\213\274\346\230\237";
static const char __pyx_k__288[] = "\351\231\215\351\255\224\347\240\264\351\202\252\302\267\351\207\221\345\210\232\346\230\216\347\216\213";
static const char __pyx_k__289[] = "\345\244\251\351\271\260\345\272\247";
static const char __pyx_k__290[] = "\346\226\260\344\270\226\345\215\216\351\234\223\302\267\345\226\265\345\210\260\347\227\205\351\231\244";
static const char __pyx_k__291[] = "\345\246\226\345\275\261\345\257\273\347\201\265\302\267\347\213\220\350\210\236\351\235\222\344\270\230";
static const char __pyx_k__292[] = "\344\270\271\351\235\222\347\272\265\346\250\252\302\267\351\276\231\346\275\255\345\242\250\344\272\221";
static const char __pyx_k__293[] = "\347\231\276\351\254\274\351\227\256\351\201\223\302\267\345\271\275\345\206\245\347\213\274\345\275\261";
static const char __pyx_k__294[] = "\350\234\203\346\265\267\345\260\230\350\270\252";
static const char __pyx_k__295[] = "\345\242\250\346\263\225\345\244\251\345\234\260";
static const char __pyx_k__296[] = "\346\230\237\346\234\224\344\271\235\345\244\251";
static const char __pyx_k__297[] = "\345\260\230\347\274\230\345\274\225\346\242\246";
static const char __pyx_k__298[] = "\347\226\276\351\271\260\346\216\240\351\207\216";
static const char __pyx_k__299[] = "\345\271\273\346\236\242\345\275\222\345\245\207";
static const char __pyx_k__300[] = "\345\210\244\351\201\223\347\213\261\347\201\253";
static const char __pyx_k__301[] = "\347\253\271\351\276\231\345\275\273\350\231\271";
static const char __pyx_k__302[] = "\350\234\273\345\275\261\351\243\236\345\205\211";
static const char __pyx_k__303[] = "\346\230\237\345\226\265\346\242\246\344\275\277";
static const char __pyx_k__304[] = "\344\274\217\351\255\224\344\277\256\347\275\227";
static const char __pyx_k__305[] = "\351\271\244\350\210\236\344\272\221\344\273\231";
static const char __pyx_k__306[] = "\346\217\275\345\215\216\345\256\265";
static const char __pyx_k__307[] = "\351\224\246\347\276\275\345\257\222\345\205\211";
static const char __pyx_k__308[] = "";
static const char __pyx_k__309[] = "\350\201\214\344\270\232";
static const char __pyx_k__310[] = "\350\264\246\345\217\267\347\261\273\345\236\213";
static const char __pyx_k__311[] = "\346\200\247\345\210\253";
static const char __pyx_k__312[] = "\350\247\222\350\211\262\347\255\211\347\272\247";
static const char __pyx_k__313[] = "\350\257\204\345\210\206";
static const char __pyx_k__314[] = "\350\241\243\345\223\201";
static const char __pyx_k__315[] = "\345\233\275\350\211\262\345\200\274";
static const char __pyx_k__316[] = "\345\267\262\344\275\277\347\224\250\345\244\251\350\265\217\347\237\263";
static const char __pyx_k__317[] = "\346\234\252\344\275\277\347\224\250\345\244\251\350\265\217\347\237\263";
static const char __pyx_k__318[] = "\351\231\220\345\256\232\347\272\271\347\216\211\351\242\235\345\272\246";
static const char __pyx_k__319[] = "\347\201\265\351\237\265\346\225\260\351\207\217";
static const char __pyx_k__320[] = "\345\244\251\351\234\223\346\237\223";
static const char __pyx_k__321[] = "\345\205\205\345\200\274\351\207\221\351\242\235";
static const char __pyx_k__322[] = "\347\250\200\346\234\211\345\244\226\350\247\202";
static const char __pyx_k__323[] = "\347\250\200\346\234\211\351\201\223\345\205\267";
static const char __pyx_k__324[] = "\345\244\251\350\265\217\345\217\221\345\236\213";
static const char __pyx_k__325[] = "\345\244\251\350\265\217\347\245\245\347\221\236";
static const char __pyx_k__326[] = "\345\244\251\350\265\217\346\212\200\350\203\275\347\232\256\350\202\244";
static const char __pyx_k__327[] = "\345\205\266\344\273\226\345\244\251\350\265\217\351\201\223\345\205\267";
static const char __pyx_k__328[] = "\345\244\251\350\265\217\345\256\240\347\211\251";
static const char __pyx_k__329[] = "\347\201\265\351\237\265\345\206\205\345\212\237";
static const char __pyx_k__330[] = "\346\273\241\347\272\247\347\276\244\344\276\240";
static const char __pyx_k__331[] = "\346\273\241\347\272\247\347\273\235\346\212\200";
static const char __pyx_k__332[] = "\351\246\226\351\242\206\346\211\223\351\200\240";
static const char __pyx_k__333[] = "\345\205\205\345\200\274\347\247\260\345\217\267";
static const char __pyx_k__334[] = "\346\255\246\345\231\250\345\244\226\350\247\202";
static const char __pyx_k__335[] = "\347\273\235\347\211\210\345\244\226\350\247\202";
static const char __pyx_k__336[] = "\345\225\206\345\237\216\345\244\226\350\247\202";
static const char __pyx_k__337[] = "\350\275\273\345\212\237\345\244\226\350\247\202";
static const char __pyx_k__338[] = "\351\207\221\350\211\262\346\255\246\345\231\250\346\211\223\351\200\240";
static const char __pyx_k__339[] = "\351\207\221\350\211\262\351\241\271\351\223\276\346\211\223\351\200\240";
static const char __pyx_k__340[] = "\351\207\221\350\211\262\351\236\213\345\255\220\346\211\223\351\200\240";
static const char __pyx_k__341[] = "\351\207\221\350\211\262\350\241\243\346\234\215\346\212\244\350\205\225\346\211\223\351\200\240";
static const char __pyx_k__342[] = "\351\207\221\350\211\262\350\205\260\345\270\246\346\211\223\351\200\240";
static const char __pyx_k__343[] = "\347\203\255\351\227\250\347\224\267\345\217\267\350\207\252\346\237\223";
static const char __pyx_k__344[] = "\347\203\255\351\227\250\345\245\263\345\217\267\350\207\252\346\237\223";
static const char __pyx_k__345[] = "\346\270\270\346\210\217\350\264\246\345\217\267";
static const char __pyx_k__346[] = "\346\270\270\346\210\217\345\257\206\347\240\201";
static const char __pyx_k__347[] = "\347\241\256\350\256\244\345\257\206\347\240\201";
static const char __pyx_k__348[] = "\350\264\246\345\217\267\346\235\245\346\272\220";
static const char __pyx_k__349[] = "\345\214\272\346\234\215";
static const char __pyx_k__350[] = "\351\200\206\346\260\264\345\257\222\346\211\213\346\270\270";
static const char __pyx_k__351[] = "?";
static const char __pyx_k_main[] = "__main__";
static const char __pyx_k_name[] = "__name__";
static const char __pyx_k_nsha[] = "nsha";
static const char __pyx_k_test[] = "__test__";
static const char __pyx_k_type[] = "type";
static const char __pyx_k_00_23[] = "00-23";
static const char __pyx_k_price[] = "price";
static const char __pyx_k_value[] = "value";
static const char __pyx_k_D_nsha[] = "D:\\nsha";
static const char __pyx_k_app_id[] = "app_id";
static const char __pyx_k_scrcpy[] = "scrcpy";
static const char __pyx_k_MAX_FPS[] = "MAX_FPS";
static const char __pyx_k_ADB_PATH[] = "ADB_PATH";
static const char __pyx_k_CD_CD_CD[] = "\350\264\246\345\217\267\344\270\223\345\214\272,\346\234\200\344\275\216\344\273\267\346\240\274\345\244\232\345\260\221,\345\214\272\346\234\215,\350\275\254\346\200\247CD,\350\275\254\350\201\214CD,\346\215\242\347\273\221CD,\346\200\247\345\210\253,\350\201\214\344\270\232,\350\264\246\345\217\267\347\261\273\345\236\213,\351\207\221\350\211\262\346\255\246\345\231\250\346\211\223\351\200\240,\351\207\221\350\211\262\351\236\213\345\255\220\346\211\223\351\200\240,\351\207\221\350\211\262\350\205\260\345\270\246\346\211\223\351\200\240,\351\207\221\350\211\262\350\241\243\346\234\215\346\212\244\350\205\225\346\211\223\351\200\240,\351\246\226\351\242\206\346\211\223\351\200\240,\346\273\241\347\272\247\347\273\235\346\212\200,\347\203\255\351\227\250\347\224\267\345\217\267\350\207\252\346\237\223,\347\203\255\351\227\250\345\245\263\345\217\267\350\207\252\346\237\223,\346\236\201\345\223\201\345\206\205\345\212\237";
static const char __pyx_k_LOG_PATH[] = "LOG_PATH";
static const char __pyx_k_pushType[] = "pushType";
static const char __pyx_k_MAX_ZUOQI[] = "MAX_ZUOQI";
static const char __pyx_k_TAP_DELAY[] = "TAP_DELAY";
static const char __pyx_k_TSS_VALUE[] = "TSS_VALUE";
static const char __pyx_k_albumPics[] = "albumPics";
static const char __pyx_k_attriName[] = "attriName";
static const char __pyx_k_ERROR_TEXT[] = "ERROR_TEXT";
static const char __pyx_k_FANYE_WUQI[] = "FANYE_WUQI";
static const char __pyx_k_searchType[] = "searchType";
static const char __pyx_k_secret_key[] = "secret_key";
static const char __pyx_k_server_url[] = "server_url";
static const char __pyx_k_DEBUG_MODEL[] = "DEBUG_MODEL";
static const char __pyx_k_SCRCPY_PATH[] = "SCRCPY_PATH";
static const char __pyx_k_description[] = "description";
static const char __pyx_k_static_logs[] = "static/logs";
static const char __pyx_k_ANDROID_PATH[] = "ANDROID_PATH";
static const char __pyx_k_CAPTURE_MODE[] = "CAPTURE_MODE";
static const char __pyx_k_FANYE_BEIBAO[] = "FANYE_BEIBAO";
static const char __pyx_k_PROJECT_NAME[] = "PROJECT_NAME";
static const char __pyx_k_PROJECT_PATH[] = "PROJECT_PATH";
static const char __pyx_k_lock_keywords[] = "lock_keywords";
static const char __pyx_k_originalPrice[] = "originalPrice";
static const char __pyx_k_VIDEO_BIT_RATE[] = "VIDEO_BIT_RATE";
static const char __pyx_k_gameCareinfoVx[] = "gameCareinfoVx";
static const char __pyx_k_gameGoodsYijia[] = "gameGoodsYijia";
static const char __pyx_k_DEVICE_MAX_LINE[] = "DEVICE_MAX_LINE";
static const char __pyx_k_PINGFEN_XIAXIAN[] = "PINGFEN_XIAXIAN";
static const char __pyx_k_SKIP_ATTRI_NAME[] = "SKIP_ATTRI_NAME";
static const char __pyx_k_WINDOW_MAX_SIZE[] = "WINDOW_MAX_SIZE";
static const char __pyx_k_api_get_account[] = "api_get_account";
static const char __pyx_k_gameAccountQufu[] = "gameAccountQufu";
static const char __pyx_k_AUTO_UPLOAD_DATA[] = "AUTO_UPLOAD_DATA";
static const char __pyx_k_MAX_FASHIS_COUNT[] = "MAX_FASHIS_COUNT";
static const char __pyx_k_SERVER_API_TOKEN[] = "SERVER_API_TOKEN";
static const char __pyx_k_black_attr_value[] = "black_attr_value";
static const char __pyx_k_gameCareinfoTime[] = "gameCareinfoTime";
static const char __pyx_k_image_server_url[] = "image_server_url";
static const char __pyx_k_AUTO_RETURN_LOGIN[] = "AUTO_RETURN_LOGIN";
static const char __pyx_k_C1_CODE_WAIT_TIME[] = "C1_CODE_WAIT_TIME";
static const char __pyx_k_MAX_CLOTHES_COUNT[] = "MAX_CLOTHES_COUNT";
static const char __pyx_k_OSS_ACCESS_KEY_ID[] = "OSS_ACCESS_KEY_ID";
static const char __pyx_k_api_upload_images[] = "api_upload_images";
static const char __pyx_k_chongzhi_chenghao[] = "chongzhi_chenghao";
static const char __pyx_k_gameCareinfoPhone[] = "gameCareinfoPhone";
static const char __pyx_k_gameGoodsSaletype[] = "gameGoodsSaletype";
static const char __pyx_k_productCategoryId[] = "productCategoryId";
static const char __pyx_k_qd561595395732389[] = "qd561595395732389";
static const char __pyx_k_cline_in_traceback[] = "cline_in_traceback";
static const char __pyx_k_create_product_req[] = "create_product_req";
static const char __pyx_k_gameCareinfoPhone2[] = "gameCareinfoPhone2";
static const char __pyx_k_productAttributeId[] = "productAttributeId";
static const char __pyx_k_zx_unlock_keywords[] = "zx_unlock_keywords";
static const char __pyx_k_DEVICE_MAX_LOG_SIZE[] = "DEVICE_MAX_LOG_SIZE";
static const char __pyx_k_FANYE_WUQI_HUANSHEN[] = "FANYE_WUQI_HUANSHEN";
static const char __pyx_k_NEIGONG_CLICK_COUNT[] = "NEIGONG_CLICK_COUNT";
static const char __pyx_k_productCategoryName[] = "productCategoryName";
static const char __pyx_k_http_127_0_0_1_17001[] = "http://127.0.0.1:17001";
static const char __pyx_k_https_api2_kkzhw_com[] = "https://api2.kkzhw.com";
static const char __pyx_k_OSS_ACCESS_KEY_SECRET[] = "OSS_ACCESS_KEY_SECRET";
static const char __pyx_k_NEIGONG_ZHUANGBEI_COUNT[] = "NEIGONG_ZHUANGBEI_COUNT";
static const char __pyx_k_https_images2_kkzhw_com[] = "https://images2.kkzhw.com/";
static const char __pyx_k_LTAI5t8j4SZCrnBiFoEzXm7J[] = "LTAI5t8j4SZCrnBiFoEzXm7J";
static const char __pyx_k_api_add_image_to_account[] = "api_add_image_to_account";
static const char __pyx_k_productAttributeValueList[] = "productAttributeValueList";
static const char __pyx_k_productAttributeCategoryId[] = "productAttributeCategoryId";
static const char __pyx_k_storage_emulated_0_Download[] = "/storage/emulated/0/Download/";
static const char __pyx_k_******************************[] = "******************************";
static const char __pyx_k_mall_portal_openapi_record_add[] = "/mall-portal/openapi/record/add_account_images";
static const char __pyx_k_mall_portal_openapi_record_get[] = "/mall-portal/openapi/record/get_nshaccount_info";
static const char __pyx_k_c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c[] = "c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c";
static const char __pyx_k_mall_portal_openapi_record_uplo[] = "/mall-portal/openapi/record/upload_image";
static const char __pyx_k_8x8coht211zh6l22dci2v7zgzav8zxs5[] = "8x8coht211zh6l22dci2v7zgzav8zxs5udy23iitt90";
/* #### Code section: decls ### */
/* #### Code section: late_includes ### */
/* #### Code section: module_state ### */
typedef struct {
  PyObject *__pyx_d;
  PyObject *__pyx_b;
  PyObject *__pyx_cython_runtime;
  PyObject *__pyx_empty_tuple;
  PyObject *__pyx_empty_bytes;
  PyObject *__pyx_empty_unicode;
  #ifdef __Pyx_CyFunction_USED
  PyTypeObject *__pyx_CyFunctionType;
  #endif
  #ifdef __Pyx_FusedFunction_USED
  PyTypeObject *__pyx_FusedFunctionType;
  #endif
  #ifdef __Pyx_Generator_USED
  PyTypeObject *__pyx_GeneratorType;
  #endif
  #ifdef __Pyx_IterableCoroutine_USED
  PyTypeObject *__pyx_IterableCoroutineType;
  #endif
  #ifdef __Pyx_Coroutine_USED
  PyTypeObject *__pyx_CoroutineAwaitType;
  #endif
  #ifdef __Pyx_Coroutine_USED
  PyTypeObject *__pyx_CoroutineType;
  #endif
  #if CYTHON_USE_MODULE_STATE
  #endif
  PyObject *__pyx_n_u_;
  PyObject *__pyx_kp_u_00_23;
  PyObject *__pyx_kp_u_150;
  PyObject *__pyx_kp_u_17;
  PyObject *__pyx_kp_u_2200;
  PyObject *__pyx_kp_u_5M;
  PyObject *__pyx_kp_u_75;
  PyObject *__pyx_kp_u_8x8coht211zh6l22dci2v7zgzav8zxs5;
  PyObject *__pyx_n_s_ADB_PATH;
  PyObject *__pyx_n_s_ANDROID_PATH;
  PyObject *__pyx_n_s_AUTO_RETURN_LOGIN;
  PyObject *__pyx_n_s_AUTO_UPLOAD_DATA;
  PyObject *__pyx_n_s_C1_CODE_WAIT_TIME;
  PyObject *__pyx_n_s_CAPTURE_MODE;
  PyObject *__pyx_n_u_CD;
  PyObject *__pyx_n_u_CD_2;
  PyObject *__pyx_n_u_CD_3;
  PyObject *__pyx_kp_u_CD_CD_CD;
  PyObject *__pyx_n_s_DEBUG_MODEL;
  PyObject *__pyx_n_s_DEVICE_MAX_LINE;
  PyObject *__pyx_n_s_DEVICE_MAX_LOG_SIZE;
  PyObject *__pyx_kp_u_D_nsha;
  PyObject *__pyx_n_s_ERROR_TEXT;
  PyObject *__pyx_n_s_FANYE_BEIBAO;
  PyObject *__pyx_n_s_FANYE_WUQI;
  PyObject *__pyx_n_s_FANYE_WUQI_HUANSHEN;
  PyObject *__pyx_n_s_HOST;
  PyObject *__pyx_n_s_LOG_PATH;
  PyObject *__pyx_n_u_LTAI5t8j4SZCrnBiFoEzXm7J;
  PyObject *__pyx_n_s_MAX_CLOTHES_COUNT;
  PyObject *__pyx_n_s_MAX_FASHIS_COUNT;
  PyObject *__pyx_n_s_MAX_FPS;
  PyObject *__pyx_n_s_MAX_ZUOQI;
  PyObject *__pyx_n_s_NEIGONG_CLICK_COUNT;
  PyObject *__pyx_n_s_NEIGONG_ZHUANGBEI_COUNT;
  PyObject *__pyx_n_s_OSS_ACCESS_KEY_ID;
  PyObject *__pyx_n_s_OSS_ACCESS_KEY_SECRET;
  PyObject *__pyx_n_s_PINGFEN_XIAXIAN;
  PyObject *__pyx_n_s_PROJECT_NAME;
  PyObject *__pyx_n_s_PROJECT_PATH;
  PyObject *__pyx_n_s_SCRCPY_PATH;
  PyObject *__pyx_n_s_SERVER_API_TOKEN;
  PyObject *__pyx_n_s_SKIP_ATTRI_NAME;
  PyObject *__pyx_n_s_TAP_DELAY;
  PyObject *__pyx_n_s_TSS_VALUE;
  PyObject *__pyx_n_s_VIDEO_BIT_RATE;
  PyObject *__pyx_n_s_WINDOW_MAX_SIZE;
  PyObject *__pyx_n_u_******************************;
  PyObject *__pyx_n_u__10;
  PyObject *__pyx_n_u__100;
  PyObject *__pyx_n_u__101;
  PyObject *__pyx_n_u__102;
  PyObject *__pyx_n_u__103;
  PyObject *__pyx_n_u__104;
  PyObject *__pyx_n_u__105;
  PyObject *__pyx_n_u__106;
  PyObject *__pyx_n_u__107;
  PyObject *__pyx_n_u__108;
  PyObject *__pyx_n_u__109;
  PyObject *__pyx_n_u__11;
  PyObject *__pyx_n_u__110;
  PyObject *__pyx_n_u__111;
  PyObject *__pyx_n_u__112;
  PyObject *__pyx_n_u__113;
  PyObject *__pyx_n_u__114;
  PyObject *__pyx_n_u__115;
  PyObject *__pyx_n_u__116;
  PyObject *__pyx_n_u__117;
  PyObject *__pyx_n_u__118;
  PyObject *__pyx_n_u__119;
  PyObject *__pyx_n_u__12;
  PyObject *__pyx_n_u__120;
  PyObject *__pyx_n_u__121;
  PyObject *__pyx_n_u__122;
  PyObject *__pyx_n_u__123;
  PyObject *__pyx_n_u__124;
  PyObject *__pyx_n_u__125;
  PyObject *__pyx_n_u__126;
  PyObject *__pyx_n_u__127;
  PyObject *__pyx_n_u__128;
  PyObject *__pyx_n_u__129;
  PyObject *__pyx_n_u__13;
  PyObject *__pyx_n_u__130;
  PyObject *__pyx_n_u__132;
  PyObject *__pyx_n_u__134;
  PyObject *__pyx_n_u__136;
  PyObject *__pyx_n_u__138;
  PyObject *__pyx_n_u__14;
  PyObject *__pyx_n_u__140;
  PyObject *__pyx_n_u__142;
  PyObject *__pyx_n_u__144;
  PyObject *__pyx_n_u__146;
  PyObject *__pyx_n_u__149;
  PyObject *__pyx_n_u__15;
  PyObject *__pyx_n_u__150;
  PyObject *__pyx_n_u__151;
  PyObject *__pyx_n_u__152;
  PyObject *__pyx_n_u__153;
  PyObject *__pyx_n_u__154;
  PyObject *__pyx_n_u__155;
  PyObject *__pyx_n_u__156;
  PyObject *__pyx_n_u__157;
  PyObject *__pyx_n_u__158;
  PyObject *__pyx_n_u__159;
  PyObject *__pyx_n_u__16;
  PyObject *__pyx_n_u__160;
  PyObject *__pyx_n_u__161;
  PyObject *__pyx_n_u__162;
  PyObject *__pyx_n_u__163;
  PyObject *__pyx_n_u__164;
  PyObject *__pyx_n_u__165;
  PyObject *__pyx_n_u__166;
  PyObject *__pyx_kp_u__167;
  PyObject *__pyx_n_u__168;
  PyObject *__pyx_n_u__169;
  PyObject *__pyx_n_u__17;
  PyObject *__pyx_n_u__170;
  PyObject *__pyx_n_u__171;
  PyObject *__pyx_n_u__172;
  PyObject *__pyx_n_u__173;
  PyObject *__pyx_n_u__174;
  PyObject *__pyx_n_u__175;
  PyObject *__pyx_n_u__176;
  PyObject *__pyx_n_u__177;
  PyObject *__pyx_n_u__178;
  PyObject *__pyx_n_u__179;
  PyObject *__pyx_n_u__18;
  PyObject *__pyx_n_u__180;
  PyObject *__pyx_n_u__181;
  PyObject *__pyx_n_u__182;
  PyObject *__pyx_n_u__183;
  PyObject *__pyx_n_u__184;
  PyObject *__pyx_n_u__185;
  PyObject *__pyx_n_u__186;
  PyObject *__pyx_n_u__187;
  PyObject *__pyx_n_u__188;
  PyObject *__pyx_n_u__189;
  PyObject *__pyx_n_u__19;
  PyObject *__pyx_n_u__190;
  PyObject *__pyx_n_u__191;
  PyObject *__pyx_n_u__192;
  PyObject *__pyx_n_u__193;
  PyObject *__pyx_n_u__194;
  PyObject *__pyx_n_u__195;
  PyObject *__pyx_n_u__196;
  PyObject *__pyx_n_u__197;
  PyObject *__pyx_n_u__198;
  PyObject *__pyx_n_u__199;
  PyObject *__pyx_n_u__2;
  PyObject *__pyx_n_u__20;
  PyObject *__pyx_n_u__200;
  PyObject *__pyx_n_u__201;
  PyObject *__pyx_n_u__202;
  PyObject *__pyx_n_u__203;
  PyObject *__pyx_n_u__204;
  PyObject *__pyx_n_u__205;
  PyObject *__pyx_n_u__206;
  PyObject *__pyx_n_u__207;
  PyObject *__pyx_n_u__208;
  PyObject *__pyx_n_u__209;
  PyObject *__pyx_n_u__21;
  PyObject *__pyx_n_u__210;
  PyObject *__pyx_n_u__211;
  PyObject *__pyx_n_u__212;
  PyObject *__pyx_n_u__213;
  PyObject *__pyx_n_u__214;
  PyObject *__pyx_n_u__215;
  PyObject *__pyx_n_u__216;
  PyObject *__pyx_n_u__217;
  PyObject *__pyx_n_u__218;
  PyObject *__pyx_n_u__219;
  PyObject *__pyx_n_u__22;
  PyObject *__pyx_n_u__220;
  PyObject *__pyx_n_u__221;
  PyObject *__pyx_n_u__222;
  PyObject *__pyx_n_u__223;
  PyObject *__pyx_n_u__224;
  PyObject *__pyx_n_u__225;
  PyObject *__pyx_n_u__226;
  PyObject *__pyx_n_u__227;
  PyObject *__pyx_n_u__228;
  PyObject *__pyx_n_u__229;
  PyObject *__pyx_n_u__23;
  PyObject *__pyx_n_u__230;
  PyObject *__pyx_n_u__231;
  PyObject *__pyx_n_u__232;
  PyObject *__pyx_n_u__233;
  PyObject *__pyx_n_u__234;
  PyObject *__pyx_n_u__235;
  PyObject *__pyx_n_u__236;
  PyObject *__pyx_n_u__237;
  PyObject *__pyx_n_u__238;
  PyObject *__pyx_n_u__239;
  PyObject *__pyx_n_u__24;
  PyObject *__pyx_n_u__240;
  PyObject *__pyx_n_u__241;
  PyObject *__pyx_n_u__242;
  PyObject *__pyx_n_u__243;
  PyObject *__pyx_n_u__244;
  PyObject *__pyx_n_u__245;
  PyObject *__pyx_n_u__246;
  PyObject *__pyx_n_u__247;
  PyObject *__pyx_n_u__248;
  PyObject *__pyx_n_u__249;
  PyObject *__pyx_n_u__25;
  PyObject *__pyx_n_u__250;
  PyObject *__pyx_n_u__251;
  PyObject *__pyx_n_u__252;
  PyObject *__pyx_n_u__253;
  PyObject *__pyx_n_u__254;
  PyObject *__pyx_n_u__255;
  PyObject *__pyx_n_u__256;
  PyObject *__pyx_n_u__257;
  PyObject *__pyx_n_u__258;
  PyObject *__pyx_n_u__259;
  PyObject *__pyx_n_u__26;
  PyObject *__pyx_n_u__260;
  PyObject *__pyx_n_u__261;
  PyObject *__pyx_n_u__262;
  PyObject *__pyx_n_u__263;
  PyObject *__pyx_n_u__264;
  PyObject *__pyx_n_u__265;
  PyObject *__pyx_n_u__266;
  PyObject *__pyx_n_u__267;
  PyObject *__pyx_n_u__268;
  PyObject *__pyx_n_u__269;
  PyObject *__pyx_n_u__27;
  PyObject *__pyx_n_u__270;
  PyObject *__pyx_n_u__271;
  PyObject *__pyx_n_u__272;
  PyObject *__pyx_n_u__273;
  PyObject *__pyx_n_u__274;
  PyObject *__pyx_kp_u__275;
  PyObject *__pyx_n_u__276;
  PyObject *__pyx_n_u__277;
  PyObject *__pyx_kp_u__278;
  PyObject *__pyx_kp_u__279;
  PyObject *__pyx_n_u__28;
  PyObject *__pyx_n_u__280;
  PyObject *__pyx_kp_u__281;
  PyObject *__pyx_n_u__282;
  PyObject *__pyx_kp_u__283;
  PyObject *__pyx_kp_u__284;
  PyObject *__pyx_kp_u__285;
  PyObject *__pyx_kp_u__286;
  PyObject *__pyx_n_u__287;
  PyObject *__pyx_kp_u__288;
  PyObject *__pyx_n_u__289;
  PyObject *__pyx_n_u__29;
  PyObject *__pyx_kp_u__290;
  PyObject *__pyx_kp_u__291;
  PyObject *__pyx_kp_u__292;
  PyObject *__pyx_kp_u__293;
  PyObject *__pyx_n_u__294;
  PyObject *__pyx_n_u__295;
  PyObject *__pyx_n_u__296;
  PyObject *__pyx_n_u__297;
  PyObject *__pyx_n_u__298;
  PyObject *__pyx_n_u__299;
  PyObject *__pyx_n_u__3;
  PyObject *__pyx_n_u__30;
  PyObject *__pyx_n_u__300;
  PyObject *__pyx_n_u__301;
  PyObject *__pyx_n_u__302;
  PyObject *__pyx_n_u__303;
  PyObject *__pyx_n_u__304;
  PyObject *__pyx_n_u__305;
  PyObject *__pyx_n_u__306;
  PyObject *__pyx_n_u__307;
  PyObject *__pyx_kp_u__308;
  PyObject *__pyx_n_u__309;
  PyObject *__pyx_n_u__31;
  PyObject *__pyx_n_u__310;
  PyObject *__pyx_n_u__311;
  PyObject *__pyx_n_u__312;
  PyObject *__pyx_n_u__313;
  PyObject *__pyx_n_u__314;
  PyObject *__pyx_n_u__315;
  PyObject *__pyx_n_u__316;
  PyObject *__pyx_n_u__317;
  PyObject *__pyx_n_u__318;
  PyObject *__pyx_n_u__319;
  PyObject *__pyx_n_u__32;
  PyObject *__pyx_n_u__320;
  PyObject *__pyx_n_u__321;
  PyObject *__pyx_n_u__322;
  PyObject *__pyx_n_u__323;
  PyObject *__pyx_n_u__324;
  PyObject *__pyx_n_u__325;
  PyObject *__pyx_n_u__326;
  PyObject *__pyx_n_u__327;
  PyObject *__pyx_n_u__328;
  PyObject *__pyx_n_u__329;
  PyObject *__pyx_n_u__33;
  PyObject *__pyx_n_u__330;
  PyObject *__pyx_n_u__331;
  PyObject *__pyx_n_u__332;
  PyObject *__pyx_n_u__333;
  PyObject *__pyx_n_u__334;
  PyObject *__pyx_n_u__335;
  PyObject *__pyx_n_u__336;
  PyObject *__pyx_n_u__337;
  PyObject *__pyx_n_u__338;
  PyObject *__pyx_n_u__339;
  PyObject *__pyx_n_u__34;
  PyObject *__pyx_n_u__340;
  PyObject *__pyx_n_u__341;
  PyObject *__pyx_n_u__342;
  PyObject *__pyx_n_u__343;
  PyObject *__pyx_n_u__344;
  PyObject *__pyx_n_u__345;
  PyObject *__pyx_n_u__346;
  PyObject *__pyx_n_u__347;
  PyObject *__pyx_n_u__348;
  PyObject *__pyx_n_u__349;
  PyObject *__pyx_kp_u__35;
  PyObject *__pyx_n_u__350;
  PyObject *__pyx_n_s__351;
  PyObject *__pyx_n_u__36;
  PyObject *__pyx_n_u__37;
  PyObject *__pyx_kp_u__38;
  PyObject *__pyx_kp_u__39;
  PyObject *__pyx_n_u__4;
  PyObject *__pyx_kp_u__40;
  PyObject *__pyx_kp_u__41;
  PyObject *__pyx_n_u__42;
  PyObject *__pyx_n_u__43;
  PyObject *__pyx_n_u__44;
  PyObject *__pyx_kp_u__45;
  PyObject *__pyx_n_u__46;
  PyObject *__pyx_n_u__47;
  PyObject *__pyx_kp_u__48;
  PyObject *__pyx_n_u__49;
  PyObject *__pyx_kp_u__5;
  PyObject *__pyx_n_u__50;
  PyObject *__pyx_kp_u__51;
  PyObject *__pyx_n_u__52;
  PyObject *__pyx_n_u__53;
  PyObject *__pyx_kp_u__54;
  PyObject *__pyx_kp_u__55;
  PyObject *__pyx_n_u__56;
  PyObject *__pyx_n_u__57;
  PyObject *__pyx_kp_u__58;
  PyObject *__pyx_n_u__59;
  PyObject *__pyx_n_u__6;
  PyObject *__pyx_n_u__60;
  PyObject *__pyx_kp_u__61;
  PyObject *__pyx_n_u__62;
  PyObject *__pyx_n_u__63;
  PyObject *__pyx_kp_u__64;
  PyObject *__pyx_n_u__65;
  PyObject *__pyx_n_u__66;
  PyObject *__pyx_n_u__67;
  PyObject *__pyx_n_u__68;
  PyObject *__pyx_n_u__69;
  PyObject *__pyx_kp_u__7;
  PyObject *__pyx_n_u__70;
  PyObject *__pyx_n_u__71;
  PyObject *__pyx_n_u__72;
  PyObject *__pyx_n_u__73;
  PyObject *__pyx_n_u__74;
  PyObject *__pyx_n_u__75;
  PyObject *__pyx_n_u__76;
  PyObject *__pyx_n_u__77;
  PyObject *__pyx_n_u__78;
  PyObject *__pyx_n_u__79;
  PyObject *__pyx_n_u__8;
  PyObject *__pyx_n_u__80;
  PyObject *__pyx_n_u__81;
  PyObject *__pyx_kp_u__82;
  PyObject *__pyx_n_u__83;
  PyObject *__pyx_kp_u__84;
  PyObject *__pyx_kp_u__85;
  PyObject *__pyx_n_u__86;
  PyObject *__pyx_n_u__87;
  PyObject *__pyx_n_u__88;
  PyObject *__pyx_n_u__89;
  PyObject *__pyx_kp_u__9;
  PyObject *__pyx_n_u__90;
  PyObject *__pyx_n_u__91;
  PyObject *__pyx_n_u__92;
  PyObject *__pyx_n_u__93;
  PyObject *__pyx_n_u__94;
  PyObject *__pyx_n_u__95;
  PyObject *__pyx_n_u__96;
  PyObject *__pyx_n_u__97;
  PyObject *__pyx_n_u__98;
  PyObject *__pyx_n_u__99;
  PyObject *__pyx_n_u_adb;
  PyObject *__pyx_n_u_albumPics;
  PyObject *__pyx_n_s_api_add_image_to_account;
  PyObject *__pyx_n_s_api_get_account;
  PyObject *__pyx_n_s_api_upload_images;
  PyObject *__pyx_n_s_app_id;
  PyObject *__pyx_n_u_attriName;
  PyObject *__pyx_n_s_black_attr_value;
  PyObject *__pyx_n_u_c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c;
  PyObject *__pyx_n_s_chongzhi_chenghao;
  PyObject *__pyx_n_s_cline_in_traceback;
  PyObject *__pyx_n_s_create_product_req;
  PyObject *__pyx_n_u_description;
  PyObject *__pyx_n_u_gameAccountQufu;
  PyObject *__pyx_n_u_gameCareinfoPhone;
  PyObject *__pyx_n_u_gameCareinfoPhone2;
  PyObject *__pyx_n_u_gameCareinfoTime;
  PyObject *__pyx_n_u_gameCareinfoVx;
  PyObject *__pyx_n_u_gameGoodsSaletype;
  PyObject *__pyx_n_u_gameGoodsYijia;
  PyObject *__pyx_kp_u_http_127_0_0_1_17001;
  PyObject *__pyx_kp_u_https_api2_kkzhw_com;
  PyObject *__pyx_kp_u_https_images2_kkzhw_com;
  PyObject *__pyx_n_s_image_server_url;
  PyObject *__pyx_n_s_lock_keywords;
  PyObject *__pyx_n_s_main;
  PyObject *__pyx_kp_u_mall_portal_openapi_record_add;
  PyObject *__pyx_kp_u_mall_portal_openapi_record_get;
  PyObject *__pyx_kp_u_mall_portal_openapi_record_uplo;
  PyObject *__pyx_n_s_name;
  PyObject *__pyx_n_u_nsha;
  PyObject *__pyx_n_u_originalPrice;
  PyObject *__pyx_n_u_pic;
  PyObject *__pyx_n_u_price;
  PyObject *__pyx_n_u_productAttributeCategoryId;
  PyObject *__pyx_n_u_productAttributeId;
  PyObject *__pyx_n_u_productAttributeValueList;
  PyObject *__pyx_n_u_productCategoryId;
  PyObject *__pyx_n_u_productCategoryName;
  PyObject *__pyx_n_u_pushType;
  PyObject *__pyx_n_u_qd561595395732389;
  PyObject *__pyx_n_u_scrcpy;
  PyObject *__pyx_n_u_searchType;
  PyObject *__pyx_n_s_secret_key;
  PyObject *__pyx_n_s_server_url;
  PyObject *__pyx_kp_u_static_logs;
  PyObject *__pyx_kp_u_storage_emulated_0_Download;
  PyObject *__pyx_n_s_test;
  PyObject *__pyx_n_u_type;
  PyObject *__pyx_n_u_value;
  PyObject *__pyx_n_s_zx_unlock_keywords;
  PyObject *__pyx_float_0_5;
  PyObject *__pyx_int_0;
  PyObject *__pyx_int_1;
  PyObject *__pyx_int_2;
  PyObject *__pyx_int_3;
  PyObject *__pyx_int_4;
  PyObject *__pyx_int_5;
  PyObject *__pyx_int_6;
  PyObject *__pyx_int_9;
  PyObject *__pyx_int_15;
  PyObject *__pyx_int_30;
  PyObject *__pyx_int_32;
  PyObject *__pyx_int_62;
  PyObject *__pyx_int_96;
  PyObject *__pyx_int_97;
  PyObject *__pyx_int_100;
  PyObject *__pyx_int_105;
  PyObject *__pyx_int_106;
  PyObject *__pyx_int_107;
  PyObject *__pyx_int_108;
  PyObject *__pyx_int_113;
  PyObject *__pyx_int_114;
  PyObject *__pyx_int_115;
  PyObject *__pyx_int_116;
  PyObject *__pyx_int_118;
  PyObject *__pyx_int_119;
  PyObject *__pyx_int_166;
  PyObject *__pyx_int_180;
  PyObject *__pyx_int_200;
  PyObject *__pyx_int_333;
  PyObject *__pyx_int_334;
  PyObject *__pyx_int_335;
  PyObject *__pyx_int_336;
  PyObject *__pyx_int_344;
  PyObject *__pyx_int_371;
  PyObject *__pyx_int_372;
  PyObject *__pyx_int_373;
  PyObject *__pyx_int_374;
  PyObject *__pyx_int_545;
  PyObject *__pyx_int_546;
  PyObject *__pyx_int_547;
  PyObject *__pyx_int_548;
  PyObject *__pyx_int_549;
  PyObject *__pyx_int_550;
  PyObject *__pyx_int_552;
  PyObject *__pyx_int_553;
  PyObject *__pyx_int_554;
  PyObject *__pyx_int_852;
  PyObject *__pyx_int_872;
  PyObject *__pyx_int_873;
  PyObject *__pyx_int_899;
  PyObject *__pyx_int_902;
  PyObject *__pyx_int_1000;
  PyObject *__pyx_int_1071;
  PyObject *__pyx_int_1072;
  PyObject *__pyx_int_1073;
  PyObject *__pyx_int_1074;
  PyObject *__pyx_int_1162;
  PyObject *__pyx_int_1180;
  PyObject *__pyx_int_1181;
  PyObject *__pyx_int_1185;
  PyObject *__pyx_int_1280;
  PyObject *__pyx_int_3000;
  PyObject *__pyx_int_10000;
  PyObject *__pyx_int_50000;
  PyObject *__pyx_int_100000;
  PyObject *__pyx_int_200000;
  PyObject *__pyx_int_500000;
  PyObject *__pyx_int_1000000;
  PyObject *__pyx_int_1500000;
  PyObject *__pyx_tuple__131;
  PyObject *__pyx_tuple__133;
  PyObject *__pyx_tuple__135;
  PyObject *__pyx_tuple__137;
  PyObject *__pyx_tuple__139;
  PyObject *__pyx_tuple__141;
  PyObject *__pyx_tuple__143;
  PyObject *__pyx_tuple__145;
  PyObject *__pyx_tuple__147;
  PyObject *__pyx_tuple__148;
} __pyx_mstate;

#if CYTHON_USE_MODULE_STATE
#ifdef __cplusplus
namespace {
  extern struct PyModuleDef __pyx_moduledef;
} /* anonymous namespace */
#else
static struct PyModuleDef __pyx_moduledef;
#endif

#define __pyx_mstate(o) ((__pyx_mstate *)__Pyx_PyModule_GetState(o))

#define __pyx_mstate_global (__pyx_mstate(PyState_FindModule(&__pyx_moduledef)))

#define __pyx_m (PyState_FindModule(&__pyx_moduledef))
#else
static __pyx_mstate __pyx_mstate_global_static =
#ifdef __cplusplus
    {};
#else
    {0};
#endif
static __pyx_mstate *__pyx_mstate_global = &__pyx_mstate_global_static;
#endif
/* #### Code section: module_state_clear ### */
#if CYTHON_USE_MODULE_STATE
static int __pyx_m_clear(PyObject *m) {
  __pyx_mstate *clear_module_state = __pyx_mstate(m);
  if (!clear_module_state) return 0;
  Py_CLEAR(clear_module_state->__pyx_d);
  Py_CLEAR(clear_module_state->__pyx_b);
  Py_CLEAR(clear_module_state->__pyx_cython_runtime);
  Py_CLEAR(clear_module_state->__pyx_empty_tuple);
  Py_CLEAR(clear_module_state->__pyx_empty_bytes);
  Py_CLEAR(clear_module_state->__pyx_empty_unicode);
  #ifdef __Pyx_CyFunction_USED
  Py_CLEAR(clear_module_state->__pyx_CyFunctionType);
  #endif
  #ifdef __Pyx_FusedFunction_USED
  Py_CLEAR(clear_module_state->__pyx_FusedFunctionType);
  #endif
  Py_CLEAR(clear_module_state->__pyx_n_u_);
  Py_CLEAR(clear_module_state->__pyx_kp_u_00_23);
  Py_CLEAR(clear_module_state->__pyx_kp_u_150);
  Py_CLEAR(clear_module_state->__pyx_kp_u_17);
  Py_CLEAR(clear_module_state->__pyx_kp_u_2200);
  Py_CLEAR(clear_module_state->__pyx_kp_u_5M);
  Py_CLEAR(clear_module_state->__pyx_kp_u_75);
  Py_CLEAR(clear_module_state->__pyx_kp_u_8x8coht211zh6l22dci2v7zgzav8zxs5);
  Py_CLEAR(clear_module_state->__pyx_n_s_ADB_PATH);
  Py_CLEAR(clear_module_state->__pyx_n_s_ANDROID_PATH);
  Py_CLEAR(clear_module_state->__pyx_n_s_AUTO_RETURN_LOGIN);
  Py_CLEAR(clear_module_state->__pyx_n_s_AUTO_UPLOAD_DATA);
  Py_CLEAR(clear_module_state->__pyx_n_s_C1_CODE_WAIT_TIME);
  Py_CLEAR(clear_module_state->__pyx_n_s_CAPTURE_MODE);
  Py_CLEAR(clear_module_state->__pyx_n_u_CD);
  Py_CLEAR(clear_module_state->__pyx_n_u_CD_2);
  Py_CLEAR(clear_module_state->__pyx_n_u_CD_3);
  Py_CLEAR(clear_module_state->__pyx_kp_u_CD_CD_CD);
  Py_CLEAR(clear_module_state->__pyx_n_s_DEBUG_MODEL);
  Py_CLEAR(clear_module_state->__pyx_n_s_DEVICE_MAX_LINE);
  Py_CLEAR(clear_module_state->__pyx_n_s_DEVICE_MAX_LOG_SIZE);
  Py_CLEAR(clear_module_state->__pyx_kp_u_D_nsha);
  Py_CLEAR(clear_module_state->__pyx_n_s_ERROR_TEXT);
  Py_CLEAR(clear_module_state->__pyx_n_s_FANYE_BEIBAO);
  Py_CLEAR(clear_module_state->__pyx_n_s_FANYE_WUQI);
  Py_CLEAR(clear_module_state->__pyx_n_s_FANYE_WUQI_HUANSHEN);
  Py_CLEAR(clear_module_state->__pyx_n_s_HOST);
  Py_CLEAR(clear_module_state->__pyx_n_s_LOG_PATH);
  Py_CLEAR(clear_module_state->__pyx_n_u_LTAI5t8j4SZCrnBiFoEzXm7J);
  Py_CLEAR(clear_module_state->__pyx_n_s_MAX_CLOTHES_COUNT);
  Py_CLEAR(clear_module_state->__pyx_n_s_MAX_FASHIS_COUNT);
  Py_CLEAR(clear_module_state->__pyx_n_s_MAX_FPS);
  Py_CLEAR(clear_module_state->__pyx_n_s_MAX_ZUOQI);
  Py_CLEAR(clear_module_state->__pyx_n_s_NEIGONG_CLICK_COUNT);
  Py_CLEAR(clear_module_state->__pyx_n_s_NEIGONG_ZHUANGBEI_COUNT);
  Py_CLEAR(clear_module_state->__pyx_n_s_OSS_ACCESS_KEY_ID);
  Py_CLEAR(clear_module_state->__pyx_n_s_OSS_ACCESS_KEY_SECRET);
  Py_CLEAR(clear_module_state->__pyx_n_s_PINGFEN_XIAXIAN);
  Py_CLEAR(clear_module_state->__pyx_n_s_PROJECT_NAME);
  Py_CLEAR(clear_module_state->__pyx_n_s_PROJECT_PATH);
  Py_CLEAR(clear_module_state->__pyx_n_s_SCRCPY_PATH);
  Py_CLEAR(clear_module_state->__pyx_n_s_SERVER_API_TOKEN);
  Py_CLEAR(clear_module_state->__pyx_n_s_SKIP_ATTRI_NAME);
  Py_CLEAR(clear_module_state->__pyx_n_s_TAP_DELAY);
  Py_CLEAR(clear_module_state->__pyx_n_s_TSS_VALUE);
  Py_CLEAR(clear_module_state->__pyx_n_s_VIDEO_BIT_RATE);
  Py_CLEAR(clear_module_state->__pyx_n_s_WINDOW_MAX_SIZE);
  Py_CLEAR(clear_module_state->__pyx_n_u_******************************);
  Py_CLEAR(clear_module_state->__pyx_n_u__10);
  Py_CLEAR(clear_module_state->__pyx_n_u__100);
  Py_CLEAR(clear_module_state->__pyx_n_u__101);
  Py_CLEAR(clear_module_state->__pyx_n_u__102);
  Py_CLEAR(clear_module_state->__pyx_n_u__103);
  Py_CLEAR(clear_module_state->__pyx_n_u__104);
  Py_CLEAR(clear_module_state->__pyx_n_u__105);
  Py_CLEAR(clear_module_state->__pyx_n_u__106);
  Py_CLEAR(clear_module_state->__pyx_n_u__107);
  Py_CLEAR(clear_module_state->__pyx_n_u__108);
  Py_CLEAR(clear_module_state->__pyx_n_u__109);
  Py_CLEAR(clear_module_state->__pyx_n_u__11);
  Py_CLEAR(clear_module_state->__pyx_n_u__110);
  Py_CLEAR(clear_module_state->__pyx_n_u__111);
  Py_CLEAR(clear_module_state->__pyx_n_u__112);
  Py_CLEAR(clear_module_state->__pyx_n_u__113);
  Py_CLEAR(clear_module_state->__pyx_n_u__114);
  Py_CLEAR(clear_module_state->__pyx_n_u__115);
  Py_CLEAR(clear_module_state->__pyx_n_u__116);
  Py_CLEAR(clear_module_state->__pyx_n_u__117);
  Py_CLEAR(clear_module_state->__pyx_n_u__118);
  Py_CLEAR(clear_module_state->__pyx_n_u__119);
  Py_CLEAR(clear_module_state->__pyx_n_u__12);
  Py_CLEAR(clear_module_state->__pyx_n_u__120);
  Py_CLEAR(clear_module_state->__pyx_n_u__121);
  Py_CLEAR(clear_module_state->__pyx_n_u__122);
  Py_CLEAR(clear_module_state->__pyx_n_u__123);
  Py_CLEAR(clear_module_state->__pyx_n_u__124);
  Py_CLEAR(clear_module_state->__pyx_n_u__125);
  Py_CLEAR(clear_module_state->__pyx_n_u__126);
  Py_CLEAR(clear_module_state->__pyx_n_u__127);
  Py_CLEAR(clear_module_state->__pyx_n_u__128);
  Py_CLEAR(clear_module_state->__pyx_n_u__129);
  Py_CLEAR(clear_module_state->__pyx_n_u__13);
  Py_CLEAR(clear_module_state->__pyx_n_u__130);
  Py_CLEAR(clear_module_state->__pyx_n_u__132);
  Py_CLEAR(clear_module_state->__pyx_n_u__134);
  Py_CLEAR(clear_module_state->__pyx_n_u__136);
  Py_CLEAR(clear_module_state->__pyx_n_u__138);
  Py_CLEAR(clear_module_state->__pyx_n_u__14);
  Py_CLEAR(clear_module_state->__pyx_n_u__140);
  Py_CLEAR(clear_module_state->__pyx_n_u__142);
  Py_CLEAR(clear_module_state->__pyx_n_u__144);
  Py_CLEAR(clear_module_state->__pyx_n_u__146);
  Py_CLEAR(clear_module_state->__pyx_n_u__149);
  Py_CLEAR(clear_module_state->__pyx_n_u__15);
  Py_CLEAR(clear_module_state->__pyx_n_u__150);
  Py_CLEAR(clear_module_state->__pyx_n_u__151);
  Py_CLEAR(clear_module_state->__pyx_n_u__152);
  Py_CLEAR(clear_module_state->__pyx_n_u__153);
  Py_CLEAR(clear_module_state->__pyx_n_u__154);
  Py_CLEAR(clear_module_state->__pyx_n_u__155);
  Py_CLEAR(clear_module_state->__pyx_n_u__156);
  Py_CLEAR(clear_module_state->__pyx_n_u__157);
  Py_CLEAR(clear_module_state->__pyx_n_u__158);
  Py_CLEAR(clear_module_state->__pyx_n_u__159);
  Py_CLEAR(clear_module_state->__pyx_n_u__16);
  Py_CLEAR(clear_module_state->__pyx_n_u__160);
  Py_CLEAR(clear_module_state->__pyx_n_u__161);
  Py_CLEAR(clear_module_state->__pyx_n_u__162);
  Py_CLEAR(clear_module_state->__pyx_n_u__163);
  Py_CLEAR(clear_module_state->__pyx_n_u__164);
  Py_CLEAR(clear_module_state->__pyx_n_u__165);
  Py_CLEAR(clear_module_state->__pyx_n_u__166);
  Py_CLEAR(clear_module_state->__pyx_kp_u__167);
  Py_CLEAR(clear_module_state->__pyx_n_u__168);
  Py_CLEAR(clear_module_state->__pyx_n_u__169);
  Py_CLEAR(clear_module_state->__pyx_n_u__17);
  Py_CLEAR(clear_module_state->__pyx_n_u__170);
  Py_CLEAR(clear_module_state->__pyx_n_u__171);
  Py_CLEAR(clear_module_state->__pyx_n_u__172);
  Py_CLEAR(clear_module_state->__pyx_n_u__173);
  Py_CLEAR(clear_module_state->__pyx_n_u__174);
  Py_CLEAR(clear_module_state->__pyx_n_u__175);
  Py_CLEAR(clear_module_state->__pyx_n_u__176);
  Py_CLEAR(clear_module_state->__pyx_n_u__177);
  Py_CLEAR(clear_module_state->__pyx_n_u__178);
  Py_CLEAR(clear_module_state->__pyx_n_u__179);
  Py_CLEAR(clear_module_state->__pyx_n_u__18);
  Py_CLEAR(clear_module_state->__pyx_n_u__180);
  Py_CLEAR(clear_module_state->__pyx_n_u__181);
  Py_CLEAR(clear_module_state->__pyx_n_u__182);
  Py_CLEAR(clear_module_state->__pyx_n_u__183);
  Py_CLEAR(clear_module_state->__pyx_n_u__184);
  Py_CLEAR(clear_module_state->__pyx_n_u__185);
  Py_CLEAR(clear_module_state->__pyx_n_u__186);
  Py_CLEAR(clear_module_state->__pyx_n_u__187);
  Py_CLEAR(clear_module_state->__pyx_n_u__188);
  Py_CLEAR(clear_module_state->__pyx_n_u__189);
  Py_CLEAR(clear_module_state->__pyx_n_u__19);
  Py_CLEAR(clear_module_state->__pyx_n_u__190);
  Py_CLEAR(clear_module_state->__pyx_n_u__191);
  Py_CLEAR(clear_module_state->__pyx_n_u__192);
  Py_CLEAR(clear_module_state->__pyx_n_u__193);
  Py_CLEAR(clear_module_state->__pyx_n_u__194);
  Py_CLEAR(clear_module_state->__pyx_n_u__195);
  Py_CLEAR(clear_module_state->__pyx_n_u__196);
  Py_CLEAR(clear_module_state->__pyx_n_u__197);
  Py_CLEAR(clear_module_state->__pyx_n_u__198);
  Py_CLEAR(clear_module_state->__pyx_n_u__199);
  Py_CLEAR(clear_module_state->__pyx_n_u__2);
  Py_CLEAR(clear_module_state->__pyx_n_u__20);
  Py_CLEAR(clear_module_state->__pyx_n_u__200);
  Py_CLEAR(clear_module_state->__pyx_n_u__201);
  Py_CLEAR(clear_module_state->__pyx_n_u__202);
  Py_CLEAR(clear_module_state->__pyx_n_u__203);
  Py_CLEAR(clear_module_state->__pyx_n_u__204);
  Py_CLEAR(clear_module_state->__pyx_n_u__205);
  Py_CLEAR(clear_module_state->__pyx_n_u__206);
  Py_CLEAR(clear_module_state->__pyx_n_u__207);
  Py_CLEAR(clear_module_state->__pyx_n_u__208);
  Py_CLEAR(clear_module_state->__pyx_n_u__209);
  Py_CLEAR(clear_module_state->__pyx_n_u__21);
  Py_CLEAR(clear_module_state->__pyx_n_u__210);
  Py_CLEAR(clear_module_state->__pyx_n_u__211);
  Py_CLEAR(clear_module_state->__pyx_n_u__212);
  Py_CLEAR(clear_module_state->__pyx_n_u__213);
  Py_CLEAR(clear_module_state->__pyx_n_u__214);
  Py_CLEAR(clear_module_state->__pyx_n_u__215);
  Py_CLEAR(clear_module_state->__pyx_n_u__216);
  Py_CLEAR(clear_module_state->__pyx_n_u__217);
  Py_CLEAR(clear_module_state->__pyx_n_u__218);
  Py_CLEAR(clear_module_state->__pyx_n_u__219);
  Py_CLEAR(clear_module_state->__pyx_n_u__22);
  Py_CLEAR(clear_module_state->__pyx_n_u__220);
  Py_CLEAR(clear_module_state->__pyx_n_u__221);
  Py_CLEAR(clear_module_state->__pyx_n_u__222);
  Py_CLEAR(clear_module_state->__pyx_n_u__223);
  Py_CLEAR(clear_module_state->__pyx_n_u__224);
  Py_CLEAR(clear_module_state->__pyx_n_u__225);
  Py_CLEAR(clear_module_state->__pyx_n_u__226);
  Py_CLEAR(clear_module_state->__pyx_n_u__227);
  Py_CLEAR(clear_module_state->__pyx_n_u__228);
  Py_CLEAR(clear_module_state->__pyx_n_u__229);
  Py_CLEAR(clear_module_state->__pyx_n_u__23);
  Py_CLEAR(clear_module_state->__pyx_n_u__230);
  Py_CLEAR(clear_module_state->__pyx_n_u__231);
  Py_CLEAR(clear_module_state->__pyx_n_u__232);
  Py_CLEAR(clear_module_state->__pyx_n_u__233);
  Py_CLEAR(clear_module_state->__pyx_n_u__234);
  Py_CLEAR(clear_module_state->__pyx_n_u__235);
  Py_CLEAR(clear_module_state->__pyx_n_u__236);
  Py_CLEAR(clear_module_state->__pyx_n_u__237);
  Py_CLEAR(clear_module_state->__pyx_n_u__238);
  Py_CLEAR(clear_module_state->__pyx_n_u__239);
  Py_CLEAR(clear_module_state->__pyx_n_u__24);
  Py_CLEAR(clear_module_state->__pyx_n_u__240);
  Py_CLEAR(clear_module_state->__pyx_n_u__241);
  Py_CLEAR(clear_module_state->__pyx_n_u__242);
  Py_CLEAR(clear_module_state->__pyx_n_u__243);
  Py_CLEAR(clear_module_state->__pyx_n_u__244);
  Py_CLEAR(clear_module_state->__pyx_n_u__245);
  Py_CLEAR(clear_module_state->__pyx_n_u__246);
  Py_CLEAR(clear_module_state->__pyx_n_u__247);
  Py_CLEAR(clear_module_state->__pyx_n_u__248);
  Py_CLEAR(clear_module_state->__pyx_n_u__249);
  Py_CLEAR(clear_module_state->__pyx_n_u__25);
  Py_CLEAR(clear_module_state->__pyx_n_u__250);
  Py_CLEAR(clear_module_state->__pyx_n_u__251);
  Py_CLEAR(clear_module_state->__pyx_n_u__252);
  Py_CLEAR(clear_module_state->__pyx_n_u__253);
  Py_CLEAR(clear_module_state->__pyx_n_u__254);
  Py_CLEAR(clear_module_state->__pyx_n_u__255);
  Py_CLEAR(clear_module_state->__pyx_n_u__256);
  Py_CLEAR(clear_module_state->__pyx_n_u__257);
  Py_CLEAR(clear_module_state->__pyx_n_u__258);
  Py_CLEAR(clear_module_state->__pyx_n_u__259);
  Py_CLEAR(clear_module_state->__pyx_n_u__26);
  Py_CLEAR(clear_module_state->__pyx_n_u__260);
  Py_CLEAR(clear_module_state->__pyx_n_u__261);
  Py_CLEAR(clear_module_state->__pyx_n_u__262);
  Py_CLEAR(clear_module_state->__pyx_n_u__263);
  Py_CLEAR(clear_module_state->__pyx_n_u__264);
  Py_CLEAR(clear_module_state->__pyx_n_u__265);
  Py_CLEAR(clear_module_state->__pyx_n_u__266);
  Py_CLEAR(clear_module_state->__pyx_n_u__267);
  Py_CLEAR(clear_module_state->__pyx_n_u__268);
  Py_CLEAR(clear_module_state->__pyx_n_u__269);
  Py_CLEAR(clear_module_state->__pyx_n_u__27);
  Py_CLEAR(clear_module_state->__pyx_n_u__270);
  Py_CLEAR(clear_module_state->__pyx_n_u__271);
  Py_CLEAR(clear_module_state->__pyx_n_u__272);
  Py_CLEAR(clear_module_state->__pyx_n_u__273);
  Py_CLEAR(clear_module_state->__pyx_n_u__274);
  Py_CLEAR(clear_module_state->__pyx_kp_u__275);
  Py_CLEAR(clear_module_state->__pyx_n_u__276);
  Py_CLEAR(clear_module_state->__pyx_n_u__277);
  Py_CLEAR(clear_module_state->__pyx_kp_u__278);
  Py_CLEAR(clear_module_state->__pyx_kp_u__279);
  Py_CLEAR(clear_module_state->__pyx_n_u__28);
  Py_CLEAR(clear_module_state->__pyx_n_u__280);
  Py_CLEAR(clear_module_state->__pyx_kp_u__281);
  Py_CLEAR(clear_module_state->__pyx_n_u__282);
  Py_CLEAR(clear_module_state->__pyx_kp_u__283);
  Py_CLEAR(clear_module_state->__pyx_kp_u__284);
  Py_CLEAR(clear_module_state->__pyx_kp_u__285);
  Py_CLEAR(clear_module_state->__pyx_kp_u__286);
  Py_CLEAR(clear_module_state->__pyx_n_u__287);
  Py_CLEAR(clear_module_state->__pyx_kp_u__288);
  Py_CLEAR(clear_module_state->__pyx_n_u__289);
  Py_CLEAR(clear_module_state->__pyx_n_u__29);
  Py_CLEAR(clear_module_state->__pyx_kp_u__290);
  Py_CLEAR(clear_module_state->__pyx_kp_u__291);
  Py_CLEAR(clear_module_state->__pyx_kp_u__292);
  Py_CLEAR(clear_module_state->__pyx_kp_u__293);
  Py_CLEAR(clear_module_state->__pyx_n_u__294);
  Py_CLEAR(clear_module_state->__pyx_n_u__295);
  Py_CLEAR(clear_module_state->__pyx_n_u__296);
  Py_CLEAR(clear_module_state->__pyx_n_u__297);
  Py_CLEAR(clear_module_state->__pyx_n_u__298);
  Py_CLEAR(clear_module_state->__pyx_n_u__299);
  Py_CLEAR(clear_module_state->__pyx_n_u__3);
  Py_CLEAR(clear_module_state->__pyx_n_u__30);
  Py_CLEAR(clear_module_state->__pyx_n_u__300);
  Py_CLEAR(clear_module_state->__pyx_n_u__301);
  Py_CLEAR(clear_module_state->__pyx_n_u__302);
  Py_CLEAR(clear_module_state->__pyx_n_u__303);
  Py_CLEAR(clear_module_state->__pyx_n_u__304);
  Py_CLEAR(clear_module_state->__pyx_n_u__305);
  Py_CLEAR(clear_module_state->__pyx_n_u__306);
  Py_CLEAR(clear_module_state->__pyx_n_u__307);
  Py_CLEAR(clear_module_state->__pyx_kp_u__308);
  Py_CLEAR(clear_module_state->__pyx_n_u__309);
  Py_CLEAR(clear_module_state->__pyx_n_u__31);
  Py_CLEAR(clear_module_state->__pyx_n_u__310);
  Py_CLEAR(clear_module_state->__pyx_n_u__311);
  Py_CLEAR(clear_module_state->__pyx_n_u__312);
  Py_CLEAR(clear_module_state->__pyx_n_u__313);
  Py_CLEAR(clear_module_state->__pyx_n_u__314);
  Py_CLEAR(clear_module_state->__pyx_n_u__315);
  Py_CLEAR(clear_module_state->__pyx_n_u__316);
  Py_CLEAR(clear_module_state->__pyx_n_u__317);
  Py_CLEAR(clear_module_state->__pyx_n_u__318);
  Py_CLEAR(clear_module_state->__pyx_n_u__319);
  Py_CLEAR(clear_module_state->__pyx_n_u__32);
  Py_CLEAR(clear_module_state->__pyx_n_u__320);
  Py_CLEAR(clear_module_state->__pyx_n_u__321);
  Py_CLEAR(clear_module_state->__pyx_n_u__322);
  Py_CLEAR(clear_module_state->__pyx_n_u__323);
  Py_CLEAR(clear_module_state->__pyx_n_u__324);
  Py_CLEAR(clear_module_state->__pyx_n_u__325);
  Py_CLEAR(clear_module_state->__pyx_n_u__326);
  Py_CLEAR(clear_module_state->__pyx_n_u__327);
  Py_CLEAR(clear_module_state->__pyx_n_u__328);
  Py_CLEAR(clear_module_state->__pyx_n_u__329);
  Py_CLEAR(clear_module_state->__pyx_n_u__33);
  Py_CLEAR(clear_module_state->__pyx_n_u__330);
  Py_CLEAR(clear_module_state->__pyx_n_u__331);
  Py_CLEAR(clear_module_state->__pyx_n_u__332);
  Py_CLEAR(clear_module_state->__pyx_n_u__333);
  Py_CLEAR(clear_module_state->__pyx_n_u__334);
  Py_CLEAR(clear_module_state->__pyx_n_u__335);
  Py_CLEAR(clear_module_state->__pyx_n_u__336);
  Py_CLEAR(clear_module_state->__pyx_n_u__337);
  Py_CLEAR(clear_module_state->__pyx_n_u__338);
  Py_CLEAR(clear_module_state->__pyx_n_u__339);
  Py_CLEAR(clear_module_state->__pyx_n_u__34);
  Py_CLEAR(clear_module_state->__pyx_n_u__340);
  Py_CLEAR(clear_module_state->__pyx_n_u__341);
  Py_CLEAR(clear_module_state->__pyx_n_u__342);
  Py_CLEAR(clear_module_state->__pyx_n_u__343);
  Py_CLEAR(clear_module_state->__pyx_n_u__344);
  Py_CLEAR(clear_module_state->__pyx_n_u__345);
  Py_CLEAR(clear_module_state->__pyx_n_u__346);
  Py_CLEAR(clear_module_state->__pyx_n_u__347);
  Py_CLEAR(clear_module_state->__pyx_n_u__348);
  Py_CLEAR(clear_module_state->__pyx_n_u__349);
  Py_CLEAR(clear_module_state->__pyx_kp_u__35);
  Py_CLEAR(clear_module_state->__pyx_n_u__350);
  Py_CLEAR(clear_module_state->__pyx_n_s__351);
  Py_CLEAR(clear_module_state->__pyx_n_u__36);
  Py_CLEAR(clear_module_state->__pyx_n_u__37);
  Py_CLEAR(clear_module_state->__pyx_kp_u__38);
  Py_CLEAR(clear_module_state->__pyx_kp_u__39);
  Py_CLEAR(clear_module_state->__pyx_n_u__4);
  Py_CLEAR(clear_module_state->__pyx_kp_u__40);
  Py_CLEAR(clear_module_state->__pyx_kp_u__41);
  Py_CLEAR(clear_module_state->__pyx_n_u__42);
  Py_CLEAR(clear_module_state->__pyx_n_u__43);
  Py_CLEAR(clear_module_state->__pyx_n_u__44);
  Py_CLEAR(clear_module_state->__pyx_kp_u__45);
  Py_CLEAR(clear_module_state->__pyx_n_u__46);
  Py_CLEAR(clear_module_state->__pyx_n_u__47);
  Py_CLEAR(clear_module_state->__pyx_kp_u__48);
  Py_CLEAR(clear_module_state->__pyx_n_u__49);
  Py_CLEAR(clear_module_state->__pyx_kp_u__5);
  Py_CLEAR(clear_module_state->__pyx_n_u__50);
  Py_CLEAR(clear_module_state->__pyx_kp_u__51);
  Py_CLEAR(clear_module_state->__pyx_n_u__52);
  Py_CLEAR(clear_module_state->__pyx_n_u__53);
  Py_CLEAR(clear_module_state->__pyx_kp_u__54);
  Py_CLEAR(clear_module_state->__pyx_kp_u__55);
  Py_CLEAR(clear_module_state->__pyx_n_u__56);
  Py_CLEAR(clear_module_state->__pyx_n_u__57);
  Py_CLEAR(clear_module_state->__pyx_kp_u__58);
  Py_CLEAR(clear_module_state->__pyx_n_u__59);
  Py_CLEAR(clear_module_state->__pyx_n_u__6);
  Py_CLEAR(clear_module_state->__pyx_n_u__60);
  Py_CLEAR(clear_module_state->__pyx_kp_u__61);
  Py_CLEAR(clear_module_state->__pyx_n_u__62);
  Py_CLEAR(clear_module_state->__pyx_n_u__63);
  Py_CLEAR(clear_module_state->__pyx_kp_u__64);
  Py_CLEAR(clear_module_state->__pyx_n_u__65);
  Py_CLEAR(clear_module_state->__pyx_n_u__66);
  Py_CLEAR(clear_module_state->__pyx_n_u__67);
  Py_CLEAR(clear_module_state->__pyx_n_u__68);
  Py_CLEAR(clear_module_state->__pyx_n_u__69);
  Py_CLEAR(clear_module_state->__pyx_kp_u__7);
  Py_CLEAR(clear_module_state->__pyx_n_u__70);
  Py_CLEAR(clear_module_state->__pyx_n_u__71);
  Py_CLEAR(clear_module_state->__pyx_n_u__72);
  Py_CLEAR(clear_module_state->__pyx_n_u__73);
  Py_CLEAR(clear_module_state->__pyx_n_u__74);
  Py_CLEAR(clear_module_state->__pyx_n_u__75);
  Py_CLEAR(clear_module_state->__pyx_n_u__76);
  Py_CLEAR(clear_module_state->__pyx_n_u__77);
  Py_CLEAR(clear_module_state->__pyx_n_u__78);
  Py_CLEAR(clear_module_state->__pyx_n_u__79);
  Py_CLEAR(clear_module_state->__pyx_n_u__8);
  Py_CLEAR(clear_module_state->__pyx_n_u__80);
  Py_CLEAR(clear_module_state->__pyx_n_u__81);
  Py_CLEAR(clear_module_state->__pyx_kp_u__82);
  Py_CLEAR(clear_module_state->__pyx_n_u__83);
  Py_CLEAR(clear_module_state->__pyx_kp_u__84);
  Py_CLEAR(clear_module_state->__pyx_kp_u__85);
  Py_CLEAR(clear_module_state->__pyx_n_u__86);
  Py_CLEAR(clear_module_state->__pyx_n_u__87);
  Py_CLEAR(clear_module_state->__pyx_n_u__88);
  Py_CLEAR(clear_module_state->__pyx_n_u__89);
  Py_CLEAR(clear_module_state->__pyx_kp_u__9);
  Py_CLEAR(clear_module_state->__pyx_n_u__90);
  Py_CLEAR(clear_module_state->__pyx_n_u__91);
  Py_CLEAR(clear_module_state->__pyx_n_u__92);
  Py_CLEAR(clear_module_state->__pyx_n_u__93);
  Py_CLEAR(clear_module_state->__pyx_n_u__94);
  Py_CLEAR(clear_module_state->__pyx_n_u__95);
  Py_CLEAR(clear_module_state->__pyx_n_u__96);
  Py_CLEAR(clear_module_state->__pyx_n_u__97);
  Py_CLEAR(clear_module_state->__pyx_n_u__98);
  Py_CLEAR(clear_module_state->__pyx_n_u__99);
  Py_CLEAR(clear_module_state->__pyx_n_u_adb);
  Py_CLEAR(clear_module_state->__pyx_n_u_albumPics);
  Py_CLEAR(clear_module_state->__pyx_n_s_api_add_image_to_account);
  Py_CLEAR(clear_module_state->__pyx_n_s_api_get_account);
  Py_CLEAR(clear_module_state->__pyx_n_s_api_upload_images);
  Py_CLEAR(clear_module_state->__pyx_n_s_app_id);
  Py_CLEAR(clear_module_state->__pyx_n_u_attriName);
  Py_CLEAR(clear_module_state->__pyx_n_s_black_attr_value);
  Py_CLEAR(clear_module_state->__pyx_n_u_c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c);
  Py_CLEAR(clear_module_state->__pyx_n_s_chongzhi_chenghao);
  Py_CLEAR(clear_module_state->__pyx_n_s_cline_in_traceback);
  Py_CLEAR(clear_module_state->__pyx_n_s_create_product_req);
  Py_CLEAR(clear_module_state->__pyx_n_u_description);
  Py_CLEAR(clear_module_state->__pyx_n_u_gameAccountQufu);
  Py_CLEAR(clear_module_state->__pyx_n_u_gameCareinfoPhone);
  Py_CLEAR(clear_module_state->__pyx_n_u_gameCareinfoPhone2);
  Py_CLEAR(clear_module_state->__pyx_n_u_gameCareinfoTime);
  Py_CLEAR(clear_module_state->__pyx_n_u_gameCareinfoVx);
  Py_CLEAR(clear_module_state->__pyx_n_u_gameGoodsSaletype);
  Py_CLEAR(clear_module_state->__pyx_n_u_gameGoodsYijia);
  Py_CLEAR(clear_module_state->__pyx_kp_u_http_127_0_0_1_17001);
  Py_CLEAR(clear_module_state->__pyx_kp_u_https_api2_kkzhw_com);
  Py_CLEAR(clear_module_state->__pyx_kp_u_https_images2_kkzhw_com);
  Py_CLEAR(clear_module_state->__pyx_n_s_image_server_url);
  Py_CLEAR(clear_module_state->__pyx_n_s_lock_keywords);
  Py_CLEAR(clear_module_state->__pyx_n_s_main);
  Py_CLEAR(clear_module_state->__pyx_kp_u_mall_portal_openapi_record_add);
  Py_CLEAR(clear_module_state->__pyx_kp_u_mall_portal_openapi_record_get);
  Py_CLEAR(clear_module_state->__pyx_kp_u_mall_portal_openapi_record_uplo);
  Py_CLEAR(clear_module_state->__pyx_n_s_name);
  Py_CLEAR(clear_module_state->__pyx_n_u_nsha);
  Py_CLEAR(clear_module_state->__pyx_n_u_originalPrice);
  Py_CLEAR(clear_module_state->__pyx_n_u_pic);
  Py_CLEAR(clear_module_state->__pyx_n_u_price);
  Py_CLEAR(clear_module_state->__pyx_n_u_productAttributeCategoryId);
  Py_CLEAR(clear_module_state->__pyx_n_u_productAttributeId);
  Py_CLEAR(clear_module_state->__pyx_n_u_productAttributeValueList);
  Py_CLEAR(clear_module_state->__pyx_n_u_productCategoryId);
  Py_CLEAR(clear_module_state->__pyx_n_u_productCategoryName);
  Py_CLEAR(clear_module_state->__pyx_n_u_pushType);
  Py_CLEAR(clear_module_state->__pyx_n_u_qd561595395732389);
  Py_CLEAR(clear_module_state->__pyx_n_u_scrcpy);
  Py_CLEAR(clear_module_state->__pyx_n_u_searchType);
  Py_CLEAR(clear_module_state->__pyx_n_s_secret_key);
  Py_CLEAR(clear_module_state->__pyx_n_s_server_url);
  Py_CLEAR(clear_module_state->__pyx_kp_u_static_logs);
  Py_CLEAR(clear_module_state->__pyx_kp_u_storage_emulated_0_Download);
  Py_CLEAR(clear_module_state->__pyx_n_s_test);
  Py_CLEAR(clear_module_state->__pyx_n_u_type);
  Py_CLEAR(clear_module_state->__pyx_n_u_value);
  Py_CLEAR(clear_module_state->__pyx_n_s_zx_unlock_keywords);
  Py_CLEAR(clear_module_state->__pyx_float_0_5);
  Py_CLEAR(clear_module_state->__pyx_int_0);
  Py_CLEAR(clear_module_state->__pyx_int_1);
  Py_CLEAR(clear_module_state->__pyx_int_2);
  Py_CLEAR(clear_module_state->__pyx_int_3);
  Py_CLEAR(clear_module_state->__pyx_int_4);
  Py_CLEAR(clear_module_state->__pyx_int_5);
  Py_CLEAR(clear_module_state->__pyx_int_6);
  Py_CLEAR(clear_module_state->__pyx_int_9);
  Py_CLEAR(clear_module_state->__pyx_int_15);
  Py_CLEAR(clear_module_state->__pyx_int_30);
  Py_CLEAR(clear_module_state->__pyx_int_32);
  Py_CLEAR(clear_module_state->__pyx_int_62);
  Py_CLEAR(clear_module_state->__pyx_int_96);
  Py_CLEAR(clear_module_state->__pyx_int_97);
  Py_CLEAR(clear_module_state->__pyx_int_100);
  Py_CLEAR(clear_module_state->__pyx_int_105);
  Py_CLEAR(clear_module_state->__pyx_int_106);
  Py_CLEAR(clear_module_state->__pyx_int_107);
  Py_CLEAR(clear_module_state->__pyx_int_108);
  Py_CLEAR(clear_module_state->__pyx_int_113);
  Py_CLEAR(clear_module_state->__pyx_int_114);
  Py_CLEAR(clear_module_state->__pyx_int_115);
  Py_CLEAR(clear_module_state->__pyx_int_116);
  Py_CLEAR(clear_module_state->__pyx_int_118);
  Py_CLEAR(clear_module_state->__pyx_int_119);
  Py_CLEAR(clear_module_state->__pyx_int_166);
  Py_CLEAR(clear_module_state->__pyx_int_180);
  Py_CLEAR(clear_module_state->__pyx_int_200);
  Py_CLEAR(clear_module_state->__pyx_int_333);
  Py_CLEAR(clear_module_state->__pyx_int_334);
  Py_CLEAR(clear_module_state->__pyx_int_335);
  Py_CLEAR(clear_module_state->__pyx_int_336);
  Py_CLEAR(clear_module_state->__pyx_int_344);
  Py_CLEAR(clear_module_state->__pyx_int_371);
  Py_CLEAR(clear_module_state->__pyx_int_372);
  Py_CLEAR(clear_module_state->__pyx_int_373);
  Py_CLEAR(clear_module_state->__pyx_int_374);
  Py_CLEAR(clear_module_state->__pyx_int_545);
  Py_CLEAR(clear_module_state->__pyx_int_546);
  Py_CLEAR(clear_module_state->__pyx_int_547);
  Py_CLEAR(clear_module_state->__pyx_int_548);
  Py_CLEAR(clear_module_state->__pyx_int_549);
  Py_CLEAR(clear_module_state->__pyx_int_550);
  Py_CLEAR(clear_module_state->__pyx_int_552);
  Py_CLEAR(clear_module_state->__pyx_int_553);
  Py_CLEAR(clear_module_state->__pyx_int_554);
  Py_CLEAR(clear_module_state->__pyx_int_852);
  Py_CLEAR(clear_module_state->__pyx_int_872);
  Py_CLEAR(clear_module_state->__pyx_int_873);
  Py_CLEAR(clear_module_state->__pyx_int_899);
  Py_CLEAR(clear_module_state->__pyx_int_902);
  Py_CLEAR(clear_module_state->__pyx_int_1000);
  Py_CLEAR(clear_module_state->__pyx_int_1071);
  Py_CLEAR(clear_module_state->__pyx_int_1072);
  Py_CLEAR(clear_module_state->__pyx_int_1073);
  Py_CLEAR(clear_module_state->__pyx_int_1074);
  Py_CLEAR(clear_module_state->__pyx_int_1162);
  Py_CLEAR(clear_module_state->__pyx_int_1180);
  Py_CLEAR(clear_module_state->__pyx_int_1181);
  Py_CLEAR(clear_module_state->__pyx_int_1185);
  Py_CLEAR(clear_module_state->__pyx_int_1280);
  Py_CLEAR(clear_module_state->__pyx_int_3000);
  Py_CLEAR(clear_module_state->__pyx_int_10000);
  Py_CLEAR(clear_module_state->__pyx_int_50000);
  Py_CLEAR(clear_module_state->__pyx_int_100000);
  Py_CLEAR(clear_module_state->__pyx_int_200000);
  Py_CLEAR(clear_module_state->__pyx_int_500000);
  Py_CLEAR(clear_module_state->__pyx_int_1000000);
  Py_CLEAR(clear_module_state->__pyx_int_1500000);
  Py_CLEAR(clear_module_state->__pyx_tuple__131);
  Py_CLEAR(clear_module_state->__pyx_tuple__133);
  Py_CLEAR(clear_module_state->__pyx_tuple__135);
  Py_CLEAR(clear_module_state->__pyx_tuple__137);
  Py_CLEAR(clear_module_state->__pyx_tuple__139);
  Py_CLEAR(clear_module_state->__pyx_tuple__141);
  Py_CLEAR(clear_module_state->__pyx_tuple__143);
  Py_CLEAR(clear_module_state->__pyx_tuple__145);
  Py_CLEAR(clear_module_state->__pyx_tuple__147);
  Py_CLEAR(clear_module_state->__pyx_tuple__148);
  return 0;
}
#endif
/* #### Code section: module_state_traverse ### */
#if CYTHON_USE_MODULE_STATE
static int __pyx_m_traverse(PyObject *m, visitproc visit, void *arg) {
  __pyx_mstate *traverse_module_state = __pyx_mstate(m);
  if (!traverse_module_state) return 0;
  Py_VISIT(traverse_module_state->__pyx_d);
  Py_VISIT(traverse_module_state->__pyx_b);
  Py_VISIT(traverse_module_state->__pyx_cython_runtime);
  Py_VISIT(traverse_module_state->__pyx_empty_tuple);
  Py_VISIT(traverse_module_state->__pyx_empty_bytes);
  Py_VISIT(traverse_module_state->__pyx_empty_unicode);
  #ifdef __Pyx_CyFunction_USED
  Py_VISIT(traverse_module_state->__pyx_CyFunctionType);
  #endif
  #ifdef __Pyx_FusedFunction_USED
  Py_VISIT(traverse_module_state->__pyx_FusedFunctionType);
  #endif
  Py_VISIT(traverse_module_state->__pyx_n_u_);
  Py_VISIT(traverse_module_state->__pyx_kp_u_00_23);
  Py_VISIT(traverse_module_state->__pyx_kp_u_150);
  Py_VISIT(traverse_module_state->__pyx_kp_u_17);
  Py_VISIT(traverse_module_state->__pyx_kp_u_2200);
  Py_VISIT(traverse_module_state->__pyx_kp_u_5M);
  Py_VISIT(traverse_module_state->__pyx_kp_u_75);
  Py_VISIT(traverse_module_state->__pyx_kp_u_8x8coht211zh6l22dci2v7zgzav8zxs5);
  Py_VISIT(traverse_module_state->__pyx_n_s_ADB_PATH);
  Py_VISIT(traverse_module_state->__pyx_n_s_ANDROID_PATH);
  Py_VISIT(traverse_module_state->__pyx_n_s_AUTO_RETURN_LOGIN);
  Py_VISIT(traverse_module_state->__pyx_n_s_AUTO_UPLOAD_DATA);
  Py_VISIT(traverse_module_state->__pyx_n_s_C1_CODE_WAIT_TIME);
  Py_VISIT(traverse_module_state->__pyx_n_s_CAPTURE_MODE);
  Py_VISIT(traverse_module_state->__pyx_n_u_CD);
  Py_VISIT(traverse_module_state->__pyx_n_u_CD_2);
  Py_VISIT(traverse_module_state->__pyx_n_u_CD_3);
  Py_VISIT(traverse_module_state->__pyx_kp_u_CD_CD_CD);
  Py_VISIT(traverse_module_state->__pyx_n_s_DEBUG_MODEL);
  Py_VISIT(traverse_module_state->__pyx_n_s_DEVICE_MAX_LINE);
  Py_VISIT(traverse_module_state->__pyx_n_s_DEVICE_MAX_LOG_SIZE);
  Py_VISIT(traverse_module_state->__pyx_kp_u_D_nsha);
  Py_VISIT(traverse_module_state->__pyx_n_s_ERROR_TEXT);
  Py_VISIT(traverse_module_state->__pyx_n_s_FANYE_BEIBAO);
  Py_VISIT(traverse_module_state->__pyx_n_s_FANYE_WUQI);
  Py_VISIT(traverse_module_state->__pyx_n_s_FANYE_WUQI_HUANSHEN);
  Py_VISIT(traverse_module_state->__pyx_n_s_HOST);
  Py_VISIT(traverse_module_state->__pyx_n_s_LOG_PATH);
  Py_VISIT(traverse_module_state->__pyx_n_u_LTAI5t8j4SZCrnBiFoEzXm7J);
  Py_VISIT(traverse_module_state->__pyx_n_s_MAX_CLOTHES_COUNT);
  Py_VISIT(traverse_module_state->__pyx_n_s_MAX_FASHIS_COUNT);
  Py_VISIT(traverse_module_state->__pyx_n_s_MAX_FPS);
  Py_VISIT(traverse_module_state->__pyx_n_s_MAX_ZUOQI);
  Py_VISIT(traverse_module_state->__pyx_n_s_NEIGONG_CLICK_COUNT);
  Py_VISIT(traverse_module_state->__pyx_n_s_NEIGONG_ZHUANGBEI_COUNT);
  Py_VISIT(traverse_module_state->__pyx_n_s_OSS_ACCESS_KEY_ID);
  Py_VISIT(traverse_module_state->__pyx_n_s_OSS_ACCESS_KEY_SECRET);
  Py_VISIT(traverse_module_state->__pyx_n_s_PINGFEN_XIAXIAN);
  Py_VISIT(traverse_module_state->__pyx_n_s_PROJECT_NAME);
  Py_VISIT(traverse_module_state->__pyx_n_s_PROJECT_PATH);
  Py_VISIT(traverse_module_state->__pyx_n_s_SCRCPY_PATH);
  Py_VISIT(traverse_module_state->__pyx_n_s_SERVER_API_TOKEN);
  Py_VISIT(traverse_module_state->__pyx_n_s_SKIP_ATTRI_NAME);
  Py_VISIT(traverse_module_state->__pyx_n_s_TAP_DELAY);
  Py_VISIT(traverse_module_state->__pyx_n_s_TSS_VALUE);
  Py_VISIT(traverse_module_state->__pyx_n_s_VIDEO_BIT_RATE);
  Py_VISIT(traverse_module_state->__pyx_n_s_WINDOW_MAX_SIZE);
  Py_VISIT(traverse_module_state->__pyx_n_u_******************************);
  Py_VISIT(traverse_module_state->__pyx_n_u__10);
  Py_VISIT(traverse_module_state->__pyx_n_u__100);
  Py_VISIT(traverse_module_state->__pyx_n_u__101);
  Py_VISIT(traverse_module_state->__pyx_n_u__102);
  Py_VISIT(traverse_module_state->__pyx_n_u__103);
  Py_VISIT(traverse_module_state->__pyx_n_u__104);
  Py_VISIT(traverse_module_state->__pyx_n_u__105);
  Py_VISIT(traverse_module_state->__pyx_n_u__106);
  Py_VISIT(traverse_module_state->__pyx_n_u__107);
  Py_VISIT(traverse_module_state->__pyx_n_u__108);
  Py_VISIT(traverse_module_state->__pyx_n_u__109);
  Py_VISIT(traverse_module_state->__pyx_n_u__11);
  Py_VISIT(traverse_module_state->__pyx_n_u__110);
  Py_VISIT(traverse_module_state->__pyx_n_u__111);
  Py_VISIT(traverse_module_state->__pyx_n_u__112);
  Py_VISIT(traverse_module_state->__pyx_n_u__113);
  Py_VISIT(traverse_module_state->__pyx_n_u__114);
  Py_VISIT(traverse_module_state->__pyx_n_u__115);
  Py_VISIT(traverse_module_state->__pyx_n_u__116);
  Py_VISIT(traverse_module_state->__pyx_n_u__117);
  Py_VISIT(traverse_module_state->__pyx_n_u__118);
  Py_VISIT(traverse_module_state->__pyx_n_u__119);
  Py_VISIT(traverse_module_state->__pyx_n_u__12);
  Py_VISIT(traverse_module_state->__pyx_n_u__120);
  Py_VISIT(traverse_module_state->__pyx_n_u__121);
  Py_VISIT(traverse_module_state->__pyx_n_u__122);
  Py_VISIT(traverse_module_state->__pyx_n_u__123);
  Py_VISIT(traverse_module_state->__pyx_n_u__124);
  Py_VISIT(traverse_module_state->__pyx_n_u__125);
  Py_VISIT(traverse_module_state->__pyx_n_u__126);
  Py_VISIT(traverse_module_state->__pyx_n_u__127);
  Py_VISIT(traverse_module_state->__pyx_n_u__128);
  Py_VISIT(traverse_module_state->__pyx_n_u__129);
  Py_VISIT(traverse_module_state->__pyx_n_u__13);
  Py_VISIT(traverse_module_state->__pyx_n_u__130);
  Py_VISIT(traverse_module_state->__pyx_n_u__132);
  Py_VISIT(traverse_module_state->__pyx_n_u__134);
  Py_VISIT(traverse_module_state->__pyx_n_u__136);
  Py_VISIT(traverse_module_state->__pyx_n_u__138);
  Py_VISIT(traverse_module_state->__pyx_n_u__14);
  Py_VISIT(traverse_module_state->__pyx_n_u__140);
  Py_VISIT(traverse_module_state->__pyx_n_u__142);
  Py_VISIT(traverse_module_state->__pyx_n_u__144);
  Py_VISIT(traverse_module_state->__pyx_n_u__146);
  Py_VISIT(traverse_module_state->__pyx_n_u__149);
  Py_VISIT(traverse_module_state->__pyx_n_u__15);
  Py_VISIT(traverse_module_state->__pyx_n_u__150);
  Py_VISIT(traverse_module_state->__pyx_n_u__151);
  Py_VISIT(traverse_module_state->__pyx_n_u__152);
  Py_VISIT(traverse_module_state->__pyx_n_u__153);
  Py_VISIT(traverse_module_state->__pyx_n_u__154);
  Py_VISIT(traverse_module_state->__pyx_n_u__155);
  Py_VISIT(traverse_module_state->__pyx_n_u__156);
  Py_VISIT(traverse_module_state->__pyx_n_u__157);
  Py_VISIT(traverse_module_state->__pyx_n_u__158);
  Py_VISIT(traverse_module_state->__pyx_n_u__159);
  Py_VISIT(traverse_module_state->__pyx_n_u__16);
  Py_VISIT(traverse_module_state->__pyx_n_u__160);
  Py_VISIT(traverse_module_state->__pyx_n_u__161);
  Py_VISIT(traverse_module_state->__pyx_n_u__162);
  Py_VISIT(traverse_module_state->__pyx_n_u__163);
  Py_VISIT(traverse_module_state->__pyx_n_u__164);
  Py_VISIT(traverse_module_state->__pyx_n_u__165);
  Py_VISIT(traverse_module_state->__pyx_n_u__166);
  Py_VISIT(traverse_module_state->__pyx_kp_u__167);
  Py_VISIT(traverse_module_state->__pyx_n_u__168);
  Py_VISIT(traverse_module_state->__pyx_n_u__169);
  Py_VISIT(traverse_module_state->__pyx_n_u__17);
  Py_VISIT(traverse_module_state->__pyx_n_u__170);
  Py_VISIT(traverse_module_state->__pyx_n_u__171);
  Py_VISIT(traverse_module_state->__pyx_n_u__172);
  Py_VISIT(traverse_module_state->__pyx_n_u__173);
  Py_VISIT(traverse_module_state->__pyx_n_u__174);
  Py_VISIT(traverse_module_state->__pyx_n_u__175);
  Py_VISIT(traverse_module_state->__pyx_n_u__176);
  Py_VISIT(traverse_module_state->__pyx_n_u__177);
  Py_VISIT(traverse_module_state->__pyx_n_u__178);
  Py_VISIT(traverse_module_state->__pyx_n_u__179);
  Py_VISIT(traverse_module_state->__pyx_n_u__18);
  Py_VISIT(traverse_module_state->__pyx_n_u__180);
  Py_VISIT(traverse_module_state->__pyx_n_u__181);
  Py_VISIT(traverse_module_state->__pyx_n_u__182);
  Py_VISIT(traverse_module_state->__pyx_n_u__183);
  Py_VISIT(traverse_module_state->__pyx_n_u__184);
  Py_VISIT(traverse_module_state->__pyx_n_u__185);
  Py_VISIT(traverse_module_state->__pyx_n_u__186);
  Py_VISIT(traverse_module_state->__pyx_n_u__187);
  Py_VISIT(traverse_module_state->__pyx_n_u__188);
  Py_VISIT(traverse_module_state->__pyx_n_u__189);
  Py_VISIT(traverse_module_state->__pyx_n_u__19);
  Py_VISIT(traverse_module_state->__pyx_n_u__190);
  Py_VISIT(traverse_module_state->__pyx_n_u__191);
  Py_VISIT(traverse_module_state->__pyx_n_u__192);
  Py_VISIT(traverse_module_state->__pyx_n_u__193);
  Py_VISIT(traverse_module_state->__pyx_n_u__194);
  Py_VISIT(traverse_module_state->__pyx_n_u__195);
  Py_VISIT(traverse_module_state->__pyx_n_u__196);
  Py_VISIT(traverse_module_state->__pyx_n_u__197);
  Py_VISIT(traverse_module_state->__pyx_n_u__198);
  Py_VISIT(traverse_module_state->__pyx_n_u__199);
  Py_VISIT(traverse_module_state->__pyx_n_u__2);
  Py_VISIT(traverse_module_state->__pyx_n_u__20);
  Py_VISIT(traverse_module_state->__pyx_n_u__200);
  Py_VISIT(traverse_module_state->__pyx_n_u__201);
  Py_VISIT(traverse_module_state->__pyx_n_u__202);
  Py_VISIT(traverse_module_state->__pyx_n_u__203);
  Py_VISIT(traverse_module_state->__pyx_n_u__204);
  Py_VISIT(traverse_module_state->__pyx_n_u__205);
  Py_VISIT(traverse_module_state->__pyx_n_u__206);
  Py_VISIT(traverse_module_state->__pyx_n_u__207);
  Py_VISIT(traverse_module_state->__pyx_n_u__208);
  Py_VISIT(traverse_module_state->__pyx_n_u__209);
  Py_VISIT(traverse_module_state->__pyx_n_u__21);
  Py_VISIT(traverse_module_state->__pyx_n_u__210);
  Py_VISIT(traverse_module_state->__pyx_n_u__211);
  Py_VISIT(traverse_module_state->__pyx_n_u__212);
  Py_VISIT(traverse_module_state->__pyx_n_u__213);
  Py_VISIT(traverse_module_state->__pyx_n_u__214);
  Py_VISIT(traverse_module_state->__pyx_n_u__215);
  Py_VISIT(traverse_module_state->__pyx_n_u__216);
  Py_VISIT(traverse_module_state->__pyx_n_u__217);
  Py_VISIT(traverse_module_state->__pyx_n_u__218);
  Py_VISIT(traverse_module_state->__pyx_n_u__219);
  Py_VISIT(traverse_module_state->__pyx_n_u__22);
  Py_VISIT(traverse_module_state->__pyx_n_u__220);
  Py_VISIT(traverse_module_state->__pyx_n_u__221);
  Py_VISIT(traverse_module_state->__pyx_n_u__222);
  Py_VISIT(traverse_module_state->__pyx_n_u__223);
  Py_VISIT(traverse_module_state->__pyx_n_u__224);
  Py_VISIT(traverse_module_state->__pyx_n_u__225);
  Py_VISIT(traverse_module_state->__pyx_n_u__226);
  Py_VISIT(traverse_module_state->__pyx_n_u__227);
  Py_VISIT(traverse_module_state->__pyx_n_u__228);
  Py_VISIT(traverse_module_state->__pyx_n_u__229);
  Py_VISIT(traverse_module_state->__pyx_n_u__23);
  Py_VISIT(traverse_module_state->__pyx_n_u__230);
  Py_VISIT(traverse_module_state->__pyx_n_u__231);
  Py_VISIT(traverse_module_state->__pyx_n_u__232);
  Py_VISIT(traverse_module_state->__pyx_n_u__233);
  Py_VISIT(traverse_module_state->__pyx_n_u__234);
  Py_VISIT(traverse_module_state->__pyx_n_u__235);
  Py_VISIT(traverse_module_state->__pyx_n_u__236);
  Py_VISIT(traverse_module_state->__pyx_n_u__237);
  Py_VISIT(traverse_module_state->__pyx_n_u__238);
  Py_VISIT(traverse_module_state->__pyx_n_u__239);
  Py_VISIT(traverse_module_state->__pyx_n_u__24);
  Py_VISIT(traverse_module_state->__pyx_n_u__240);
  Py_VISIT(traverse_module_state->__pyx_n_u__241);
  Py_VISIT(traverse_module_state->__pyx_n_u__242);
  Py_VISIT(traverse_module_state->__pyx_n_u__243);
  Py_VISIT(traverse_module_state->__pyx_n_u__244);
  Py_VISIT(traverse_module_state->__pyx_n_u__245);
  Py_VISIT(traverse_module_state->__pyx_n_u__246);
  Py_VISIT(traverse_module_state->__pyx_n_u__247);
  Py_VISIT(traverse_module_state->__pyx_n_u__248);
  Py_VISIT(traverse_module_state->__pyx_n_u__249);
  Py_VISIT(traverse_module_state->__pyx_n_u__25);
  Py_VISIT(traverse_module_state->__pyx_n_u__250);
  Py_VISIT(traverse_module_state->__pyx_n_u__251);
  Py_VISIT(traverse_module_state->__pyx_n_u__252);
  Py_VISIT(traverse_module_state->__pyx_n_u__253);
  Py_VISIT(traverse_module_state->__pyx_n_u__254);
  Py_VISIT(traverse_module_state->__pyx_n_u__255);
  Py_VISIT(traverse_module_state->__pyx_n_u__256);
  Py_VISIT(traverse_module_state->__pyx_n_u__257);
  Py_VISIT(traverse_module_state->__pyx_n_u__258);
  Py_VISIT(traverse_module_state->__pyx_n_u__259);
  Py_VISIT(traverse_module_state->__pyx_n_u__26);
  Py_VISIT(traverse_module_state->__pyx_n_u__260);
  Py_VISIT(traverse_module_state->__pyx_n_u__261);
  Py_VISIT(traverse_module_state->__pyx_n_u__262);
  Py_VISIT(traverse_module_state->__pyx_n_u__263);
  Py_VISIT(traverse_module_state->__pyx_n_u__264);
  Py_VISIT(traverse_module_state->__pyx_n_u__265);
  Py_VISIT(traverse_module_state->__pyx_n_u__266);
  Py_VISIT(traverse_module_state->__pyx_n_u__267);
  Py_VISIT(traverse_module_state->__pyx_n_u__268);
  Py_VISIT(traverse_module_state->__pyx_n_u__269);
  Py_VISIT(traverse_module_state->__pyx_n_u__27);
  Py_VISIT(traverse_module_state->__pyx_n_u__270);
  Py_VISIT(traverse_module_state->__pyx_n_u__271);
  Py_VISIT(traverse_module_state->__pyx_n_u__272);
  Py_VISIT(traverse_module_state->__pyx_n_u__273);
  Py_VISIT(traverse_module_state->__pyx_n_u__274);
  Py_VISIT(traverse_module_state->__pyx_kp_u__275);
  Py_VISIT(traverse_module_state->__pyx_n_u__276);
  Py_VISIT(traverse_module_state->__pyx_n_u__277);
  Py_VISIT(traverse_module_state->__pyx_kp_u__278);
  Py_VISIT(traverse_module_state->__pyx_kp_u__279);
  Py_VISIT(traverse_module_state->__pyx_n_u__28);
  Py_VISIT(traverse_module_state->__pyx_n_u__280);
  Py_VISIT(traverse_module_state->__pyx_kp_u__281);
  Py_VISIT(traverse_module_state->__pyx_n_u__282);
  Py_VISIT(traverse_module_state->__pyx_kp_u__283);
  Py_VISIT(traverse_module_state->__pyx_kp_u__284);
  Py_VISIT(traverse_module_state->__pyx_kp_u__285);
  Py_VISIT(traverse_module_state->__pyx_kp_u__286);
  Py_VISIT(traverse_module_state->__pyx_n_u__287);
  Py_VISIT(traverse_module_state->__pyx_kp_u__288);
  Py_VISIT(traverse_module_state->__pyx_n_u__289);
  Py_VISIT(traverse_module_state->__pyx_n_u__29);
  Py_VISIT(traverse_module_state->__pyx_kp_u__290);
  Py_VISIT(traverse_module_state->__pyx_kp_u__291);
  Py_VISIT(traverse_module_state->__pyx_kp_u__292);
  Py_VISIT(traverse_module_state->__pyx_kp_u__293);
  Py_VISIT(traverse_module_state->__pyx_n_u__294);
  Py_VISIT(traverse_module_state->__pyx_n_u__295);
  Py_VISIT(traverse_module_state->__pyx_n_u__296);
  Py_VISIT(traverse_module_state->__pyx_n_u__297);
  Py_VISIT(traverse_module_state->__pyx_n_u__298);
  Py_VISIT(traverse_module_state->__pyx_n_u__299);
  Py_VISIT(traverse_module_state->__pyx_n_u__3);
  Py_VISIT(traverse_module_state->__pyx_n_u__30);
  Py_VISIT(traverse_module_state->__pyx_n_u__300);
  Py_VISIT(traverse_module_state->__pyx_n_u__301);
  Py_VISIT(traverse_module_state->__pyx_n_u__302);
  Py_VISIT(traverse_module_state->__pyx_n_u__303);
  Py_VISIT(traverse_module_state->__pyx_n_u__304);
  Py_VISIT(traverse_module_state->__pyx_n_u__305);
  Py_VISIT(traverse_module_state->__pyx_n_u__306);
  Py_VISIT(traverse_module_state->__pyx_n_u__307);
  Py_VISIT(traverse_module_state->__pyx_kp_u__308);
  Py_VISIT(traverse_module_state->__pyx_n_u__309);
  Py_VISIT(traverse_module_state->__pyx_n_u__31);
  Py_VISIT(traverse_module_state->__pyx_n_u__310);
  Py_VISIT(traverse_module_state->__pyx_n_u__311);
  Py_VISIT(traverse_module_state->__pyx_n_u__312);
  Py_VISIT(traverse_module_state->__pyx_n_u__313);
  Py_VISIT(traverse_module_state->__pyx_n_u__314);
  Py_VISIT(traverse_module_state->__pyx_n_u__315);
  Py_VISIT(traverse_module_state->__pyx_n_u__316);
  Py_VISIT(traverse_module_state->__pyx_n_u__317);
  Py_VISIT(traverse_module_state->__pyx_n_u__318);
  Py_VISIT(traverse_module_state->__pyx_n_u__319);
  Py_VISIT(traverse_module_state->__pyx_n_u__32);
  Py_VISIT(traverse_module_state->__pyx_n_u__320);
  Py_VISIT(traverse_module_state->__pyx_n_u__321);
  Py_VISIT(traverse_module_state->__pyx_n_u__322);
  Py_VISIT(traverse_module_state->__pyx_n_u__323);
  Py_VISIT(traverse_module_state->__pyx_n_u__324);
  Py_VISIT(traverse_module_state->__pyx_n_u__325);
  Py_VISIT(traverse_module_state->__pyx_n_u__326);
  Py_VISIT(traverse_module_state->__pyx_n_u__327);
  Py_VISIT(traverse_module_state->__pyx_n_u__328);
  Py_VISIT(traverse_module_state->__pyx_n_u__329);
  Py_VISIT(traverse_module_state->__pyx_n_u__33);
  Py_VISIT(traverse_module_state->__pyx_n_u__330);
  Py_VISIT(traverse_module_state->__pyx_n_u__331);
  Py_VISIT(traverse_module_state->__pyx_n_u__332);
  Py_VISIT(traverse_module_state->__pyx_n_u__333);
  Py_VISIT(traverse_module_state->__pyx_n_u__334);
  Py_VISIT(traverse_module_state->__pyx_n_u__335);
  Py_VISIT(traverse_module_state->__pyx_n_u__336);
  Py_VISIT(traverse_module_state->__pyx_n_u__337);
  Py_VISIT(traverse_module_state->__pyx_n_u__338);
  Py_VISIT(traverse_module_state->__pyx_n_u__339);
  Py_VISIT(traverse_module_state->__pyx_n_u__34);
  Py_VISIT(traverse_module_state->__pyx_n_u__340);
  Py_VISIT(traverse_module_state->__pyx_n_u__341);
  Py_VISIT(traverse_module_state->__pyx_n_u__342);
  Py_VISIT(traverse_module_state->__pyx_n_u__343);
  Py_VISIT(traverse_module_state->__pyx_n_u__344);
  Py_VISIT(traverse_module_state->__pyx_n_u__345);
  Py_VISIT(traverse_module_state->__pyx_n_u__346);
  Py_VISIT(traverse_module_state->__pyx_n_u__347);
  Py_VISIT(traverse_module_state->__pyx_n_u__348);
  Py_VISIT(traverse_module_state->__pyx_n_u__349);
  Py_VISIT(traverse_module_state->__pyx_kp_u__35);
  Py_VISIT(traverse_module_state->__pyx_n_u__350);
  Py_VISIT(traverse_module_state->__pyx_n_s__351);
  Py_VISIT(traverse_module_state->__pyx_n_u__36);
  Py_VISIT(traverse_module_state->__pyx_n_u__37);
  Py_VISIT(traverse_module_state->__pyx_kp_u__38);
  Py_VISIT(traverse_module_state->__pyx_kp_u__39);
  Py_VISIT(traverse_module_state->__pyx_n_u__4);
  Py_VISIT(traverse_module_state->__pyx_kp_u__40);
  Py_VISIT(traverse_module_state->__pyx_kp_u__41);
  Py_VISIT(traverse_module_state->__pyx_n_u__42);
  Py_VISIT(traverse_module_state->__pyx_n_u__43);
  Py_VISIT(traverse_module_state->__pyx_n_u__44);
  Py_VISIT(traverse_module_state->__pyx_kp_u__45);
  Py_VISIT(traverse_module_state->__pyx_n_u__46);
  Py_VISIT(traverse_module_state->__pyx_n_u__47);
  Py_VISIT(traverse_module_state->__pyx_kp_u__48);
  Py_VISIT(traverse_module_state->__pyx_n_u__49);
  Py_VISIT(traverse_module_state->__pyx_kp_u__5);
  Py_VISIT(traverse_module_state->__pyx_n_u__50);
  Py_VISIT(traverse_module_state->__pyx_kp_u__51);
  Py_VISIT(traverse_module_state->__pyx_n_u__52);
  Py_VISIT(traverse_module_state->__pyx_n_u__53);
  Py_VISIT(traverse_module_state->__pyx_kp_u__54);
  Py_VISIT(traverse_module_state->__pyx_kp_u__55);
  Py_VISIT(traverse_module_state->__pyx_n_u__56);
  Py_VISIT(traverse_module_state->__pyx_n_u__57);
  Py_VISIT(traverse_module_state->__pyx_kp_u__58);
  Py_VISIT(traverse_module_state->__pyx_n_u__59);
  Py_VISIT(traverse_module_state->__pyx_n_u__6);
  Py_VISIT(traverse_module_state->__pyx_n_u__60);
  Py_VISIT(traverse_module_state->__pyx_kp_u__61);
  Py_VISIT(traverse_module_state->__pyx_n_u__62);
  Py_VISIT(traverse_module_state->__pyx_n_u__63);
  Py_VISIT(traverse_module_state->__pyx_kp_u__64);
  Py_VISIT(traverse_module_state->__pyx_n_u__65);
  Py_VISIT(traverse_module_state->__pyx_n_u__66);
  Py_VISIT(traverse_module_state->__pyx_n_u__67);
  Py_VISIT(traverse_module_state->__pyx_n_u__68);
  Py_VISIT(traverse_module_state->__pyx_n_u__69);
  Py_VISIT(traverse_module_state->__pyx_kp_u__7);
  Py_VISIT(traverse_module_state->__pyx_n_u__70);
  Py_VISIT(traverse_module_state->__pyx_n_u__71);
  Py_VISIT(traverse_module_state->__pyx_n_u__72);
  Py_VISIT(traverse_module_state->__pyx_n_u__73);
  Py_VISIT(traverse_module_state->__pyx_n_u__74);
  Py_VISIT(traverse_module_state->__pyx_n_u__75);
  Py_VISIT(traverse_module_state->__pyx_n_u__76);
  Py_VISIT(traverse_module_state->__pyx_n_u__77);
  Py_VISIT(traverse_module_state->__pyx_n_u__78);
  Py_VISIT(traverse_module_state->__pyx_n_u__79);
  Py_VISIT(traverse_module_state->__pyx_n_u__8);
  Py_VISIT(traverse_module_state->__pyx_n_u__80);
  Py_VISIT(traverse_module_state->__pyx_n_u__81);
  Py_VISIT(traverse_module_state->__pyx_kp_u__82);
  Py_VISIT(traverse_module_state->__pyx_n_u__83);
  Py_VISIT(traverse_module_state->__pyx_kp_u__84);
  Py_VISIT(traverse_module_state->__pyx_kp_u__85);
  Py_VISIT(traverse_module_state->__pyx_n_u__86);
  Py_VISIT(traverse_module_state->__pyx_n_u__87);
  Py_VISIT(traverse_module_state->__pyx_n_u__88);
  Py_VISIT(traverse_module_state->__pyx_n_u__89);
  Py_VISIT(traverse_module_state->__pyx_kp_u__9);
  Py_VISIT(traverse_module_state->__pyx_n_u__90);
  Py_VISIT(traverse_module_state->__pyx_n_u__91);
  Py_VISIT(traverse_module_state->__pyx_n_u__92);
  Py_VISIT(traverse_module_state->__pyx_n_u__93);
  Py_VISIT(traverse_module_state->__pyx_n_u__94);
  Py_VISIT(traverse_module_state->__pyx_n_u__95);
  Py_VISIT(traverse_module_state->__pyx_n_u__96);
  Py_VISIT(traverse_module_state->__pyx_n_u__97);
  Py_VISIT(traverse_module_state->__pyx_n_u__98);
  Py_VISIT(traverse_module_state->__pyx_n_u__99);
  Py_VISIT(traverse_module_state->__pyx_n_u_adb);
  Py_VISIT(traverse_module_state->__pyx_n_u_albumPics);
  Py_VISIT(traverse_module_state->__pyx_n_s_api_add_image_to_account);
  Py_VISIT(traverse_module_state->__pyx_n_s_api_get_account);
  Py_VISIT(traverse_module_state->__pyx_n_s_api_upload_images);
  Py_VISIT(traverse_module_state->__pyx_n_s_app_id);
  Py_VISIT(traverse_module_state->__pyx_n_u_attriName);
  Py_VISIT(traverse_module_state->__pyx_n_s_black_attr_value);
  Py_VISIT(traverse_module_state->__pyx_n_u_c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c);
  Py_VISIT(traverse_module_state->__pyx_n_s_chongzhi_chenghao);
  Py_VISIT(traverse_module_state->__pyx_n_s_cline_in_traceback);
  Py_VISIT(traverse_module_state->__pyx_n_s_create_product_req);
  Py_VISIT(traverse_module_state->__pyx_n_u_description);
  Py_VISIT(traverse_module_state->__pyx_n_u_gameAccountQufu);
  Py_VISIT(traverse_module_state->__pyx_n_u_gameCareinfoPhone);
  Py_VISIT(traverse_module_state->__pyx_n_u_gameCareinfoPhone2);
  Py_VISIT(traverse_module_state->__pyx_n_u_gameCareinfoTime);
  Py_VISIT(traverse_module_state->__pyx_n_u_gameCareinfoVx);
  Py_VISIT(traverse_module_state->__pyx_n_u_gameGoodsSaletype);
  Py_VISIT(traverse_module_state->__pyx_n_u_gameGoodsYijia);
  Py_VISIT(traverse_module_state->__pyx_kp_u_http_127_0_0_1_17001);
  Py_VISIT(traverse_module_state->__pyx_kp_u_https_api2_kkzhw_com);
  Py_VISIT(traverse_module_state->__pyx_kp_u_https_images2_kkzhw_com);
  Py_VISIT(traverse_module_state->__pyx_n_s_image_server_url);
  Py_VISIT(traverse_module_state->__pyx_n_s_lock_keywords);
  Py_VISIT(traverse_module_state->__pyx_n_s_main);
  Py_VISIT(traverse_module_state->__pyx_kp_u_mall_portal_openapi_record_add);
  Py_VISIT(traverse_module_state->__pyx_kp_u_mall_portal_openapi_record_get);
  Py_VISIT(traverse_module_state->__pyx_kp_u_mall_portal_openapi_record_uplo);
  Py_VISIT(traverse_module_state->__pyx_n_s_name);
  Py_VISIT(traverse_module_state->__pyx_n_u_nsha);
  Py_VISIT(traverse_module_state->__pyx_n_u_originalPrice);
  Py_VISIT(traverse_module_state->__pyx_n_u_pic);
  Py_VISIT(traverse_module_state->__pyx_n_u_price);
  Py_VISIT(traverse_module_state->__pyx_n_u_productAttributeCategoryId);
  Py_VISIT(traverse_module_state->__pyx_n_u_productAttributeId);
  Py_VISIT(traverse_module_state->__pyx_n_u_productAttributeValueList);
  Py_VISIT(traverse_module_state->__pyx_n_u_productCategoryId);
  Py_VISIT(traverse_module_state->__pyx_n_u_productCategoryName);
  Py_VISIT(traverse_module_state->__pyx_n_u_pushType);
  Py_VISIT(traverse_module_state->__pyx_n_u_qd561595395732389);
  Py_VISIT(traverse_module_state->__pyx_n_u_scrcpy);
  Py_VISIT(traverse_module_state->__pyx_n_u_searchType);
  Py_VISIT(traverse_module_state->__pyx_n_s_secret_key);
  Py_VISIT(traverse_module_state->__pyx_n_s_server_url);
  Py_VISIT(traverse_module_state->__pyx_kp_u_static_logs);
  Py_VISIT(traverse_module_state->__pyx_kp_u_storage_emulated_0_Download);
  Py_VISIT(traverse_module_state->__pyx_n_s_test);
  Py_VISIT(traverse_module_state->__pyx_n_u_type);
  Py_VISIT(traverse_module_state->__pyx_n_u_value);
  Py_VISIT(traverse_module_state->__pyx_n_s_zx_unlock_keywords);
  Py_VISIT(traverse_module_state->__pyx_float_0_5);
  Py_VISIT(traverse_module_state->__pyx_int_0);
  Py_VISIT(traverse_module_state->__pyx_int_1);
  Py_VISIT(traverse_module_state->__pyx_int_2);
  Py_VISIT(traverse_module_state->__pyx_int_3);
  Py_VISIT(traverse_module_state->__pyx_int_4);
  Py_VISIT(traverse_module_state->__pyx_int_5);
  Py_VISIT(traverse_module_state->__pyx_int_6);
  Py_VISIT(traverse_module_state->__pyx_int_9);
  Py_VISIT(traverse_module_state->__pyx_int_15);
  Py_VISIT(traverse_module_state->__pyx_int_30);
  Py_VISIT(traverse_module_state->__pyx_int_32);
  Py_VISIT(traverse_module_state->__pyx_int_62);
  Py_VISIT(traverse_module_state->__pyx_int_96);
  Py_VISIT(traverse_module_state->__pyx_int_97);
  Py_VISIT(traverse_module_state->__pyx_int_100);
  Py_VISIT(traverse_module_state->__pyx_int_105);
  Py_VISIT(traverse_module_state->__pyx_int_106);
  Py_VISIT(traverse_module_state->__pyx_int_107);
  Py_VISIT(traverse_module_state->__pyx_int_108);
  Py_VISIT(traverse_module_state->__pyx_int_113);
  Py_VISIT(traverse_module_state->__pyx_int_114);
  Py_VISIT(traverse_module_state->__pyx_int_115);
  Py_VISIT(traverse_module_state->__pyx_int_116);
  Py_VISIT(traverse_module_state->__pyx_int_118);
  Py_VISIT(traverse_module_state->__pyx_int_119);
  Py_VISIT(traverse_module_state->__pyx_int_166);
  Py_VISIT(traverse_module_state->__pyx_int_180);
  Py_VISIT(traverse_module_state->__pyx_int_200);
  Py_VISIT(traverse_module_state->__pyx_int_333);
  Py_VISIT(traverse_module_state->__pyx_int_334);
  Py_VISIT(traverse_module_state->__pyx_int_335);
  Py_VISIT(traverse_module_state->__pyx_int_336);
  Py_VISIT(traverse_module_state->__pyx_int_344);
  Py_VISIT(traverse_module_state->__pyx_int_371);
  Py_VISIT(traverse_module_state->__pyx_int_372);
  Py_VISIT(traverse_module_state->__pyx_int_373);
  Py_VISIT(traverse_module_state->__pyx_int_374);
  Py_VISIT(traverse_module_state->__pyx_int_545);
  Py_VISIT(traverse_module_state->__pyx_int_546);
  Py_VISIT(traverse_module_state->__pyx_int_547);
  Py_VISIT(traverse_module_state->__pyx_int_548);
  Py_VISIT(traverse_module_state->__pyx_int_549);
  Py_VISIT(traverse_module_state->__pyx_int_550);
  Py_VISIT(traverse_module_state->__pyx_int_552);
  Py_VISIT(traverse_module_state->__pyx_int_553);
  Py_VISIT(traverse_module_state->__pyx_int_554);
  Py_VISIT(traverse_module_state->__pyx_int_852);
  Py_VISIT(traverse_module_state->__pyx_int_872);
  Py_VISIT(traverse_module_state->__pyx_int_873);
  Py_VISIT(traverse_module_state->__pyx_int_899);
  Py_VISIT(traverse_module_state->__pyx_int_902);
  Py_VISIT(traverse_module_state->__pyx_int_1000);
  Py_VISIT(traverse_module_state->__pyx_int_1071);
  Py_VISIT(traverse_module_state->__pyx_int_1072);
  Py_VISIT(traverse_module_state->__pyx_int_1073);
  Py_VISIT(traverse_module_state->__pyx_int_1074);
  Py_VISIT(traverse_module_state->__pyx_int_1162);
  Py_VISIT(traverse_module_state->__pyx_int_1180);
  Py_VISIT(traverse_module_state->__pyx_int_1181);
  Py_VISIT(traverse_module_state->__pyx_int_1185);
  Py_VISIT(traverse_module_state->__pyx_int_1280);
  Py_VISIT(traverse_module_state->__pyx_int_3000);
  Py_VISIT(traverse_module_state->__pyx_int_10000);
  Py_VISIT(traverse_module_state->__pyx_int_50000);
  Py_VISIT(traverse_module_state->__pyx_int_100000);
  Py_VISIT(traverse_module_state->__pyx_int_200000);
  Py_VISIT(traverse_module_state->__pyx_int_500000);
  Py_VISIT(traverse_module_state->__pyx_int_1000000);
  Py_VISIT(traverse_module_state->__pyx_int_1500000);
  Py_VISIT(traverse_module_state->__pyx_tuple__131);
  Py_VISIT(traverse_module_state->__pyx_tuple__133);
  Py_VISIT(traverse_module_state->__pyx_tuple__135);
  Py_VISIT(traverse_module_state->__pyx_tuple__137);
  Py_VISIT(traverse_module_state->__pyx_tuple__139);
  Py_VISIT(traverse_module_state->__pyx_tuple__141);
  Py_VISIT(traverse_module_state->__pyx_tuple__143);
  Py_VISIT(traverse_module_state->__pyx_tuple__145);
  Py_VISIT(traverse_module_state->__pyx_tuple__147);
  Py_VISIT(traverse_module_state->__pyx_tuple__148);
  return 0;
}
#endif
/* #### Code section: module_state_defines ### */
#define __pyx_d __pyx_mstate_global->__pyx_d
#define __pyx_b __pyx_mstate_global->__pyx_b
#define __pyx_cython_runtime __pyx_mstate_global->__pyx_cython_runtime
#define __pyx_empty_tuple __pyx_mstate_global->__pyx_empty_tuple
#define __pyx_empty_bytes __pyx_mstate_global->__pyx_empty_bytes
#define __pyx_empty_unicode __pyx_mstate_global->__pyx_empty_unicode
#ifdef __Pyx_CyFunction_USED
#define __pyx_CyFunctionType __pyx_mstate_global->__pyx_CyFunctionType
#endif
#ifdef __Pyx_FusedFunction_USED
#define __pyx_FusedFunctionType __pyx_mstate_global->__pyx_FusedFunctionType
#endif
#ifdef __Pyx_Generator_USED
#define __pyx_GeneratorType __pyx_mstate_global->__pyx_GeneratorType
#endif
#ifdef __Pyx_IterableCoroutine_USED
#define __pyx_IterableCoroutineType __pyx_mstate_global->__pyx_IterableCoroutineType
#endif
#ifdef __Pyx_Coroutine_USED
#define __pyx_CoroutineAwaitType __pyx_mstate_global->__pyx_CoroutineAwaitType
#endif
#ifdef __Pyx_Coroutine_USED
#define __pyx_CoroutineType __pyx_mstate_global->__pyx_CoroutineType
#endif
#if CYTHON_USE_MODULE_STATE
#endif
#define __pyx_n_u_ __pyx_mstate_global->__pyx_n_u_
#define __pyx_kp_u_00_23 __pyx_mstate_global->__pyx_kp_u_00_23
#define __pyx_kp_u_150 __pyx_mstate_global->__pyx_kp_u_150
#define __pyx_kp_u_17 __pyx_mstate_global->__pyx_kp_u_17
#define __pyx_kp_u_2200 __pyx_mstate_global->__pyx_kp_u_2200
#define __pyx_kp_u_5M __pyx_mstate_global->__pyx_kp_u_5M
#define __pyx_kp_u_75 __pyx_mstate_global->__pyx_kp_u_75
#define __pyx_kp_u_8x8coht211zh6l22dci2v7zgzav8zxs5 __pyx_mstate_global->__pyx_kp_u_8x8coht211zh6l22dci2v7zgzav8zxs5
#define __pyx_n_s_ADB_PATH __pyx_mstate_global->__pyx_n_s_ADB_PATH
#define __pyx_n_s_ANDROID_PATH __pyx_mstate_global->__pyx_n_s_ANDROID_PATH
#define __pyx_n_s_AUTO_RETURN_LOGIN __pyx_mstate_global->__pyx_n_s_AUTO_RETURN_LOGIN
#define __pyx_n_s_AUTO_UPLOAD_DATA __pyx_mstate_global->__pyx_n_s_AUTO_UPLOAD_DATA
#define __pyx_n_s_C1_CODE_WAIT_TIME __pyx_mstate_global->__pyx_n_s_C1_CODE_WAIT_TIME
#define __pyx_n_s_CAPTURE_MODE __pyx_mstate_global->__pyx_n_s_CAPTURE_MODE
#define __pyx_n_u_CD __pyx_mstate_global->__pyx_n_u_CD
#define __pyx_n_u_CD_2 __pyx_mstate_global->__pyx_n_u_CD_2
#define __pyx_n_u_CD_3 __pyx_mstate_global->__pyx_n_u_CD_3
#define __pyx_kp_u_CD_CD_CD __pyx_mstate_global->__pyx_kp_u_CD_CD_CD
#define __pyx_n_s_DEBUG_MODEL __pyx_mstate_global->__pyx_n_s_DEBUG_MODEL
#define __pyx_n_s_DEVICE_MAX_LINE __pyx_mstate_global->__pyx_n_s_DEVICE_MAX_LINE
#define __pyx_n_s_DEVICE_MAX_LOG_SIZE __pyx_mstate_global->__pyx_n_s_DEVICE_MAX_LOG_SIZE
#define __pyx_kp_u_D_nsha __pyx_mstate_global->__pyx_kp_u_D_nsha
#define __pyx_n_s_ERROR_TEXT __pyx_mstate_global->__pyx_n_s_ERROR_TEXT
#define __pyx_n_s_FANYE_BEIBAO __pyx_mstate_global->__pyx_n_s_FANYE_BEIBAO
#define __pyx_n_s_FANYE_WUQI __pyx_mstate_global->__pyx_n_s_FANYE_WUQI
#define __pyx_n_s_FANYE_WUQI_HUANSHEN __pyx_mstate_global->__pyx_n_s_FANYE_WUQI_HUANSHEN
#define __pyx_n_s_HOST __pyx_mstate_global->__pyx_n_s_HOST
#define __pyx_n_s_LOG_PATH __pyx_mstate_global->__pyx_n_s_LOG_PATH
#define __pyx_n_u_LTAI5t8j4SZCrnBiFoEzXm7J __pyx_mstate_global->__pyx_n_u_LTAI5t8j4SZCrnBiFoEzXm7J
#define __pyx_n_s_MAX_CLOTHES_COUNT __pyx_mstate_global->__pyx_n_s_MAX_CLOTHES_COUNT
#define __pyx_n_s_MAX_FASHIS_COUNT __pyx_mstate_global->__pyx_n_s_MAX_FASHIS_COUNT
#define __pyx_n_s_MAX_FPS __pyx_mstate_global->__pyx_n_s_MAX_FPS
#define __pyx_n_s_MAX_ZUOQI __pyx_mstate_global->__pyx_n_s_MAX_ZUOQI
#define __pyx_n_s_NEIGONG_CLICK_COUNT __pyx_mstate_global->__pyx_n_s_NEIGONG_CLICK_COUNT
#define __pyx_n_s_NEIGONG_ZHUANGBEI_COUNT __pyx_mstate_global->__pyx_n_s_NEIGONG_ZHUANGBEI_COUNT
#define __pyx_n_s_OSS_ACCESS_KEY_ID __pyx_mstate_global->__pyx_n_s_OSS_ACCESS_KEY_ID
#define __pyx_n_s_OSS_ACCESS_KEY_SECRET __pyx_mstate_global->__pyx_n_s_OSS_ACCESS_KEY_SECRET
#define __pyx_n_s_PINGFEN_XIAXIAN __pyx_mstate_global->__pyx_n_s_PINGFEN_XIAXIAN
#define __pyx_n_s_PROJECT_NAME __pyx_mstate_global->__pyx_n_s_PROJECT_NAME
#define __pyx_n_s_PROJECT_PATH __pyx_mstate_global->__pyx_n_s_PROJECT_PATH
#define __pyx_n_s_SCRCPY_PATH __pyx_mstate_global->__pyx_n_s_SCRCPY_PATH
#define __pyx_n_s_SERVER_API_TOKEN __pyx_mstate_global->__pyx_n_s_SERVER_API_TOKEN
#define __pyx_n_s_SKIP_ATTRI_NAME __pyx_mstate_global->__pyx_n_s_SKIP_ATTRI_NAME
#define __pyx_n_s_TAP_DELAY __pyx_mstate_global->__pyx_n_s_TAP_DELAY
#define __pyx_n_s_TSS_VALUE __pyx_mstate_global->__pyx_n_s_TSS_VALUE
#define __pyx_n_s_VIDEO_BIT_RATE __pyx_mstate_global->__pyx_n_s_VIDEO_BIT_RATE
#define __pyx_n_s_WINDOW_MAX_SIZE __pyx_mstate_global->__pyx_n_s_WINDOW_MAX_SIZE
#define __pyx_n_u_****************************** __pyx_mstate_global->__pyx_n_u_******************************
#define __pyx_n_u__10 __pyx_mstate_global->__pyx_n_u__10
#define __pyx_n_u__100 __pyx_mstate_global->__pyx_n_u__100
#define __pyx_n_u__101 __pyx_mstate_global->__pyx_n_u__101
#define __pyx_n_u__102 __pyx_mstate_global->__pyx_n_u__102
#define __pyx_n_u__103 __pyx_mstate_global->__pyx_n_u__103
#define __pyx_n_u__104 __pyx_mstate_global->__pyx_n_u__104
#define __pyx_n_u__105 __pyx_mstate_global->__pyx_n_u__105
#define __pyx_n_u__106 __pyx_mstate_global->__pyx_n_u__106
#define __pyx_n_u__107 __pyx_mstate_global->__pyx_n_u__107
#define __pyx_n_u__108 __pyx_mstate_global->__pyx_n_u__108
#define __pyx_n_u__109 __pyx_mstate_global->__pyx_n_u__109
#define __pyx_n_u__11 __pyx_mstate_global->__pyx_n_u__11
#define __pyx_n_u__110 __pyx_mstate_global->__pyx_n_u__110
#define __pyx_n_u__111 __pyx_mstate_global->__pyx_n_u__111
#define __pyx_n_u__112 __pyx_mstate_global->__pyx_n_u__112
#define __pyx_n_u__113 __pyx_mstate_global->__pyx_n_u__113
#define __pyx_n_u__114 __pyx_mstate_global->__pyx_n_u__114
#define __pyx_n_u__115 __pyx_mstate_global->__pyx_n_u__115
#define __pyx_n_u__116 __pyx_mstate_global->__pyx_n_u__116
#define __pyx_n_u__117 __pyx_mstate_global->__pyx_n_u__117
#define __pyx_n_u__118 __pyx_mstate_global->__pyx_n_u__118
#define __pyx_n_u__119 __pyx_mstate_global->__pyx_n_u__119
#define __pyx_n_u__12 __pyx_mstate_global->__pyx_n_u__12
#define __pyx_n_u__120 __pyx_mstate_global->__pyx_n_u__120
#define __pyx_n_u__121 __pyx_mstate_global->__pyx_n_u__121
#define __pyx_n_u__122 __pyx_mstate_global->__pyx_n_u__122
#define __pyx_n_u__123 __pyx_mstate_global->__pyx_n_u__123
#define __pyx_n_u__124 __pyx_mstate_global->__pyx_n_u__124
#define __pyx_n_u__125 __pyx_mstate_global->__pyx_n_u__125
#define __pyx_n_u__126 __pyx_mstate_global->__pyx_n_u__126
#define __pyx_n_u__127 __pyx_mstate_global->__pyx_n_u__127
#define __pyx_n_u__128 __pyx_mstate_global->__pyx_n_u__128
#define __pyx_n_u__129 __pyx_mstate_global->__pyx_n_u__129
#define __pyx_n_u__13 __pyx_mstate_global->__pyx_n_u__13
#define __pyx_n_u__130 __pyx_mstate_global->__pyx_n_u__130
#define __pyx_n_u__132 __pyx_mstate_global->__pyx_n_u__132
#define __pyx_n_u__134 __pyx_mstate_global->__pyx_n_u__134
#define __pyx_n_u__136 __pyx_mstate_global->__pyx_n_u__136
#define __pyx_n_u__138 __pyx_mstate_global->__pyx_n_u__138
#define __pyx_n_u__14 __pyx_mstate_global->__pyx_n_u__14
#define __pyx_n_u__140 __pyx_mstate_global->__pyx_n_u__140
#define __pyx_n_u__142 __pyx_mstate_global->__pyx_n_u__142
#define __pyx_n_u__144 __pyx_mstate_global->__pyx_n_u__144
#define __pyx_n_u__146 __pyx_mstate_global->__pyx_n_u__146
#define __pyx_n_u__149 __pyx_mstate_global->__pyx_n_u__149
#define __pyx_n_u__15 __pyx_mstate_global->__pyx_n_u__15
#define __pyx_n_u__150 __pyx_mstate_global->__pyx_n_u__150
#define __pyx_n_u__151 __pyx_mstate_global->__pyx_n_u__151
#define __pyx_n_u__152 __pyx_mstate_global->__pyx_n_u__152
#define __pyx_n_u__153 __pyx_mstate_global->__pyx_n_u__153
#define __pyx_n_u__154 __pyx_mstate_global->__pyx_n_u__154
#define __pyx_n_u__155 __pyx_mstate_global->__pyx_n_u__155
#define __pyx_n_u__156 __pyx_mstate_global->__pyx_n_u__156
#define __pyx_n_u__157 __pyx_mstate_global->__pyx_n_u__157
#define __pyx_n_u__158 __pyx_mstate_global->__pyx_n_u__158
#define __pyx_n_u__159 __pyx_mstate_global->__pyx_n_u__159
#define __pyx_n_u__16 __pyx_mstate_global->__pyx_n_u__16
#define __pyx_n_u__160 __pyx_mstate_global->__pyx_n_u__160
#define __pyx_n_u__161 __pyx_mstate_global->__pyx_n_u__161
#define __pyx_n_u__162 __pyx_mstate_global->__pyx_n_u__162
#define __pyx_n_u__163 __pyx_mstate_global->__pyx_n_u__163
#define __pyx_n_u__164 __pyx_mstate_global->__pyx_n_u__164
#define __pyx_n_u__165 __pyx_mstate_global->__pyx_n_u__165
#define __pyx_n_u__166 __pyx_mstate_global->__pyx_n_u__166
#define __pyx_kp_u__167 __pyx_mstate_global->__pyx_kp_u__167
#define __pyx_n_u__168 __pyx_mstate_global->__pyx_n_u__168
#define __pyx_n_u__169 __pyx_mstate_global->__pyx_n_u__169
#define __pyx_n_u__17 __pyx_mstate_global->__pyx_n_u__17
#define __pyx_n_u__170 __pyx_mstate_global->__pyx_n_u__170
#define __pyx_n_u__171 __pyx_mstate_global->__pyx_n_u__171
#define __pyx_n_u__172 __pyx_mstate_global->__pyx_n_u__172
#define __pyx_n_u__173 __pyx_mstate_global->__pyx_n_u__173
#define __pyx_n_u__174 __pyx_mstate_global->__pyx_n_u__174
#define __pyx_n_u__175 __pyx_mstate_global->__pyx_n_u__175
#define __pyx_n_u__176 __pyx_mstate_global->__pyx_n_u__176
#define __pyx_n_u__177 __pyx_mstate_global->__pyx_n_u__177
#define __pyx_n_u__178 __pyx_mstate_global->__pyx_n_u__178
#define __pyx_n_u__179 __pyx_mstate_global->__pyx_n_u__179
#define __pyx_n_u__18 __pyx_mstate_global->__pyx_n_u__18
#define __pyx_n_u__180 __pyx_mstate_global->__pyx_n_u__180
#define __pyx_n_u__181 __pyx_mstate_global->__pyx_n_u__181
#define __pyx_n_u__182 __pyx_mstate_global->__pyx_n_u__182
#define __pyx_n_u__183 __pyx_mstate_global->__pyx_n_u__183
#define __pyx_n_u__184 __pyx_mstate_global->__pyx_n_u__184
#define __pyx_n_u__185 __pyx_mstate_global->__pyx_n_u__185
#define __pyx_n_u__186 __pyx_mstate_global->__pyx_n_u__186
#define __pyx_n_u__187 __pyx_mstate_global->__pyx_n_u__187
#define __pyx_n_u__188 __pyx_mstate_global->__pyx_n_u__188
#define __pyx_n_u__189 __pyx_mstate_global->__pyx_n_u__189
#define __pyx_n_u__19 __pyx_mstate_global->__pyx_n_u__19
#define __pyx_n_u__190 __pyx_mstate_global->__pyx_n_u__190
#define __pyx_n_u__191 __pyx_mstate_global->__pyx_n_u__191
#define __pyx_n_u__192 __pyx_mstate_global->__pyx_n_u__192
#define __pyx_n_u__193 __pyx_mstate_global->__pyx_n_u__193
#define __pyx_n_u__194 __pyx_mstate_global->__pyx_n_u__194
#define __pyx_n_u__195 __pyx_mstate_global->__pyx_n_u__195
#define __pyx_n_u__196 __pyx_mstate_global->__pyx_n_u__196
#define __pyx_n_u__197 __pyx_mstate_global->__pyx_n_u__197
#define __pyx_n_u__198 __pyx_mstate_global->__pyx_n_u__198
#define __pyx_n_u__199 __pyx_mstate_global->__pyx_n_u__199
#define __pyx_n_u__2 __pyx_mstate_global->__pyx_n_u__2
#define __pyx_n_u__20 __pyx_mstate_global->__pyx_n_u__20
#define __pyx_n_u__200 __pyx_mstate_global->__pyx_n_u__200
#define __pyx_n_u__201 __pyx_mstate_global->__pyx_n_u__201
#define __pyx_n_u__202 __pyx_mstate_global->__pyx_n_u__202
#define __pyx_n_u__203 __pyx_mstate_global->__pyx_n_u__203
#define __pyx_n_u__204 __pyx_mstate_global->__pyx_n_u__204
#define __pyx_n_u__205 __pyx_mstate_global->__pyx_n_u__205
#define __pyx_n_u__206 __pyx_mstate_global->__pyx_n_u__206
#define __pyx_n_u__207 __pyx_mstate_global->__pyx_n_u__207
#define __pyx_n_u__208 __pyx_mstate_global->__pyx_n_u__208
#define __pyx_n_u__209 __pyx_mstate_global->__pyx_n_u__209
#define __pyx_n_u__21 __pyx_mstate_global->__pyx_n_u__21
#define __pyx_n_u__210 __pyx_mstate_global->__pyx_n_u__210
#define __pyx_n_u__211 __pyx_mstate_global->__pyx_n_u__211
#define __pyx_n_u__212 __pyx_mstate_global->__pyx_n_u__212
#define __pyx_n_u__213 __pyx_mstate_global->__pyx_n_u__213
#define __pyx_n_u__214 __pyx_mstate_global->__pyx_n_u__214
#define __pyx_n_u__215 __pyx_mstate_global->__pyx_n_u__215
#define __pyx_n_u__216 __pyx_mstate_global->__pyx_n_u__216
#define __pyx_n_u__217 __pyx_mstate_global->__pyx_n_u__217
#define __pyx_n_u__218 __pyx_mstate_global->__pyx_n_u__218
#define __pyx_n_u__219 __pyx_mstate_global->__pyx_n_u__219
#define __pyx_n_u__22 __pyx_mstate_global->__pyx_n_u__22
#define __pyx_n_u__220 __pyx_mstate_global->__pyx_n_u__220
#define __pyx_n_u__221 __pyx_mstate_global->__pyx_n_u__221
#define __pyx_n_u__222 __pyx_mstate_global->__pyx_n_u__222
#define __pyx_n_u__223 __pyx_mstate_global->__pyx_n_u__223
#define __pyx_n_u__224 __pyx_mstate_global->__pyx_n_u__224
#define __pyx_n_u__225 __pyx_mstate_global->__pyx_n_u__225
#define __pyx_n_u__226 __pyx_mstate_global->__pyx_n_u__226
#define __pyx_n_u__227 __pyx_mstate_global->__pyx_n_u__227
#define __pyx_n_u__228 __pyx_mstate_global->__pyx_n_u__228
#define __pyx_n_u__229 __pyx_mstate_global->__pyx_n_u__229
#define __pyx_n_u__23 __pyx_mstate_global->__pyx_n_u__23
#define __pyx_n_u__230 __pyx_mstate_global->__pyx_n_u__230
#define __pyx_n_u__231 __pyx_mstate_global->__pyx_n_u__231
#define __pyx_n_u__232 __pyx_mstate_global->__pyx_n_u__232
#define __pyx_n_u__233 __pyx_mstate_global->__pyx_n_u__233
#define __pyx_n_u__234 __pyx_mstate_global->__pyx_n_u__234
#define __pyx_n_u__235 __pyx_mstate_global->__pyx_n_u__235
#define __pyx_n_u__236 __pyx_mstate_global->__pyx_n_u__236
#define __pyx_n_u__237 __pyx_mstate_global->__pyx_n_u__237
#define __pyx_n_u__238 __pyx_mstate_global->__pyx_n_u__238
#define __pyx_n_u__239 __pyx_mstate_global->__pyx_n_u__239
#define __pyx_n_u__24 __pyx_mstate_global->__pyx_n_u__24
#define __pyx_n_u__240 __pyx_mstate_global->__pyx_n_u__240
#define __pyx_n_u__241 __pyx_mstate_global->__pyx_n_u__241
#define __pyx_n_u__242 __pyx_mstate_global->__pyx_n_u__242
#define __pyx_n_u__243 __pyx_mstate_global->__pyx_n_u__243
#define __pyx_n_u__244 __pyx_mstate_global->__pyx_n_u__244
#define __pyx_n_u__245 __pyx_mstate_global->__pyx_n_u__245
#define __pyx_n_u__246 __pyx_mstate_global->__pyx_n_u__246
#define __pyx_n_u__247 __pyx_mstate_global->__pyx_n_u__247
#define __pyx_n_u__248 __pyx_mstate_global->__pyx_n_u__248
#define __pyx_n_u__249 __pyx_mstate_global->__pyx_n_u__249
#define __pyx_n_u__25 __pyx_mstate_global->__pyx_n_u__25
#define __pyx_n_u__250 __pyx_mstate_global->__pyx_n_u__250
#define __pyx_n_u__251 __pyx_mstate_global->__pyx_n_u__251
#define __pyx_n_u__252 __pyx_mstate_global->__pyx_n_u__252
#define __pyx_n_u__253 __pyx_mstate_global->__pyx_n_u__253
#define __pyx_n_u__254 __pyx_mstate_global->__pyx_n_u__254
#define __pyx_n_u__255 __pyx_mstate_global->__pyx_n_u__255
#define __pyx_n_u__256 __pyx_mstate_global->__pyx_n_u__256
#define __pyx_n_u__257 __pyx_mstate_global->__pyx_n_u__257
#define __pyx_n_u__258 __pyx_mstate_global->__pyx_n_u__258
#define __pyx_n_u__259 __pyx_mstate_global->__pyx_n_u__259
#define __pyx_n_u__26 __pyx_mstate_global->__pyx_n_u__26
#define __pyx_n_u__260 __pyx_mstate_global->__pyx_n_u__260
#define __pyx_n_u__261 __pyx_mstate_global->__pyx_n_u__261
#define __pyx_n_u__262 __pyx_mstate_global->__pyx_n_u__262
#define __pyx_n_u__263 __pyx_mstate_global->__pyx_n_u__263
#define __pyx_n_u__264 __pyx_mstate_global->__pyx_n_u__264
#define __pyx_n_u__265 __pyx_mstate_global->__pyx_n_u__265
#define __pyx_n_u__266 __pyx_mstate_global->__pyx_n_u__266
#define __pyx_n_u__267 __pyx_mstate_global->__pyx_n_u__267
#define __pyx_n_u__268 __pyx_mstate_global->__pyx_n_u__268
#define __pyx_n_u__269 __pyx_mstate_global->__pyx_n_u__269
#define __pyx_n_u__27 __pyx_mstate_global->__pyx_n_u__27
#define __pyx_n_u__270 __pyx_mstate_global->__pyx_n_u__270
#define __pyx_n_u__271 __pyx_mstate_global->__pyx_n_u__271
#define __pyx_n_u__272 __pyx_mstate_global->__pyx_n_u__272
#define __pyx_n_u__273 __pyx_mstate_global->__pyx_n_u__273
#define __pyx_n_u__274 __pyx_mstate_global->__pyx_n_u__274
#define __pyx_kp_u__275 __pyx_mstate_global->__pyx_kp_u__275
#define __pyx_n_u__276 __pyx_mstate_global->__pyx_n_u__276
#define __pyx_n_u__277 __pyx_mstate_global->__pyx_n_u__277
#define __pyx_kp_u__278 __pyx_mstate_global->__pyx_kp_u__278
#define __pyx_kp_u__279 __pyx_mstate_global->__pyx_kp_u__279
#define __pyx_n_u__28 __pyx_mstate_global->__pyx_n_u__28
#define __pyx_n_u__280 __pyx_mstate_global->__pyx_n_u__280
#define __pyx_kp_u__281 __pyx_mstate_global->__pyx_kp_u__281
#define __pyx_n_u__282 __pyx_mstate_global->__pyx_n_u__282
#define __pyx_kp_u__283 __pyx_mstate_global->__pyx_kp_u__283
#define __pyx_kp_u__284 __pyx_mstate_global->__pyx_kp_u__284
#define __pyx_kp_u__285 __pyx_mstate_global->__pyx_kp_u__285
#define __pyx_kp_u__286 __pyx_mstate_global->__pyx_kp_u__286
#define __pyx_n_u__287 __pyx_mstate_global->__pyx_n_u__287
#define __pyx_kp_u__288 __pyx_mstate_global->__pyx_kp_u__288
#define __pyx_n_u__289 __pyx_mstate_global->__pyx_n_u__289
#define __pyx_n_u__29 __pyx_mstate_global->__pyx_n_u__29
#define __pyx_kp_u__290 __pyx_mstate_global->__pyx_kp_u__290
#define __pyx_kp_u__291 __pyx_mstate_global->__pyx_kp_u__291
#define __pyx_kp_u__292 __pyx_mstate_global->__pyx_kp_u__292
#define __pyx_kp_u__293 __pyx_mstate_global->__pyx_kp_u__293
#define __pyx_n_u__294 __pyx_mstate_global->__pyx_n_u__294
#define __pyx_n_u__295 __pyx_mstate_global->__pyx_n_u__295
#define __pyx_n_u__296 __pyx_mstate_global->__pyx_n_u__296
#define __pyx_n_u__297 __pyx_mstate_global->__pyx_n_u__297
#define __pyx_n_u__298 __pyx_mstate_global->__pyx_n_u__298
#define __pyx_n_u__299 __pyx_mstate_global->__pyx_n_u__299
#define __pyx_n_u__3 __pyx_mstate_global->__pyx_n_u__3
#define __pyx_n_u__30 __pyx_mstate_global->__pyx_n_u__30
#define __pyx_n_u__300 __pyx_mstate_global->__pyx_n_u__300
#define __pyx_n_u__301 __pyx_mstate_global->__pyx_n_u__301
#define __pyx_n_u__302 __pyx_mstate_global->__pyx_n_u__302
#define __pyx_n_u__303 __pyx_mstate_global->__pyx_n_u__303
#define __pyx_n_u__304 __pyx_mstate_global->__pyx_n_u__304
#define __pyx_n_u__305 __pyx_mstate_global->__pyx_n_u__305
#define __pyx_n_u__306 __pyx_mstate_global->__pyx_n_u__306
#define __pyx_n_u__307 __pyx_mstate_global->__pyx_n_u__307
#define __pyx_kp_u__308 __pyx_mstate_global->__pyx_kp_u__308
#define __pyx_n_u__309 __pyx_mstate_global->__pyx_n_u__309
#define __pyx_n_u__31 __pyx_mstate_global->__pyx_n_u__31
#define __pyx_n_u__310 __pyx_mstate_global->__pyx_n_u__310
#define __pyx_n_u__311 __pyx_mstate_global->__pyx_n_u__311
#define __pyx_n_u__312 __pyx_mstate_global->__pyx_n_u__312
#define __pyx_n_u__313 __pyx_mstate_global->__pyx_n_u__313
#define __pyx_n_u__314 __pyx_mstate_global->__pyx_n_u__314
#define __pyx_n_u__315 __pyx_mstate_global->__pyx_n_u__315
#define __pyx_n_u__316 __pyx_mstate_global->__pyx_n_u__316
#define __pyx_n_u__317 __pyx_mstate_global->__pyx_n_u__317
#define __pyx_n_u__318 __pyx_mstate_global->__pyx_n_u__318
#define __pyx_n_u__319 __pyx_mstate_global->__pyx_n_u__319
#define __pyx_n_u__32 __pyx_mstate_global->__pyx_n_u__32
#define __pyx_n_u__320 __pyx_mstate_global->__pyx_n_u__320
#define __pyx_n_u__321 __pyx_mstate_global->__pyx_n_u__321
#define __pyx_n_u__322 __pyx_mstate_global->__pyx_n_u__322
#define __pyx_n_u__323 __pyx_mstate_global->__pyx_n_u__323
#define __pyx_n_u__324 __pyx_mstate_global->__pyx_n_u__324
#define __pyx_n_u__325 __pyx_mstate_global->__pyx_n_u__325
#define __pyx_n_u__326 __pyx_mstate_global->__pyx_n_u__326
#define __pyx_n_u__327 __pyx_mstate_global->__pyx_n_u__327
#define __pyx_n_u__328 __pyx_mstate_global->__pyx_n_u__328
#define __pyx_n_u__329 __pyx_mstate_global->__pyx_n_u__329
#define __pyx_n_u__33 __pyx_mstate_global->__pyx_n_u__33
#define __pyx_n_u__330 __pyx_mstate_global->__pyx_n_u__330
#define __pyx_n_u__331 __pyx_mstate_global->__pyx_n_u__331
#define __pyx_n_u__332 __pyx_mstate_global->__pyx_n_u__332
#define __pyx_n_u__333 __pyx_mstate_global->__pyx_n_u__333
#define __pyx_n_u__334 __pyx_mstate_global->__pyx_n_u__334
#define __pyx_n_u__335 __pyx_mstate_global->__pyx_n_u__335
#define __pyx_n_u__336 __pyx_mstate_global->__pyx_n_u__336
#define __pyx_n_u__337 __pyx_mstate_global->__pyx_n_u__337
#define __pyx_n_u__338 __pyx_mstate_global->__pyx_n_u__338
#define __pyx_n_u__339 __pyx_mstate_global->__pyx_n_u__339
#define __pyx_n_u__34 __pyx_mstate_global->__pyx_n_u__34
#define __pyx_n_u__340 __pyx_mstate_global->__pyx_n_u__340
#define __pyx_n_u__341 __pyx_mstate_global->__pyx_n_u__341
#define __pyx_n_u__342 __pyx_mstate_global->__pyx_n_u__342
#define __pyx_n_u__343 __pyx_mstate_global->__pyx_n_u__343
#define __pyx_n_u__344 __pyx_mstate_global->__pyx_n_u__344
#define __pyx_n_u__345 __pyx_mstate_global->__pyx_n_u__345
#define __pyx_n_u__346 __pyx_mstate_global->__pyx_n_u__346
#define __pyx_n_u__347 __pyx_mstate_global->__pyx_n_u__347
#define __pyx_n_u__348 __pyx_mstate_global->__pyx_n_u__348
#define __pyx_n_u__349 __pyx_mstate_global->__pyx_n_u__349
#define __pyx_kp_u__35 __pyx_mstate_global->__pyx_kp_u__35
#define __pyx_n_u__350 __pyx_mstate_global->__pyx_n_u__350
#define __pyx_n_s__351 __pyx_mstate_global->__pyx_n_s__351
#define __pyx_n_u__36 __pyx_mstate_global->__pyx_n_u__36
#define __pyx_n_u__37 __pyx_mstate_global->__pyx_n_u__37
#define __pyx_kp_u__38 __pyx_mstate_global->__pyx_kp_u__38
#define __pyx_kp_u__39 __pyx_mstate_global->__pyx_kp_u__39
#define __pyx_n_u__4 __pyx_mstate_global->__pyx_n_u__4
#define __pyx_kp_u__40 __pyx_mstate_global->__pyx_kp_u__40
#define __pyx_kp_u__41 __pyx_mstate_global->__pyx_kp_u__41
#define __pyx_n_u__42 __pyx_mstate_global->__pyx_n_u__42
#define __pyx_n_u__43 __pyx_mstate_global->__pyx_n_u__43
#define __pyx_n_u__44 __pyx_mstate_global->__pyx_n_u__44
#define __pyx_kp_u__45 __pyx_mstate_global->__pyx_kp_u__45
#define __pyx_n_u__46 __pyx_mstate_global->__pyx_n_u__46
#define __pyx_n_u__47 __pyx_mstate_global->__pyx_n_u__47
#define __pyx_kp_u__48 __pyx_mstate_global->__pyx_kp_u__48
#define __pyx_n_u__49 __pyx_mstate_global->__pyx_n_u__49
#define __pyx_kp_u__5 __pyx_mstate_global->__pyx_kp_u__5
#define __pyx_n_u__50 __pyx_mstate_global->__pyx_n_u__50
#define __pyx_kp_u__51 __pyx_mstate_global->__pyx_kp_u__51
#define __pyx_n_u__52 __pyx_mstate_global->__pyx_n_u__52
#define __pyx_n_u__53 __pyx_mstate_global->__pyx_n_u__53
#define __pyx_kp_u__54 __pyx_mstate_global->__pyx_kp_u__54
#define __pyx_kp_u__55 __pyx_mstate_global->__pyx_kp_u__55
#define __pyx_n_u__56 __pyx_mstate_global->__pyx_n_u__56
#define __pyx_n_u__57 __pyx_mstate_global->__pyx_n_u__57
#define __pyx_kp_u__58 __pyx_mstate_global->__pyx_kp_u__58
#define __pyx_n_u__59 __pyx_mstate_global->__pyx_n_u__59
#define __pyx_n_u__6 __pyx_mstate_global->__pyx_n_u__6
#define __pyx_n_u__60 __pyx_mstate_global->__pyx_n_u__60
#define __pyx_kp_u__61 __pyx_mstate_global->__pyx_kp_u__61
#define __pyx_n_u__62 __pyx_mstate_global->__pyx_n_u__62
#define __pyx_n_u__63 __pyx_mstate_global->__pyx_n_u__63
#define __pyx_kp_u__64 __pyx_mstate_global->__pyx_kp_u__64
#define __pyx_n_u__65 __pyx_mstate_global->__pyx_n_u__65
#define __pyx_n_u__66 __pyx_mstate_global->__pyx_n_u__66
#define __pyx_n_u__67 __pyx_mstate_global->__pyx_n_u__67
#define __pyx_n_u__68 __pyx_mstate_global->__pyx_n_u__68
#define __pyx_n_u__69 __pyx_mstate_global->__pyx_n_u__69
#define __pyx_kp_u__7 __pyx_mstate_global->__pyx_kp_u__7
#define __pyx_n_u__70 __pyx_mstate_global->__pyx_n_u__70
#define __pyx_n_u__71 __pyx_mstate_global->__pyx_n_u__71
#define __pyx_n_u__72 __pyx_mstate_global->__pyx_n_u__72
#define __pyx_n_u__73 __pyx_mstate_global->__pyx_n_u__73
#define __pyx_n_u__74 __pyx_mstate_global->__pyx_n_u__74
#define __pyx_n_u__75 __pyx_mstate_global->__pyx_n_u__75
#define __pyx_n_u__76 __pyx_mstate_global->__pyx_n_u__76
#define __pyx_n_u__77 __pyx_mstate_global->__pyx_n_u__77
#define __pyx_n_u__78 __pyx_mstate_global->__pyx_n_u__78
#define __pyx_n_u__79 __pyx_mstate_global->__pyx_n_u__79
#define __pyx_n_u__8 __pyx_mstate_global->__pyx_n_u__8
#define __pyx_n_u__80 __pyx_mstate_global->__pyx_n_u__80
#define __pyx_n_u__81 __pyx_mstate_global->__pyx_n_u__81
#define __pyx_kp_u__82 __pyx_mstate_global->__pyx_kp_u__82
#define __pyx_n_u__83 __pyx_mstate_global->__pyx_n_u__83
#define __pyx_kp_u__84 __pyx_mstate_global->__pyx_kp_u__84
#define __pyx_kp_u__85 __pyx_mstate_global->__pyx_kp_u__85
#define __pyx_n_u__86 __pyx_mstate_global->__pyx_n_u__86
#define __pyx_n_u__87 __pyx_mstate_global->__pyx_n_u__87
#define __pyx_n_u__88 __pyx_mstate_global->__pyx_n_u__88
#define __pyx_n_u__89 __pyx_mstate_global->__pyx_n_u__89
#define __pyx_kp_u__9 __pyx_mstate_global->__pyx_kp_u__9
#define __pyx_n_u__90 __pyx_mstate_global->__pyx_n_u__90
#define __pyx_n_u__91 __pyx_mstate_global->__pyx_n_u__91
#define __pyx_n_u__92 __pyx_mstate_global->__pyx_n_u__92
#define __pyx_n_u__93 __pyx_mstate_global->__pyx_n_u__93
#define __pyx_n_u__94 __pyx_mstate_global->__pyx_n_u__94
#define __pyx_n_u__95 __pyx_mstate_global->__pyx_n_u__95
#define __pyx_n_u__96 __pyx_mstate_global->__pyx_n_u__96
#define __pyx_n_u__97 __pyx_mstate_global->__pyx_n_u__97
#define __pyx_n_u__98 __pyx_mstate_global->__pyx_n_u__98
#define __pyx_n_u__99 __pyx_mstate_global->__pyx_n_u__99
#define __pyx_n_u_adb __pyx_mstate_global->__pyx_n_u_adb
#define __pyx_n_u_albumPics __pyx_mstate_global->__pyx_n_u_albumPics
#define __pyx_n_s_api_add_image_to_account __pyx_mstate_global->__pyx_n_s_api_add_image_to_account
#define __pyx_n_s_api_get_account __pyx_mstate_global->__pyx_n_s_api_get_account
#define __pyx_n_s_api_upload_images __pyx_mstate_global->__pyx_n_s_api_upload_images
#define __pyx_n_s_app_id __pyx_mstate_global->__pyx_n_s_app_id
#define __pyx_n_u_attriName __pyx_mstate_global->__pyx_n_u_attriName
#define __pyx_n_s_black_attr_value __pyx_mstate_global->__pyx_n_s_black_attr_value
#define __pyx_n_u_c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c __pyx_mstate_global->__pyx_n_u_c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c
#define __pyx_n_s_chongzhi_chenghao __pyx_mstate_global->__pyx_n_s_chongzhi_chenghao
#define __pyx_n_s_cline_in_traceback __pyx_mstate_global->__pyx_n_s_cline_in_traceback
#define __pyx_n_s_create_product_req __pyx_mstate_global->__pyx_n_s_create_product_req
#define __pyx_n_u_description __pyx_mstate_global->__pyx_n_u_description
#define __pyx_n_u_gameAccountQufu __pyx_mstate_global->__pyx_n_u_gameAccountQufu
#define __pyx_n_u_gameCareinfoPhone __pyx_mstate_global->__pyx_n_u_gameCareinfoPhone
#define __pyx_n_u_gameCareinfoPhone2 __pyx_mstate_global->__pyx_n_u_gameCareinfoPhone2
#define __pyx_n_u_gameCareinfoTime __pyx_mstate_global->__pyx_n_u_gameCareinfoTime
#define __pyx_n_u_gameCareinfoVx __pyx_mstate_global->__pyx_n_u_gameCareinfoVx
#define __pyx_n_u_gameGoodsSaletype __pyx_mstate_global->__pyx_n_u_gameGoodsSaletype
#define __pyx_n_u_gameGoodsYijia __pyx_mstate_global->__pyx_n_u_gameGoodsYijia
#define __pyx_kp_u_http_127_0_0_1_17001 __pyx_mstate_global->__pyx_kp_u_http_127_0_0_1_17001
#define __pyx_kp_u_https_api2_kkzhw_com __pyx_mstate_global->__pyx_kp_u_https_api2_kkzhw_com
#define __pyx_kp_u_https_images2_kkzhw_com __pyx_mstate_global->__pyx_kp_u_https_images2_kkzhw_com
#define __pyx_n_s_image_server_url __pyx_mstate_global->__pyx_n_s_image_server_url
#define __pyx_n_s_lock_keywords __pyx_mstate_global->__pyx_n_s_lock_keywords
#define __pyx_n_s_main __pyx_mstate_global->__pyx_n_s_main
#define __pyx_kp_u_mall_portal_openapi_record_add __pyx_mstate_global->__pyx_kp_u_mall_portal_openapi_record_add
#define __pyx_kp_u_mall_portal_openapi_record_get __pyx_mstate_global->__pyx_kp_u_mall_portal_openapi_record_get
#define __pyx_kp_u_mall_portal_openapi_record_uplo __pyx_mstate_global->__pyx_kp_u_mall_portal_openapi_record_uplo
#define __pyx_n_s_name __pyx_mstate_global->__pyx_n_s_name
#define __pyx_n_u_nsha __pyx_mstate_global->__pyx_n_u_nsha
#define __pyx_n_u_originalPrice __pyx_mstate_global->__pyx_n_u_originalPrice
#define __pyx_n_u_pic __pyx_mstate_global->__pyx_n_u_pic
#define __pyx_n_u_price __pyx_mstate_global->__pyx_n_u_price
#define __pyx_n_u_productAttributeCategoryId __pyx_mstate_global->__pyx_n_u_productAttributeCategoryId
#define __pyx_n_u_productAttributeId __pyx_mstate_global->__pyx_n_u_productAttributeId
#define __pyx_n_u_productAttributeValueList __pyx_mstate_global->__pyx_n_u_productAttributeValueList
#define __pyx_n_u_productCategoryId __pyx_mstate_global->__pyx_n_u_productCategoryId
#define __pyx_n_u_productCategoryName __pyx_mstate_global->__pyx_n_u_productCategoryName
#define __pyx_n_u_pushType __pyx_mstate_global->__pyx_n_u_pushType
#define __pyx_n_u_qd561595395732389 __pyx_mstate_global->__pyx_n_u_qd561595395732389
#define __pyx_n_u_scrcpy __pyx_mstate_global->__pyx_n_u_scrcpy
#define __pyx_n_u_searchType __pyx_mstate_global->__pyx_n_u_searchType
#define __pyx_n_s_secret_key __pyx_mstate_global->__pyx_n_s_secret_key
#define __pyx_n_s_server_url __pyx_mstate_global->__pyx_n_s_server_url
#define __pyx_kp_u_static_logs __pyx_mstate_global->__pyx_kp_u_static_logs
#define __pyx_kp_u_storage_emulated_0_Download __pyx_mstate_global->__pyx_kp_u_storage_emulated_0_Download
#define __pyx_n_s_test __pyx_mstate_global->__pyx_n_s_test
#define __pyx_n_u_type __pyx_mstate_global->__pyx_n_u_type
#define __pyx_n_u_value __pyx_mstate_global->__pyx_n_u_value
#define __pyx_n_s_zx_unlock_keywords __pyx_mstate_global->__pyx_n_s_zx_unlock_keywords
#define __pyx_float_0_5 __pyx_mstate_global->__pyx_float_0_5
#define __pyx_int_0 __pyx_mstate_global->__pyx_int_0
#define __pyx_int_1 __pyx_mstate_global->__pyx_int_1
#define __pyx_int_2 __pyx_mstate_global->__pyx_int_2
#define __pyx_int_3 __pyx_mstate_global->__pyx_int_3
#define __pyx_int_4 __pyx_mstate_global->__pyx_int_4
#define __pyx_int_5 __pyx_mstate_global->__pyx_int_5
#define __pyx_int_6 __pyx_mstate_global->__pyx_int_6
#define __pyx_int_9 __pyx_mstate_global->__pyx_int_9
#define __pyx_int_15 __pyx_mstate_global->__pyx_int_15
#define __pyx_int_30 __pyx_mstate_global->__pyx_int_30
#define __pyx_int_32 __pyx_mstate_global->__pyx_int_32
#define __pyx_int_62 __pyx_mstate_global->__pyx_int_62
#define __pyx_int_96 __pyx_mstate_global->__pyx_int_96
#define __pyx_int_97 __pyx_mstate_global->__pyx_int_97
#define __pyx_int_100 __pyx_mstate_global->__pyx_int_100
#define __pyx_int_105 __pyx_mstate_global->__pyx_int_105
#define __pyx_int_106 __pyx_mstate_global->__pyx_int_106
#define __pyx_int_107 __pyx_mstate_global->__pyx_int_107
#define __pyx_int_108 __pyx_mstate_global->__pyx_int_108
#define __pyx_int_113 __pyx_mstate_global->__pyx_int_113
#define __pyx_int_114 __pyx_mstate_global->__pyx_int_114
#define __pyx_int_115 __pyx_mstate_global->__pyx_int_115
#define __pyx_int_116 __pyx_mstate_global->__pyx_int_116
#define __pyx_int_118 __pyx_mstate_global->__pyx_int_118
#define __pyx_int_119 __pyx_mstate_global->__pyx_int_119
#define __pyx_int_166 __pyx_mstate_global->__pyx_int_166
#define __pyx_int_180 __pyx_mstate_global->__pyx_int_180
#define __pyx_int_200 __pyx_mstate_global->__pyx_int_200
#define __pyx_int_333 __pyx_mstate_global->__pyx_int_333
#define __pyx_int_334 __pyx_mstate_global->__pyx_int_334
#define __pyx_int_335 __pyx_mstate_global->__pyx_int_335
#define __pyx_int_336 __pyx_mstate_global->__pyx_int_336
#define __pyx_int_344 __pyx_mstate_global->__pyx_int_344
#define __pyx_int_371 __pyx_mstate_global->__pyx_int_371
#define __pyx_int_372 __pyx_mstate_global->__pyx_int_372
#define __pyx_int_373 __pyx_mstate_global->__pyx_int_373
#define __pyx_int_374 __pyx_mstate_global->__pyx_int_374
#define __pyx_int_545 __pyx_mstate_global->__pyx_int_545
#define __pyx_int_546 __pyx_mstate_global->__pyx_int_546
#define __pyx_int_547 __pyx_mstate_global->__pyx_int_547
#define __pyx_int_548 __pyx_mstate_global->__pyx_int_548
#define __pyx_int_549 __pyx_mstate_global->__pyx_int_549
#define __pyx_int_550 __pyx_mstate_global->__pyx_int_550
#define __pyx_int_552 __pyx_mstate_global->__pyx_int_552
#define __pyx_int_553 __pyx_mstate_global->__pyx_int_553
#define __pyx_int_554 __pyx_mstate_global->__pyx_int_554
#define __pyx_int_852 __pyx_mstate_global->__pyx_int_852
#define __pyx_int_872 __pyx_mstate_global->__pyx_int_872
#define __pyx_int_873 __pyx_mstate_global->__pyx_int_873
#define __pyx_int_899 __pyx_mstate_global->__pyx_int_899
#define __pyx_int_902 __pyx_mstate_global->__pyx_int_902
#define __pyx_int_1000 __pyx_mstate_global->__pyx_int_1000
#define __pyx_int_1071 __pyx_mstate_global->__pyx_int_1071
#define __pyx_int_1072 __pyx_mstate_global->__pyx_int_1072
#define __pyx_int_1073 __pyx_mstate_global->__pyx_int_1073
#define __pyx_int_1074 __pyx_mstate_global->__pyx_int_1074
#define __pyx_int_1162 __pyx_mstate_global->__pyx_int_1162
#define __pyx_int_1180 __pyx_mstate_global->__pyx_int_1180
#define __pyx_int_1181 __pyx_mstate_global->__pyx_int_1181
#define __pyx_int_1185 __pyx_mstate_global->__pyx_int_1185
#define __pyx_int_1280 __pyx_mstate_global->__pyx_int_1280
#define __pyx_int_3000 __pyx_mstate_global->__pyx_int_3000
#define __pyx_int_10000 __pyx_mstate_global->__pyx_int_10000
#define __pyx_int_50000 __pyx_mstate_global->__pyx_int_50000
#define __pyx_int_100000 __pyx_mstate_global->__pyx_int_100000
#define __pyx_int_200000 __pyx_mstate_global->__pyx_int_200000
#define __pyx_int_500000 __pyx_mstate_global->__pyx_int_500000
#define __pyx_int_1000000 __pyx_mstate_global->__pyx_int_1000000
#define __pyx_int_1500000 __pyx_mstate_global->__pyx_int_1500000
#define __pyx_tuple__131 __pyx_mstate_global->__pyx_tuple__131
#define __pyx_tuple__133 __pyx_mstate_global->__pyx_tuple__133
#define __pyx_tuple__135 __pyx_mstate_global->__pyx_tuple__135
#define __pyx_tuple__137 __pyx_mstate_global->__pyx_tuple__137
#define __pyx_tuple__139 __pyx_mstate_global->__pyx_tuple__139
#define __pyx_tuple__141 __pyx_mstate_global->__pyx_tuple__141
#define __pyx_tuple__143 __pyx_mstate_global->__pyx_tuple__143
#define __pyx_tuple__145 __pyx_mstate_global->__pyx_tuple__145
#define __pyx_tuple__147 __pyx_mstate_global->__pyx_tuple__147
#define __pyx_tuple__148 __pyx_mstate_global->__pyx_tuple__148
/* #### Code section: module_code ### */

static PyMethodDef __pyx_methods[] = {
  {0, 0, 0, 0}
};
#ifndef CYTHON_SMALL_CODE
#if defined(__clang__)
    #define CYTHON_SMALL_CODE
#elif defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 3))
    #define CYTHON_SMALL_CODE __attribute__((cold))
#else
    #define CYTHON_SMALL_CODE
#endif
#endif
/* #### Code section: pystring_table ### */

static int __Pyx_CreateStringTabAndInitStrings(void) {
  __Pyx_StringTabEntry __pyx_string_tab[] = {
    {&__pyx_n_u_, __pyx_k_, sizeof(__pyx_k_), 0, 1, 0, 1},
    {&__pyx_kp_u_00_23, __pyx_k_00_23, sizeof(__pyx_k_00_23), 0, 1, 0, 0},
    {&__pyx_kp_u_150, __pyx_k_150, sizeof(__pyx_k_150), 0, 1, 0, 0},
    {&__pyx_kp_u_17, __pyx_k_17, sizeof(__pyx_k_17), 0, 1, 0, 0},
    {&__pyx_kp_u_2200, __pyx_k_2200, sizeof(__pyx_k_2200), 0, 1, 0, 0},
    {&__pyx_kp_u_5M, __pyx_k_5M, sizeof(__pyx_k_5M), 0, 1, 0, 0},
    {&__pyx_kp_u_75, __pyx_k_75, sizeof(__pyx_k_75), 0, 1, 0, 0},
    {&__pyx_kp_u_8x8coht211zh6l22dci2v7zgzav8zxs5, __pyx_k_8x8coht211zh6l22dci2v7zgzav8zxs5, sizeof(__pyx_k_8x8coht211zh6l22dci2v7zgzav8zxs5), 0, 1, 0, 0},
    {&__pyx_n_s_ADB_PATH, __pyx_k_ADB_PATH, sizeof(__pyx_k_ADB_PATH), 0, 0, 1, 1},
    {&__pyx_n_s_ANDROID_PATH, __pyx_k_ANDROID_PATH, sizeof(__pyx_k_ANDROID_PATH), 0, 0, 1, 1},
    {&__pyx_n_s_AUTO_RETURN_LOGIN, __pyx_k_AUTO_RETURN_LOGIN, sizeof(__pyx_k_AUTO_RETURN_LOGIN), 0, 0, 1, 1},
    {&__pyx_n_s_AUTO_UPLOAD_DATA, __pyx_k_AUTO_UPLOAD_DATA, sizeof(__pyx_k_AUTO_UPLOAD_DATA), 0, 0, 1, 1},
    {&__pyx_n_s_C1_CODE_WAIT_TIME, __pyx_k_C1_CODE_WAIT_TIME, sizeof(__pyx_k_C1_CODE_WAIT_TIME), 0, 0, 1, 1},
    {&__pyx_n_s_CAPTURE_MODE, __pyx_k_CAPTURE_MODE, sizeof(__pyx_k_CAPTURE_MODE), 0, 0, 1, 1},
    {&__pyx_n_u_CD, __pyx_k_CD, sizeof(__pyx_k_CD), 0, 1, 0, 1},
    {&__pyx_n_u_CD_2, __pyx_k_CD_2, sizeof(__pyx_k_CD_2), 0, 1, 0, 1},
    {&__pyx_n_u_CD_3, __pyx_k_CD_3, sizeof(__pyx_k_CD_3), 0, 1, 0, 1},
    {&__pyx_kp_u_CD_CD_CD, __pyx_k_CD_CD_CD, sizeof(__pyx_k_CD_CD_CD), 0, 1, 0, 0},
    {&__pyx_n_s_DEBUG_MODEL, __pyx_k_DEBUG_MODEL, sizeof(__pyx_k_DEBUG_MODEL), 0, 0, 1, 1},
    {&__pyx_n_s_DEVICE_MAX_LINE, __pyx_k_DEVICE_MAX_LINE, sizeof(__pyx_k_DEVICE_MAX_LINE), 0, 0, 1, 1},
    {&__pyx_n_s_DEVICE_MAX_LOG_SIZE, __pyx_k_DEVICE_MAX_LOG_SIZE, sizeof(__pyx_k_DEVICE_MAX_LOG_SIZE), 0, 0, 1, 1},
    {&__pyx_kp_u_D_nsha, __pyx_k_D_nsha, sizeof(__pyx_k_D_nsha), 0, 1, 0, 0},
    {&__pyx_n_s_ERROR_TEXT, __pyx_k_ERROR_TEXT, sizeof(__pyx_k_ERROR_TEXT), 0, 0, 1, 1},
    {&__pyx_n_s_FANYE_BEIBAO, __pyx_k_FANYE_BEIBAO, sizeof(__pyx_k_FANYE_BEIBAO), 0, 0, 1, 1},
    {&__pyx_n_s_FANYE_WUQI, __pyx_k_FANYE_WUQI, sizeof(__pyx_k_FANYE_WUQI), 0, 0, 1, 1},
    {&__pyx_n_s_FANYE_WUQI_HUANSHEN, __pyx_k_FANYE_WUQI_HUANSHEN, sizeof(__pyx_k_FANYE_WUQI_HUANSHEN), 0, 0, 1, 1},
    {&__pyx_n_s_HOST, __pyx_k_HOST, sizeof(__pyx_k_HOST), 0, 0, 1, 1},
    {&__pyx_n_s_LOG_PATH, __pyx_k_LOG_PATH, sizeof(__pyx_k_LOG_PATH), 0, 0, 1, 1},
    {&__pyx_n_u_LTAI5t8j4SZCrnBiFoEzXm7J, __pyx_k_LTAI5t8j4SZCrnBiFoEzXm7J, sizeof(__pyx_k_LTAI5t8j4SZCrnBiFoEzXm7J), 0, 1, 0, 1},
    {&__pyx_n_s_MAX_CLOTHES_COUNT, __pyx_k_MAX_CLOTHES_COUNT, sizeof(__pyx_k_MAX_CLOTHES_COUNT), 0, 0, 1, 1},
    {&__pyx_n_s_MAX_FASHIS_COUNT, __pyx_k_MAX_FASHIS_COUNT, sizeof(__pyx_k_MAX_FASHIS_COUNT), 0, 0, 1, 1},
    {&__pyx_n_s_MAX_FPS, __pyx_k_MAX_FPS, sizeof(__pyx_k_MAX_FPS), 0, 0, 1, 1},
    {&__pyx_n_s_MAX_ZUOQI, __pyx_k_MAX_ZUOQI, sizeof(__pyx_k_MAX_ZUOQI), 0, 0, 1, 1},
    {&__pyx_n_s_NEIGONG_CLICK_COUNT, __pyx_k_NEIGONG_CLICK_COUNT, sizeof(__pyx_k_NEIGONG_CLICK_COUNT), 0, 0, 1, 1},
    {&__pyx_n_s_NEIGONG_ZHUANGBEI_COUNT, __pyx_k_NEIGONG_ZHUANGBEI_COUNT, sizeof(__pyx_k_NEIGONG_ZHUANGBEI_COUNT), 0, 0, 1, 1},
    {&__pyx_n_s_OSS_ACCESS_KEY_ID, __pyx_k_OSS_ACCESS_KEY_ID, sizeof(__pyx_k_OSS_ACCESS_KEY_ID), 0, 0, 1, 1},
    {&__pyx_n_s_OSS_ACCESS_KEY_SECRET, __pyx_k_OSS_ACCESS_KEY_SECRET, sizeof(__pyx_k_OSS_ACCESS_KEY_SECRET), 0, 0, 1, 1},
    {&__pyx_n_s_PINGFEN_XIAXIAN, __pyx_k_PINGFEN_XIAXIAN, sizeof(__pyx_k_PINGFEN_XIAXIAN), 0, 0, 1, 1},
    {&__pyx_n_s_PROJECT_NAME, __pyx_k_PROJECT_NAME, sizeof(__pyx_k_PROJECT_NAME), 0, 0, 1, 1},
    {&__pyx_n_s_PROJECT_PATH, __pyx_k_PROJECT_PATH, sizeof(__pyx_k_PROJECT_PATH), 0, 0, 1, 1},
    {&__pyx_n_s_SCRCPY_PATH, __pyx_k_SCRCPY_PATH, sizeof(__pyx_k_SCRCPY_PATH), 0, 0, 1, 1},
    {&__pyx_n_s_SERVER_API_TOKEN, __pyx_k_SERVER_API_TOKEN, sizeof(__pyx_k_SERVER_API_TOKEN), 0, 0, 1, 1},
    {&__pyx_n_s_SKIP_ATTRI_NAME, __pyx_k_SKIP_ATTRI_NAME, sizeof(__pyx_k_SKIP_ATTRI_NAME), 0, 0, 1, 1},
    {&__pyx_n_s_TAP_DELAY, __pyx_k_TAP_DELAY, sizeof(__pyx_k_TAP_DELAY), 0, 0, 1, 1},
    {&__pyx_n_s_TSS_VALUE, __pyx_k_TSS_VALUE, sizeof(__pyx_k_TSS_VALUE), 0, 0, 1, 1},
    {&__pyx_n_s_VIDEO_BIT_RATE, __pyx_k_VIDEO_BIT_RATE, sizeof(__pyx_k_VIDEO_BIT_RATE), 0, 0, 1, 1},
    {&__pyx_n_s_WINDOW_MAX_SIZE, __pyx_k_WINDOW_MAX_SIZE, sizeof(__pyx_k_WINDOW_MAX_SIZE), 0, 0, 1, 1},
    {&__pyx_n_u_******************************, __pyx_k_******************************, sizeof(__pyx_k_******************************), 0, 1, 0, 1},
    {&__pyx_n_u__10, __pyx_k__10, sizeof(__pyx_k__10), 0, 1, 0, 1},
    {&__pyx_n_u__100, __pyx_k__100, sizeof(__pyx_k__100), 0, 1, 0, 1},
    {&__pyx_n_u__101, __pyx_k__101, sizeof(__pyx_k__101), 0, 1, 0, 1},
    {&__pyx_n_u__102, __pyx_k__102, sizeof(__pyx_k__102), 0, 1, 0, 1},
    {&__pyx_n_u__103, __pyx_k__103, sizeof(__pyx_k__103), 0, 1, 0, 1},
    {&__pyx_n_u__104, __pyx_k__104, sizeof(__pyx_k__104), 0, 1, 0, 1},
    {&__pyx_n_u__105, __pyx_k__105, sizeof(__pyx_k__105), 0, 1, 0, 1},
    {&__pyx_n_u__106, __pyx_k__106, sizeof(__pyx_k__106), 0, 1, 0, 1},
    {&__pyx_n_u__107, __pyx_k__107, sizeof(__pyx_k__107), 0, 1, 0, 1},
    {&__pyx_n_u__108, __pyx_k__108, sizeof(__pyx_k__108), 0, 1, 0, 1},
    {&__pyx_n_u__109, __pyx_k__109, sizeof(__pyx_k__109), 0, 1, 0, 1},
    {&__pyx_n_u__11, __pyx_k__11, sizeof(__pyx_k__11), 0, 1, 0, 1},
    {&__pyx_n_u__110, __pyx_k__110, sizeof(__pyx_k__110), 0, 1, 0, 1},
    {&__pyx_n_u__111, __pyx_k__111, sizeof(__pyx_k__111), 0, 1, 0, 1},
    {&__pyx_n_u__112, __pyx_k__112, sizeof(__pyx_k__112), 0, 1, 0, 1},
    {&__pyx_n_u__113, __pyx_k__113, sizeof(__pyx_k__113), 0, 1, 0, 1},
    {&__pyx_n_u__114, __pyx_k__114, sizeof(__pyx_k__114), 0, 1, 0, 1},
    {&__pyx_n_u__115, __pyx_k__115, sizeof(__pyx_k__115), 0, 1, 0, 1},
    {&__pyx_n_u__116, __pyx_k__116, sizeof(__pyx_k__116), 0, 1, 0, 1},
    {&__pyx_n_u__117, __pyx_k__117, sizeof(__pyx_k__117), 0, 1, 0, 1},
    {&__pyx_n_u__118, __pyx_k__118, sizeof(__pyx_k__118), 0, 1, 0, 1},
    {&__pyx_n_u__119, __pyx_k__119, sizeof(__pyx_k__119), 0, 1, 0, 1},
    {&__pyx_n_u__12, __pyx_k__12, sizeof(__pyx_k__12), 0, 1, 0, 1},
    {&__pyx_n_u__120, __pyx_k__120, sizeof(__pyx_k__120), 0, 1, 0, 1},
    {&__pyx_n_u__121, __pyx_k__121, sizeof(__pyx_k__121), 0, 1, 0, 1},
    {&__pyx_n_u__122, __pyx_k__122, sizeof(__pyx_k__122), 0, 1, 0, 1},
    {&__pyx_n_u__123, __pyx_k__123, sizeof(__pyx_k__123), 0, 1, 0, 1},
    {&__pyx_n_u__124, __pyx_k__124, sizeof(__pyx_k__124), 0, 1, 0, 1},
    {&__pyx_n_u__125, __pyx_k__125, sizeof(__pyx_k__125), 0, 1, 0, 1},
    {&__pyx_n_u__126, __pyx_k__126, sizeof(__pyx_k__126), 0, 1, 0, 1},
    {&__pyx_n_u__127, __pyx_k__127, sizeof(__pyx_k__127), 0, 1, 0, 1},
    {&__pyx_n_u__128, __pyx_k__128, sizeof(__pyx_k__128), 0, 1, 0, 1},
    {&__pyx_n_u__129, __pyx_k__129, sizeof(__pyx_k__129), 0, 1, 0, 1},
    {&__pyx_n_u__13, __pyx_k__13, sizeof(__pyx_k__13), 0, 1, 0, 1},
    {&__pyx_n_u__130, __pyx_k__130, sizeof(__pyx_k__130), 0, 1, 0, 1},
    {&__pyx_n_u__132, __pyx_k__132, sizeof(__pyx_k__132), 0, 1, 0, 1},
    {&__pyx_n_u__134, __pyx_k__134, sizeof(__pyx_k__134), 0, 1, 0, 1},
    {&__pyx_n_u__136, __pyx_k__136, sizeof(__pyx_k__136), 0, 1, 0, 1},
    {&__pyx_n_u__138, __pyx_k__138, sizeof(__pyx_k__138), 0, 1, 0, 1},
    {&__pyx_n_u__14, __pyx_k__14, sizeof(__pyx_k__14), 0, 1, 0, 1},
    {&__pyx_n_u__140, __pyx_k__140, sizeof(__pyx_k__140), 0, 1, 0, 1},
    {&__pyx_n_u__142, __pyx_k__142, sizeof(__pyx_k__142), 0, 1, 0, 1},
    {&__pyx_n_u__144, __pyx_k__144, sizeof(__pyx_k__144), 0, 1, 0, 1},
    {&__pyx_n_u__146, __pyx_k__146, sizeof(__pyx_k__146), 0, 1, 0, 1},
    {&__pyx_n_u__149, __pyx_k__149, sizeof(__pyx_k__149), 0, 1, 0, 1},
    {&__pyx_n_u__15, __pyx_k__15, sizeof(__pyx_k__15), 0, 1, 0, 1},
    {&__pyx_n_u__150, __pyx_k__150, sizeof(__pyx_k__150), 0, 1, 0, 1},
    {&__pyx_n_u__151, __pyx_k__151, sizeof(__pyx_k__151), 0, 1, 0, 1},
    {&__pyx_n_u__152, __pyx_k__152, sizeof(__pyx_k__152), 0, 1, 0, 1},
    {&__pyx_n_u__153, __pyx_k__153, sizeof(__pyx_k__153), 0, 1, 0, 1},
    {&__pyx_n_u__154, __pyx_k__154, sizeof(__pyx_k__154), 0, 1, 0, 1},
    {&__pyx_n_u__155, __pyx_k__155, sizeof(__pyx_k__155), 0, 1, 0, 1},
    {&__pyx_n_u__156, __pyx_k__156, sizeof(__pyx_k__156), 0, 1, 0, 1},
    {&__pyx_n_u__157, __pyx_k__157, sizeof(__pyx_k__157), 0, 1, 0, 1},
    {&__pyx_n_u__158, __pyx_k__158, sizeof(__pyx_k__158), 0, 1, 0, 1},
    {&__pyx_n_u__159, __pyx_k__159, sizeof(__pyx_k__159), 0, 1, 0, 1},
    {&__pyx_n_u__16, __pyx_k__16, sizeof(__pyx_k__16), 0, 1, 0, 1},
    {&__pyx_n_u__160, __pyx_k__160, sizeof(__pyx_k__160), 0, 1, 0, 1},
    {&__pyx_n_u__161, __pyx_k__161, sizeof(__pyx_k__161), 0, 1, 0, 1},
    {&__pyx_n_u__162, __pyx_k__162, sizeof(__pyx_k__162), 0, 1, 0, 1},
    {&__pyx_n_u__163, __pyx_k__163, sizeof(__pyx_k__163), 0, 1, 0, 1},
    {&__pyx_n_u__164, __pyx_k__164, sizeof(__pyx_k__164), 0, 1, 0, 1},
    {&__pyx_n_u__165, __pyx_k__165, sizeof(__pyx_k__165), 0, 1, 0, 1},
    {&__pyx_n_u__166, __pyx_k__166, sizeof(__pyx_k__166), 0, 1, 0, 1},
    {&__pyx_kp_u__167, __pyx_k__167, sizeof(__pyx_k__167), 0, 1, 0, 0},
    {&__pyx_n_u__168, __pyx_k__168, sizeof(__pyx_k__168), 0, 1, 0, 1},
    {&__pyx_n_u__169, __pyx_k__169, sizeof(__pyx_k__169), 0, 1, 0, 1},
    {&__pyx_n_u__17, __pyx_k__17, sizeof(__pyx_k__17), 0, 1, 0, 1},
    {&__pyx_n_u__170, __pyx_k__170, sizeof(__pyx_k__170), 0, 1, 0, 1},
    {&__pyx_n_u__171, __pyx_k__171, sizeof(__pyx_k__171), 0, 1, 0, 1},
    {&__pyx_n_u__172, __pyx_k__172, sizeof(__pyx_k__172), 0, 1, 0, 1},
    {&__pyx_n_u__173, __pyx_k__173, sizeof(__pyx_k__173), 0, 1, 0, 1},
    {&__pyx_n_u__174, __pyx_k__174, sizeof(__pyx_k__174), 0, 1, 0, 1},
    {&__pyx_n_u__175, __pyx_k__175, sizeof(__pyx_k__175), 0, 1, 0, 1},
    {&__pyx_n_u__176, __pyx_k__176, sizeof(__pyx_k__176), 0, 1, 0, 1},
    {&__pyx_n_u__177, __pyx_k__177, sizeof(__pyx_k__177), 0, 1, 0, 1},
    {&__pyx_n_u__178, __pyx_k__178, sizeof(__pyx_k__178), 0, 1, 0, 1},
    {&__pyx_n_u__179, __pyx_k__179, sizeof(__pyx_k__179), 0, 1, 0, 1},
    {&__pyx_n_u__18, __pyx_k__18, sizeof(__pyx_k__18), 0, 1, 0, 1},
    {&__pyx_n_u__180, __pyx_k__180, sizeof(__pyx_k__180), 0, 1, 0, 1},
    {&__pyx_n_u__181, __pyx_k__181, sizeof(__pyx_k__181), 0, 1, 0, 1},
    {&__pyx_n_u__182, __pyx_k__182, sizeof(__pyx_k__182), 0, 1, 0, 1},
    {&__pyx_n_u__183, __pyx_k__183, sizeof(__pyx_k__183), 0, 1, 0, 1},
    {&__pyx_n_u__184, __pyx_k__184, sizeof(__pyx_k__184), 0, 1, 0, 1},
    {&__pyx_n_u__185, __pyx_k__185, sizeof(__pyx_k__185), 0, 1, 0, 1},
    {&__pyx_n_u__186, __pyx_k__186, sizeof(__pyx_k__186), 0, 1, 0, 1},
    {&__pyx_n_u__187, __pyx_k__187, sizeof(__pyx_k__187), 0, 1, 0, 1},
    {&__pyx_n_u__188, __pyx_k__188, sizeof(__pyx_k__188), 0, 1, 0, 1},
    {&__pyx_n_u__189, __pyx_k__189, sizeof(__pyx_k__189), 0, 1, 0, 1},
    {&__pyx_n_u__19, __pyx_k__19, sizeof(__pyx_k__19), 0, 1, 0, 1},
    {&__pyx_n_u__190, __pyx_k__190, sizeof(__pyx_k__190), 0, 1, 0, 1},
    {&__pyx_n_u__191, __pyx_k__191, sizeof(__pyx_k__191), 0, 1, 0, 1},
    {&__pyx_n_u__192, __pyx_k__192, sizeof(__pyx_k__192), 0, 1, 0, 1},
    {&__pyx_n_u__193, __pyx_k__193, sizeof(__pyx_k__193), 0, 1, 0, 1},
    {&__pyx_n_u__194, __pyx_k__194, sizeof(__pyx_k__194), 0, 1, 0, 1},
    {&__pyx_n_u__195, __pyx_k__195, sizeof(__pyx_k__195), 0, 1, 0, 1},
    {&__pyx_n_u__196, __pyx_k__196, sizeof(__pyx_k__196), 0, 1, 0, 1},
    {&__pyx_n_u__197, __pyx_k__197, sizeof(__pyx_k__197), 0, 1, 0, 1},
    {&__pyx_n_u__198, __pyx_k__198, sizeof(__pyx_k__198), 0, 1, 0, 1},
    {&__pyx_n_u__199, __pyx_k__199, sizeof(__pyx_k__199), 0, 1, 0, 1},
    {&__pyx_n_u__2, __pyx_k__2, sizeof(__pyx_k__2), 0, 1, 0, 1},
    {&__pyx_n_u__20, __pyx_k__20, sizeof(__pyx_k__20), 0, 1, 0, 1},
    {&__pyx_n_u__200, __pyx_k__200, sizeof(__pyx_k__200), 0, 1, 0, 1},
    {&__pyx_n_u__201, __pyx_k__201, sizeof(__pyx_k__201), 0, 1, 0, 1},
    {&__pyx_n_u__202, __pyx_k__202, sizeof(__pyx_k__202), 0, 1, 0, 1},
    {&__pyx_n_u__203, __pyx_k__203, sizeof(__pyx_k__203), 0, 1, 0, 1},
    {&__pyx_n_u__204, __pyx_k__204, sizeof(__pyx_k__204), 0, 1, 0, 1},
    {&__pyx_n_u__205, __pyx_k__205, sizeof(__pyx_k__205), 0, 1, 0, 1},
    {&__pyx_n_u__206, __pyx_k__206, sizeof(__pyx_k__206), 0, 1, 0, 1},
    {&__pyx_n_u__207, __pyx_k__207, sizeof(__pyx_k__207), 0, 1, 0, 1},
    {&__pyx_n_u__208, __pyx_k__208, sizeof(__pyx_k__208), 0, 1, 0, 1},
    {&__pyx_n_u__209, __pyx_k__209, sizeof(__pyx_k__209), 0, 1, 0, 1},
    {&__pyx_n_u__21, __pyx_k__21, sizeof(__pyx_k__21), 0, 1, 0, 1},
    {&__pyx_n_u__210, __pyx_k__210, sizeof(__pyx_k__210), 0, 1, 0, 1},
    {&__pyx_n_u__211, __pyx_k__211, sizeof(__pyx_k__211), 0, 1, 0, 1},
    {&__pyx_n_u__212, __pyx_k__212, sizeof(__pyx_k__212), 0, 1, 0, 1},
    {&__pyx_n_u__213, __pyx_k__213, sizeof(__pyx_k__213), 0, 1, 0, 1},
    {&__pyx_n_u__214, __pyx_k__214, sizeof(__pyx_k__214), 0, 1, 0, 1},
    {&__pyx_n_u__215, __pyx_k__215, sizeof(__pyx_k__215), 0, 1, 0, 1},
    {&__pyx_n_u__216, __pyx_k__216, sizeof(__pyx_k__216), 0, 1, 0, 1},
    {&__pyx_n_u__217, __pyx_k__217, sizeof(__pyx_k__217), 0, 1, 0, 1},
    {&__pyx_n_u__218, __pyx_k__218, sizeof(__pyx_k__218), 0, 1, 0, 1},
    {&__pyx_n_u__219, __pyx_k__219, sizeof(__pyx_k__219), 0, 1, 0, 1},
    {&__pyx_n_u__22, __pyx_k__22, sizeof(__pyx_k__22), 0, 1, 0, 1},
    {&__pyx_n_u__220, __pyx_k__220, sizeof(__pyx_k__220), 0, 1, 0, 1},
    {&__pyx_n_u__221, __pyx_k__221, sizeof(__pyx_k__221), 0, 1, 0, 1},
    {&__pyx_n_u__222, __pyx_k__222, sizeof(__pyx_k__222), 0, 1, 0, 1},
    {&__pyx_n_u__223, __pyx_k__223, sizeof(__pyx_k__223), 0, 1, 0, 1},
    {&__pyx_n_u__224, __pyx_k__224, sizeof(__pyx_k__224), 0, 1, 0, 1},
    {&__pyx_n_u__225, __pyx_k__225, sizeof(__pyx_k__225), 0, 1, 0, 1},
    {&__pyx_n_u__226, __pyx_k__226, sizeof(__pyx_k__226), 0, 1, 0, 1},
    {&__pyx_n_u__227, __pyx_k__227, sizeof(__pyx_k__227), 0, 1, 0, 1},
    {&__pyx_n_u__228, __pyx_k__228, sizeof(__pyx_k__228), 0, 1, 0, 1},
    {&__pyx_n_u__229, __pyx_k__229, sizeof(__pyx_k__229), 0, 1, 0, 1},
    {&__pyx_n_u__23, __pyx_k__23, sizeof(__pyx_k__23), 0, 1, 0, 1},
    {&__pyx_n_u__230, __pyx_k__230, sizeof(__pyx_k__230), 0, 1, 0, 1},
    {&__pyx_n_u__231, __pyx_k__231, sizeof(__pyx_k__231), 0, 1, 0, 1},
    {&__pyx_n_u__232, __pyx_k__232, sizeof(__pyx_k__232), 0, 1, 0, 1},
    {&__pyx_n_u__233, __pyx_k__233, sizeof(__pyx_k__233), 0, 1, 0, 1},
    {&__pyx_n_u__234, __pyx_k__234, sizeof(__pyx_k__234), 0, 1, 0, 1},
    {&__pyx_n_u__235, __pyx_k__235, sizeof(__pyx_k__235), 0, 1, 0, 1},
    {&__pyx_n_u__236, __pyx_k__236, sizeof(__pyx_k__236), 0, 1, 0, 1},
    {&__pyx_n_u__237, __pyx_k__237, sizeof(__pyx_k__237), 0, 1, 0, 1},
    {&__pyx_n_u__238, __pyx_k__238, sizeof(__pyx_k__238), 0, 1, 0, 1},
    {&__pyx_n_u__239, __pyx_k__239, sizeof(__pyx_k__239), 0, 1, 0, 1},
    {&__pyx_n_u__24, __pyx_k__24, sizeof(__pyx_k__24), 0, 1, 0, 1},
    {&__pyx_n_u__240, __pyx_k__240, sizeof(__pyx_k__240), 0, 1, 0, 1},
    {&__pyx_n_u__241, __pyx_k__241, sizeof(__pyx_k__241), 0, 1, 0, 1},
    {&__pyx_n_u__242, __pyx_k__242, sizeof(__pyx_k__242), 0, 1, 0, 1},
    {&__pyx_n_u__243, __pyx_k__243, sizeof(__pyx_k__243), 0, 1, 0, 1},
    {&__pyx_n_u__244, __pyx_k__244, sizeof(__pyx_k__244), 0, 1, 0, 1},
    {&__pyx_n_u__245, __pyx_k__245, sizeof(__pyx_k__245), 0, 1, 0, 1},
    {&__pyx_n_u__246, __pyx_k__246, sizeof(__pyx_k__246), 0, 1, 0, 1},
    {&__pyx_n_u__247, __pyx_k__247, sizeof(__pyx_k__247), 0, 1, 0, 1},
    {&__pyx_n_u__248, __pyx_k__248, sizeof(__pyx_k__248), 0, 1, 0, 1},
    {&__pyx_n_u__249, __pyx_k__249, sizeof(__pyx_k__249), 0, 1, 0, 1},
    {&__pyx_n_u__25, __pyx_k__25, sizeof(__pyx_k__25), 0, 1, 0, 1},
    {&__pyx_n_u__250, __pyx_k__250, sizeof(__pyx_k__250), 0, 1, 0, 1},
    {&__pyx_n_u__251, __pyx_k__251, sizeof(__pyx_k__251), 0, 1, 0, 1},
    {&__pyx_n_u__252, __pyx_k__252, sizeof(__pyx_k__252), 0, 1, 0, 1},
    {&__pyx_n_u__253, __pyx_k__253, sizeof(__pyx_k__253), 0, 1, 0, 1},
    {&__pyx_n_u__254, __pyx_k__254, sizeof(__pyx_k__254), 0, 1, 0, 1},
    {&__pyx_n_u__255, __pyx_k__255, sizeof(__pyx_k__255), 0, 1, 0, 1},
    {&__pyx_n_u__256, __pyx_k__256, sizeof(__pyx_k__256), 0, 1, 0, 1},
    {&__pyx_n_u__257, __pyx_k__257, sizeof(__pyx_k__257), 0, 1, 0, 1},
    {&__pyx_n_u__258, __pyx_k__258, sizeof(__pyx_k__258), 0, 1, 0, 1},
    {&__pyx_n_u__259, __pyx_k__259, sizeof(__pyx_k__259), 0, 1, 0, 1},
    {&__pyx_n_u__26, __pyx_k__26, sizeof(__pyx_k__26), 0, 1, 0, 1},
    {&__pyx_n_u__260, __pyx_k__260, sizeof(__pyx_k__260), 0, 1, 0, 1},
    {&__pyx_n_u__261, __pyx_k__261, sizeof(__pyx_k__261), 0, 1, 0, 1},
    {&__pyx_n_u__262, __pyx_k__262, sizeof(__pyx_k__262), 0, 1, 0, 1},
    {&__pyx_n_u__263, __pyx_k__263, sizeof(__pyx_k__263), 0, 1, 0, 1},
    {&__pyx_n_u__264, __pyx_k__264, sizeof(__pyx_k__264), 0, 1, 0, 1},
    {&__pyx_n_u__265, __pyx_k__265, sizeof(__pyx_k__265), 0, 1, 0, 1},
    {&__pyx_n_u__266, __pyx_k__266, sizeof(__pyx_k__266), 0, 1, 0, 1},
    {&__pyx_n_u__267, __pyx_k__267, sizeof(__pyx_k__267), 0, 1, 0, 1},
    {&__pyx_n_u__268, __pyx_k__268, sizeof(__pyx_k__268), 0, 1, 0, 1},
    {&__pyx_n_u__269, __pyx_k__269, sizeof(__pyx_k__269), 0, 1, 0, 1},
    {&__pyx_n_u__27, __pyx_k__27, sizeof(__pyx_k__27), 0, 1, 0, 1},
    {&__pyx_n_u__270, __pyx_k__270, sizeof(__pyx_k__270), 0, 1, 0, 1},
    {&__pyx_n_u__271, __pyx_k__271, sizeof(__pyx_k__271), 0, 1, 0, 1},
    {&__pyx_n_u__272, __pyx_k__272, sizeof(__pyx_k__272), 0, 1, 0, 1},
    {&__pyx_n_u__273, __pyx_k__273, sizeof(__pyx_k__273), 0, 1, 0, 1},
    {&__pyx_n_u__274, __pyx_k__274, sizeof(__pyx_k__274), 0, 1, 0, 1},
    {&__pyx_kp_u__275, __pyx_k__275, sizeof(__pyx_k__275), 0, 1, 0, 0},
    {&__pyx_n_u__276, __pyx_k__276, sizeof(__pyx_k__276), 0, 1, 0, 1},
    {&__pyx_n_u__277, __pyx_k__277, sizeof(__pyx_k__277), 0, 1, 0, 1},
    {&__pyx_kp_u__278, __pyx_k__278, sizeof(__pyx_k__278), 0, 1, 0, 0},
    {&__pyx_kp_u__279, __pyx_k__279, sizeof(__pyx_k__279), 0, 1, 0, 0},
    {&__pyx_n_u__28, __pyx_k__28, sizeof(__pyx_k__28), 0, 1, 0, 1},
    {&__pyx_n_u__280, __pyx_k__280, sizeof(__pyx_k__280), 0, 1, 0, 1},
    {&__pyx_kp_u__281, __pyx_k__281, sizeof(__pyx_k__281), 0, 1, 0, 0},
    {&__pyx_n_u__282, __pyx_k__282, sizeof(__pyx_k__282), 0, 1, 0, 1},
    {&__pyx_kp_u__283, __pyx_k__283, sizeof(__pyx_k__283), 0, 1, 0, 0},
    {&__pyx_kp_u__284, __pyx_k__284, sizeof(__pyx_k__284), 0, 1, 0, 0},
    {&__pyx_kp_u__285, __pyx_k__285, sizeof(__pyx_k__285), 0, 1, 0, 0},
    {&__pyx_kp_u__286, __pyx_k__286, sizeof(__pyx_k__286), 0, 1, 0, 0},
    {&__pyx_n_u__287, __pyx_k__287, sizeof(__pyx_k__287), 0, 1, 0, 1},
    {&__pyx_kp_u__288, __pyx_k__288, sizeof(__pyx_k__288), 0, 1, 0, 0},
    {&__pyx_n_u__289, __pyx_k__289, sizeof(__pyx_k__289), 0, 1, 0, 1},
    {&__pyx_n_u__29, __pyx_k__29, sizeof(__pyx_k__29), 0, 1, 0, 1},
    {&__pyx_kp_u__290, __pyx_k__290, sizeof(__pyx_k__290), 0, 1, 0, 0},
    {&__pyx_kp_u__291, __pyx_k__291, sizeof(__pyx_k__291), 0, 1, 0, 0},
    {&__pyx_kp_u__292, __pyx_k__292, sizeof(__pyx_k__292), 0, 1, 0, 0},
    {&__pyx_kp_u__293, __pyx_k__293, sizeof(__pyx_k__293), 0, 1, 0, 0},
    {&__pyx_n_u__294, __pyx_k__294, sizeof(__pyx_k__294), 0, 1, 0, 1},
    {&__pyx_n_u__295, __pyx_k__295, sizeof(__pyx_k__295), 0, 1, 0, 1},
    {&__pyx_n_u__296, __pyx_k__296, sizeof(__pyx_k__296), 0, 1, 0, 1},
    {&__pyx_n_u__297, __pyx_k__297, sizeof(__pyx_k__297), 0, 1, 0, 1},
    {&__pyx_n_u__298, __pyx_k__298, sizeof(__pyx_k__298), 0, 1, 0, 1},
    {&__pyx_n_u__299, __pyx_k__299, sizeof(__pyx_k__299), 0, 1, 0, 1},
    {&__pyx_n_u__3, __pyx_k__3, sizeof(__pyx_k__3), 0, 1, 0, 1},
    {&__pyx_n_u__30, __pyx_k__30, sizeof(__pyx_k__30), 0, 1, 0, 1},
    {&__pyx_n_u__300, __pyx_k__300, sizeof(__pyx_k__300), 0, 1, 0, 1},
    {&__pyx_n_u__301, __pyx_k__301, sizeof(__pyx_k__301), 0, 1, 0, 1},
    {&__pyx_n_u__302, __pyx_k__302, sizeof(__pyx_k__302), 0, 1, 0, 1},
    {&__pyx_n_u__303, __pyx_k__303, sizeof(__pyx_k__303), 0, 1, 0, 1},
    {&__pyx_n_u__304, __pyx_k__304, sizeof(__pyx_k__304), 0, 1, 0, 1},
    {&__pyx_n_u__305, __pyx_k__305, sizeof(__pyx_k__305), 0, 1, 0, 1},
    {&__pyx_n_u__306, __pyx_k__306, sizeof(__pyx_k__306), 0, 1, 0, 1},
    {&__pyx_n_u__307, __pyx_k__307, sizeof(__pyx_k__307), 0, 1, 0, 1},
    {&__pyx_kp_u__308, __pyx_k__308, sizeof(__pyx_k__308), 0, 1, 0, 0},
    {&__pyx_n_u__309, __pyx_k__309, sizeof(__pyx_k__309), 0, 1, 0, 1},
    {&__pyx_n_u__31, __pyx_k__31, sizeof(__pyx_k__31), 0, 1, 0, 1},
    {&__pyx_n_u__310, __pyx_k__310, sizeof(__pyx_k__310), 0, 1, 0, 1},
    {&__pyx_n_u__311, __pyx_k__311, sizeof(__pyx_k__311), 0, 1, 0, 1},
    {&__pyx_n_u__312, __pyx_k__312, sizeof(__pyx_k__312), 0, 1, 0, 1},
    {&__pyx_n_u__313, __pyx_k__313, sizeof(__pyx_k__313), 0, 1, 0, 1},
    {&__pyx_n_u__314, __pyx_k__314, sizeof(__pyx_k__314), 0, 1, 0, 1},
    {&__pyx_n_u__315, __pyx_k__315, sizeof(__pyx_k__315), 0, 1, 0, 1},
    {&__pyx_n_u__316, __pyx_k__316, sizeof(__pyx_k__316), 0, 1, 0, 1},
    {&__pyx_n_u__317, __pyx_k__317, sizeof(__pyx_k__317), 0, 1, 0, 1},
    {&__pyx_n_u__318, __pyx_k__318, sizeof(__pyx_k__318), 0, 1, 0, 1},
    {&__pyx_n_u__319, __pyx_k__319, sizeof(__pyx_k__319), 0, 1, 0, 1},
    {&__pyx_n_u__32, __pyx_k__32, sizeof(__pyx_k__32), 0, 1, 0, 1},
    {&__pyx_n_u__320, __pyx_k__320, sizeof(__pyx_k__320), 0, 1, 0, 1},
    {&__pyx_n_u__321, __pyx_k__321, sizeof(__pyx_k__321), 0, 1, 0, 1},
    {&__pyx_n_u__322, __pyx_k__322, sizeof(__pyx_k__322), 0, 1, 0, 1},
    {&__pyx_n_u__323, __pyx_k__323, sizeof(__pyx_k__323), 0, 1, 0, 1},
    {&__pyx_n_u__324, __pyx_k__324, sizeof(__pyx_k__324), 0, 1, 0, 1},
    {&__pyx_n_u__325, __pyx_k__325, sizeof(__pyx_k__325), 0, 1, 0, 1},
    {&__pyx_n_u__326, __pyx_k__326, sizeof(__pyx_k__326), 0, 1, 0, 1},
    {&__pyx_n_u__327, __pyx_k__327, sizeof(__pyx_k__327), 0, 1, 0, 1},
    {&__pyx_n_u__328, __pyx_k__328, sizeof(__pyx_k__328), 0, 1, 0, 1},
    {&__pyx_n_u__329, __pyx_k__329, sizeof(__pyx_k__329), 0, 1, 0, 1},
    {&__pyx_n_u__33, __pyx_k__33, sizeof(__pyx_k__33), 0, 1, 0, 1},
    {&__pyx_n_u__330, __pyx_k__330, sizeof(__pyx_k__330), 0, 1, 0, 1},
    {&__pyx_n_u__331, __pyx_k__331, sizeof(__pyx_k__331), 0, 1, 0, 1},
    {&__pyx_n_u__332, __pyx_k__332, sizeof(__pyx_k__332), 0, 1, 0, 1},
    {&__pyx_n_u__333, __pyx_k__333, sizeof(__pyx_k__333), 0, 1, 0, 1},
    {&__pyx_n_u__334, __pyx_k__334, sizeof(__pyx_k__334), 0, 1, 0, 1},
    {&__pyx_n_u__335, __pyx_k__335, sizeof(__pyx_k__335), 0, 1, 0, 1},
    {&__pyx_n_u__336, __pyx_k__336, sizeof(__pyx_k__336), 0, 1, 0, 1},
    {&__pyx_n_u__337, __pyx_k__337, sizeof(__pyx_k__337), 0, 1, 0, 1},
    {&__pyx_n_u__338, __pyx_k__338, sizeof(__pyx_k__338), 0, 1, 0, 1},
    {&__pyx_n_u__339, __pyx_k__339, sizeof(__pyx_k__339), 0, 1, 0, 1},
    {&__pyx_n_u__34, __pyx_k__34, sizeof(__pyx_k__34), 0, 1, 0, 1},
    {&__pyx_n_u__340, __pyx_k__340, sizeof(__pyx_k__340), 0, 1, 0, 1},
    {&__pyx_n_u__341, __pyx_k__341, sizeof(__pyx_k__341), 0, 1, 0, 1},
    {&__pyx_n_u__342, __pyx_k__342, sizeof(__pyx_k__342), 0, 1, 0, 1},
    {&__pyx_n_u__343, __pyx_k__343, sizeof(__pyx_k__343), 0, 1, 0, 1},
    {&__pyx_n_u__344, __pyx_k__344, sizeof(__pyx_k__344), 0, 1, 0, 1},
    {&__pyx_n_u__345, __pyx_k__345, sizeof(__pyx_k__345), 0, 1, 0, 1},
    {&__pyx_n_u__346, __pyx_k__346, sizeof(__pyx_k__346), 0, 1, 0, 1},
    {&__pyx_n_u__347, __pyx_k__347, sizeof(__pyx_k__347), 0, 1, 0, 1},
    {&__pyx_n_u__348, __pyx_k__348, sizeof(__pyx_k__348), 0, 1, 0, 1},
    {&__pyx_n_u__349, __pyx_k__349, sizeof(__pyx_k__349), 0, 1, 0, 1},
    {&__pyx_kp_u__35, __pyx_k__35, sizeof(__pyx_k__35), 0, 1, 0, 0},
    {&__pyx_n_u__350, __pyx_k__350, sizeof(__pyx_k__350), 0, 1, 0, 1},
    {&__pyx_n_s__351, __pyx_k__351, sizeof(__pyx_k__351), 0, 0, 1, 1},
    {&__pyx_n_u__36, __pyx_k__36, sizeof(__pyx_k__36), 0, 1, 0, 1},
    {&__pyx_n_u__37, __pyx_k__37, sizeof(__pyx_k__37), 0, 1, 0, 1},
    {&__pyx_kp_u__38, __pyx_k__38, sizeof(__pyx_k__38), 0, 1, 0, 0},
    {&__pyx_kp_u__39, __pyx_k__39, sizeof(__pyx_k__39), 0, 1, 0, 0},
    {&__pyx_n_u__4, __pyx_k__4, sizeof(__pyx_k__4), 0, 1, 0, 1},
    {&__pyx_kp_u__40, __pyx_k__40, sizeof(__pyx_k__40), 0, 1, 0, 0},
    {&__pyx_kp_u__41, __pyx_k__41, sizeof(__pyx_k__41), 0, 1, 0, 0},
    {&__pyx_n_u__42, __pyx_k__42, sizeof(__pyx_k__42), 0, 1, 0, 1},
    {&__pyx_n_u__43, __pyx_k__43, sizeof(__pyx_k__43), 0, 1, 0, 1},
    {&__pyx_n_u__44, __pyx_k__44, sizeof(__pyx_k__44), 0, 1, 0, 1},
    {&__pyx_kp_u__45, __pyx_k__45, sizeof(__pyx_k__45), 0, 1, 0, 0},
    {&__pyx_n_u__46, __pyx_k__46, sizeof(__pyx_k__46), 0, 1, 0, 1},
    {&__pyx_n_u__47, __pyx_k__47, sizeof(__pyx_k__47), 0, 1, 0, 1},
    {&__pyx_kp_u__48, __pyx_k__48, sizeof(__pyx_k__48), 0, 1, 0, 0},
    {&__pyx_n_u__49, __pyx_k__49, sizeof(__pyx_k__49), 0, 1, 0, 1},
    {&__pyx_kp_u__5, __pyx_k__5, sizeof(__pyx_k__5), 0, 1, 0, 0},
    {&__pyx_n_u__50, __pyx_k__50, sizeof(__pyx_k__50), 0, 1, 0, 1},
    {&__pyx_kp_u__51, __pyx_k__51, sizeof(__pyx_k__51), 0, 1, 0, 0},
    {&__pyx_n_u__52, __pyx_k__52, sizeof(__pyx_k__52), 0, 1, 0, 1},
    {&__pyx_n_u__53, __pyx_k__53, sizeof(__pyx_k__53), 0, 1, 0, 1},
    {&__pyx_kp_u__54, __pyx_k__54, sizeof(__pyx_k__54), 0, 1, 0, 0},
    {&__pyx_kp_u__55, __pyx_k__55, sizeof(__pyx_k__55), 0, 1, 0, 0},
    {&__pyx_n_u__56, __pyx_k__56, sizeof(__pyx_k__56), 0, 1, 0, 1},
    {&__pyx_n_u__57, __pyx_k__57, sizeof(__pyx_k__57), 0, 1, 0, 1},
    {&__pyx_kp_u__58, __pyx_k__58, sizeof(__pyx_k__58), 0, 1, 0, 0},
    {&__pyx_n_u__59, __pyx_k__59, sizeof(__pyx_k__59), 0, 1, 0, 1},
    {&__pyx_n_u__6, __pyx_k__6, sizeof(__pyx_k__6), 0, 1, 0, 1},
    {&__pyx_n_u__60, __pyx_k__60, sizeof(__pyx_k__60), 0, 1, 0, 1},
    {&__pyx_kp_u__61, __pyx_k__61, sizeof(__pyx_k__61), 0, 1, 0, 0},
    {&__pyx_n_u__62, __pyx_k__62, sizeof(__pyx_k__62), 0, 1, 0, 1},
    {&__pyx_n_u__63, __pyx_k__63, sizeof(__pyx_k__63), 0, 1, 0, 1},
    {&__pyx_kp_u__64, __pyx_k__64, sizeof(__pyx_k__64), 0, 1, 0, 0},
    {&__pyx_n_u__65, __pyx_k__65, sizeof(__pyx_k__65), 0, 1, 0, 1},
    {&__pyx_n_u__66, __pyx_k__66, sizeof(__pyx_k__66), 0, 1, 0, 1},
    {&__pyx_n_u__67, __pyx_k__67, sizeof(__pyx_k__67), 0, 1, 0, 1},
    {&__pyx_n_u__68, __pyx_k__68, sizeof(__pyx_k__68), 0, 1, 0, 1},
    {&__pyx_n_u__69, __pyx_k__69, sizeof(__pyx_k__69), 0, 1, 0, 1},
    {&__pyx_kp_u__7, __pyx_k__7, sizeof(__pyx_k__7), 0, 1, 0, 0},
    {&__pyx_n_u__70, __pyx_k__70, sizeof(__pyx_k__70), 0, 1, 0, 1},
    {&__pyx_n_u__71, __pyx_k__71, sizeof(__pyx_k__71), 0, 1, 0, 1},
    {&__pyx_n_u__72, __pyx_k__72, sizeof(__pyx_k__72), 0, 1, 0, 1},
    {&__pyx_n_u__73, __pyx_k__73, sizeof(__pyx_k__73), 0, 1, 0, 1},
    {&__pyx_n_u__74, __pyx_k__74, sizeof(__pyx_k__74), 0, 1, 0, 1},
    {&__pyx_n_u__75, __pyx_k__75, sizeof(__pyx_k__75), 0, 1, 0, 1},
    {&__pyx_n_u__76, __pyx_k__76, sizeof(__pyx_k__76), 0, 1, 0, 1},
    {&__pyx_n_u__77, __pyx_k__77, sizeof(__pyx_k__77), 0, 1, 0, 1},
    {&__pyx_n_u__78, __pyx_k__78, sizeof(__pyx_k__78), 0, 1, 0, 1},
    {&__pyx_n_u__79, __pyx_k__79, sizeof(__pyx_k__79), 0, 1, 0, 1},
    {&__pyx_n_u__8, __pyx_k__8, sizeof(__pyx_k__8), 0, 1, 0, 1},
    {&__pyx_n_u__80, __pyx_k__80, sizeof(__pyx_k__80), 0, 1, 0, 1},
    {&__pyx_n_u__81, __pyx_k__81, sizeof(__pyx_k__81), 0, 1, 0, 1},
    {&__pyx_kp_u__82, __pyx_k__82, sizeof(__pyx_k__82), 0, 1, 0, 0},
    {&__pyx_n_u__83, __pyx_k__83, sizeof(__pyx_k__83), 0, 1, 0, 1},
    {&__pyx_kp_u__84, __pyx_k__84, sizeof(__pyx_k__84), 0, 1, 0, 0},
    {&__pyx_kp_u__85, __pyx_k__85, sizeof(__pyx_k__85), 0, 1, 0, 0},
    {&__pyx_n_u__86, __pyx_k__86, sizeof(__pyx_k__86), 0, 1, 0, 1},
    {&__pyx_n_u__87, __pyx_k__87, sizeof(__pyx_k__87), 0, 1, 0, 1},
    {&__pyx_n_u__88, __pyx_k__88, sizeof(__pyx_k__88), 0, 1, 0, 1},
    {&__pyx_n_u__89, __pyx_k__89, sizeof(__pyx_k__89), 0, 1, 0, 1},
    {&__pyx_kp_u__9, __pyx_k__9, sizeof(__pyx_k__9), 0, 1, 0, 0},
    {&__pyx_n_u__90, __pyx_k__90, sizeof(__pyx_k__90), 0, 1, 0, 1},
    {&__pyx_n_u__91, __pyx_k__91, sizeof(__pyx_k__91), 0, 1, 0, 1},
    {&__pyx_n_u__92, __pyx_k__92, sizeof(__pyx_k__92), 0, 1, 0, 1},
    {&__pyx_n_u__93, __pyx_k__93, sizeof(__pyx_k__93), 0, 1, 0, 1},
    {&__pyx_n_u__94, __pyx_k__94, sizeof(__pyx_k__94), 0, 1, 0, 1},
    {&__pyx_n_u__95, __pyx_k__95, sizeof(__pyx_k__95), 0, 1, 0, 1},
    {&__pyx_n_u__96, __pyx_k__96, sizeof(__pyx_k__96), 0, 1, 0, 1},
    {&__pyx_n_u__97, __pyx_k__97, sizeof(__pyx_k__97), 0, 1, 0, 1},
    {&__pyx_n_u__98, __pyx_k__98, sizeof(__pyx_k__98), 0, 1, 0, 1},
    {&__pyx_n_u__99, __pyx_k__99, sizeof(__pyx_k__99), 0, 1, 0, 1},
    {&__pyx_n_u_adb, __pyx_k_adb, sizeof(__pyx_k_adb), 0, 1, 0, 1},
    {&__pyx_n_u_albumPics, __pyx_k_albumPics, sizeof(__pyx_k_albumPics), 0, 1, 0, 1},
    {&__pyx_n_s_api_add_image_to_account, __pyx_k_api_add_image_to_account, sizeof(__pyx_k_api_add_image_to_account), 0, 0, 1, 1},
    {&__pyx_n_s_api_get_account, __pyx_k_api_get_account, sizeof(__pyx_k_api_get_account), 0, 0, 1, 1},
    {&__pyx_n_s_api_upload_images, __pyx_k_api_upload_images, sizeof(__pyx_k_api_upload_images), 0, 0, 1, 1},
    {&__pyx_n_s_app_id, __pyx_k_app_id, sizeof(__pyx_k_app_id), 0, 0, 1, 1},
    {&__pyx_n_u_attriName, __pyx_k_attriName, sizeof(__pyx_k_attriName), 0, 1, 0, 1},
    {&__pyx_n_s_black_attr_value, __pyx_k_black_attr_value, sizeof(__pyx_k_black_attr_value), 0, 0, 1, 1},
    {&__pyx_n_u_c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c, __pyx_k_c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c, sizeof(__pyx_k_c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c), 0, 1, 0, 1},
    {&__pyx_n_s_chongzhi_chenghao, __pyx_k_chongzhi_chenghao, sizeof(__pyx_k_chongzhi_chenghao), 0, 0, 1, 1},
    {&__pyx_n_s_cline_in_traceback, __pyx_k_cline_in_traceback, sizeof(__pyx_k_cline_in_traceback), 0, 0, 1, 1},
    {&__pyx_n_s_create_product_req, __pyx_k_create_product_req, sizeof(__pyx_k_create_product_req), 0, 0, 1, 1},
    {&__pyx_n_u_description, __pyx_k_description, sizeof(__pyx_k_description), 0, 1, 0, 1},
    {&__pyx_n_u_gameAccountQufu, __pyx_k_gameAccountQufu, sizeof(__pyx_k_gameAccountQufu), 0, 1, 0, 1},
    {&__pyx_n_u_gameCareinfoPhone, __pyx_k_gameCareinfoPhone, sizeof(__pyx_k_gameCareinfoPhone), 0, 1, 0, 1},
    {&__pyx_n_u_gameCareinfoPhone2, __pyx_k_gameCareinfoPhone2, sizeof(__pyx_k_gameCareinfoPhone2), 0, 1, 0, 1},
    {&__pyx_n_u_gameCareinfoTime, __pyx_k_gameCareinfoTime, sizeof(__pyx_k_gameCareinfoTime), 0, 1, 0, 1},
    {&__pyx_n_u_gameCareinfoVx, __pyx_k_gameCareinfoVx, sizeof(__pyx_k_gameCareinfoVx), 0, 1, 0, 1},
    {&__pyx_n_u_gameGoodsSaletype, __pyx_k_gameGoodsSaletype, sizeof(__pyx_k_gameGoodsSaletype), 0, 1, 0, 1},
    {&__pyx_n_u_gameGoodsYijia, __pyx_k_gameGoodsYijia, sizeof(__pyx_k_gameGoodsYijia), 0, 1, 0, 1},
    {&__pyx_kp_u_http_127_0_0_1_17001, __pyx_k_http_127_0_0_1_17001, sizeof(__pyx_k_http_127_0_0_1_17001), 0, 1, 0, 0},
    {&__pyx_kp_u_https_api2_kkzhw_com, __pyx_k_https_api2_kkzhw_com, sizeof(__pyx_k_https_api2_kkzhw_com), 0, 1, 0, 0},
    {&__pyx_kp_u_https_images2_kkzhw_com, __pyx_k_https_images2_kkzhw_com, sizeof(__pyx_k_https_images2_kkzhw_com), 0, 1, 0, 0},
    {&__pyx_n_s_image_server_url, __pyx_k_image_server_url, sizeof(__pyx_k_image_server_url), 0, 0, 1, 1},
    {&__pyx_n_s_lock_keywords, __pyx_k_lock_keywords, sizeof(__pyx_k_lock_keywords), 0, 0, 1, 1},
    {&__pyx_n_s_main, __pyx_k_main, sizeof(__pyx_k_main), 0, 0, 1, 1},
    {&__pyx_kp_u_mall_portal_openapi_record_add, __pyx_k_mall_portal_openapi_record_add, sizeof(__pyx_k_mall_portal_openapi_record_add), 0, 1, 0, 0},
    {&__pyx_kp_u_mall_portal_openapi_record_get, __pyx_k_mall_portal_openapi_record_get, sizeof(__pyx_k_mall_portal_openapi_record_get), 0, 1, 0, 0},
    {&__pyx_kp_u_mall_portal_openapi_record_uplo, __pyx_k_mall_portal_openapi_record_uplo, sizeof(__pyx_k_mall_portal_openapi_record_uplo), 0, 1, 0, 0},
    {&__pyx_n_s_name, __pyx_k_name, sizeof(__pyx_k_name), 0, 0, 1, 1},
    {&__pyx_n_u_nsha, __pyx_k_nsha, sizeof(__pyx_k_nsha), 0, 1, 0, 1},
    {&__pyx_n_u_originalPrice, __pyx_k_originalPrice, sizeof(__pyx_k_originalPrice), 0, 1, 0, 1},
    {&__pyx_n_u_pic, __pyx_k_pic, sizeof(__pyx_k_pic), 0, 1, 0, 1},
    {&__pyx_n_u_price, __pyx_k_price, sizeof(__pyx_k_price), 0, 1, 0, 1},
    {&__pyx_n_u_productAttributeCategoryId, __pyx_k_productAttributeCategoryId, sizeof(__pyx_k_productAttributeCategoryId), 0, 1, 0, 1},
    {&__pyx_n_u_productAttributeId, __pyx_k_productAttributeId, sizeof(__pyx_k_productAttributeId), 0, 1, 0, 1},
    {&__pyx_n_u_productAttributeValueList, __pyx_k_productAttributeValueList, sizeof(__pyx_k_productAttributeValueList), 0, 1, 0, 1},
    {&__pyx_n_u_productCategoryId, __pyx_k_productCategoryId, sizeof(__pyx_k_productCategoryId), 0, 1, 0, 1},
    {&__pyx_n_u_productCategoryName, __pyx_k_productCategoryName, sizeof(__pyx_k_productCategoryName), 0, 1, 0, 1},
    {&__pyx_n_u_pushType, __pyx_k_pushType, sizeof(__pyx_k_pushType), 0, 1, 0, 1},
    {&__pyx_n_u_qd561595395732389, __pyx_k_qd561595395732389, sizeof(__pyx_k_qd561595395732389), 0, 1, 0, 1},
    {&__pyx_n_u_scrcpy, __pyx_k_scrcpy, sizeof(__pyx_k_scrcpy), 0, 1, 0, 1},
    {&__pyx_n_u_searchType, __pyx_k_searchType, sizeof(__pyx_k_searchType), 0, 1, 0, 1},
    {&__pyx_n_s_secret_key, __pyx_k_secret_key, sizeof(__pyx_k_secret_key), 0, 0, 1, 1},
    {&__pyx_n_s_server_url, __pyx_k_server_url, sizeof(__pyx_k_server_url), 0, 0, 1, 1},
    {&__pyx_kp_u_static_logs, __pyx_k_static_logs, sizeof(__pyx_k_static_logs), 0, 1, 0, 0},
    {&__pyx_kp_u_storage_emulated_0_Download, __pyx_k_storage_emulated_0_Download, sizeof(__pyx_k_storage_emulated_0_Download), 0, 1, 0, 0},
    {&__pyx_n_s_test, __pyx_k_test, sizeof(__pyx_k_test), 0, 0, 1, 1},
    {&__pyx_n_u_type, __pyx_k_type, sizeof(__pyx_k_type), 0, 1, 0, 1},
    {&__pyx_n_u_value, __pyx_k_value, sizeof(__pyx_k_value), 0, 1, 0, 1},
    {&__pyx_n_s_zx_unlock_keywords, __pyx_k_zx_unlock_keywords, sizeof(__pyx_k_zx_unlock_keywords), 0, 0, 1, 1},
    {0, 0, 0, 0, 0, 0, 0}
  };
  return __Pyx_InitStrings(__pyx_string_tab);
}
/* #### Code section: cached_builtins ### */
static CYTHON_SMALL_CODE int __Pyx_InitCachedBuiltins(void) {
  return 0;
}
/* #### Code section: cached_constants ### */

static CYTHON_SMALL_CODE int __Pyx_InitCachedConstants(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_InitCachedConstants", 0);

  /* "configs.py":150
 * }
 * chongzhi_chenghao = [
 *     ("",200),             # <<<<<<<<<<<<<<
 *     ("",1000),
 *     ("",3000),
 */
  __pyx_tuple__131 = PyTuple_Pack(2, __pyx_n_u__130, __pyx_int_200); if (unlikely(!__pyx_tuple__131)) __PYX_ERR(0, 150, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__131);
  __Pyx_GIVEREF(__pyx_tuple__131);

  /* "configs.py":151
 * chongzhi_chenghao = [
 *     ("",200),
 *     ("",1000),             # <<<<<<<<<<<<<<
 *     ("",3000),
 *     ("",10000),
 */
  __pyx_tuple__133 = PyTuple_Pack(2, __pyx_n_u__132, __pyx_int_1000); if (unlikely(!__pyx_tuple__133)) __PYX_ERR(0, 151, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__133);
  __Pyx_GIVEREF(__pyx_tuple__133);

  /* "configs.py":152
 *     ("",200),
 *     ("",1000),
 *     ("",3000),             # <<<<<<<<<<<<<<
 *     ("",10000),
 *     ("",50000),
 */
  __pyx_tuple__135 = PyTuple_Pack(2, __pyx_n_u__134, __pyx_int_3000); if (unlikely(!__pyx_tuple__135)) __PYX_ERR(0, 152, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__135);
  __Pyx_GIVEREF(__pyx_tuple__135);

  /* "configs.py":153
 *     ("",1000),
 *     ("",3000),
 *     ("",10000),             # <<<<<<<<<<<<<<
 *     ("",50000),
 *     ("",100000),
 */
  __pyx_tuple__137 = PyTuple_Pack(2, __pyx_n_u__136, __pyx_int_10000); if (unlikely(!__pyx_tuple__137)) __PYX_ERR(0, 153, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__137);
  __Pyx_GIVEREF(__pyx_tuple__137);

  /* "configs.py":154
 *     ("",3000),
 *     ("",10000),
 *     ("",50000),             # <<<<<<<<<<<<<<
 *     ("",100000),
 *     ("",200000),
 */
  __pyx_tuple__139 = PyTuple_Pack(2, __pyx_n_u__138, __pyx_int_50000); if (unlikely(!__pyx_tuple__139)) __PYX_ERR(0, 154, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__139);
  __Pyx_GIVEREF(__pyx_tuple__139);

  /* "configs.py":155
 *     ("",10000),
 *     ("",50000),
 *     ("",100000),             # <<<<<<<<<<<<<<
 *     ("",200000),
 *     ("",500000),
 */
  __pyx_tuple__141 = PyTuple_Pack(2, __pyx_n_u__140, __pyx_int_100000); if (unlikely(!__pyx_tuple__141)) __PYX_ERR(0, 155, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__141);
  __Pyx_GIVEREF(__pyx_tuple__141);

  /* "configs.py":156
 *     ("",50000),
 *     ("",100000),
 *     ("",200000),             # <<<<<<<<<<<<<<
 *     ("",500000),
 *     ("",1000000),
 */
  __pyx_tuple__143 = PyTuple_Pack(2, __pyx_n_u__142, __pyx_int_200000); if (unlikely(!__pyx_tuple__143)) __PYX_ERR(0, 156, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__143);
  __Pyx_GIVEREF(__pyx_tuple__143);

  /* "configs.py":157
 *     ("",100000),
 *     ("",200000),
 *     ("",500000),             # <<<<<<<<<<<<<<
 *     ("",1000000),
 *     ("150",1500000)
 */
  __pyx_tuple__145 = PyTuple_Pack(2, __pyx_n_u__144, __pyx_int_500000); if (unlikely(!__pyx_tuple__145)) __PYX_ERR(0, 157, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__145);
  __Pyx_GIVEREF(__pyx_tuple__145);

  /* "configs.py":158
 *     ("",200000),
 *     ("",500000),
 *     ("",1000000),             # <<<<<<<<<<<<<<
 *     ("150",1500000)
 * ]
 */
  __pyx_tuple__147 = PyTuple_Pack(2, __pyx_n_u__146, __pyx_int_1000000); if (unlikely(!__pyx_tuple__147)) __PYX_ERR(0, 158, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__147);
  __Pyx_GIVEREF(__pyx_tuple__147);

  /* "configs.py":159
 *     ("",500000),
 *     ("",1000000),
 *     ("150",1500000)             # <<<<<<<<<<<<<<
 * ]
 * lock_keywords = {"", "", "", "","","","","", "","","",""}
 */
  __pyx_tuple__148 = PyTuple_Pack(2, __pyx_kp_u_150, __pyx_int_1500000); if (unlikely(!__pyx_tuple__148)) __PYX_ERR(0, 159, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__148);
  __Pyx_GIVEREF(__pyx_tuple__148);
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_RefNannyFinishContext();
  return -1;
}
/* #### Code section: init_constants ### */

static CYTHON_SMALL_CODE int __Pyx_InitConstants(void) {
  if (__Pyx_CreateStringTabAndInitStrings() < 0) __PYX_ERR(0, 1, __pyx_L1_error);
  __pyx_float_0_5 = PyFloat_FromDouble(0.5); if (unlikely(!__pyx_float_0_5)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_0 = PyInt_FromLong(0); if (unlikely(!__pyx_int_0)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1 = PyInt_FromLong(1); if (unlikely(!__pyx_int_1)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_2 = PyInt_FromLong(2); if (unlikely(!__pyx_int_2)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_3 = PyInt_FromLong(3); if (unlikely(!__pyx_int_3)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_4 = PyInt_FromLong(4); if (unlikely(!__pyx_int_4)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_5 = PyInt_FromLong(5); if (unlikely(!__pyx_int_5)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_6 = PyInt_FromLong(6); if (unlikely(!__pyx_int_6)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_9 = PyInt_FromLong(9); if (unlikely(!__pyx_int_9)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_15 = PyInt_FromLong(15); if (unlikely(!__pyx_int_15)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_30 = PyInt_FromLong(30); if (unlikely(!__pyx_int_30)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_32 = PyInt_FromLong(32); if (unlikely(!__pyx_int_32)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_62 = PyInt_FromLong(62); if (unlikely(!__pyx_int_62)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_96 = PyInt_FromLong(96); if (unlikely(!__pyx_int_96)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_97 = PyInt_FromLong(97); if (unlikely(!__pyx_int_97)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_100 = PyInt_FromLong(100); if (unlikely(!__pyx_int_100)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_105 = PyInt_FromLong(105); if (unlikely(!__pyx_int_105)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_106 = PyInt_FromLong(106); if (unlikely(!__pyx_int_106)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_107 = PyInt_FromLong(107); if (unlikely(!__pyx_int_107)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_108 = PyInt_FromLong(108); if (unlikely(!__pyx_int_108)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_113 = PyInt_FromLong(113); if (unlikely(!__pyx_int_113)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_114 = PyInt_FromLong(114); if (unlikely(!__pyx_int_114)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_115 = PyInt_FromLong(115); if (unlikely(!__pyx_int_115)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_116 = PyInt_FromLong(116); if (unlikely(!__pyx_int_116)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_118 = PyInt_FromLong(118); if (unlikely(!__pyx_int_118)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_119 = PyInt_FromLong(119); if (unlikely(!__pyx_int_119)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_166 = PyInt_FromLong(166); if (unlikely(!__pyx_int_166)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_180 = PyInt_FromLong(180); if (unlikely(!__pyx_int_180)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_200 = PyInt_FromLong(200); if (unlikely(!__pyx_int_200)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_333 = PyInt_FromLong(333); if (unlikely(!__pyx_int_333)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_334 = PyInt_FromLong(334); if (unlikely(!__pyx_int_334)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_335 = PyInt_FromLong(335); if (unlikely(!__pyx_int_335)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_336 = PyInt_FromLong(336); if (unlikely(!__pyx_int_336)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_344 = PyInt_FromLong(344); if (unlikely(!__pyx_int_344)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_371 = PyInt_FromLong(371); if (unlikely(!__pyx_int_371)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_372 = PyInt_FromLong(372); if (unlikely(!__pyx_int_372)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_373 = PyInt_FromLong(373); if (unlikely(!__pyx_int_373)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_374 = PyInt_FromLong(374); if (unlikely(!__pyx_int_374)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_545 = PyInt_FromLong(545); if (unlikely(!__pyx_int_545)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_546 = PyInt_FromLong(546); if (unlikely(!__pyx_int_546)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_547 = PyInt_FromLong(547); if (unlikely(!__pyx_int_547)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_548 = PyInt_FromLong(548); if (unlikely(!__pyx_int_548)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_549 = PyInt_FromLong(549); if (unlikely(!__pyx_int_549)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_550 = PyInt_FromLong(550); if (unlikely(!__pyx_int_550)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_552 = PyInt_FromLong(552); if (unlikely(!__pyx_int_552)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_553 = PyInt_FromLong(553); if (unlikely(!__pyx_int_553)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_554 = PyInt_FromLong(554); if (unlikely(!__pyx_int_554)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_852 = PyInt_FromLong(852); if (unlikely(!__pyx_int_852)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_872 = PyInt_FromLong(872); if (unlikely(!__pyx_int_872)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_873 = PyInt_FromLong(873); if (unlikely(!__pyx_int_873)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_899 = PyInt_FromLong(899); if (unlikely(!__pyx_int_899)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_902 = PyInt_FromLong(902); if (unlikely(!__pyx_int_902)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1000 = PyInt_FromLong(1000); if (unlikely(!__pyx_int_1000)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1071 = PyInt_FromLong(1071); if (unlikely(!__pyx_int_1071)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1072 = PyInt_FromLong(1072); if (unlikely(!__pyx_int_1072)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1073 = PyInt_FromLong(1073); if (unlikely(!__pyx_int_1073)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1074 = PyInt_FromLong(1074); if (unlikely(!__pyx_int_1074)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1162 = PyInt_FromLong(1162); if (unlikely(!__pyx_int_1162)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1180 = PyInt_FromLong(1180); if (unlikely(!__pyx_int_1180)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1181 = PyInt_FromLong(1181); if (unlikely(!__pyx_int_1181)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1185 = PyInt_FromLong(1185); if (unlikely(!__pyx_int_1185)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1280 = PyInt_FromLong(1280); if (unlikely(!__pyx_int_1280)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_3000 = PyInt_FromLong(3000); if (unlikely(!__pyx_int_3000)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_10000 = PyInt_FromLong(10000L); if (unlikely(!__pyx_int_10000)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_50000 = PyInt_FromLong(50000L); if (unlikely(!__pyx_int_50000)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_100000 = PyInt_FromLong(100000L); if (unlikely(!__pyx_int_100000)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_200000 = PyInt_FromLong(200000L); if (unlikely(!__pyx_int_200000)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_500000 = PyInt_FromLong(500000L); if (unlikely(!__pyx_int_500000)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1000000 = PyInt_FromLong(1000000L); if (unlikely(!__pyx_int_1000000)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_int_1500000 = PyInt_FromLong(1500000L); if (unlikely(!__pyx_int_1500000)) __PYX_ERR(0, 1, __pyx_L1_error)
  return 0;
  __pyx_L1_error:;
  return -1;
}
/* #### Code section: init_globals ### */

static CYTHON_SMALL_CODE int __Pyx_InitGlobals(void) {
  return 0;
}
/* #### Code section: init_module ### */

static CYTHON_SMALL_CODE int __Pyx_modinit_global_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_import_code(void); /*proto*/

static int __Pyx_modinit_global_init_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_global_init_code", 0);
  /*--- Global init code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_variable_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_export_code", 0);
  /*--- Variable export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_function_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_export_code", 0);
  /*--- Function export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_type_init_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_init_code", 0);
  /*--- Type init code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_type_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_import_code", 0);
  /*--- Type import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_variable_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_import_code", 0);
  /*--- Variable import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_function_import_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_import_code", 0);
  /*--- Function import code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}


#if PY_MAJOR_VERSION >= 3
#if CYTHON_PEP489_MULTI_PHASE_INIT
static PyObject* __pyx_pymod_create(PyObject *spec, PyModuleDef *def); /*proto*/
static int __pyx_pymod_exec_configs(PyObject* module); /*proto*/
static PyModuleDef_Slot __pyx_moduledef_slots[] = {
  {Py_mod_create, (void*)__pyx_pymod_create},
  {Py_mod_exec, (void*)__pyx_pymod_exec_configs},
  {0, NULL}
};
#endif

#ifdef __cplusplus
namespace {
  struct PyModuleDef __pyx_moduledef =
  #else
  static struct PyModuleDef __pyx_moduledef =
  #endif
  {
      PyModuleDef_HEAD_INIT,
      "configs",
      0, /* m_doc */
    #if CYTHON_PEP489_MULTI_PHASE_INIT
      0, /* m_size */
    #elif CYTHON_USE_MODULE_STATE
      sizeof(__pyx_mstate), /* m_size */
    #else
      -1, /* m_size */
    #endif
      __pyx_methods /* m_methods */,
    #if CYTHON_PEP489_MULTI_PHASE_INIT
      __pyx_moduledef_slots, /* m_slots */
    #else
      NULL, /* m_reload */
    #endif
    #if CYTHON_USE_MODULE_STATE
      __pyx_m_traverse, /* m_traverse */
      __pyx_m_clear, /* m_clear */
      NULL /* m_free */
    #else
      NULL, /* m_traverse */
      NULL, /* m_clear */
      NULL /* m_free */
    #endif
  };
  #ifdef __cplusplus
} /* anonymous namespace */
#endif
#endif

#ifndef CYTHON_NO_PYINIT_EXPORT
#define __Pyx_PyMODINIT_FUNC PyMODINIT_FUNC
#elif PY_MAJOR_VERSION < 3
#ifdef __cplusplus
#define __Pyx_PyMODINIT_FUNC extern "C" void
#else
#define __Pyx_PyMODINIT_FUNC void
#endif
#else
#ifdef __cplusplus
#define __Pyx_PyMODINIT_FUNC extern "C" PyObject *
#else
#define __Pyx_PyMODINIT_FUNC PyObject *
#endif
#endif


#if PY_MAJOR_VERSION < 3
__Pyx_PyMODINIT_FUNC initconfigs(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC initconfigs(void)
#else
__Pyx_PyMODINIT_FUNC PyInit_configs(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC PyInit_configs(void)
#if CYTHON_PEP489_MULTI_PHASE_INIT
{
  return PyModuleDef_Init(&__pyx_moduledef);
}
static CYTHON_SMALL_CODE int __Pyx_check_single_interpreter(void) {
    #if PY_VERSION_HEX >= 0x030700A1
    static PY_INT64_T main_interpreter_id = -1;
    PY_INT64_T current_id = PyInterpreterState_GetID(PyThreadState_Get()->interp);
    if (main_interpreter_id == -1) {
        main_interpreter_id = current_id;
        return (unlikely(current_id == -1)) ? -1 : 0;
    } else if (unlikely(main_interpreter_id != current_id))
    #else
    static PyInterpreterState *main_interpreter = NULL;
    PyInterpreterState *current_interpreter = PyThreadState_Get()->interp;
    if (!main_interpreter) {
        main_interpreter = current_interpreter;
    } else if (unlikely(main_interpreter != current_interpreter))
    #endif
    {
        PyErr_SetString(
            PyExc_ImportError,
            "Interpreter change detected - this module can only be loaded into one interpreter per process.");
        return -1;
    }
    return 0;
}
#if CYTHON_COMPILING_IN_LIMITED_API
static CYTHON_SMALL_CODE int __Pyx_copy_spec_to_module(PyObject *spec, PyObject *module, const char* from_name, const char* to_name, int allow_none)
#else
static CYTHON_SMALL_CODE int __Pyx_copy_spec_to_module(PyObject *spec, PyObject *moddict, const char* from_name, const char* to_name, int allow_none)
#endif
{
    PyObject *value = PyObject_GetAttrString(spec, from_name);
    int result = 0;
    if (likely(value)) {
        if (allow_none || value != Py_None) {
#if CYTHON_COMPILING_IN_LIMITED_API
            result = PyModule_AddObject(module, to_name, value);
#else
            result = PyDict_SetItemString(moddict, to_name, value);
#endif
        }
        Py_DECREF(value);
    } else if (PyErr_ExceptionMatches(PyExc_AttributeError)) {
        PyErr_Clear();
    } else {
        result = -1;
    }
    return result;
}
static CYTHON_SMALL_CODE PyObject* __pyx_pymod_create(PyObject *spec, PyModuleDef *def) {
    PyObject *module = NULL, *moddict, *modname;
    CYTHON_UNUSED_VAR(def);
    if (__Pyx_check_single_interpreter())
        return NULL;
    if (__pyx_m)
        return __Pyx_NewRef(__pyx_m);
    modname = PyObject_GetAttrString(spec, "name");
    if (unlikely(!modname)) goto bad;
    module = PyModule_NewObject(modname);
    Py_DECREF(modname);
    if (unlikely(!module)) goto bad;
#if CYTHON_COMPILING_IN_LIMITED_API
    moddict = module;
#else
    moddict = PyModule_GetDict(module);
    if (unlikely(!moddict)) goto bad;
#endif
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "loader", "__loader__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "origin", "__file__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "parent", "__package__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "submodule_search_locations", "__path__", 0) < 0)) goto bad;
    return module;
bad:
    Py_XDECREF(module);
    return NULL;
}


static CYTHON_SMALL_CODE int __pyx_pymod_exec_configs(PyObject *__pyx_pyinit_module)
#endif
#endif
{
  int stringtab_initialized = 0;
  #if CYTHON_USE_MODULE_STATE
  int pystate_addmodule_run = 0;
  #endif
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  PyObject *__pyx_t_4 = NULL;
  PyObject *__pyx_t_5 = NULL;
  PyObject *__pyx_t_6 = NULL;
  PyObject *__pyx_t_7 = NULL;
  PyObject *__pyx_t_8 = NULL;
  PyObject *__pyx_t_9 = NULL;
  PyObject *__pyx_t_10 = NULL;
  PyObject *__pyx_t_11 = NULL;
  PyObject *__pyx_t_12 = NULL;
  PyObject *__pyx_t_13 = NULL;
  PyObject *__pyx_t_14 = NULL;
  PyObject *__pyx_t_15 = NULL;
  PyObject *__pyx_t_16 = NULL;
  PyObject *__pyx_t_17 = NULL;
  PyObject *__pyx_t_18 = NULL;
  PyObject *__pyx_t_19 = NULL;
  PyObject *__pyx_t_20 = NULL;
  PyObject *__pyx_t_21 = NULL;
  PyObject *__pyx_t_22 = NULL;
  PyObject *__pyx_t_23 = NULL;
  PyObject *__pyx_t_24 = NULL;
  PyObject *__pyx_t_25 = NULL;
  PyObject *__pyx_t_26 = NULL;
  PyObject *__pyx_t_27 = NULL;
  PyObject *__pyx_t_28 = NULL;
  PyObject *__pyx_t_29 = NULL;
  PyObject *__pyx_t_30 = NULL;
  PyObject *__pyx_t_31 = NULL;
  PyObject *__pyx_t_32 = NULL;
  PyObject *__pyx_t_33 = NULL;
  PyObject *__pyx_t_34 = NULL;
  PyObject *__pyx_t_35 = NULL;
  PyObject *__pyx_t_36 = NULL;
  PyObject *__pyx_t_37 = NULL;
  PyObject *__pyx_t_38 = NULL;
  PyObject *__pyx_t_39 = NULL;
  PyObject *__pyx_t_40 = NULL;
  PyObject *__pyx_t_41 = NULL;
  PyObject *__pyx_t_42 = NULL;
  PyObject *__pyx_t_43 = NULL;
  PyObject *__pyx_t_44 = NULL;
  PyObject *__pyx_t_45 = NULL;
  PyObject *__pyx_t_46 = NULL;
  PyObject *__pyx_t_47 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannyDeclarations
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  if (__pyx_m) {
    if (__pyx_m == __pyx_pyinit_module) return 0;
    PyErr_SetString(PyExc_RuntimeError, "Module 'configs' has already been imported. Re-initialisation is not supported.");
    return -1;
  }
  #elif PY_MAJOR_VERSION >= 3
  if (__pyx_m) return __Pyx_NewRef(__pyx_m);
  #endif
  /*--- Module creation code ---*/
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  __pyx_m = __pyx_pyinit_module;
  Py_INCREF(__pyx_m);
  #else
  #if PY_MAJOR_VERSION < 3
  __pyx_m = Py_InitModule4("configs", __pyx_methods, 0, 0, PYTHON_API_VERSION); Py_XINCREF(__pyx_m);
  if (unlikely(!__pyx_m)) __PYX_ERR(0, 1, __pyx_L1_error)
  #elif CYTHON_USE_MODULE_STATE
  __pyx_t_1 = PyModule_Create(&__pyx_moduledef); if (unlikely(!__pyx_t_1)) __PYX_ERR(0, 1, __pyx_L1_error)
  {
    int add_module_result = PyState_AddModule(__pyx_t_1, &__pyx_moduledef);
    __pyx_t_1 = 0; /* transfer ownership from __pyx_t_1 to "configs" pseudovariable */
    if (unlikely((add_module_result < 0))) __PYX_ERR(0, 1, __pyx_L1_error)
    pystate_addmodule_run = 1;
  }
  #else
  __pyx_m = PyModule_Create(&__pyx_moduledef);
  if (unlikely(!__pyx_m)) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #endif
  CYTHON_UNUSED_VAR(__pyx_t_1);
  __pyx_d = PyModule_GetDict(__pyx_m); if (unlikely(!__pyx_d)) __PYX_ERR(0, 1, __pyx_L1_error)
  Py_INCREF(__pyx_d);
  __pyx_b = __Pyx_PyImport_AddModuleRef(__Pyx_BUILTIN_MODULE_NAME); if (unlikely(!__pyx_b)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_cython_runtime = __Pyx_PyImport_AddModuleRef((const char *) "cython_runtime"); if (unlikely(!__pyx_cython_runtime)) __PYX_ERR(0, 1, __pyx_L1_error)
  if (PyObject_SetAttrString(__pyx_m, "__builtins__", __pyx_b) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #if CYTHON_REFNANNY
__Pyx_RefNanny = __Pyx_RefNannyImportAPI("refnanny");
if (!__Pyx_RefNanny) {
  PyErr_Clear();
  __Pyx_RefNanny = __Pyx_RefNannyImportAPI("Cython.Runtime.refnanny");
  if (!__Pyx_RefNanny)
      Py_FatalError("failed to import 'refnanny' module");
}
#endif
  __Pyx_RefNannySetupContext("__Pyx_PyMODINIT_FUNC PyInit_configs(void)", 0);
  if (__Pyx_check_binary_version(__PYX_LIMITED_VERSION_HEX, __Pyx_get_runtime_version(), CYTHON_COMPILING_IN_LIMITED_API) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #ifdef __Pxy_PyFrame_Initialize_Offsets
  __Pxy_PyFrame_Initialize_Offsets();
  #endif
  __pyx_empty_tuple = PyTuple_New(0); if (unlikely(!__pyx_empty_tuple)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_empty_bytes = PyBytes_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_bytes)) __PYX_ERR(0, 1, __pyx_L1_error)
  __pyx_empty_unicode = PyUnicode_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_unicode)) __PYX_ERR(0, 1, __pyx_L1_error)
  #ifdef __Pyx_CyFunction_USED
  if (__pyx_CyFunction_init(__pyx_m) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_FusedFunction_USED
  if (__pyx_FusedFunction_init(__pyx_m) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Coroutine_USED
  if (__pyx_Coroutine_init(__pyx_m) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Generator_USED
  if (__pyx_Generator_init(__pyx_m) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_AsyncGen_USED
  if (__pyx_AsyncGen_init(__pyx_m) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_StopAsyncIteration_USED
  if (__pyx_StopAsyncIteration_init(__pyx_m) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  /*--- Library function declarations ---*/
  /*--- Threads initialization code ---*/
  #if defined(WITH_THREAD) && PY_VERSION_HEX < 0x030700F0 && defined(__PYX_FORCE_INIT_THREADS) && __PYX_FORCE_INIT_THREADS
  PyEval_InitThreads();
  #endif
  /*--- Initialize various global constants etc. ---*/
  if (__Pyx_InitConstants() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  stringtab_initialized = 1;
  if (__Pyx_InitGlobals() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #if PY_MAJOR_VERSION < 3 && (__PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT)
  if (__Pyx_init_sys_getdefaultencoding_params() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif
  if (__pyx_module_is_main_configs) {
    if (PyObject_SetAttr(__pyx_m, __pyx_n_s_name, __pyx_n_s_main) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  }
  #if PY_MAJOR_VERSION >= 3
  {
    PyObject *modules = PyImport_GetModuleDict(); if (unlikely(!modules)) __PYX_ERR(0, 1, __pyx_L1_error)
    if (!PyDict_GetItemString(modules, "configs")) {
      if (unlikely((PyDict_SetItemString(modules, "configs", __pyx_m) < 0))) __PYX_ERR(0, 1, __pyx_L1_error)
    }
  }
  #endif
  /*--- Builtin init code ---*/
  if (__Pyx_InitCachedBuiltins() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  /*--- Constants init code ---*/
  if (__Pyx_InitCachedConstants() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  /*--- Global type/function init code ---*/
  (void)__Pyx_modinit_global_init_code();
  (void)__Pyx_modinit_variable_export_code();
  (void)__Pyx_modinit_function_export_code();
  (void)__Pyx_modinit_type_init_code();
  (void)__Pyx_modinit_type_import_code();
  (void)__Pyx_modinit_variable_import_code();
  (void)__Pyx_modinit_function_import_code();
  /*--- Execution code ---*/
  #if defined(__Pyx_Generator_USED) || defined(__Pyx_Coroutine_USED)
  if (__Pyx_patch_abc() < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  #endif

  /* "configs.py":1
 * DEBUG_MODEL=1             # <<<<<<<<<<<<<<
 * PROJECT_NAME="nsha"
 * LOG_PATH="static/logs"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_DEBUG_MODEL, __pyx_int_1) < 0) __PYX_ERR(0, 1, __pyx_L1_error)

  /* "configs.py":2
 * DEBUG_MODEL=1
 * PROJECT_NAME="nsha"             # <<<<<<<<<<<<<<
 * LOG_PATH="static/logs"
 * HOST = "http://127.0.0.1:17001"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_PROJECT_NAME, __pyx_n_u_nsha) < 0) __PYX_ERR(0, 2, __pyx_L1_error)

  /* "configs.py":3
 * DEBUG_MODEL=1
 * PROJECT_NAME="nsha"
 * LOG_PATH="static/logs"             # <<<<<<<<<<<<<<
 * HOST = "http://127.0.0.1:17001"
 * PROJECT_PATH = "D:\\nsha"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_LOG_PATH, __pyx_kp_u_static_logs) < 0) __PYX_ERR(0, 3, __pyx_L1_error)

  /* "configs.py":4
 * PROJECT_NAME="nsha"
 * LOG_PATH="static/logs"
 * HOST = "http://127.0.0.1:17001"             # <<<<<<<<<<<<<<
 * PROJECT_PATH = "D:\\nsha"
 * ANDROID_PATH = "/storage/emulated/0/Download/"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_HOST, __pyx_kp_u_http_127_0_0_1_17001) < 0) __PYX_ERR(0, 4, __pyx_L1_error)

  /* "configs.py":5
 * LOG_PATH="static/logs"
 * HOST = "http://127.0.0.1:17001"
 * PROJECT_PATH = "D:\\nsha"             # <<<<<<<<<<<<<<
 * ANDROID_PATH = "/storage/emulated/0/Download/"
 * ADB_PATH = "adb"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_PROJECT_PATH, __pyx_kp_u_D_nsha) < 0) __PYX_ERR(0, 5, __pyx_L1_error)

  /* "configs.py":6
 * HOST = "http://127.0.0.1:17001"
 * PROJECT_PATH = "D:\\nsha"
 * ANDROID_PATH = "/storage/emulated/0/Download/"             # <<<<<<<<<<<<<<
 * ADB_PATH = "adb"
 * SCRCPY_PATH = "scrcpy"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_ANDROID_PATH, __pyx_kp_u_storage_emulated_0_Download) < 0) __PYX_ERR(0, 6, __pyx_L1_error)

  /* "configs.py":7
 * PROJECT_PATH = "D:\\nsha"
 * ANDROID_PATH = "/storage/emulated/0/Download/"
 * ADB_PATH = "adb"             # <<<<<<<<<<<<<<
 * SCRCPY_PATH = "scrcpy"
 * DEVICE_MAX_LOG_SIZE = 30    #  MB
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_ADB_PATH, __pyx_n_u_adb) < 0) __PYX_ERR(0, 7, __pyx_L1_error)

  /* "configs.py":8
 * ANDROID_PATH = "/storage/emulated/0/Download/"
 * ADB_PATH = "adb"
 * SCRCPY_PATH = "scrcpy"             # <<<<<<<<<<<<<<
 * DEVICE_MAX_LOG_SIZE = 30    #  MB
 * DEVICE_MAX_LINE = 100       #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_SCRCPY_PATH, __pyx_n_u_scrcpy) < 0) __PYX_ERR(0, 8, __pyx_L1_error)

  /* "configs.py":9
 * ADB_PATH = "adb"
 * SCRCPY_PATH = "scrcpy"
 * DEVICE_MAX_LOG_SIZE = 30    #  MB             # <<<<<<<<<<<<<<
 * DEVICE_MAX_LINE = 100       #
 * AUTO_UPLOAD_DATA = True         #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_DEVICE_MAX_LOG_SIZE, __pyx_int_30) < 0) __PYX_ERR(0, 9, __pyx_L1_error)

  /* "configs.py":10
 * SCRCPY_PATH = "scrcpy"
 * DEVICE_MAX_LOG_SIZE = 30    #  MB
 * DEVICE_MAX_LINE = 100       #             # <<<<<<<<<<<<<<
 * AUTO_UPLOAD_DATA = True         #
 * AUTO_RETURN_LOGIN = True        #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_DEVICE_MAX_LINE, __pyx_int_100) < 0) __PYX_ERR(0, 10, __pyx_L1_error)

  /* "configs.py":11
 * DEVICE_MAX_LOG_SIZE = 30    #  MB
 * DEVICE_MAX_LINE = 100       #
 * AUTO_UPLOAD_DATA = True         #             # <<<<<<<<<<<<<<
 * AUTO_RETURN_LOGIN = True        #
 * 
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_AUTO_UPLOAD_DATA, Py_True) < 0) __PYX_ERR(0, 11, __pyx_L1_error)

  /* "configs.py":12
 * DEVICE_MAX_LINE = 100       #
 * AUTO_UPLOAD_DATA = True         #
 * AUTO_RETURN_LOGIN = True        #             # <<<<<<<<<<<<<<
 * 
 * server_url = "https://api2.kkzhw.com"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_AUTO_RETURN_LOGIN, Py_True) < 0) __PYX_ERR(0, 12, __pyx_L1_error)

  /* "configs.py":14
 * AUTO_RETURN_LOGIN = True        #
 * 
 * server_url = "https://api2.kkzhw.com"             # <<<<<<<<<<<<<<
 * image_server_url = "https://images2.kkzhw.com/"
 * api_upload_images = "/mall-portal/openapi/record/upload_image"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_server_url, __pyx_kp_u_https_api2_kkzhw_com) < 0) __PYX_ERR(0, 14, __pyx_L1_error)

  /* "configs.py":15
 * 
 * server_url = "https://api2.kkzhw.com"
 * image_server_url = "https://images2.kkzhw.com/"             # <<<<<<<<<<<<<<
 * api_upload_images = "/mall-portal/openapi/record/upload_image"
 * api_add_image_to_account = "/mall-portal/openapi/record/add_account_images"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_image_server_url, __pyx_kp_u_https_images2_kkzhw_com) < 0) __PYX_ERR(0, 15, __pyx_L1_error)

  /* "configs.py":16
 * server_url = "https://api2.kkzhw.com"
 * image_server_url = "https://images2.kkzhw.com/"
 * api_upload_images = "/mall-portal/openapi/record/upload_image"             # <<<<<<<<<<<<<<
 * api_add_image_to_account = "/mall-portal/openapi/record/add_account_images"
 * api_get_account = "/mall-portal/openapi/record/get_nshaccount_info"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_api_upload_images, __pyx_kp_u_mall_portal_openapi_record_uplo) < 0) __PYX_ERR(0, 16, __pyx_L1_error)

  /* "configs.py":17
 * image_server_url = "https://images2.kkzhw.com/"
 * api_upload_images = "/mall-portal/openapi/record/upload_image"
 * api_add_image_to_account = "/mall-portal/openapi/record/add_account_images"             # <<<<<<<<<<<<<<
 * api_get_account = "/mall-portal/openapi/record/get_nshaccount_info"
 * app_id = "qd561595395732389"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_api_add_image_to_account, __pyx_kp_u_mall_portal_openapi_record_add) < 0) __PYX_ERR(0, 17, __pyx_L1_error)

  /* "configs.py":18
 * api_upload_images = "/mall-portal/openapi/record/upload_image"
 * api_add_image_to_account = "/mall-portal/openapi/record/add_account_images"
 * api_get_account = "/mall-portal/openapi/record/get_nshaccount_info"             # <<<<<<<<<<<<<<
 * app_id = "qd561595395732389"
 * secret_key = "8x8coht211zh6l22dci2v7zgzav8zxs5udy23iitt90"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_api_get_account, __pyx_kp_u_mall_portal_openapi_record_get) < 0) __PYX_ERR(0, 18, __pyx_L1_error)

  /* "configs.py":19
 * api_add_image_to_account = "/mall-portal/openapi/record/add_account_images"
 * api_get_account = "/mall-portal/openapi/record/get_nshaccount_info"
 * app_id = "qd561595395732389"             # <<<<<<<<<<<<<<
 * secret_key = "8x8coht211zh6l22dci2v7zgzav8zxs5udy23iitt90"
 * 
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_app_id, __pyx_n_u_qd561595395732389) < 0) __PYX_ERR(0, 19, __pyx_L1_error)

  /* "configs.py":20
 * api_get_account = "/mall-portal/openapi/record/get_nshaccount_info"
 * app_id = "qd561595395732389"
 * secret_key = "8x8coht211zh6l22dci2v7zgzav8zxs5udy23iitt90"             # <<<<<<<<<<<<<<
 * 
 * SERVER_API_TOKEN = "c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_secret_key, __pyx_kp_u_8x8coht211zh6l22dci2v7zgzav8zxs5) < 0) __PYX_ERR(0, 20, __pyx_L1_error)

  /* "configs.py":22
 * secret_key = "8x8coht211zh6l22dci2v7zgzav8zxs5udy23iitt90"
 * 
 * SERVER_API_TOKEN = "c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c"             # <<<<<<<<<<<<<<
 * 
 * OSS_ACCESS_KEY_ID = "LTAI5t8j4SZCrnBiFoEzXm7J"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_SERVER_API_TOKEN, __pyx_n_u_c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c) < 0) __PYX_ERR(0, 22, __pyx_L1_error)

  /* "configs.py":24
 * SERVER_API_TOKEN = "c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c"
 * 
 * OSS_ACCESS_KEY_ID = "LTAI5t8j4SZCrnBiFoEzXm7J"             # <<<<<<<<<<<<<<
 * OSS_ACCESS_KEY_SECRET = "******************************"
 * 
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_OSS_ACCESS_KEY_ID, __pyx_n_u_LTAI5t8j4SZCrnBiFoEzXm7J) < 0) __PYX_ERR(0, 24, __pyx_L1_error)

  /* "configs.py":25
 * 
 * OSS_ACCESS_KEY_ID = "LTAI5t8j4SZCrnBiFoEzXm7J"
 * OSS_ACCESS_KEY_SECRET = "******************************"             # <<<<<<<<<<<<<<
 * 
 * WINDOW_MAX_SIZE = 1280      #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_OSS_ACCESS_KEY_SECRET, __pyx_n_u_******************************) < 0) __PYX_ERR(0, 25, __pyx_L1_error)

  /* "configs.py":27
 * OSS_ACCESS_KEY_SECRET = "******************************"
 * 
 * WINDOW_MAX_SIZE = 1280      #             # <<<<<<<<<<<<<<
 * VIDEO_BIT_RATE = "5M"      #
 * MAX_FPS = 15                #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_WINDOW_MAX_SIZE, __pyx_int_1280) < 0) __PYX_ERR(0, 27, __pyx_L1_error)

  /* "configs.py":28
 * 
 * WINDOW_MAX_SIZE = 1280      #
 * VIDEO_BIT_RATE = "5M"      #             # <<<<<<<<<<<<<<
 * MAX_FPS = 15                #
 * 
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_VIDEO_BIT_RATE, __pyx_kp_u_5M) < 0) __PYX_ERR(0, 28, __pyx_L1_error)

  /* "configs.py":29
 * WINDOW_MAX_SIZE = 1280      #
 * VIDEO_BIT_RATE = "5M"      #
 * MAX_FPS = 15                #             # <<<<<<<<<<<<<<
 * 
 * C1_CODE_WAIT_TIME = 180     #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_MAX_FPS, __pyx_int_15) < 0) __PYX_ERR(0, 29, __pyx_L1_error)

  /* "configs.py":31
 * MAX_FPS = 15                #
 * 
 * C1_CODE_WAIT_TIME = 180     #             # <<<<<<<<<<<<<<
 * 
 * TAP_DELAY = 0.5
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_C1_CODE_WAIT_TIME, __pyx_int_180) < 0) __PYX_ERR(0, 31, __pyx_L1_error)

  /* "configs.py":33
 * C1_CODE_WAIT_TIME = 180     #
 * 
 * TAP_DELAY = 0.5             # <<<<<<<<<<<<<<
 * 
 * PINGFEN_XIAXIAN = "2200"        #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_TAP_DELAY, __pyx_float_0_5) < 0) __PYX_ERR(0, 33, __pyx_L1_error)

  /* "configs.py":35
 * TAP_DELAY = 0.5
 * 
 * PINGFEN_XIAXIAN = "2200"        #             # <<<<<<<<<<<<<<
 * NEIGONG_ZHUANGBEI_COUNT = 6     #
 * NEIGONG_CLICK_COUNT = 32        #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_PINGFEN_XIAXIAN, __pyx_kp_u_2200) < 0) __PYX_ERR(0, 35, __pyx_L1_error)

  /* "configs.py":36
 * 
 * PINGFEN_XIAXIAN = "2200"        #
 * NEIGONG_ZHUANGBEI_COUNT = 6     #             # <<<<<<<<<<<<<<
 * NEIGONG_CLICK_COUNT = 32        #
 * FANYE_BEIBAO = 5                #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_NEIGONG_ZHUANGBEI_COUNT, __pyx_int_6) < 0) __PYX_ERR(0, 36, __pyx_L1_error)

  /* "configs.py":37
 * PINGFEN_XIAXIAN = "2200"        #
 * NEIGONG_ZHUANGBEI_COUNT = 6     #
 * NEIGONG_CLICK_COUNT = 32        #             # <<<<<<<<<<<<<<
 * FANYE_BEIBAO = 5                #
 * FANYE_WUQI = 9                  #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_NEIGONG_CLICK_COUNT, __pyx_int_32) < 0) __PYX_ERR(0, 37, __pyx_L1_error)

  /* "configs.py":38
 * NEIGONG_ZHUANGBEI_COUNT = 6     #
 * NEIGONG_CLICK_COUNT = 32        #
 * FANYE_BEIBAO = 5                #             # <<<<<<<<<<<<<<
 * FANYE_WUQI = 9                  #
 * FANYE_WUQI_HUANSHEN = 4         #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_FANYE_BEIBAO, __pyx_int_5) < 0) __PYX_ERR(0, 38, __pyx_L1_error)

  /* "configs.py":39
 * NEIGONG_CLICK_COUNT = 32        #
 * FANYE_BEIBAO = 5                #
 * FANYE_WUQI = 9                  #             # <<<<<<<<<<<<<<
 * FANYE_WUQI_HUANSHEN = 4         #
 * 
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_FANYE_WUQI, __pyx_int_9) < 0) __PYX_ERR(0, 39, __pyx_L1_error)

  /* "configs.py":40
 * FANYE_BEIBAO = 5                #
 * FANYE_WUQI = 9                  #
 * FANYE_WUQI_HUANSHEN = 4         #             # <<<<<<<<<<<<<<
 * 
 * MAX_CLOTHES_COUNT = 32       #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_FANYE_WUQI_HUANSHEN, __pyx_int_4) < 0) __PYX_ERR(0, 40, __pyx_L1_error)

  /* "configs.py":42
 * FANYE_WUQI_HUANSHEN = 4         #
 * 
 * MAX_CLOTHES_COUNT = 32       #             # <<<<<<<<<<<<<<
 * MAX_FASHIS_COUNT = 32       #
 * MAX_ZUOQI = 32              #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_MAX_CLOTHES_COUNT, __pyx_int_32) < 0) __PYX_ERR(0, 42, __pyx_L1_error)

  /* "configs.py":43
 * 
 * MAX_CLOTHES_COUNT = 32       #
 * MAX_FASHIS_COUNT = 32       #             # <<<<<<<<<<<<<<
 * MAX_ZUOQI = 32              #
 * 
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_MAX_FASHIS_COUNT, __pyx_int_32) < 0) __PYX_ERR(0, 43, __pyx_L1_error)

  /* "configs.py":44
 * MAX_CLOTHES_COUNT = 32       #
 * MAX_FASHIS_COUNT = 32       #
 * MAX_ZUOQI = 32              #             # <<<<<<<<<<<<<<
 * 
 * CAPTURE_MODE = 1            #  0 (scrcpy) 1 (screencap)
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_MAX_ZUOQI, __pyx_int_32) < 0) __PYX_ERR(0, 44, __pyx_L1_error)

  /* "configs.py":46
 * MAX_ZUOQI = 32              #
 * 
 * CAPTURE_MODE = 1            #  0 (scrcpy) 1 (screencap)             # <<<<<<<<<<<<<<
 * 
 * SKIP_ATTRI_NAME = ",,,CD,CD,CD,,,,,,,,,,,,"
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_CAPTURE_MODE, __pyx_int_1) < 0) __PYX_ERR(0, 46, __pyx_L1_error)

  /* "configs.py":48
 * CAPTURE_MODE = 1            #  0 (scrcpy) 1 (screencap)
 * 
 * SKIP_ATTRI_NAME = ",,,CD,CD,CD,,,,,,,,,,,,"             # <<<<<<<<<<<<<<
 * 
 * #
 */
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_SKIP_ATTRI_NAME, __pyx_kp_u_CD_CD_CD) < 0) __PYX_ERR(0, 48, __pyx_L1_error)

  /* "configs.py":52
 * #
 * ERROR_TEXT = {
 *     "":"",             # <<<<<<<<<<<<<<
 *     "":"",
 *     "":"",
 */
  __pyx_t_2 = __Pyx_PyDict_NewPresized(53); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 52, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_, __pyx_n_u__2) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__3, __pyx_n_u__4) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__5, __pyx_n_u__6) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__7, __pyx_n_u__8) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__9, __pyx_n_u__10) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__11, __pyx_n_u__12) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__13, __pyx_n_u__14) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__15, __pyx_n_u__16) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__17, __pyx_n_u__16) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__18, __pyx_n_u__16) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__19, __pyx_n_u__20) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__21, __pyx_n_u__22) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__23, __pyx_n_u__24) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__25, __pyx_n_u__24) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__26, __pyx_n_u__24) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__27, __pyx_n_u__28) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__29, __pyx_n_u__30) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__31, __pyx_n_u__32) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__33, __pyx_n_u__34) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__35, __pyx_n_u__34) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__36, __pyx_n_u__37) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__38, __pyx_n_u__37) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__39, __pyx_n_u__37) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__40, __pyx_n_u__37) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__41, __pyx_n_u__37) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__42, __pyx_n_u__37) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__43, __pyx_n_u__44) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__45, __pyx_n_u__44) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__46, __pyx_n_u__47) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__48, __pyx_n_u__47) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__49, __pyx_n_u__50) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__51, __pyx_n_u__50) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__52, __pyx_n_u__53) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__54, __pyx_n_u__53) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__55, __pyx_n_u__56) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__57, __pyx_n_u__56) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__58, __pyx_n_u__59) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__60, __pyx_n_u__59) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__61, __pyx_n_u__62) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__63, __pyx_n_u__62) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__64, __pyx_n_u__65) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__66, __pyx_n_u__65) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__67, __pyx_n_u__22) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__68, __pyx_n_u__69) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__70, __pyx_n_u__69) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__71, __pyx_n_u__72) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__73, __pyx_n_u__74) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__75, __pyx_n_u__20) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__76, __pyx_n_u__77) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__78, __pyx_n_u__79) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__80, __pyx_n_u__81) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__82, __pyx_n_u__83) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__84, __pyx_kp_u__85) < 0) __PYX_ERR(0, 52, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_ERROR_TEXT, __pyx_t_2) < 0) __PYX_ERR(0, 51, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "configs.py":108
 * }
 * black_attr_value = {
 *     "":"",             # <<<<<<<<<<<<<<
 *     "":"",
 *     "":"",
 */
  __pyx_t_2 = __Pyx_PyDict_NewPresized(40); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 108, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__86, __pyx_n_u__87) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__88, __pyx_n_u__87) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__89, __pyx_n_u__87) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__90, __pyx_n_u__87) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__91, __pyx_n_u__87) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__92, __pyx_n_u__87) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__93, __pyx_n_u__87) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__94, __pyx_n_u__87) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__95, __pyx_n_u__96) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__97, __pyx_n_u__96) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__98, __pyx_n_u__96) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__99, __pyx_n_u__96) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__100, __pyx_n_u__96) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__101, __pyx_n_u__96) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__102, __pyx_n_u__96) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__103, __pyx_n_u__96) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__104, __pyx_n_u__105) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__106, __pyx_n_u__105) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__107, __pyx_n_u__105) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__108, __pyx_n_u__105) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__109, __pyx_n_u__105) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__110, __pyx_n_u__105) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__111, __pyx_n_u__105) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__112, __pyx_n_u__105) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__113, __pyx_n_u__114) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__115, __pyx_n_u__114) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__116, __pyx_n_u__114) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__117, __pyx_n_u__114) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__118, __pyx_n_u__114) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__119, __pyx_n_u__114) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__120, __pyx_n_u__114) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__121, __pyx_n_u__114) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__122, __pyx_n_u__123) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__124, __pyx_n_u__123) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__125, __pyx_n_u__123) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__126, __pyx_n_u__123) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__127, __pyx_n_u__123) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__72, __pyx_n_u__123) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__128, __pyx_n_u__123) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__129, __pyx_n_u__123) < 0) __PYX_ERR(0, 108, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_black_attr_value, __pyx_t_2) < 0) __PYX_ERR(0, 107, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "configs.py":149
 *     "":""
 * }
 * chongzhi_chenghao = [             # <<<<<<<<<<<<<<
 *     ("",200),
 *     ("",1000),
 */
  __pyx_t_2 = PyList_New(10); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 149, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_INCREF(__pyx_tuple__131);
  __Pyx_GIVEREF(__pyx_tuple__131);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_2, 0, __pyx_tuple__131)) __PYX_ERR(0, 149, __pyx_L1_error);
  __Pyx_INCREF(__pyx_tuple__133);
  __Pyx_GIVEREF(__pyx_tuple__133);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_2, 1, __pyx_tuple__133)) __PYX_ERR(0, 149, __pyx_L1_error);
  __Pyx_INCREF(__pyx_tuple__135);
  __Pyx_GIVEREF(__pyx_tuple__135);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_2, 2, __pyx_tuple__135)) __PYX_ERR(0, 149, __pyx_L1_error);
  __Pyx_INCREF(__pyx_tuple__137);
  __Pyx_GIVEREF(__pyx_tuple__137);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_2, 3, __pyx_tuple__137)) __PYX_ERR(0, 149, __pyx_L1_error);
  __Pyx_INCREF(__pyx_tuple__139);
  __Pyx_GIVEREF(__pyx_tuple__139);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_2, 4, __pyx_tuple__139)) __PYX_ERR(0, 149, __pyx_L1_error);
  __Pyx_INCREF(__pyx_tuple__141);
  __Pyx_GIVEREF(__pyx_tuple__141);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_2, 5, __pyx_tuple__141)) __PYX_ERR(0, 149, __pyx_L1_error);
  __Pyx_INCREF(__pyx_tuple__143);
  __Pyx_GIVEREF(__pyx_tuple__143);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_2, 6, __pyx_tuple__143)) __PYX_ERR(0, 149, __pyx_L1_error);
  __Pyx_INCREF(__pyx_tuple__145);
  __Pyx_GIVEREF(__pyx_tuple__145);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_2, 7, __pyx_tuple__145)) __PYX_ERR(0, 149, __pyx_L1_error);
  __Pyx_INCREF(__pyx_tuple__147);
  __Pyx_GIVEREF(__pyx_tuple__147);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_2, 8, __pyx_tuple__147)) __PYX_ERR(0, 149, __pyx_L1_error);
  __Pyx_INCREF(__pyx_tuple__148);
  __Pyx_GIVEREF(__pyx_tuple__148);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_2, 9, __pyx_tuple__148)) __PYX_ERR(0, 149, __pyx_L1_error);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_chongzhi_chenghao, __pyx_t_2) < 0) __PYX_ERR(0, 149, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "configs.py":161
 *     ("150",1500000)
 * ]
 * lock_keywords = {"", "", "", "","","","","", "","","",""}             # <<<<<<<<<<<<<<
 * zx_unlock_keywords = {"","",""}
 * TSS_VALUE = {
 */
  __pyx_t_2 = PySet_New(0); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 161, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PySet_Add(__pyx_t_2, __pyx_n_u__149) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__150) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__151) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__152) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__153) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__154) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__155) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__156) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__157) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__158) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__159) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__160) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_lock_keywords, __pyx_t_2) < 0) __PYX_ERR(0, 161, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "configs.py":162
 * ]
 * lock_keywords = {"", "", "", "","","","","", "","","",""}
 * zx_unlock_keywords = {"","",""}             # <<<<<<<<<<<<<<
 * TSS_VALUE = {
 *     "": 0,
 */
  __pyx_t_2 = PySet_New(0); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 162, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PySet_Add(__pyx_t_2, __pyx_n_u__161) < 0) __PYX_ERR(0, 162, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__162) < 0) __PYX_ERR(0, 162, __pyx_L1_error)
  if (PySet_Add(__pyx_t_2, __pyx_n_u__163) < 0) __PYX_ERR(0, 162, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_zx_unlock_keywords, __pyx_t_2) < 0) __PYX_ERR(0, 162, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "configs.py":164
 * zx_unlock_keywords = {"","",""}
 * TSS_VALUE = {
 *     "": 0,             # <<<<<<<<<<<<<<
 *     "": 0,
 *     "": 0,
 */
  __pyx_t_2 = __Pyx_PyDict_NewPresized(215); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 164, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__164, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__165, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__166, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__167, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__168, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__169, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__170, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__171, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__172, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__173, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__174, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__175, __pyx_int_62) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__176, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__177, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__178, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__179, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__180, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__181, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__182, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__183, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__184, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__185, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__186, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__187, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__188, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__189, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__190, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__191, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__192, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__193, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__194, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__195, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__196, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__197, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__198, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__199, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__200, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__201, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__202, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__203, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__204, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__205, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__206, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__207, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__208, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__209, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__210, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__2, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__211, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__212, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__213, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__214, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__215, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__216, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__217, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__218, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__219, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__220, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__221, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__222, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__223, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__224, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__225, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__226, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__227, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__228, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__229, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__230, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__231, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__232, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__233, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__234, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__235, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__236, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__237, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__238, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__239, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__240, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__241, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__242, __pyx_int_4) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__243, __pyx_int_4) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__244, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__245, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__246, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__14, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__74, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__247, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__79, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__248, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__249, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__250, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__251, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__252, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__253, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__20, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__254, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__255, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__256, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__22, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__69, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__257, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__24, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__258, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__28, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__259, __pyx_int_4) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__260, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__261, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__262, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__263, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__264, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__81, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__265, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__266, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__267, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__83, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__268, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__269, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__270, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__271, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__77, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__272, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__273, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__274, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__275, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__85, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__276, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__277, __pyx_int_3) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__278, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__279, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__280, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__281, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__282, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__283, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__284, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__285, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__9, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__5, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__10, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__6, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__7, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__286, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__287, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__288, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__289, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__290, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__291, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__292, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_kp_u__293, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__86, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__88, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__89, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__90, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__91, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__92, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__93, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__94, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__87, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__113, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__115, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__116, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__117, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__118, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__119, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__120, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__121, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__114, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__104, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__106, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__107, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__108, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__109, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__110, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__111, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__112, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__105, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__122, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__124, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__125, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__126, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__127, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__72, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__128, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__129, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__123, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__95, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__97, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__98, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__99, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__100, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__101, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__102, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__103, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__96, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__294, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__295, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__296, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__297, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__298, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__299, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__300, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__301, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__302, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__303, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__304, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__305, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__47, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__306, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__37, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__44, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__50, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__53, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__65, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__34, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__307, __pyx_int_0) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u__62, __pyx_int_1) < 0) __PYX_ERR(0, 164, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_TSS_VALUE, __pyx_t_2) < 0) __PYX_ERR(0, 163, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "configs.py":391
 * 
 * create_product_req = {
 *     "gameCareinfoPhone2": "",             # <<<<<<<<<<<<<<
 *     "pushType": 1,
 *     "gameAccountQufu": "",
 */
  __pyx_t_2 = __Pyx_PyDict_NewPresized(17); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 391, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_gameCareinfoPhone2, __pyx_kp_u__308) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_pushType, __pyx_int_1) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_gameAccountQufu, __pyx_kp_u__308) < 0) __PYX_ERR(0, 391, __pyx_L1_error)

  /* "configs.py":396
 *     "productAttributeValueList": [
 *         {
 *             "productAttributeId": 114,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_3 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_3)) __PYX_ERR(0, 396, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_3);
  if (PyDict_SetItem(__pyx_t_3, __pyx_n_u_productAttributeId, __pyx_int_114) < 0) __PYX_ERR(0, 396, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_3, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 396, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_3, __pyx_n_u_attriName, __pyx_n_u__309) < 0) __PYX_ERR(0, 396, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_3, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 396, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_3, __pyx_n_u_searchType, __pyx_int_1) < 0) __PYX_ERR(0, 396, __pyx_L1_error)

  /* "configs.py":403
 *         },
 *         {
 *             "productAttributeId": 108,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_4 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_4)) __PYX_ERR(0, 403, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_4);
  if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_productAttributeId, __pyx_int_108) < 0) __PYX_ERR(0, 403, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 403, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_attriName, __pyx_n_u__310) < 0) __PYX_ERR(0, 403, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 403, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_4, __pyx_n_u_searchType, __pyx_int_1) < 0) __PYX_ERR(0, 403, __pyx_L1_error)

  /* "configs.py":410
 *         },
 *         {
 *             "productAttributeId": 105,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_5 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_5)) __PYX_ERR(0, 410, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_5);
  if (PyDict_SetItem(__pyx_t_5, __pyx_n_u_productAttributeId, __pyx_int_105) < 0) __PYX_ERR(0, 410, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_5, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 410, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_5, __pyx_n_u_attriName, __pyx_n_u__311) < 0) __PYX_ERR(0, 410, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_5, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 410, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_5, __pyx_n_u_searchType, __pyx_int_1) < 0) __PYX_ERR(0, 410, __pyx_L1_error)

  /* "configs.py":417
 *         },
 *         {
 *             "productAttributeId": 852,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "CD",
 */
  __pyx_t_6 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_6)) __PYX_ERR(0, 417, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_6);
  if (PyDict_SetItem(__pyx_t_6, __pyx_n_u_productAttributeId, __pyx_int_852) < 0) __PYX_ERR(0, 417, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_6, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 417, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_6, __pyx_n_u_attriName, __pyx_n_u_CD) < 0) __PYX_ERR(0, 417, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_6, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 417, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_6, __pyx_n_u_searchType, __pyx_int_1) < 0) __PYX_ERR(0, 417, __pyx_L1_error)

  /* "configs.py":424
 *         },
 *         {
 *             "productAttributeId": 107,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "CD",
 */
  __pyx_t_7 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_7)) __PYX_ERR(0, 424, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_7);
  if (PyDict_SetItem(__pyx_t_7, __pyx_n_u_productAttributeId, __pyx_int_107) < 0) __PYX_ERR(0, 424, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_7, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 424, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_7, __pyx_n_u_attriName, __pyx_n_u_CD_2) < 0) __PYX_ERR(0, 424, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_7, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 424, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_7, __pyx_n_u_searchType, __pyx_int_1) < 0) __PYX_ERR(0, 424, __pyx_L1_error)

  /* "configs.py":431
 *         },
 *         {
 *             "productAttributeId": 106,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "CD",
 */
  __pyx_t_8 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_8)) __PYX_ERR(0, 431, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_8);
  if (PyDict_SetItem(__pyx_t_8, __pyx_n_u_productAttributeId, __pyx_int_106) < 0) __PYX_ERR(0, 431, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_8, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 431, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_8, __pyx_n_u_attriName, __pyx_n_u_CD_3) < 0) __PYX_ERR(0, 431, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_8, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 431, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_8, __pyx_n_u_searchType, __pyx_int_1) < 0) __PYX_ERR(0, 431, __pyx_L1_error)

  /* "configs.py":438
 *         },
 *         {
 *             "productAttributeId": 872,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_9 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_9)) __PYX_ERR(0, 438, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_9);
  if (PyDict_SetItem(__pyx_t_9, __pyx_n_u_productAttributeId, __pyx_int_872) < 0) __PYX_ERR(0, 438, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_9, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 438, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_9, __pyx_n_u_attriName, __pyx_n_u__312) < 0) __PYX_ERR(0, 438, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_9, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 438, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_9, __pyx_n_u_searchType, __pyx_int_0) < 0) __PYX_ERR(0, 438, __pyx_L1_error)

  /* "configs.py":445
 *         },
 *         {
 *             "productAttributeId": 96,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_10 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_10)) __PYX_ERR(0, 445, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_10);
  if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_productAttributeId, __pyx_int_96) < 0) __PYX_ERR(0, 445, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 445, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_attriName, __pyx_n_u__313) < 0) __PYX_ERR(0, 445, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 445, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_10, __pyx_n_u_searchType, __pyx_int_2) < 0) __PYX_ERR(0, 445, __pyx_L1_error)

  /* "configs.py":452
 *         },
 *         {
 *             "productAttributeId": 97,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_11 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_11)) __PYX_ERR(0, 452, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_11);
  if (PyDict_SetItem(__pyx_t_11, __pyx_n_u_productAttributeId, __pyx_int_97) < 0) __PYX_ERR(0, 452, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_11, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 452, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_11, __pyx_n_u_attriName, __pyx_n_u__314) < 0) __PYX_ERR(0, 452, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_11, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 452, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_11, __pyx_n_u_searchType, __pyx_int_2) < 0) __PYX_ERR(0, 452, __pyx_L1_error)

  /* "configs.py":459
 *         },
 *         {
 *             "productAttributeId": 119,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_12 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_12)) __PYX_ERR(0, 459, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_12);
  if (PyDict_SetItem(__pyx_t_12, __pyx_n_u_productAttributeId, __pyx_int_119) < 0) __PYX_ERR(0, 459, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_12, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 459, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_12, __pyx_n_u_attriName, __pyx_n_u__315) < 0) __PYX_ERR(0, 459, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_12, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 459, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_12, __pyx_n_u_searchType, __pyx_int_2) < 0) __PYX_ERR(0, 459, __pyx_L1_error)

  /* "configs.py":466
 *         },
 *         {
 *             "productAttributeId": 118,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_13 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_13)) __PYX_ERR(0, 466, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_13);
  if (PyDict_SetItem(__pyx_t_13, __pyx_n_u_productAttributeId, __pyx_int_118) < 0) __PYX_ERR(0, 466, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_13, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 466, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_13, __pyx_n_u_attriName, __pyx_n_u__316) < 0) __PYX_ERR(0, 466, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_13, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 466, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_13, __pyx_n_u_searchType, __pyx_int_2) < 0) __PYX_ERR(0, 466, __pyx_L1_error)

  /* "configs.py":473
 *         },
 *         {
 *             "productAttributeId": 873,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_14 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_14)) __PYX_ERR(0, 473, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_14);
  if (PyDict_SetItem(__pyx_t_14, __pyx_n_u_productAttributeId, __pyx_int_873) < 0) __PYX_ERR(0, 473, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_14, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 473, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_14, __pyx_n_u_attriName, __pyx_n_u__317) < 0) __PYX_ERR(0, 473, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_14, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 473, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_14, __pyx_n_u_searchType, __pyx_int_2) < 0) __PYX_ERR(0, 473, __pyx_L1_error)

  /* "configs.py":480
 *         },
 *         {
 *             "productAttributeId": 1185,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_15 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_15)) __PYX_ERR(0, 480, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_15);
  if (PyDict_SetItem(__pyx_t_15, __pyx_n_u_productAttributeId, __pyx_int_1185) < 0) __PYX_ERR(0, 480, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_15, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 480, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_15, __pyx_n_u_attriName, __pyx_n_u__318) < 0) __PYX_ERR(0, 480, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_15, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 480, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_15, __pyx_n_u_searchType, __pyx_int_2) < 0) __PYX_ERR(0, 480, __pyx_L1_error)

  /* "configs.py":487
 *         },
 *         {
 *             "productAttributeId": 371,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_16 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_16)) __PYX_ERR(0, 487, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_16);
  if (PyDict_SetItem(__pyx_t_16, __pyx_n_u_productAttributeId, __pyx_int_371) < 0) __PYX_ERR(0, 487, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_16, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 487, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_16, __pyx_n_u_attriName, __pyx_n_u__319) < 0) __PYX_ERR(0, 487, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_16, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 487, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_16, __pyx_n_u_searchType, __pyx_int_2) < 0) __PYX_ERR(0, 487, __pyx_L1_error)

  /* "configs.py":494
 *         },
 *         {
 *             "productAttributeId": 166,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_17 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_17)) __PYX_ERR(0, 494, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_17);
  if (PyDict_SetItem(__pyx_t_17, __pyx_n_u_productAttributeId, __pyx_int_166) < 0) __PYX_ERR(0, 494, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_17, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 494, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_17, __pyx_n_u_attriName, __pyx_n_u__320) < 0) __PYX_ERR(0, 494, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_17, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 494, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_17, __pyx_n_u_searchType, __pyx_int_2) < 0) __PYX_ERR(0, 494, __pyx_L1_error)

  /* "configs.py":501
 *         },
 *         {
 *             "productAttributeId": 374,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_18 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_18)) __PYX_ERR(0, 501, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_18);
  if (PyDict_SetItem(__pyx_t_18, __pyx_n_u_productAttributeId, __pyx_int_374) < 0) __PYX_ERR(0, 501, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_18, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 501, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_18, __pyx_n_u_attriName, __pyx_n_u__321) < 0) __PYX_ERR(0, 501, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_18, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 501, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_18, __pyx_n_u_searchType, __pyx_int_2) < 0) __PYX_ERR(0, 501, __pyx_L1_error)

  /* "configs.py":508
 *         },
 *         {
 *             "productAttributeId": 344,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_19 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_19)) __PYX_ERR(0, 508, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_19);
  if (PyDict_SetItem(__pyx_t_19, __pyx_n_u_productAttributeId, __pyx_int_344) < 0) __PYX_ERR(0, 508, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_19, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 508, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_19, __pyx_n_u_attriName, __pyx_n_u__322) < 0) __PYX_ERR(0, 508, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_19, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 508, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_19, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 508, __pyx_L1_error)

  /* "configs.py":515
 *         },
 *         {
 *             "productAttributeId": 899,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_20 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_20)) __PYX_ERR(0, 515, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_20);
  if (PyDict_SetItem(__pyx_t_20, __pyx_n_u_productAttributeId, __pyx_int_899) < 0) __PYX_ERR(0, 515, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_20, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 515, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_20, __pyx_n_u_attriName, __pyx_n_u__323) < 0) __PYX_ERR(0, 515, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_20, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 515, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_20, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 515, __pyx_L1_error)

  /* "configs.py":522
 *         },
 *         {
 *             "productAttributeId": 115,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_21 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_21)) __PYX_ERR(0, 522, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_21);
  if (PyDict_SetItem(__pyx_t_21, __pyx_n_u_productAttributeId, __pyx_int_115) < 0) __PYX_ERR(0, 522, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_21, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 522, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_21, __pyx_n_u_attriName, __pyx_n_u__324) < 0) __PYX_ERR(0, 522, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_21, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 522, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_21, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 522, __pyx_L1_error)

  /* "configs.py":529
 *         },
 *         {
 *             "productAttributeId": 116,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_22 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_22)) __PYX_ERR(0, 529, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_22);
  if (PyDict_SetItem(__pyx_t_22, __pyx_n_u_productAttributeId, __pyx_int_116) < 0) __PYX_ERR(0, 529, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_22, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 529, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_22, __pyx_n_u_attriName, __pyx_n_u__325) < 0) __PYX_ERR(0, 529, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_22, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 529, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_22, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 529, __pyx_L1_error)

  /* "configs.py":536
 *         },
 *         {
 *             "productAttributeId": 372,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_23 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_23)) __PYX_ERR(0, 536, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_23);
  if (PyDict_SetItem(__pyx_t_23, __pyx_n_u_productAttributeId, __pyx_int_372) < 0) __PYX_ERR(0, 536, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_23, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 536, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_23, __pyx_n_u_attriName, __pyx_n_u__326) < 0) __PYX_ERR(0, 536, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_23, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 536, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_23, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 536, __pyx_L1_error)

  /* "configs.py":543
 *         },
 *         {
 *             "productAttributeId": 554,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_24 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_24)) __PYX_ERR(0, 543, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_24);
  if (PyDict_SetItem(__pyx_t_24, __pyx_n_u_productAttributeId, __pyx_int_554) < 0) __PYX_ERR(0, 543, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_24, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 543, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_24, __pyx_n_u_attriName, __pyx_n_u__327) < 0) __PYX_ERR(0, 543, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_24, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 543, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_24, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 543, __pyx_L1_error)

  /* "configs.py":550
 *         },
 *         {
 *             "productAttributeId": 1071,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_25 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_25)) __PYX_ERR(0, 550, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_25);
  if (PyDict_SetItem(__pyx_t_25, __pyx_n_u_productAttributeId, __pyx_int_1071) < 0) __PYX_ERR(0, 550, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_25, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 550, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_25, __pyx_n_u_attriName, __pyx_n_u__328) < 0) __PYX_ERR(0, 550, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_25, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 550, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_25, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 550, __pyx_L1_error)

  /* "configs.py":557
 *         },
 *         {
 *             "productAttributeId": 902,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_26 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_26)) __PYX_ERR(0, 557, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_26);
  if (PyDict_SetItem(__pyx_t_26, __pyx_n_u_productAttributeId, __pyx_int_902) < 0) __PYX_ERR(0, 557, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_26, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 557, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_26, __pyx_n_u_attriName, __pyx_n_u__329) < 0) __PYX_ERR(0, 557, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_26, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 557, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_26, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 557, __pyx_L1_error)

  /* "configs.py":564
 *         },
 *         {
 *             "productAttributeId": 1072,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_27 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_27)) __PYX_ERR(0, 564, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_27);
  if (PyDict_SetItem(__pyx_t_27, __pyx_n_u_productAttributeId, __pyx_int_1072) < 0) __PYX_ERR(0, 564, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_27, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 564, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_27, __pyx_n_u_attriName, __pyx_n_u__330) < 0) __PYX_ERR(0, 564, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_27, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 564, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_27, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 564, __pyx_L1_error)

  /* "configs.py":571
 *         },
 *         {
 *             "productAttributeId": 1073,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_28 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_28)) __PYX_ERR(0, 571, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_28);
  if (PyDict_SetItem(__pyx_t_28, __pyx_n_u_productAttributeId, __pyx_int_1073) < 0) __PYX_ERR(0, 571, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_28, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 571, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_28, __pyx_n_u_attriName, __pyx_n_u__331) < 0) __PYX_ERR(0, 571, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_28, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 571, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_28, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 571, __pyx_L1_error)

  /* "configs.py":578
 *         },
 *         {
 *             "productAttributeId": 1074,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_29 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_29)) __PYX_ERR(0, 578, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_29);
  if (PyDict_SetItem(__pyx_t_29, __pyx_n_u_productAttributeId, __pyx_int_1074) < 0) __PYX_ERR(0, 578, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_29, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 578, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_29, __pyx_n_u_attriName, __pyx_n_u__332) < 0) __PYX_ERR(0, 578, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_29, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 578, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_29, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 578, __pyx_L1_error)

  /* "configs.py":585
 *         },
 *         {
 *             "productAttributeId": 545,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_30 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_30)) __PYX_ERR(0, 585, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_30);
  if (PyDict_SetItem(__pyx_t_30, __pyx_n_u_productAttributeId, __pyx_int_545) < 0) __PYX_ERR(0, 585, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_30, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 585, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_30, __pyx_n_u_attriName, __pyx_n_u__333) < 0) __PYX_ERR(0, 585, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_30, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 585, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_30, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 585, __pyx_L1_error)

  /* "configs.py":592
 *         },
 *         {
 *             "productAttributeId": 373,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_31 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_31)) __PYX_ERR(0, 592, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_31);
  if (PyDict_SetItem(__pyx_t_31, __pyx_n_u_productAttributeId, __pyx_int_373) < 0) __PYX_ERR(0, 592, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_31, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 592, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_31, __pyx_n_u_attriName, __pyx_n_u__334) < 0) __PYX_ERR(0, 592, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_31, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 592, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_31, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 592, __pyx_L1_error)

  /* "configs.py":599
 *         },
 *         {
 *             "productAttributeId": 546,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_32 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_32)) __PYX_ERR(0, 599, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_32);
  if (PyDict_SetItem(__pyx_t_32, __pyx_n_u_productAttributeId, __pyx_int_546) < 0) __PYX_ERR(0, 599, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_32, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 599, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_32, __pyx_n_u_attriName, __pyx_n_u__335) < 0) __PYX_ERR(0, 599, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_32, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 599, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_32, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 599, __pyx_L1_error)

  /* "configs.py":606
 *         },
 *         {
 *             "productAttributeId": 553,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_33 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_33)) __PYX_ERR(0, 606, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_33);
  if (PyDict_SetItem(__pyx_t_33, __pyx_n_u_productAttributeId, __pyx_int_553) < 0) __PYX_ERR(0, 606, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_33, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 606, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_33, __pyx_n_u_attriName, __pyx_n_u__336) < 0) __PYX_ERR(0, 606, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_33, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 606, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_33, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 606, __pyx_L1_error)

  /* "configs.py":613
 *         },
 *         {
 *             "productAttributeId": 550,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_34 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_34)) __PYX_ERR(0, 613, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_34);
  if (PyDict_SetItem(__pyx_t_34, __pyx_n_u_productAttributeId, __pyx_int_550) < 0) __PYX_ERR(0, 613, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_34, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 613, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_34, __pyx_n_u_attriName, __pyx_n_u__337) < 0) __PYX_ERR(0, 613, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_34, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 613, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_34, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 613, __pyx_L1_error)

  /* "configs.py":620
 *         },
 *         {
 *             "productAttributeId": 552,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_35 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_35)) __PYX_ERR(0, 620, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_35);
  if (PyDict_SetItem(__pyx_t_35, __pyx_n_u_productAttributeId, __pyx_int_552) < 0) __PYX_ERR(0, 620, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_35, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 620, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_35, __pyx_n_u_attriName, __pyx_n_u__338) < 0) __PYX_ERR(0, 620, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_35, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 620, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_35, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 620, __pyx_L1_error)

  /* "configs.py":627
 *         },
 *         {
 *             "productAttributeId": 547,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_36 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_36)) __PYX_ERR(0, 627, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_36);
  if (PyDict_SetItem(__pyx_t_36, __pyx_n_u_productAttributeId, __pyx_int_547) < 0) __PYX_ERR(0, 627, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_36, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 627, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_36, __pyx_n_u_attriName, __pyx_n_u__339) < 0) __PYX_ERR(0, 627, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_36, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 627, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_36, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 627, __pyx_L1_error)

  /* "configs.py":634
 *         },
 *         {
 *             "productAttributeId": 548,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_37 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_37)) __PYX_ERR(0, 634, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_37);
  if (PyDict_SetItem(__pyx_t_37, __pyx_n_u_productAttributeId, __pyx_int_548) < 0) __PYX_ERR(0, 634, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_37, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 634, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_37, __pyx_n_u_attriName, __pyx_n_u__340) < 0) __PYX_ERR(0, 634, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_37, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 634, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_37, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 634, __pyx_L1_error)

  /* "configs.py":641
 *         },
 *         {
 *             "productAttributeId": 549,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_38 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_38)) __PYX_ERR(0, 641, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_38);
  if (PyDict_SetItem(__pyx_t_38, __pyx_n_u_productAttributeId, __pyx_int_549) < 0) __PYX_ERR(0, 641, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_38, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 641, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_38, __pyx_n_u_attriName, __pyx_n_u__341) < 0) __PYX_ERR(0, 641, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_38, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 641, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_38, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 641, __pyx_L1_error)

  /* "configs.py":648
 *         },
 *         {
 *             "productAttributeId": 1162,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_39 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_39)) __PYX_ERR(0, 648, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_39);
  if (PyDict_SetItem(__pyx_t_39, __pyx_n_u_productAttributeId, __pyx_int_1162) < 0) __PYX_ERR(0, 648, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_39, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 648, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_39, __pyx_n_u_attriName, __pyx_n_u__342) < 0) __PYX_ERR(0, 648, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_39, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 648, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_39, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 648, __pyx_L1_error)

  /* "configs.py":655
 *         },
 *         {
 *             "productAttributeId": 1180,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_40 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_40)) __PYX_ERR(0, 655, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_40);
  if (PyDict_SetItem(__pyx_t_40, __pyx_n_u_productAttributeId, __pyx_int_1180) < 0) __PYX_ERR(0, 655, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_40, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 655, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_40, __pyx_n_u_attriName, __pyx_n_u__343) < 0) __PYX_ERR(0, 655, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_40, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 655, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_40, __pyx_n_u_searchType, __pyx_int_3) < 0) __PYX_ERR(0, 655, __pyx_L1_error)

  /* "configs.py":662
 *         },
 *         {
 *             "productAttributeId": 1181,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_41 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_41)) __PYX_ERR(0, 662, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_41);
  if (PyDict_SetItem(__pyx_t_41, __pyx_n_u_productAttributeId, __pyx_int_1181) < 0) __PYX_ERR(0, 662, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_41, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 662, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_41, __pyx_n_u_attriName, __pyx_n_u__344) < 0) __PYX_ERR(0, 662, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_41, __pyx_n_u_type, __pyx_int_2) < 0) __PYX_ERR(0, 662, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_41, __pyx_n_u_searchType, __pyx_int_1) < 0) __PYX_ERR(0, 662, __pyx_L1_error)

  /* "configs.py":669
 *         },
 *         {
 *             "productAttributeId": 333,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_42 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_42)) __PYX_ERR(0, 669, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_42);
  if (PyDict_SetItem(__pyx_t_42, __pyx_n_u_productAttributeId, __pyx_int_333) < 0) __PYX_ERR(0, 669, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_42, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 669, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_42, __pyx_n_u_attriName, __pyx_n_u__345) < 0) __PYX_ERR(0, 669, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_42, __pyx_n_u_type, __pyx_int_5) < 0) __PYX_ERR(0, 669, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_42, __pyx_n_u_searchType, __pyx_int_0) < 0) __PYX_ERR(0, 669, __pyx_L1_error)

  /* "configs.py":676
 *         },
 *         {
 *             "productAttributeId": 334,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_43 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_43)) __PYX_ERR(0, 676, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_43);
  if (PyDict_SetItem(__pyx_t_43, __pyx_n_u_productAttributeId, __pyx_int_334) < 0) __PYX_ERR(0, 676, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_43, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 676, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_43, __pyx_n_u_attriName, __pyx_n_u__346) < 0) __PYX_ERR(0, 676, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_43, __pyx_n_u_type, __pyx_int_5) < 0) __PYX_ERR(0, 676, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_43, __pyx_n_u_searchType, __pyx_int_0) < 0) __PYX_ERR(0, 676, __pyx_L1_error)

  /* "configs.py":683
 *         },
 *         {
 *             "productAttributeId": 335,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_44 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_44)) __PYX_ERR(0, 683, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_44);
  if (PyDict_SetItem(__pyx_t_44, __pyx_n_u_productAttributeId, __pyx_int_335) < 0) __PYX_ERR(0, 683, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_44, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 683, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_44, __pyx_n_u_attriName, __pyx_n_u__347) < 0) __PYX_ERR(0, 683, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_44, __pyx_n_u_type, __pyx_int_5) < 0) __PYX_ERR(0, 683, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_44, __pyx_n_u_searchType, __pyx_int_0) < 0) __PYX_ERR(0, 683, __pyx_L1_error)

  /* "configs.py":690
 *         },
 *         {
 *             "productAttributeId": 336,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_45 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_45)) __PYX_ERR(0, 690, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_45);
  if (PyDict_SetItem(__pyx_t_45, __pyx_n_u_productAttributeId, __pyx_int_336) < 0) __PYX_ERR(0, 690, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_45, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 690, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_45, __pyx_n_u_attriName, __pyx_n_u__348) < 0) __PYX_ERR(0, 690, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_45, __pyx_n_u_type, __pyx_int_5) < 0) __PYX_ERR(0, 690, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_45, __pyx_n_u_searchType, __pyx_int_0) < 0) __PYX_ERR(0, 690, __pyx_L1_error)

  /* "configs.py":697
 *         },
 *         {
 *             "productAttributeId": 113,             # <<<<<<<<<<<<<<
 *             "value": "",
 *             "attriName": "",
 */
  __pyx_t_46 = __Pyx_PyDict_NewPresized(5); if (unlikely(!__pyx_t_46)) __PYX_ERR(0, 697, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_46);
  if (PyDict_SetItem(__pyx_t_46, __pyx_n_u_productAttributeId, __pyx_int_113) < 0) __PYX_ERR(0, 697, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_46, __pyx_n_u_value, __pyx_kp_u__308) < 0) __PYX_ERR(0, 697, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_46, __pyx_n_u_attriName, __pyx_n_u__349) < 0) __PYX_ERR(0, 697, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_46, __pyx_n_u_type, __pyx_int_1) < 0) __PYX_ERR(0, 697, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_46, __pyx_n_u_searchType, __pyx_int_1) < 0) __PYX_ERR(0, 697, __pyx_L1_error)

  /* "configs.py":394
 *     "pushType": 1,
 *     "gameAccountQufu": "",
 *     "productAttributeValueList": [             # <<<<<<<<<<<<<<
 *         {
 *             "productAttributeId": 114,
 */
  __pyx_t_47 = PyList_New(44); if (unlikely(!__pyx_t_47)) __PYX_ERR(0, 394, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_47);
  __Pyx_GIVEREF(__pyx_t_3);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 0, __pyx_t_3)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_4);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 1, __pyx_t_4)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_5);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 2, __pyx_t_5)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_6);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 3, __pyx_t_6)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_7);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 4, __pyx_t_7)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_8);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 5, __pyx_t_8)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_9);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 6, __pyx_t_9)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_10);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 7, __pyx_t_10)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_11);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 8, __pyx_t_11)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_12);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 9, __pyx_t_12)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_13);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 10, __pyx_t_13)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_14);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 11, __pyx_t_14)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_15);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 12, __pyx_t_15)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_16);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 13, __pyx_t_16)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_17);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 14, __pyx_t_17)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_18);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 15, __pyx_t_18)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_19);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 16, __pyx_t_19)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_20);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 17, __pyx_t_20)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_21);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 18, __pyx_t_21)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_22);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 19, __pyx_t_22)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_23);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 20, __pyx_t_23)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_24);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 21, __pyx_t_24)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_25);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 22, __pyx_t_25)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_26);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 23, __pyx_t_26)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_27);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 24, __pyx_t_27)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_28);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 25, __pyx_t_28)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_29);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 26, __pyx_t_29)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_30);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 27, __pyx_t_30)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_31);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 28, __pyx_t_31)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_32);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 29, __pyx_t_32)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_33);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 30, __pyx_t_33)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_34);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 31, __pyx_t_34)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_35);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 32, __pyx_t_35)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_36);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 33, __pyx_t_36)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_37);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 34, __pyx_t_37)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_38);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 35, __pyx_t_38)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_39);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 36, __pyx_t_39)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_40);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 37, __pyx_t_40)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_41);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 38, __pyx_t_41)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_42);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 39, __pyx_t_42)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_43);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 40, __pyx_t_43)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_44);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 41, __pyx_t_44)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_45);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 42, __pyx_t_45)) __PYX_ERR(0, 394, __pyx_L1_error);
  __Pyx_GIVEREF(__pyx_t_46);
  if (__Pyx_PyList_SET_ITEM(__pyx_t_47, 43, __pyx_t_46)) __PYX_ERR(0, 394, __pyx_L1_error);
  __pyx_t_3 = 0;
  __pyx_t_4 = 0;
  __pyx_t_5 = 0;
  __pyx_t_6 = 0;
  __pyx_t_7 = 0;
  __pyx_t_8 = 0;
  __pyx_t_9 = 0;
  __pyx_t_10 = 0;
  __pyx_t_11 = 0;
  __pyx_t_12 = 0;
  __pyx_t_13 = 0;
  __pyx_t_14 = 0;
  __pyx_t_15 = 0;
  __pyx_t_16 = 0;
  __pyx_t_17 = 0;
  __pyx_t_18 = 0;
  __pyx_t_19 = 0;
  __pyx_t_20 = 0;
  __pyx_t_21 = 0;
  __pyx_t_22 = 0;
  __pyx_t_23 = 0;
  __pyx_t_24 = 0;
  __pyx_t_25 = 0;
  __pyx_t_26 = 0;
  __pyx_t_27 = 0;
  __pyx_t_28 = 0;
  __pyx_t_29 = 0;
  __pyx_t_30 = 0;
  __pyx_t_31 = 0;
  __pyx_t_32 = 0;
  __pyx_t_33 = 0;
  __pyx_t_34 = 0;
  __pyx_t_35 = 0;
  __pyx_t_36 = 0;
  __pyx_t_37 = 0;
  __pyx_t_38 = 0;
  __pyx_t_39 = 0;
  __pyx_t_40 = 0;
  __pyx_t_41 = 0;
  __pyx_t_42 = 0;
  __pyx_t_43 = 0;
  __pyx_t_44 = 0;
  __pyx_t_45 = 0;
  __pyx_t_46 = 0;
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_productAttributeValueList, __pyx_t_47) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_47); __pyx_t_47 = 0;
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_pic, __pyx_kp_u__308) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_albumPics, __pyx_kp_u__308) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_description, __pyx_kp_u__308) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_gameCareinfoPhone, __pyx_kp_u__308) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_gameCareinfoVx, __pyx_kp_u__308) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_gameCareinfoTime, __pyx_kp_u_00_23) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_price, __pyx_kp_u__308) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_originalPrice, __pyx_kp_u__308) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_productCategoryId, __pyx_kp_u_75) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_productCategoryName, __pyx_n_u__350) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_productAttributeCategoryId, __pyx_kp_u_17) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_gameGoodsSaletype, __pyx_int_0) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_t_2, __pyx_n_u_gameGoodsYijia, __pyx_int_1) < 0) __PYX_ERR(0, 391, __pyx_L1_error)
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_create_product_req, __pyx_t_2) < 0) __PYX_ERR(0, 390, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /* "configs.py":1
 * DEBUG_MODEL=1             # <<<<<<<<<<<<<<
 * PROJECT_NAME="nsha"
 * LOG_PATH="static/logs"
 */
  __pyx_t_2 = __Pyx_PyDict_NewPresized(0); if (unlikely(!__pyx_t_2)) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_test, __pyx_t_2) < 0) __PYX_ERR(0, 1, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;

  /*--- Wrapped vars code ---*/

  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_XDECREF(__pyx_t_3);
  __Pyx_XDECREF(__pyx_t_4);
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_XDECREF(__pyx_t_6);
  __Pyx_XDECREF(__pyx_t_7);
  __Pyx_XDECREF(__pyx_t_8);
  __Pyx_XDECREF(__pyx_t_9);
  __Pyx_XDECREF(__pyx_t_10);
  __Pyx_XDECREF(__pyx_t_11);
  __Pyx_XDECREF(__pyx_t_12);
  __Pyx_XDECREF(__pyx_t_13);
  __Pyx_XDECREF(__pyx_t_14);
  __Pyx_XDECREF(__pyx_t_15);
  __Pyx_XDECREF(__pyx_t_16);
  __Pyx_XDECREF(__pyx_t_17);
  __Pyx_XDECREF(__pyx_t_18);
  __Pyx_XDECREF(__pyx_t_19);
  __Pyx_XDECREF(__pyx_t_20);
  __Pyx_XDECREF(__pyx_t_21);
  __Pyx_XDECREF(__pyx_t_22);
  __Pyx_XDECREF(__pyx_t_23);
  __Pyx_XDECREF(__pyx_t_24);
  __Pyx_XDECREF(__pyx_t_25);
  __Pyx_XDECREF(__pyx_t_26);
  __Pyx_XDECREF(__pyx_t_27);
  __Pyx_XDECREF(__pyx_t_28);
  __Pyx_XDECREF(__pyx_t_29);
  __Pyx_XDECREF(__pyx_t_30);
  __Pyx_XDECREF(__pyx_t_31);
  __Pyx_XDECREF(__pyx_t_32);
  __Pyx_XDECREF(__pyx_t_33);
  __Pyx_XDECREF(__pyx_t_34);
  __Pyx_XDECREF(__pyx_t_35);
  __Pyx_XDECREF(__pyx_t_36);
  __Pyx_XDECREF(__pyx_t_37);
  __Pyx_XDECREF(__pyx_t_38);
  __Pyx_XDECREF(__pyx_t_39);
  __Pyx_XDECREF(__pyx_t_40);
  __Pyx_XDECREF(__pyx_t_41);
  __Pyx_XDECREF(__pyx_t_42);
  __Pyx_XDECREF(__pyx_t_43);
  __Pyx_XDECREF(__pyx_t_44);
  __Pyx_XDECREF(__pyx_t_45);
  __Pyx_XDECREF(__pyx_t_46);
  __Pyx_XDECREF(__pyx_t_47);
  if (__pyx_m) {
    if (__pyx_d && stringtab_initialized) {
      __Pyx_AddTraceback("init configs", __pyx_clineno, __pyx_lineno, __pyx_filename);
    }
    #if !CYTHON_USE_MODULE_STATE
    Py_CLEAR(__pyx_m);
    #else
    Py_DECREF(__pyx_m);
    if (pystate_addmodule_run) {
      PyObject *tp, *value, *tb;
      PyErr_Fetch(&tp, &value, &tb);
      PyState_RemoveModule(&__pyx_moduledef);
      PyErr_Restore(tp, value, tb);
    }
    #endif
  } else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_ImportError, "init configs");
  }
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  return (__pyx_m != NULL) ? 0 : -1;
  #elif PY_MAJOR_VERSION >= 3
  return __pyx_m;
  #else
  return;
  #endif
}
/* #### Code section: cleanup_globals ### */
/* #### Code section: cleanup_module ### */
/* #### Code section: main_method ### */
/* #### Code section: utility_code_pragmas ### */
#ifdef _MSC_VER
#pragma warning( push )
/* Warning 4127: conditional expression is constant
 * Cython uses constant conditional expressions to allow in inline functions to be optimized at
 * compile-time, so this warning is not useful
 */
#pragma warning( disable : 4127 )
#endif



/* #### Code section: utility_code_def ### */

/* --- Runtime support code --- */
/* Refnanny */
#if CYTHON_REFNANNY
static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname) {
    PyObject *m = NULL, *p = NULL;
    void *r = NULL;
    m = PyImport_ImportModule(modname);
    if (!m) goto end;
    p = PyObject_GetAttrString(m, "RefNannyAPI");
    if (!p) goto end;
    r = PyLong_AsVoidPtr(p);
end:
    Py_XDECREF(p);
    Py_XDECREF(m);
    return (__Pyx_RefNannyAPIStruct *)r;
}
#endif

/* PyDictVersioning */
#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj) {
    PyObject *dict = Py_TYPE(obj)->tp_dict;
    return likely(dict) ? __PYX_GET_DICT_VERSION(dict) : 0;
}
static CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj) {
    PyObject **dictptr = NULL;
    Py_ssize_t offset = Py_TYPE(obj)->tp_dictoffset;
    if (offset) {
#if CYTHON_COMPILING_IN_CPYTHON
        dictptr = (likely(offset > 0)) ? (PyObject **) ((char *)obj + offset) : _PyObject_GetDictPtr(obj);
#else
        dictptr = _PyObject_GetDictPtr(obj);
#endif
    }
    return (dictptr && *dictptr) ? __PYX_GET_DICT_VERSION(*dictptr) : 0;
}
static CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version) {
    PyObject *dict = Py_TYPE(obj)->tp_dict;
    if (unlikely(!dict) || unlikely(tp_dict_version != __PYX_GET_DICT_VERSION(dict)))
        return 0;
    return obj_dict_version == __Pyx_get_object_dict_version(obj);
}
#endif

/* PyErrExceptionMatches */
#if CYTHON_FAST_THREAD_STATE
static int __Pyx_PyErr_ExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {
    Py_ssize_t i, n;
    n = PyTuple_GET_SIZE(tuple);
#if PY_MAJOR_VERSION >= 3
    for (i=0; i<n; i++) {
        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;
    }
#endif
    for (i=0; i<n; i++) {
        if (__Pyx_PyErr_GivenExceptionMatches(exc_type, PyTuple_GET_ITEM(tuple, i))) return 1;
    }
    return 0;
}
static CYTHON_INLINE int __Pyx_PyErr_ExceptionMatchesInState(PyThreadState* tstate, PyObject* err) {
    int result;
    PyObject *exc_type;
#if PY_VERSION_HEX >= 0x030C00A6
    PyObject *current_exception = tstate->current_exception;
    if (unlikely(!current_exception)) return 0;
    exc_type = (PyObject*) Py_TYPE(current_exception);
    if (exc_type == err) return 1;
#else
    exc_type = tstate->curexc_type;
    if (exc_type == err) return 1;
    if (unlikely(!exc_type)) return 0;
#endif
    #if CYTHON_AVOID_BORROWED_REFS
    Py_INCREF(exc_type);
    #endif
    if (unlikely(PyTuple_Check(err))) {
        result = __Pyx_PyErr_ExceptionMatchesTuple(exc_type, err);
    } else {
        result = __Pyx_PyErr_GivenExceptionMatches(exc_type, err);
    }
    #if CYTHON_AVOID_BORROWED_REFS
    Py_DECREF(exc_type);
    #endif
    return result;
}
#endif

/* PyErrFetchRestore */
#if CYTHON_FAST_THREAD_STATE
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb) {
#if PY_VERSION_HEX >= 0x030C00A6
    PyObject *tmp_value;
    assert(type == NULL || (value != NULL && type == (PyObject*) Py_TYPE(value)));
    if (value) {
        #if CYTHON_COMPILING_IN_CPYTHON
        if (unlikely(((PyBaseExceptionObject*) value)->traceback != tb))
        #endif
            PyException_SetTraceback(value, tb);
    }
    tmp_value = tstate->current_exception;
    tstate->current_exception = value;
    Py_XDECREF(tmp_value);
    Py_XDECREF(type);
    Py_XDECREF(tb);
#else
    PyObject *tmp_type, *tmp_value, *tmp_tb;
    tmp_type = tstate->curexc_type;
    tmp_value = tstate->curexc_value;
    tmp_tb = tstate->curexc_traceback;
    tstate->curexc_type = type;
    tstate->curexc_value = value;
    tstate->curexc_traceback = tb;
    Py_XDECREF(tmp_type);
    Py_XDECREF(tmp_value);
    Py_XDECREF(tmp_tb);
#endif
}
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb) {
#if PY_VERSION_HEX >= 0x030C00A6
    PyObject* exc_value;
    exc_value = tstate->current_exception;
    tstate->current_exception = 0;
    *value = exc_value;
    *type = NULL;
    *tb = NULL;
    if (exc_value) {
        *type = (PyObject*) Py_TYPE(exc_value);
        Py_INCREF(*type);
        #if CYTHON_COMPILING_IN_CPYTHON
        *tb = ((PyBaseExceptionObject*) exc_value)->traceback;
        Py_XINCREF(*tb);
        #else
        *tb = PyException_GetTraceback(exc_value);
        #endif
    }
#else
    *type = tstate->curexc_type;
    *value = tstate->curexc_value;
    *tb = tstate->curexc_traceback;
    tstate->curexc_type = 0;
    tstate->curexc_value = 0;
    tstate->curexc_traceback = 0;
#endif
}
#endif

/* PyObjectGetAttrStr */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name) {
    PyTypeObject* tp = Py_TYPE(obj);
    if (likely(tp->tp_getattro))
        return tp->tp_getattro(obj, attr_name);
#if PY_MAJOR_VERSION < 3
    if (likely(tp->tp_getattr))
        return tp->tp_getattr(obj, PyString_AS_STRING(attr_name));
#endif
    return PyObject_GetAttr(obj, attr_name);
}
#endif

/* PyObjectGetAttrStrNoError */
#if __PYX_LIMITED_VERSION_HEX < 0x030d00A1
static void __Pyx_PyObject_GetAttrStr_ClearAttributeError(void) {
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    if (likely(__Pyx_PyErr_ExceptionMatches(PyExc_AttributeError)))
        __Pyx_PyErr_Clear();
}
#endif
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStrNoError(PyObject* obj, PyObject* attr_name) {
    PyObject *result;
#if __PYX_LIMITED_VERSION_HEX >= 0x030d00A1
    (void) PyObject_GetOptionalAttr(obj, attr_name, &result);
    return result;
#else
#if CYTHON_COMPILING_IN_CPYTHON && CYTHON_USE_TYPE_SLOTS && PY_VERSION_HEX >= 0x030700B1
    PyTypeObject* tp = Py_TYPE(obj);
    if (likely(tp->tp_getattro == PyObject_GenericGetAttr)) {
        return _PyObject_GenericGetAttrWithDict(obj, attr_name, NULL, 1);
    }
#endif
    result = __Pyx_PyObject_GetAttrStr(obj, attr_name);
    if (unlikely(!result)) {
        __Pyx_PyObject_GetAttrStr_ClearAttributeError();
    }
    return result;
#endif
}

/* CLineInTraceback */
#ifndef CYTHON_CLINE_IN_TRACEBACK
static int __Pyx_CLineForTraceback(PyThreadState *tstate, int c_line) {
    PyObject *use_cline;
    PyObject *ptype, *pvalue, *ptraceback;
#if CYTHON_COMPILING_IN_CPYTHON
    PyObject **cython_runtime_dict;
#endif
    CYTHON_MAYBE_UNUSED_VAR(tstate);
    if (unlikely(!__pyx_cython_runtime)) {
        return c_line;
    }
    __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);
#if CYTHON_COMPILING_IN_CPYTHON
    cython_runtime_dict = _PyObject_GetDictPtr(__pyx_cython_runtime);
    if (likely(cython_runtime_dict)) {
        __PYX_PY_DICT_LOOKUP_IF_MODIFIED(
            use_cline, *cython_runtime_dict,
            __Pyx_PyDict_GetItemStr(*cython_runtime_dict, __pyx_n_s_cline_in_traceback))
    } else
#endif
    {
      PyObject *use_cline_obj = __Pyx_PyObject_GetAttrStrNoError(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback);
      if (use_cline_obj) {
        use_cline = PyObject_Not(use_cline_obj) ? Py_False : Py_True;
        Py_DECREF(use_cline_obj);
      } else {
        PyErr_Clear();
        use_cline = NULL;
      }
    }
    if (!use_cline) {
        c_line = 0;
        (void) PyObject_SetAttr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback, Py_False);
    }
    else if (use_cline == Py_False || (use_cline != Py_True && PyObject_Not(use_cline) != 0)) {
        c_line = 0;
    }
    __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);
    return c_line;
}
#endif

/* CodeObjectCache */
#if !CYTHON_COMPILING_IN_LIMITED_API
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line) {
    int start = 0, mid = 0, end = count - 1;
    if (end >= 0 && code_line > entries[end].code_line) {
        return count;
    }
    while (start < end) {
        mid = start + (end - start) / 2;
        if (code_line < entries[mid].code_line) {
            end = mid;
        } else if (code_line > entries[mid].code_line) {
             start = mid + 1;
        } else {
            return mid;
        }
    }
    if (code_line <= entries[mid].code_line) {
        return mid;
    } else {
        return mid + 1;
    }
}
static PyCodeObject *__pyx_find_code_object(int code_line) {
    PyCodeObject* code_object;
    int pos;
    if (unlikely(!code_line) || unlikely(!__pyx_code_cache.entries)) {
        return NULL;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if (unlikely(pos >= __pyx_code_cache.count) || unlikely(__pyx_code_cache.entries[pos].code_line != code_line)) {
        return NULL;
    }
    code_object = __pyx_code_cache.entries[pos].code_object;
    Py_INCREF(code_object);
    return code_object;
}
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object) {
    int pos, i;
    __Pyx_CodeObjectCacheEntry* entries = __pyx_code_cache.entries;
    if (unlikely(!code_line)) {
        return;
    }
    if (unlikely(!entries)) {
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Malloc(64*sizeof(__Pyx_CodeObjectCacheEntry));
        if (likely(entries)) {
            __pyx_code_cache.entries = entries;
            __pyx_code_cache.max_count = 64;
            __pyx_code_cache.count = 1;
            entries[0].code_line = code_line;
            entries[0].code_object = code_object;
            Py_INCREF(code_object);
        }
        return;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if ((pos < __pyx_code_cache.count) && unlikely(__pyx_code_cache.entries[pos].code_line == code_line)) {
        PyCodeObject* tmp = entries[pos].code_object;
        entries[pos].code_object = code_object;
        Py_DECREF(tmp);
        return;
    }
    if (__pyx_code_cache.count == __pyx_code_cache.max_count) {
        int new_max = __pyx_code_cache.max_count + 64;
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Realloc(
            __pyx_code_cache.entries, ((size_t)new_max) * sizeof(__Pyx_CodeObjectCacheEntry));
        if (unlikely(!entries)) {
            return;
        }
        __pyx_code_cache.entries = entries;
        __pyx_code_cache.max_count = new_max;
    }
    for (i=__pyx_code_cache.count; i>pos; i--) {
        entries[i] = entries[i-1];
    }
    entries[pos].code_line = code_line;
    entries[pos].code_object = code_object;
    __pyx_code_cache.count++;
    Py_INCREF(code_object);
}
#endif

/* AddTraceback */
#include "compile.h"
#include "frameobject.h"
#include "traceback.h"
#if PY_VERSION_HEX >= 0x030b00a6 && !CYTHON_COMPILING_IN_LIMITED_API && !defined(PYPY_VERSION)
  #ifndef Py_BUILD_CORE
    #define Py_BUILD_CORE 1
  #endif
  #include "internal/pycore_frame.h"
#endif
#if CYTHON_COMPILING_IN_LIMITED_API
static PyObject *__Pyx_PyCode_Replace_For_AddTraceback(PyObject *code, PyObject *scratch_dict,
                                                       PyObject *firstlineno, PyObject *name) {
    PyObject *replace = NULL;
    if (unlikely(PyDict_SetItemString(scratch_dict, "co_firstlineno", firstlineno))) return NULL;
    if (unlikely(PyDict_SetItemString(scratch_dict, "co_name", name))) return NULL;
    replace = PyObject_GetAttrString(code, "replace");
    if (likely(replace)) {
        PyObject *result;
        result = PyObject_Call(replace, __pyx_empty_tuple, scratch_dict);
        Py_DECREF(replace);
        return result;
    }
    PyErr_Clear();
    #if __PYX_LIMITED_VERSION_HEX < 0x030780000
    {
        PyObject *compiled = NULL, *result = NULL;
        if (unlikely(PyDict_SetItemString(scratch_dict, "code", code))) return NULL;
        if (unlikely(PyDict_SetItemString(scratch_dict, "type", (PyObject*)(&PyType_Type)))) return NULL;
        compiled = Py_CompileString(
            "out = type(code)(\n"
            "  code.co_argcount, code.co_kwonlyargcount, code.co_nlocals, code.co_stacksize,\n"
            "  code.co_flags, code.co_code, code.co_consts, code.co_names,\n"
            "  code.co_varnames, code.co_filename, co_name, co_firstlineno,\n"
            "  code.co_lnotab)\n", "<dummy>", Py_file_input);
        if (!compiled) return NULL;
        result = PyEval_EvalCode(compiled, scratch_dict, scratch_dict);
        Py_DECREF(compiled);
        if (!result) PyErr_Print();
        Py_DECREF(result);
        result = PyDict_GetItemString(scratch_dict, "out");
        if (result) Py_INCREF(result);
        return result;
    }
    #else
    return NULL;
    #endif
}
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename) {
    PyObject *code_object = NULL, *py_py_line = NULL, *py_funcname = NULL, *dict = NULL;
    PyObject *replace = NULL, *getframe = NULL, *frame = NULL;
    PyObject *exc_type, *exc_value, *exc_traceback;
    int success = 0;
    if (c_line) {
        (void) __pyx_cfilenm;
        (void) __Pyx_CLineForTraceback(__Pyx_PyThreadState_Current, c_line);
    }
    PyErr_Fetch(&exc_type, &exc_value, &exc_traceback);
    code_object = Py_CompileString("_getframe()", filename, Py_eval_input);
    if (unlikely(!code_object)) goto bad;
    py_py_line = PyLong_FromLong(py_line);
    if (unlikely(!py_py_line)) goto bad;
    py_funcname = PyUnicode_FromString(funcname);
    if (unlikely(!py_funcname)) goto bad;
    dict = PyDict_New();
    if (unlikely(!dict)) goto bad;
    {
        PyObject *old_code_object = code_object;
        code_object = __Pyx_PyCode_Replace_For_AddTraceback(code_object, dict, py_py_line, py_funcname);
        Py_DECREF(old_code_object);
    }
    if (unlikely(!code_object)) goto bad;
    getframe = PySys_GetObject("_getframe");
    if (unlikely(!getframe)) goto bad;
    if (unlikely(PyDict_SetItemString(dict, "_getframe", getframe))) goto bad;
    frame = PyEval_EvalCode(code_object, dict, dict);
    if (unlikely(!frame) || frame == Py_None) goto bad;
    success = 1;
  bad:
    PyErr_Restore(exc_type, exc_value, exc_traceback);
    Py_XDECREF(code_object);
    Py_XDECREF(py_py_line);
    Py_XDECREF(py_funcname);
    Py_XDECREF(dict);
    Py_XDECREF(replace);
    if (success) {
        PyTraceBack_Here(
            (struct _frame*)frame);
    }
    Py_XDECREF(frame);
}
#else
static PyCodeObject* __Pyx_CreateCodeObjectForTraceback(
            const char *funcname, int c_line,
            int py_line, const char *filename) {
    PyCodeObject *py_code = NULL;
    PyObject *py_funcname = NULL;
    #if PY_MAJOR_VERSION < 3
    PyObject *py_srcfile = NULL;
    py_srcfile = PyString_FromString(filename);
    if (!py_srcfile) goto bad;
    #endif
    if (c_line) {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        if (!py_funcname) goto bad;
        #else
        py_funcname = PyUnicode_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        if (!py_funcname) goto bad;
        funcname = PyUnicode_AsUTF8(py_funcname);
        if (!funcname) goto bad;
        #endif
    }
    else {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromString(funcname);
        if (!py_funcname) goto bad;
        #endif
    }
    #if PY_MAJOR_VERSION < 3
    py_code = __Pyx_PyCode_New(
        0,
        0,
        0,
        0,
        0,
        0,
        __pyx_empty_bytes, /*PyObject *code,*/
        __pyx_empty_tuple, /*PyObject *consts,*/
        __pyx_empty_tuple, /*PyObject *names,*/
        __pyx_empty_tuple, /*PyObject *varnames,*/
        __pyx_empty_tuple, /*PyObject *freevars,*/
        __pyx_empty_tuple, /*PyObject *cellvars,*/
        py_srcfile,   /*PyObject *filename,*/
        py_funcname,  /*PyObject *name,*/
        py_line,
        __pyx_empty_bytes  /*PyObject *lnotab*/
    );
    Py_DECREF(py_srcfile);
    #else
    py_code = PyCode_NewEmpty(filename, funcname, py_line);
    #endif
    Py_XDECREF(py_funcname);
    return py_code;
bad:
    Py_XDECREF(py_funcname);
    #if PY_MAJOR_VERSION < 3
    Py_XDECREF(py_srcfile);
    #endif
    return NULL;
}
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename) {
    PyCodeObject *py_code = 0;
    PyFrameObject *py_frame = 0;
    PyThreadState *tstate = __Pyx_PyThreadState_Current;
    PyObject *ptype, *pvalue, *ptraceback;
    if (c_line) {
        c_line = __Pyx_CLineForTraceback(tstate, c_line);
    }
    py_code = __pyx_find_code_object(c_line ? -c_line : py_line);
    if (!py_code) {
        __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);
        py_code = __Pyx_CreateCodeObjectForTraceback(
            funcname, c_line, py_line, filename);
        if (!py_code) {
            /* If the code object creation fails, then we should clear the
               fetched exception references and propagate the new exception */
            Py_XDECREF(ptype);
            Py_XDECREF(pvalue);
            Py_XDECREF(ptraceback);
            goto bad;
        }
        __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);
        __pyx_insert_code_object(c_line ? -c_line : py_line, py_code);
    }
    py_frame = PyFrame_New(
        tstate,            /*PyThreadState *tstate,*/
        py_code,           /*PyCodeObject *code,*/
        __pyx_d,    /*PyObject *globals,*/
        0                  /*PyObject *locals*/
    );
    if (!py_frame) goto bad;
    __Pyx_PyFrame_SetLineNumber(py_frame, py_line);
    PyTraceBack_Here(py_frame);
bad:
    Py_XDECREF(py_code);
    Py_XDECREF(py_frame);
}
#endif

/* FormatTypeName */
#if CYTHON_COMPILING_IN_LIMITED_API
static __Pyx_TypeName
__Pyx_PyType_GetName(PyTypeObject* tp)
{
    PyObject *name = __Pyx_PyObject_GetAttrStr((PyObject *)tp,
                                               __pyx_n_s_name);
    if (unlikely(name == NULL) || unlikely(!PyUnicode_Check(name))) {
        PyErr_Clear();
        Py_XDECREF(name);
        name = __Pyx_NewRef(__pyx_n_s__351);
    }
    return name;
}
#endif

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const long neg_one = (long) -1, const_zero = (long) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(long) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(long) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(long) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        unsigned char *bytes = (unsigned char *)&value;
#if !CYTHON_COMPILING_IN_LIMITED_API && PY_VERSION_HEX >= 0x030d00A4
        if (is_unsigned) {
            return PyLong_FromUnsignedNativeBytes(bytes, sizeof(value), -1);
        } else {
            return PyLong_FromNativeBytes(bytes, sizeof(value), -1);
        }
#elif !CYTHON_COMPILING_IN_LIMITED_API && PY_VERSION_HEX < 0x030d0000
        int one = 1; int little = (int)*(unsigned char *)&one;
        return _PyLong_FromByteArray(bytes, sizeof(long),
                                     little, !is_unsigned);
#else
        int one = 1; int little = (int)*(unsigned char *)&one;
        PyObject *from_bytes, *result = NULL;
        PyObject *py_bytes = NULL, *arg_tuple = NULL, *kwds = NULL, *order_str = NULL;
        from_bytes = PyObject_GetAttrString((PyObject*)&PyLong_Type, "from_bytes");
        if (!from_bytes) return NULL;
        py_bytes = PyBytes_FromStringAndSize((char*)bytes, sizeof(long));
        if (!py_bytes) goto limited_bad;
        order_str = PyUnicode_FromString(little ? "little" : "big");
        if (!order_str) goto limited_bad;
        arg_tuple = PyTuple_Pack(2, py_bytes, order_str);
        if (!arg_tuple) goto limited_bad;
        if (!is_unsigned) {
            kwds = PyDict_New();
            if (!kwds) goto limited_bad;
            if (PyDict_SetItemString(kwds, "signed", __Pyx_NewRef(Py_True))) goto limited_bad;
        }
        result = PyObject_Call(from_bytes, arg_tuple, kwds);
        limited_bad:
        Py_XDECREF(kwds);
        Py_XDECREF(arg_tuple);
        Py_XDECREF(order_str);
        Py_XDECREF(py_bytes);
        Py_XDECREF(from_bytes);
        return result;
#endif
    }
}

/* CIntFromPyVerify */
#define __PYX_VERIFY_RETURN_INT(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 0)
#define __PYX_VERIFY_RETURN_INT_EXC(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 1)
#define __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, exc)\
    {\
        func_type value = func_value;\
        if (sizeof(target_type) < sizeof(func_type)) {\
            if (unlikely(value != (func_type) (target_type) value)) {\
                func_type zero = 0;\
                if (exc && unlikely(value == (func_type)-1 && PyErr_Occurred()))\
                    return (target_type) -1;\
                if (is_unsigned && unlikely(value < zero))\
                    goto raise_neg_overflow;\
                else\
                    goto raise_overflow;\
            }\
        }\
        return (target_type) value;\
    }

/* CIntFromPy */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const long neg_one = (long) -1, const_zero = (long) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if ((sizeof(long) < sizeof(long))) {
            __PYX_VERIFY_RETURN_INT(long, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (long) val;
        }
    }
#endif
    if (unlikely(!PyLong_Check(x))) {
        long val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (long) -1;
        val = __Pyx_PyInt_As_long(tmp);
        Py_DECREF(tmp);
        return val;
    }
    if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
        if (unlikely(__Pyx_PyLong_IsNeg(x))) {
            goto raise_neg_overflow;
        } else if (__Pyx_PyLong_IsCompact(x)) {
            __PYX_VERIFY_RETURN_INT(long, __Pyx_compact_upylong, __Pyx_PyLong_CompactValueUnsigned(x))
        } else {
            const digit* digits = __Pyx_PyLong_Digits(x);
            assert(__Pyx_PyLong_DigitCount(x) > 1);
            switch (__Pyx_PyLong_DigitCount(x)) {
                case 2:
                    if ((8 * sizeof(long) > 1 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 2 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(long) >= 2 * PyLong_SHIFT)) {
                            return (long) (((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if ((8 * sizeof(long) > 2 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 3 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(long) >= 3 * PyLong_SHIFT)) {
                            return (long) (((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if ((8 * sizeof(long) > 3 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 4 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(long) >= 4 * PyLong_SHIFT)) {
                            return (long) (((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
            }
        }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
        if (unlikely(Py_SIZE(x) < 0)) {
            goto raise_neg_overflow;
        }
#else
        {
            int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
            if (unlikely(result < 0))
                return (long) -1;
            if (unlikely(result == 1))
                goto raise_neg_overflow;
        }
#endif
        if ((sizeof(long) <= sizeof(unsigned long))) {
            __PYX_VERIFY_RETURN_INT_EXC(long, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
        } else if ((sizeof(long) <= sizeof(unsigned PY_LONG_LONG))) {
            __PYX_VERIFY_RETURN_INT_EXC(long, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
        }
    } else {
#if CYTHON_USE_PYLONG_INTERNALS
        if (__Pyx_PyLong_IsCompact(x)) {
            __PYX_VERIFY_RETURN_INT(long, __Pyx_compact_pylong, __Pyx_PyLong_CompactValue(x))
        } else {
            const digit* digits = __Pyx_PyLong_Digits(x);
            assert(__Pyx_PyLong_DigitCount(x) > 1);
            switch (__Pyx_PyLong_SignedDigitCount(x)) {
                case -2:
                    if ((8 * sizeof(long) - 1 > 1 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 2 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(long) - 1 > 2 * PyLong_SHIFT)) {
                            return (long) (((long)-1)*(((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if ((8 * sizeof(long) > 1 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 2 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(long) - 1 > 2 * PyLong_SHIFT)) {
                            return (long) ((((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if ((8 * sizeof(long) - 1 > 2 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 3 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(long) - 1 > 3 * PyLong_SHIFT)) {
                            return (long) (((long)-1)*(((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if ((8 * sizeof(long) > 2 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 3 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(long) - 1 > 3 * PyLong_SHIFT)) {
                            return (long) ((((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if ((8 * sizeof(long) - 1 > 3 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 4 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(long) - 1 > 4 * PyLong_SHIFT)) {
                            return (long) (((long)-1)*(((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if ((8 * sizeof(long) > 3 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 4 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(long) - 1 > 4 * PyLong_SHIFT)) {
                            return (long) ((((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
            }
        }
#endif
        if ((sizeof(long) <= sizeof(long))) {
            __PYX_VERIFY_RETURN_INT_EXC(long, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
        } else if ((sizeof(long) <= sizeof(PY_LONG_LONG))) {
            __PYX_VERIFY_RETURN_INT_EXC(long, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
        }
    }
    {
        long val;
        int ret = -1;
#if PY_VERSION_HEX >= 0x030d00A6 && !CYTHON_COMPILING_IN_LIMITED_API
        Py_ssize_t bytes_copied = PyLong_AsNativeBytes(
            x, &val, sizeof(val), Py_ASNATIVEBYTES_NATIVE_ENDIAN | (is_unsigned ? Py_ASNATIVEBYTES_UNSIGNED_BUFFER | Py_ASNATIVEBYTES_REJECT_NEGATIVE : 0));
        if (unlikely(bytes_copied == -1)) {
        } else if (unlikely(bytes_copied > (Py_ssize_t) sizeof(val))) {
            goto raise_overflow;
        } else {
            ret = 0;
        }
#elif PY_VERSION_HEX < 0x030d0000 && !(CYTHON_COMPILING_IN_PYPY || CYTHON_COMPILING_IN_LIMITED_API) || defined(_PyLong_AsByteArray)
        int one = 1; int is_little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&val;
        ret = _PyLong_AsByteArray((PyLongObject *)x,
                                    bytes, sizeof(val),
                                    is_little, !is_unsigned);
#else
        PyObject *v;
        PyObject *stepval = NULL, *mask = NULL, *shift = NULL;
        int bits, remaining_bits, is_negative = 0;
        int chunk_size = (sizeof(long) < 8) ? 30 : 62;
        if (likely(PyLong_CheckExact(x))) {
            v = __Pyx_NewRef(x);
        } else {
            v = PyNumber_Long(x);
            if (unlikely(!v)) return (long) -1;
            assert(PyLong_CheckExact(v));
        }
        {
            int result = PyObject_RichCompareBool(v, Py_False, Py_LT);
            if (unlikely(result < 0)) {
                Py_DECREF(v);
                return (long) -1;
            }
            is_negative = result == 1;
        }
        if (is_unsigned && unlikely(is_negative)) {
            Py_DECREF(v);
            goto raise_neg_overflow;
        } else if (is_negative) {
            stepval = PyNumber_Invert(v);
            Py_DECREF(v);
            if (unlikely(!stepval))
                return (long) -1;
        } else {
            stepval = v;
        }
        v = NULL;
        val = (long) 0;
        mask = PyLong_FromLong((1L << chunk_size) - 1); if (unlikely(!mask)) goto done;
        shift = PyLong_FromLong(chunk_size); if (unlikely(!shift)) goto done;
        for (bits = 0; bits < (int) sizeof(long) * 8 - chunk_size; bits += chunk_size) {
            PyObject *tmp, *digit;
            long idigit;
            digit = PyNumber_And(stepval, mask);
            if (unlikely(!digit)) goto done;
            idigit = PyLong_AsLong(digit);
            Py_DECREF(digit);
            if (unlikely(idigit < 0)) goto done;
            val |= ((long) idigit) << bits;
            tmp = PyNumber_Rshift(stepval, shift);
            if (unlikely(!tmp)) goto done;
            Py_DECREF(stepval); stepval = tmp;
        }
        Py_DECREF(shift); shift = NULL;
        Py_DECREF(mask); mask = NULL;
        {
            long idigit = PyLong_AsLong(stepval);
            if (unlikely(idigit < 0)) goto done;
            remaining_bits = ((int) sizeof(long) * 8) - bits - (is_unsigned ? 0 : 1);
            if (unlikely(idigit >= (1L << remaining_bits)))
                goto raise_overflow;
            val |= ((long) idigit) << bits;
        }
        if (!is_unsigned) {
            if (unlikely(val & (((long) 1) << (sizeof(long) * 8 - 1))))
                goto raise_overflow;
            if (is_negative)
                val = ~val;
        }
        ret = 0;
    done:
        Py_XDECREF(shift);
        Py_XDECREF(mask);
        Py_XDECREF(stepval);
#endif
        if (unlikely(ret))
            return (long) -1;
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to long");
    return (long) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to long");
    return (long) -1;
}

/* CIntFromPy */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const int neg_one = (int) -1, const_zero = (int) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if ((sizeof(int) < sizeof(long))) {
            __PYX_VERIFY_RETURN_INT(int, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (int) val;
        }
    }
#endif
    if (unlikely(!PyLong_Check(x))) {
        int val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (int) -1;
        val = __Pyx_PyInt_As_int(tmp);
        Py_DECREF(tmp);
        return val;
    }
    if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
        if (unlikely(__Pyx_PyLong_IsNeg(x))) {
            goto raise_neg_overflow;
        } else if (__Pyx_PyLong_IsCompact(x)) {
            __PYX_VERIFY_RETURN_INT(int, __Pyx_compact_upylong, __Pyx_PyLong_CompactValueUnsigned(x))
        } else {
            const digit* digits = __Pyx_PyLong_Digits(x);
            assert(__Pyx_PyLong_DigitCount(x) > 1);
            switch (__Pyx_PyLong_DigitCount(x)) {
                case 2:
                    if ((8 * sizeof(int) > 1 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 2 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(int) >= 2 * PyLong_SHIFT)) {
                            return (int) (((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if ((8 * sizeof(int) > 2 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 3 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(int) >= 3 * PyLong_SHIFT)) {
                            return (int) (((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if ((8 * sizeof(int) > 3 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 4 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(int) >= 4 * PyLong_SHIFT)) {
                            return (int) (((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
            }
        }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
        if (unlikely(Py_SIZE(x) < 0)) {
            goto raise_neg_overflow;
        }
#else
        {
            int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
            if (unlikely(result < 0))
                return (int) -1;
            if (unlikely(result == 1))
                goto raise_neg_overflow;
        }
#endif
        if ((sizeof(int) <= sizeof(unsigned long))) {
            __PYX_VERIFY_RETURN_INT_EXC(int, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
        } else if ((sizeof(int) <= sizeof(unsigned PY_LONG_LONG))) {
            __PYX_VERIFY_RETURN_INT_EXC(int, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
        }
    } else {
#if CYTHON_USE_PYLONG_INTERNALS
        if (__Pyx_PyLong_IsCompact(x)) {
            __PYX_VERIFY_RETURN_INT(int, __Pyx_compact_pylong, __Pyx_PyLong_CompactValue(x))
        } else {
            const digit* digits = __Pyx_PyLong_Digits(x);
            assert(__Pyx_PyLong_DigitCount(x) > 1);
            switch (__Pyx_PyLong_SignedDigitCount(x)) {
                case -2:
                    if ((8 * sizeof(int) - 1 > 1 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 2 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(int) - 1 > 2 * PyLong_SHIFT)) {
                            return (int) (((int)-1)*(((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if ((8 * sizeof(int) > 1 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 2 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(int) - 1 > 2 * PyLong_SHIFT)) {
                            return (int) ((((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if ((8 * sizeof(int) - 1 > 2 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 3 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(int) - 1 > 3 * PyLong_SHIFT)) {
                            return (int) (((int)-1)*(((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if ((8 * sizeof(int) > 2 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 3 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(int) - 1 > 3 * PyLong_SHIFT)) {
                            return (int) ((((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if ((8 * sizeof(int) - 1 > 3 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 4 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(int) - 1 > 4 * PyLong_SHIFT)) {
                            return (int) (((int)-1)*(((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if ((8 * sizeof(int) > 3 * PyLong_SHIFT)) {
                        if ((8 * sizeof(unsigned long) > 4 * PyLong_SHIFT)) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if ((8 * sizeof(int) - 1 > 4 * PyLong_SHIFT)) {
                            return (int) ((((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
            }
        }
#endif
        if ((sizeof(int) <= sizeof(long))) {
            __PYX_VERIFY_RETURN_INT_EXC(int, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
        } else if ((sizeof(int) <= sizeof(PY_LONG_LONG))) {
            __PYX_VERIFY_RETURN_INT_EXC(int, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
        }
    }
    {
        int val;
        int ret = -1;
#if PY_VERSION_HEX >= 0x030d00A6 && !CYTHON_COMPILING_IN_LIMITED_API
        Py_ssize_t bytes_copied = PyLong_AsNativeBytes(
            x, &val, sizeof(val), Py_ASNATIVEBYTES_NATIVE_ENDIAN | (is_unsigned ? Py_ASNATIVEBYTES_UNSIGNED_BUFFER | Py_ASNATIVEBYTES_REJECT_NEGATIVE : 0));
        if (unlikely(bytes_copied == -1)) {
        } else if (unlikely(bytes_copied > (Py_ssize_t) sizeof(val))) {
            goto raise_overflow;
        } else {
            ret = 0;
        }
#elif PY_VERSION_HEX < 0x030d0000 && !(CYTHON_COMPILING_IN_PYPY || CYTHON_COMPILING_IN_LIMITED_API) || defined(_PyLong_AsByteArray)
        int one = 1; int is_little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&val;
        ret = _PyLong_AsByteArray((PyLongObject *)x,
                                    bytes, sizeof(val),
                                    is_little, !is_unsigned);
#else
        PyObject *v;
        PyObject *stepval = NULL, *mask = NULL, *shift = NULL;
        int bits, remaining_bits, is_negative = 0;
        int chunk_size = (sizeof(long) < 8) ? 30 : 62;
        if (likely(PyLong_CheckExact(x))) {
            v = __Pyx_NewRef(x);
        } else {
            v = PyNumber_Long(x);
            if (unlikely(!v)) return (int) -1;
            assert(PyLong_CheckExact(v));
        }
        {
            int result = PyObject_RichCompareBool(v, Py_False, Py_LT);
            if (unlikely(result < 0)) {
                Py_DECREF(v);
                return (int) -1;
            }
            is_negative = result == 1;
        }
        if (is_unsigned && unlikely(is_negative)) {
            Py_DECREF(v);
            goto raise_neg_overflow;
        } else if (is_negative) {
            stepval = PyNumber_Invert(v);
            Py_DECREF(v);
            if (unlikely(!stepval))
                return (int) -1;
        } else {
            stepval = v;
        }
        v = NULL;
        val = (int) 0;
        mask = PyLong_FromLong((1L << chunk_size) - 1); if (unlikely(!mask)) goto done;
        shift = PyLong_FromLong(chunk_size); if (unlikely(!shift)) goto done;
        for (bits = 0; bits < (int) sizeof(int) * 8 - chunk_size; bits += chunk_size) {
            PyObject *tmp, *digit;
            long idigit;
            digit = PyNumber_And(stepval, mask);
            if (unlikely(!digit)) goto done;
            idigit = PyLong_AsLong(digit);
            Py_DECREF(digit);
            if (unlikely(idigit < 0)) goto done;
            val |= ((int) idigit) << bits;
            tmp = PyNumber_Rshift(stepval, shift);
            if (unlikely(!tmp)) goto done;
            Py_DECREF(stepval); stepval = tmp;
        }
        Py_DECREF(shift); shift = NULL;
        Py_DECREF(mask); mask = NULL;
        {
            long idigit = PyLong_AsLong(stepval);
            if (unlikely(idigit < 0)) goto done;
            remaining_bits = ((int) sizeof(int) * 8) - bits - (is_unsigned ? 0 : 1);
            if (unlikely(idigit >= (1L << remaining_bits)))
                goto raise_overflow;
            val |= ((int) idigit) << bits;
        }
        if (!is_unsigned) {
            if (unlikely(val & (((int) 1) << (sizeof(int) * 8 - 1))))
                goto raise_overflow;
            if (is_negative)
                val = ~val;
        }
        ret = 0;
    done:
        Py_XDECREF(shift);
        Py_XDECREF(mask);
        Py_XDECREF(stepval);
#endif
        if (unlikely(ret))
            return (int) -1;
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to int");
    return (int) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to int");
    return (int) -1;
}

/* FastTypeChecks */
#if CYTHON_COMPILING_IN_CPYTHON
static int __Pyx_InBases(PyTypeObject *a, PyTypeObject *b) {
    while (a) {
        a = __Pyx_PyType_GetSlot(a, tp_base, PyTypeObject*);
        if (a == b)
            return 1;
    }
    return b == &PyBaseObject_Type;
}
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b) {
    PyObject *mro;
    if (a == b) return 1;
    mro = a->tp_mro;
    if (likely(mro)) {
        Py_ssize_t i, n;
        n = PyTuple_GET_SIZE(mro);
        for (i = 0; i < n; i++) {
            if (PyTuple_GET_ITEM(mro, i) == (PyObject *)b)
                return 1;
        }
        return 0;
    }
    return __Pyx_InBases(a, b);
}
static CYTHON_INLINE int __Pyx_IsAnySubtype2(PyTypeObject *cls, PyTypeObject *a, PyTypeObject *b) {
    PyObject *mro;
    if (cls == a || cls == b) return 1;
    mro = cls->tp_mro;
    if (likely(mro)) {
        Py_ssize_t i, n;
        n = PyTuple_GET_SIZE(mro);
        for (i = 0; i < n; i++) {
            PyObject *base = PyTuple_GET_ITEM(mro, i);
            if (base == (PyObject *)a || base == (PyObject *)b)
                return 1;
        }
        return 0;
    }
    return __Pyx_InBases(cls, a) || __Pyx_InBases(cls, b);
}
#if PY_MAJOR_VERSION == 2
static int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject* exc_type2) {
    PyObject *exception, *value, *tb;
    int res;
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    __Pyx_ErrFetch(&exception, &value, &tb);
    res = exc_type1 ? PyObject_IsSubclass(err, exc_type1) : 0;
    if (unlikely(res == -1)) {
        PyErr_WriteUnraisable(err);
        res = 0;
    }
    if (!res) {
        res = PyObject_IsSubclass(err, exc_type2);
        if (unlikely(res == -1)) {
            PyErr_WriteUnraisable(err);
            res = 0;
        }
    }
    __Pyx_ErrRestore(exception, value, tb);
    return res;
}
#else
static CYTHON_INLINE int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject *exc_type2) {
    if (exc_type1) {
        return __Pyx_IsAnySubtype2((PyTypeObject*)err, (PyTypeObject*)exc_type1, (PyTypeObject*)exc_type2);
    } else {
        return __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type2);
    }
}
#endif
static int __Pyx_PyErr_GivenExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {
    Py_ssize_t i, n;
    assert(PyExceptionClass_Check(exc_type));
    n = PyTuple_GET_SIZE(tuple);
#if PY_MAJOR_VERSION >= 3
    for (i=0; i<n; i++) {
        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;
    }
#endif
    for (i=0; i<n; i++) {
        PyObject *t = PyTuple_GET_ITEM(tuple, i);
        #if PY_MAJOR_VERSION < 3
        if (likely(exc_type == t)) return 1;
        #endif
        if (likely(PyExceptionClass_Check(t))) {
            if (__Pyx_inner_PyErr_GivenExceptionMatches2(exc_type, NULL, t)) return 1;
        } else {
        }
    }
    return 0;
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject* exc_type) {
    if (likely(err == exc_type)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        if (likely(PyExceptionClass_Check(exc_type))) {
            return __Pyx_inner_PyErr_GivenExceptionMatches2(err, NULL, exc_type);
        } else if (likely(PyTuple_Check(exc_type))) {
            return __Pyx_PyErr_GivenExceptionMatchesTuple(err, exc_type);
        } else {
        }
    }
    return PyErr_GivenExceptionMatches(err, exc_type);
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *exc_type1, PyObject *exc_type2) {
    assert(PyExceptionClass_Check(exc_type1));
    assert(PyExceptionClass_Check(exc_type2));
    if (likely(err == exc_type1 || err == exc_type2)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        return __Pyx_inner_PyErr_GivenExceptionMatches2(err, exc_type1, exc_type2);
    }
    return (PyErr_GivenExceptionMatches(err, exc_type1) || PyErr_GivenExceptionMatches(err, exc_type2));
}
#endif

/* CheckBinaryVersion */
static unsigned long __Pyx_get_runtime_version(void) {
#if __PYX_LIMITED_VERSION_HEX >= 0x030B00A4
    return Py_Version & ~0xFFUL;
#else
    const char* rt_version = Py_GetVersion();
    unsigned long version = 0;
    unsigned long factor = 0x01000000UL;
    unsigned int digit = 0;
    int i = 0;
    while (factor) {
        while ('0' <= rt_version[i] && rt_version[i] <= '9') {
            digit = digit * 10 + (unsigned int) (rt_version[i] - '0');
            ++i;
        }
        version += factor * digit;
        if (rt_version[i] != '.')
            break;
        digit = 0;
        factor >>= 8;
        ++i;
    }
    return version;
#endif
}
static int __Pyx_check_binary_version(unsigned long ct_version, unsigned long rt_version, int allow_newer) {
    const unsigned long MAJOR_MINOR = 0xFFFF0000UL;
    if ((rt_version & MAJOR_MINOR) == (ct_version & MAJOR_MINOR))
        return 0;
    if (likely(allow_newer && (rt_version & MAJOR_MINOR) > (ct_version & MAJOR_MINOR)))
        return 1;
    {
        char message[200];
        PyOS_snprintf(message, sizeof(message),
                      "compile time Python version %d.%d "
                      "of module '%.100s' "
                      "%s "
                      "runtime version %d.%d",
                       (int) (ct_version >> 24), (int) ((ct_version >> 16) & 0xFF),
                       __Pyx_MODULE_NAME,
                       (allow_newer) ? "was newer than" : "does not match",
                       (int) (rt_version >> 24), (int) ((rt_version >> 16) & 0xFF)
       );
        return PyErr_WarnEx(NULL, message, 1);
    }
}

/* InitStrings */
#if PY_MAJOR_VERSION >= 3
static int __Pyx_InitString(__Pyx_StringTabEntry t, PyObject **str) {
    if (t.is_unicode | t.is_str) {
        if (t.intern) {
            *str = PyUnicode_InternFromString(t.s);
        } else if (t.encoding) {
            *str = PyUnicode_Decode(t.s, t.n - 1, t.encoding, NULL);
        } else {
            *str = PyUnicode_FromStringAndSize(t.s, t.n - 1);
        }
    } else {
        *str = PyBytes_FromStringAndSize(t.s, t.n - 1);
    }
    if (!*str)
        return -1;
    if (PyObject_Hash(*str) == -1)
        return -1;
    return 0;
}
#endif
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t) {
    while (t->p) {
        #if PY_MAJOR_VERSION >= 3
        __Pyx_InitString(*t, t->p);
        #else
        if (t->is_unicode) {
            *t->p = PyUnicode_DecodeUTF8(t->s, t->n - 1, NULL);
        } else if (t->intern) {
            *t->p = PyString_InternFromString(t->s);
        } else {
            *t->p = PyString_FromStringAndSize(t->s, t->n - 1);
        }
        if (!*t->p)
            return -1;
        if (PyObject_Hash(*t->p) == -1)
            return -1;
        #endif
        ++t;
    }
    return 0;
}

#include <string.h>
static CYTHON_INLINE Py_ssize_t __Pyx_ssize_strlen(const char *s) {
    size_t len = strlen(s);
    if (unlikely(len > (size_t) PY_SSIZE_T_MAX)) {
        PyErr_SetString(PyExc_OverflowError, "byte string is too long");
        return -1;
    }
    return (Py_ssize_t) len;
}
static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char* c_str) {
    Py_ssize_t len = __Pyx_ssize_strlen(c_str);
    if (unlikely(len < 0)) return NULL;
    return __Pyx_PyUnicode_FromStringAndSize(c_str, len);
}
static CYTHON_INLINE PyObject* __Pyx_PyByteArray_FromString(const char* c_str) {
    Py_ssize_t len = __Pyx_ssize_strlen(c_str);
    if (unlikely(len < 0)) return NULL;
    return PyByteArray_FromStringAndSize(c_str, len);
}
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject* o) {
    Py_ssize_t ignore;
    return __Pyx_PyObject_AsStringAndSize(o, &ignore);
}
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
#if !CYTHON_PEP393_ENABLED
static const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    char* defenc_c;
    PyObject* defenc = _PyUnicode_AsDefaultEncodedString(o, NULL);
    if (!defenc) return NULL;
    defenc_c = PyBytes_AS_STRING(defenc);
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    {
        char* end = defenc_c + PyBytes_GET_SIZE(defenc);
        char* c;
        for (c = defenc_c; c < end; c++) {
            if ((unsigned char) (*c) >= 128) {
                PyUnicode_AsASCIIString(o);
                return NULL;
            }
        }
    }
#endif
    *length = PyBytes_GET_SIZE(defenc);
    return defenc_c;
}
#else
static CYTHON_INLINE const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    if (unlikely(__Pyx_PyUnicode_READY(o) == -1)) return NULL;
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    if (likely(PyUnicode_IS_ASCII(o))) {
        *length = PyUnicode_GET_LENGTH(o);
        return PyUnicode_AsUTF8(o);
    } else {
        PyUnicode_AsASCIIString(o);
        return NULL;
    }
#else
    return PyUnicode_AsUTF8AndSize(o, length);
#endif
}
#endif
#endif
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
    if (
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
            __Pyx_sys_getdefaultencoding_not_ascii &&
#endif
            PyUnicode_Check(o)) {
        return __Pyx_PyUnicode_AsStringAndSize(o, length);
    } else
#endif
#if (!CYTHON_COMPILING_IN_PYPY && !CYTHON_COMPILING_IN_LIMITED_API) || (defined(PyByteArray_AS_STRING) && defined(PyByteArray_GET_SIZE))
    if (PyByteArray_Check(o)) {
        *length = PyByteArray_GET_SIZE(o);
        return PyByteArray_AS_STRING(o);
    } else
#endif
    {
        char* result;
        int r = PyBytes_AsStringAndSize(o, &result, length);
        if (unlikely(r < 0)) {
            return NULL;
        } else {
            return result;
        }
    }
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject* x) {
   int is_true = x == Py_True;
   if (is_true | (x == Py_False) | (x == Py_None)) return is_true;
   else return PyObject_IsTrue(x);
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject* x) {
    int retval;
    if (unlikely(!x)) return -1;
    retval = __Pyx_PyObject_IsTrue(x);
    Py_DECREF(x);
    return retval;
}
static PyObject* __Pyx_PyNumber_IntOrLongWrongResultType(PyObject* result, const char* type_name) {
    __Pyx_TypeName result_type_name = __Pyx_PyType_GetName(Py_TYPE(result));
#if PY_MAJOR_VERSION >= 3
    if (PyLong_Check(result)) {
        if (PyErr_WarnFormat(PyExc_DeprecationWarning, 1,
                "__int__ returned non-int (type " __Pyx_FMT_TYPENAME ").  "
                "The ability to return an instance of a strict subclass of int is deprecated, "
                "and may be removed in a future version of Python.",
                result_type_name)) {
            __Pyx_DECREF_TypeName(result_type_name);
            Py_DECREF(result);
            return NULL;
        }
        __Pyx_DECREF_TypeName(result_type_name);
        return result;
    }
#endif
    PyErr_Format(PyExc_TypeError,
                 "__%.4s__ returned non-%.4s (type " __Pyx_FMT_TYPENAME ")",
                 type_name, type_name, result_type_name);
    __Pyx_DECREF_TypeName(result_type_name);
    Py_DECREF(result);
    return NULL;
}
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x) {
#if CYTHON_USE_TYPE_SLOTS
  PyNumberMethods *m;
#endif
  const char *name = NULL;
  PyObject *res = NULL;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_Check(x) || PyLong_Check(x)))
#else
  if (likely(PyLong_Check(x)))
#endif
    return __Pyx_NewRef(x);
#if CYTHON_USE_TYPE_SLOTS
  m = Py_TYPE(x)->tp_as_number;
  #if PY_MAJOR_VERSION < 3
  if (m && m->nb_int) {
    name = "int";
    res = m->nb_int(x);
  }
  else if (m && m->nb_long) {
    name = "long";
    res = m->nb_long(x);
  }
  #else
  if (likely(m && m->nb_int)) {
    name = "int";
    res = m->nb_int(x);
  }
  #endif
#else
  if (!PyBytes_CheckExact(x) && !PyUnicode_CheckExact(x)) {
    res = PyNumber_Int(x);
  }
#endif
  if (likely(res)) {
#if PY_MAJOR_VERSION < 3
    if (unlikely(!PyInt_Check(res) && !PyLong_Check(res))) {
#else
    if (unlikely(!PyLong_CheckExact(res))) {
#endif
        return __Pyx_PyNumber_IntOrLongWrongResultType(res, name);
    }
  }
  else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_TypeError,
                    "an integer is required");
  }
  return res;
}
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject* b) {
  Py_ssize_t ival;
  PyObject *x;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_CheckExact(b))) {
    if (sizeof(Py_ssize_t) >= sizeof(long))
        return PyInt_AS_LONG(b);
    else
        return PyInt_AsSsize_t(b);
  }
#endif
  if (likely(PyLong_CheckExact(b))) {
    #if CYTHON_USE_PYLONG_INTERNALS
    if (likely(__Pyx_PyLong_IsCompact(b))) {
        return __Pyx_PyLong_CompactValue(b);
    } else {
      const digit* digits = __Pyx_PyLong_Digits(b);
      const Py_ssize_t size = __Pyx_PyLong_SignedDigitCount(b);
      switch (size) {
         case 2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
      }
    }
    #endif
    return PyLong_AsSsize_t(b);
  }
  x = PyNumber_Index(b);
  if (!x) return -1;
  ival = PyInt_AsSsize_t(x);
  Py_DECREF(x);
  return ival;
}
static CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject* o) {
  if (sizeof(Py_hash_t) == sizeof(Py_ssize_t)) {
    return (Py_hash_t) __Pyx_PyIndex_AsSsize_t(o);
#if PY_MAJOR_VERSION < 3
  } else if (likely(PyInt_CheckExact(o))) {
    return PyInt_AS_LONG(o);
#endif
  } else {
    Py_ssize_t ival;
    PyObject *x;
    x = PyNumber_Index(o);
    if (!x) return -1;
    ival = PyInt_AsLong(x);
    Py_DECREF(x);
    return ival;
  }
}
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b) {
  return b ? __Pyx_NewRef(Py_True) : __Pyx_NewRef(Py_False);
}
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t ival) {
    return PyInt_FromSize_t(ival);
}


/* #### Code section: utility_code_pragmas_end ### */
#ifdef _MSC_VER
#pragma warning( pop )
#endif



/* #### Code section: end ### */
#endif /* Py_PYTHON_H */
