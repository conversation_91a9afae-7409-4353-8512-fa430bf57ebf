import pathlib
import sqlite3


class OptSqlite(object):
    def __init__(self, dbname="new.db"):
        """
        :param dbname  数据库名称
        """
        self.dir = pathlib.PurePath(__file__).parent
        self.db = pathlib.PurePath(self.dir, dbname)
        self.conn = sqlite3.connect(self.db)
        self.cur = self.conn.cursor()
        self.new_table("record_task")

    def close(self):
        """
        关闭连接
        """
        self.cur.close()
        self.conn.close()

    def new_table(self, table_name):
        """
        新建录号任务表
        """
        sql = f'''
                CREATE TABLE record_task (
                  id INTEGER PRIMARY KEY,  -- id
                  device_id INTEGER DEFAULT NULL,         -- 设备id
                  product_sn TEXT DEFAULT NULL,           -- 商品编号
                  product_category_name TEXT DEFAULT NULL, -- 商品分类名称
                  status TEXT DEFAULT NULL,                -- 状态
                  stage TEXT DEFAULT NULL,                 -- 录制阶段
                  game_account TEXT DEFAULT NULL,          -- 游戏账号
                  game_password TEXT DEFAULT NULL,         -- 游戏密码
                  snapshot TEXT DEFAULT NULL,              -- 设备截图
                  log_file TEXT DEFAULT NULL,              -- 日志文件
                  
                  note TEXT DEFAULT NULL,                   -- 备注
                  pic TEXT DEFAULT NULL,                    -- 封面图
                  album_pics TEXT DEFAULT NULL,             -- 内容图列表
                  attributes TEXT DEFAULT NULL,              -- 属性对象列表
                    
                  create_time DATETIME DEFAULT NULL,       -- 创建时间
                  update_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 修改时间
                  finish_time DATETIME DEFAULT NULL      -- 完成时间
                );
            '''
        try:
            self.cur.execute(sql)
            print("创建表成功")
        except Exception as e:
            print("创建表失败")
            print(f"失败原因是：{e}")

    def get_one_task(self, task_id):
        """
        获取一个任务
        """

        self.get_user_phone_sql = f"""
            SELECT * FROM record_task WHERE id = "{task_id}" """
        try:
            self.result = self.cur.execute(self.get_user_phone_sql)
            return self.result.fetchone()
        except Exception as e:
            print(f"失败原因是：{e}")

    def get_all_tasks(self):
        """
        """
        try:
            self.result = self.cur.execute("SELECT * FROM record_task")
            return self.result.fetchall()
        except Exception as e:
            print(f"失败原因是：{e}")

    def set_one_task(self, task):
        """
        增加或修改任务
        """
        # Prepare to check if the task exists
        task_id = task.get("id")
        existing_task = (task_id and self.get_one_task(task_id))

        # Initialize the SQL command and parameters
        if existing_task:
            # Build the UPDATE statement
            update_parts = []
            parameters = []

            for key, value in task.items():
                if value is not None and key != "id":  # Ignore 'id' for update
                    update_parts.append(f"{key} = ?")
                    parameters.append(value)

            # Add the condition for the specific task
            sql_update = f"UPDATE record_task SET {', '.join(update_parts)} WHERE id = ?"
            parameters.append(task_id)  # Add task_id to parameters
            self.v = tuple(parameters)

            self.set_user_phone_sql = sql_update
        else:
            # Prepare to insert a new task
            columns = ", ".join(task.keys())
            placeholders = ", ".join("?" * len(task))
            self.set_user_phone_sql = f"INSERT INTO record_task ({columns}) VALUES ({placeholders})"
            self.v = tuple(task.values())

        try:
            self.cur.execute(self.set_user_phone_sql, self.v)
            self.conn.commit()
        except Exception as e:
            print(f"失败原因是：{e}")

    def delete_one_task(self, id):
        """
        删除一个任务
        """
        self.delete_sql = f'''DELETE FROM record_task 
                WHERE id="{id}"'''

        try:
            self.cur.execute(self.delete_sql)
            self.conn.commit()
        except Exception as e:
            print(f"删除失败原因是：{e}")


if __name__ == "__main__":
    # 实例化
    my_query = OptSqlite("luhao.db")

    # 创建一张表
    # my_query.new_table("address_book")

    task = {
        'id': 1,
        'device_id': 1,
        'product_sn': 'TESTNSH05997687',
        'product_category_name': '手机',
        'status': 'COMPLETED',
        'stage': '阶段一1',
        'game_account': 'xxx1',
        'game_password': 'xxx1',
        'snapshot': 'xxx1',
        'log_file': 'xxx1',

        'note': 'xxx1',
        'pic': 'xxx1',
        'album_pics': 'xxx1',
        'attributes': 'xxx1'

    }
    my_query.set_one_task(task)

    task = my_query.get_one_task(1)
    print(task)

    # my_query.delete_one_task(1)

    # 关闭连接
    my_query.close()
