; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define MyAppName "录号器-安装程序"
#define MyAppVersion "0.5.35"
;#define MyAppVersion GetEnv('VERSION')
#define MyAppPublisher "kkzhw.com"
#define MyAppURL "https://kkzhw.com/"
#define MyAppExeName "看看账号网逆水寒手游录号.exe"

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
AppId={{1E81F561-79BD-47AF-80D1-EEA081B42508}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
;AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName=D:\local_luhao
; "ArchitecturesAllowed=x64compatible" specifies that Setup cannot run
; on anything but x64 and Windows 11 on Arm.
ArchitecturesAllowed=x64compatible
; "ArchitecturesInstallIn64BitMode=x64compatible" requests that the
; install be done in "64-bit mode" on x64 or Windows 11 on Arm,
; meaning it should use the native 64-bit Program Files directory and
; the 64-bit view of the registry.
ArchitecturesInstallIn64BitMode=x64compatible
DefaultGroupName=
; Uncomment the following line to run in non administrative install mode (install for current user only.)
;PrivilegesRequired=lowest
OutputBaseFilename=录号器-安装程序
Compression=zip
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "chinesesimplified"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "dist\local_luhao\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\local_luhao\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
;Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppExeName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
;Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent runasoriginaluser

[Code]
function IsProcessRunning(const ProcessName: string): Boolean;
var
  ResultCode: Integer;
  Output: string;
begin
  // 使用 tasklist 过滤进程
  Exec('cmd.exe', '/C tasklist /FI "IMAGENAME eq ' + ProcessName + '" /FO LIST', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);

  // 如果输出包含进程名称，则表示进程正在运行
  Result := (ResultCode = 0) and (Pos(ProcessName, GetEnv('TEMP')) > 0);
end;

procedure KillProcess(const ProcessName: string);
var
  ResultCode: Integer;
begin
  // 强制终止指定进程
  Exec('taskkill.exe', '/F /IM ' + ProcessName, '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
end;

function InitializeSetup(): Boolean;
begin
  Result := True;

  // 检查 看看账号网逆水寒手游录号.exe 是否正在运行
  if IsProcessRunning('看看账号网逆水寒手游录号.exe') then
  begin
    if MsgBox('看看账号网逆水寒手游录号 正在运行，是否终止进程继续安装？', mbConfirmation, MB_YESNO) = IDYES then
    begin
      KillProcess('看看账号网逆水寒手游录号.exe');
    end
    else
    begin
      Result := False;  // 如果用户选择不终止进程，则取消安装
    end;
  end;
end;