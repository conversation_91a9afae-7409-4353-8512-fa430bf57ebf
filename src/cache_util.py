import time


class SimpleCache:
    def __init__(self, maxsize=100, ttl=60):
        """
        maxsize: 缓存的最大条目数
        ttl: 每个缓存项的生存时间（秒），即 Time To Live
        """
        self.cache = {}
        self.maxsize = maxsize
        self.ttl = ttl  # 缓存项的存活时间（秒）

    def set(self, key, value):
        """
        添加一个缓存条目，记录插入时间
        """
        if len(self.cache) >= self.maxsize:
            self._evict()  # 淘汰最老的缓存
        expire_at = time.time() + self.ttl  # 设置过期时间
        self.cache[key] = (value, expire_at)  # 缓存值和过期时间

    def get(self, key):
        """
        获取缓存，如果过期则返回 None
        """
        if key in self.cache:
            value, expire_at = self.cache[key]
            if time.time() < expire_at:  # 未过期
                return value
            else:
                # 缓存已过期，删除该项
                del self.cache[key]
        return None

    def delete(self, key):
        """
        删除指定的缓存项
        """
        if key in self.cache:
            del self.cache[key]

    def clear(self):
        """
        清空所有缓存
        """
        self.cache.clear()

    def _evict(self):
        """
        淘汰最早插入的缓存项
        """
        oldest_key = min(self.cache, key=lambda k: self.cache[k][1])  # 找到过期时间最早的缓存项
        del self.cache[oldest_key]


if __name__ == "__main__":
    # 示例用法
    cache = SimpleCache(maxsize=3, ttl=10)  # 最大缓存3项，缓存有效期10秒

    cache.set("user_1", {"name": "Alice"})
    cache.set("user_2", {"name": "Bob"})
    cache.set("user_3", {"name": "Charlie"})

    print(cache.get("user_1"))  # 输出: {'name': 'Alice'}

    time.sleep(11)  # 等待 11 秒，缓存过期

    print(cache.get("user_1"))  # 输出: None，已过期

    cache.set("user_4", {"name": "David"})  # 触发淘汰
    print(cache.get("user_2"))  # 输出: None，被淘汰
