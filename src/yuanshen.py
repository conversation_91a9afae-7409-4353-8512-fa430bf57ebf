# -*- encoding=utf8 -*-
__author__ = "labugao"

from airtest.core.api import *
from airtest.aircv import *
from paddleocr import PaddleOCR
import requests
import json
from airtest.aircv.cal_confidence import cal_ccoeff_confidence  
from airtest.core.settings import Settings as ST
import oss2  
import os  
import numpy as np  
import configparser
from PIL import Image, ImageDraw, ImageFont  
import shutil  
import logging  


#####################################################


#############################################################################全局变量


#########################################################################
import datetime  
import random  
import string

import configs


######################################################################
def clear_folder(folder_path):
    new_folder_name = f'{folder_path}_' + str(int(time.time()))  
    if os.path.exists(folder_path):  
        shutil.move(folder_path, new_folder_name)  
        print(f"文件夹 '{folder_path}' 已重命名为 '{new_folder_name}'")  
    os.makedirs(folder_path, exist_ok=True)  # 使用exist_ok=True避免如果文件夹已存在时抛出异常  
    print(f"新的空文件夹 '{folder_path}' 已创建")
######################################################################
##########################################
def kktouch(x, y, sleep_time):  
    touch((x,y))
    sleep(sleep_time)
    
##############################################

def generate_oss_path():  
    # 获取当前日期并格式化为 yyyyMMdd  
    today = datetime.datetime.now().strftime("%Y%m%d")  
      
    # 生成六位随机字符数字串  
    # 注意：这里使用了string.ascii_letters + string.digits来确保生成的是字母和数字的混合  
    # 如果只想要数字，可以去掉string.ascii_letters部分  
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=6))  
      
    # 获取当前毫秒时间戳  
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
      
    # 拼接路径  
    oss_path = f"{today}/"  
      
    return oss_path
###################################################################
def read_config(filename):  
    config = {}  
    with open(filename, 'r', encoding='utf-8') as file:  
        for line in file:  
            # 去除每行两端的空白字符，并检查是否为空行或注释行（假设以#开头的行为注释）  
            line = line.strip()  
            if not line or line.startswith('#'):  
                continue  
            # 使用等号分割键和值  
            key, value = line.split('=', 1)  
            # 去除值两端的引号（如果有的话）  
            value = value.strip('"').strip("'")  
            config[key] = value  
    return config  
########################################################################

def generate_oss_fileName():  
    # 生成六位随机字符数字串  
    # 注意：这里使用了string.ascii_letters + string.digits来确保生成的是字母和数字的混合  
    # 如果只想要数字，可以去掉string.ascii_letters部分  
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=7))  
      
    # 获取当前毫秒时间戳  
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
      
    # 拼接路径  
    oss_fileName = f"{random_str}_{current_millis}.jpg"  
      
    return oss_fileName
############################################################################


################################################
def resize_image_by_width(input_image_path, output_image_path, target_width):  
    # 打开原始图片  
    with Image.open(input_image_path) as img:  
        # 获取原始图片的宽度和高度  
        original_width, original_height = img.size  
          
        # 计算缩放比例  
        ratio = original_width / float(target_width)  
          
        # 根据缩放比例计算新的高度  
        new_height = int(original_height / ratio)  
          
        # 缩放图片  
        resized_img = img.resize((target_width, new_height), Image.LANCZOS)  
          
        # 保存缩放后的图片  
        resized_img.save(output_image_path)  
##############################################################################


#########################################################################
image_path_set = set()
def upload_kk_img_to_oss(folder_path):
    bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称  
    endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint  
    access_key_id = configs.OSS_ACCESS_KEY_ID
    access_key_secret = configs.OSS_ACCESS_KEY_SECRET
    print(f"开始上传文件夹{folder_path} 内容")

    auth = oss2.Auth(access_key_id, access_key_secret)
    bucket = oss2.Bucket(auth, endpoint, bucket_name)  

    for filename in os.listdir(folder_path):
        # 构造本地文件的完整路径  
        print("=========================发现图片1："+filename)
        local_file_path = os.path.join(folder_path, filename)  
        if local_file_path.endswith('jpg'):
            print("=========================发现图片2："+filename)
            # 构造上传到OSS后的文件名（可以包含前缀）  
            oss_file_path = "mall/images/"+ generate_oss_path()  

            oss_fileName = oss_file_path+generate_oss_fileName()
            with open(local_file_path, 'rb') as fileobj:  
                bucket.put_object(oss_fileName, fileobj)
                print(f'文件 {local_file_path} 已上传到OSS，路径为 {oss_file_path}')  
                image_path_set.add(oss_fileName)

#     dumpsJson = json.dumps(image_path_set,ensure_ascii=False) 
    logger.info(f"{image_path_set}")
    return 'True'

###################################################################

filename ="kk_img_abcd.jpg"
print(filename.startswith('kk_img') and filename.endswith('jpg')) 
#############################################################################根据sn查询账号密码
def requestGameAccountInfo(productSn):  
    kk_url = 'http://api.yyd8.com/mall-portal/import/product/getProductAccountInfo?productSn='+productSn
    response = requests.get(kk_url)
    # 检查请求是否成功  
    if response.status_code == 200:  
        # 打印返回的内容  
        print(response.text)
        responseObj = json.loads(response.text)
        product_dict = responseObj.get('data')
        return product_dict
#         account = data_dict.get("gameAccount")
#         password = data_dict.get("gamePassword")

#         print(account)
#         print(password)

#         return account, password

def requestCategoryProductSn(categoryId):  
    kk_url = f'http://api.yyd8.com/mall-portal/import/product/getTheProductAccountInfo?categoryId={categoryId}'
    response = requests.get(kk_url)
    # 检查请求是否成功  
    if response.status_code == 200:  
        # 打印返回的内容  
        print(response.text)
        responseObj = json.loads(response.text)
        productSn = responseObj.get('data')
        return productSn
    else:
        return ""
###################################################################根据sn查询账号密码


##OCR TOUCH#########################################ffffffffff##########
def ocr_touch(target_text):
    sleep(1)
    try:
      # 截屏当前画面
        pic_path=f"{snapImgPath}\\now.png"
        snapshot(pic_path)

         # 使用PaddleOCR识别图片文字
        ocr_result = ocr.ocr(pic_path, cls=True)

        # 遍历识别结果，找到目标文字的坐标
        target_coords = None
        for line in ocr_result:
            for word_info in line:
                #获取识别结果的文字信息
                textinfo = word_info[1][0]
                print(textinfo)

                if target_text in textinfo:
                    # 获取文字的坐标（中心点）
                    x1, y1 = word_info[0][0]
                    x2, y2 = word_info[0][2]
                    target_coords = ((x1 + x2) / 2, (y1 + y2) / 2)
                    break
            if target_coords:
                break

        # 使用Airtest点击坐标
        if target_coords:
            touch(target_coords)
            sleep(1)
        else:
            print(f"未找到目标文字：{target_text}")  
    except Exception as e:
        print(f"未找到目标文字：{target_text}")  
    
#####################################################################




##ocr text####################################################################
def ocr_text_all() :
    sleep(1)
    try:
        # 截屏当前画面
        pic_path=f"{snapImgPath}\\now.png"
        snapshot(pic_path)
         # 使用PaddleOCR识别图片文字
        ocr_result = ocr.ocr(pic_path, cls=True)
        # 遍历识别结果，找到目标文字的坐标
        target_coords = None
        for line in ocr_result:
            for word_info in line:
                #获取识别结果的文字信息
                textinfo = word_info[1][0]
    #             print(f"ocr:{textinfo}")
        return ocr_result
    except Exception as e:
        return None
    


def ocr_text(start_x,start_y,end_x,end_y) :
    sleep(1)
    try:
        # 截屏当前画面
        pic_path=f"{snapImgPath}\\now.png"
        screen = G.DEVICE.snapshot()  
        cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
        cropped_screen = Image.fromarray(cropped_screen)
        cropped_screen.save(f"{snapImgPath}\\now.png")

         # 使用PaddleOCR识别图片文字
        ocr_result = ocr.ocr(pic_path, cls=True)
        # 遍历识别结果，找到目标文字的坐标
        target_coords = None
        for line in ocr_result:
            for word_info in line:
                #获取识别结果的文字信息
                textinfo = word_info[1][0]
    #             print(f"ocr:{textinfo}")
        return ocr_result
    except Exception as e:
        return None
    
##ocr text#################################################################
#########################################################
def swipe_to_end(judge_area,swipe_start,swipe_end):
    for i in range(10):
        start_screen = G.DEVICE.snapshot()
        cropped_start_screen = aircv.crop_image(start_screen, judge_area)
        swipe(swipe_start, swipe_end, duration=3)
#         swipe(swipe_start, swipe_end)
        sleep(1)
        end_screen = G.DEVICE.snapshot()
        cropped_end_screen = aircv.crop_image(end_screen, judge_area)
        confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)  
        print("拖动后计算目标区域相似度:", confidence)  
        if confidence > 0.8:
            print(f"区域相似度:{confidence},结束滚动，共滚动{i}次")
            break
    sleep(1)
############################
##全屏截图方法，去掉UUID
def save_cropped_screen(modename):  
    sleep(1)
    # 获取屏幕截图（这里假设G.DEVICE是全局可访问的）  
    screen = G.DEVICE.snapshot()  
#     cropped_screen = aircv.crop_image(screen, (1, 1, width, height-30))  
#     cropped_screen_image = Image.fromarray(cropped_screen)  
    
    cropped_screen = aircv.crop_image(screen, (1, 1, width, height-30))
    pil_img = cv2_2_pil(cropped_screen)
    
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
    filename = f'{snapImgPath}\\{modename}_{current_millis}.jpg'

    pil_img.save(filename)
    
    
    
def save_cropped_area_screen(modename,start_x,start_y,end_x,end_y):  
    sleep(1)
    # 获取屏幕截图（这里假设G.DEVICE是全局可访问的）  
    screen = G.DEVICE.snapshot()  
#     cropped_screen = aircv.crop_image(screen, (1, 1, width, height-30))  
#     cropped_screen_image = Image.fromarray(cropped_screen)  
    
    cropped_screen = aircv.crop_image(screen, (start_x, start_x, end_x, end_y))
    pil_img = cv2_2_pil(cropped_screen)
    
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
    filename = f'{snapImgPath}\\{modename}_{current_millis}.jpg'

    pil_img.save(filename)
#############################################################################
###########################################
# def add_string_to_char_array(char_array, name_to_add,value_to_add):  
#     if name_to_add in char_array:  
#         return False  
#     else:  
#         char_array[name_to_add] = f"{value_to_add}命"  # 使用None作为占位符值  
#         return True  
    
def add_to_json_array(json_array, name, value):  
    # 创建一个新的字典，模拟JSON对象  
    new_json_object = {"name": name, "value": value}
    # 将这个新的JSON对象添加到JSON数组中  
    json_array.append(new_json_object)  
    return json_array

##########################################


# ####测试区块######################
#  # 初始化PaddleOCR
# ocr = PaddleOCR(use_angle_cls=True, lang='ch')  # 可以根据需要选择语言
# ocr_text()

# ####测试区块######################


#第一步：根据sn获取账号密码
# productSn = "TRS763496665"## co
# productSn = "TRS869055512" ##穆总
# productSn = "TRS568767543" ##18368862467
# snapImgPath = f"C:\\Users\\<USER>\\airtest_log\\{productSn}"


##########################################################
# for j in range(10000):
#     productSn = requestCategoryProductSn(81)
#     if productSn is None:
#         print('无可录制的元神号，等待5s')
#         sleep(5)
#     else:
#         print(f'查询到可录制的元神号，{productSn}')
#         break


####################################################
# 使用函数读取配置文件  


productSn = "TRS869055512"
projectPath = f"D:\\kkzhw\\airtest_log"

# config = read_config(f'{projectPath}\\configs.txt')  

with open(f'{projectPath}\\configs.ini', 'r', encoding='utf-8') as config_file:  
    config_content = config_file.read() 
config = configparser.ConfigParser()  
config.read_string(config_content)
  


snapImgPath = f"{projectPath}\\{productSn}"

print("{snapImgPath}")
# 判断文件夹是否存在

# 如果存在则清空文件夹
clear_folder(snapImgPath)

#############################################################################全局变量
# 设置全局的截图精度为90
ST.SNAPSHOT_QUALITY = 90
# 获取当前设备的屏幕宽度和高度  
width, height = device().get_current_resolution()  
center_x = width // 2  
center_y = height // 2  
###############################################################################

    
##################################
# 创建一个logger  
logger = logging.getLogger('my_logger')  
logger.setLevel(logging.DEBUG)  
# 创建一个handler，用于写入日志文件  
fh = logging.FileHandler(f'{snapImgPath}\\my_app.log')  
fh.setLevel(logging.DEBUG)  
# 定义handler的输出格式  
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')  
fh.setFormatter(formatter)  
# 给logger添加handler  
logger.addHandler(fh)  


logger2 = logging.getLogger("airtest")
logger2.setLevel(logging.ERROR)
##########################################
auto_setup(__file__)

# 初始化PaddleOCR
ocr = PaddleOCR(use_angle_cls=True, lang='ch')  # 可以根据需要选择语言
###########################################################

productInfo = '{}'
for j in range(100):
    productInfo = requestGameAccountInfo(productSn)
    acc = productInfo['游戏账号']
    pss = productInfo['游戏密码']
    if acc == '' or pss == '':
        print('账号或密码为空，等待1s')
        sleep(1)
    else:
        break
        
print('查询到账号和密码，开始登录')
print(acc)
print(pss)
###############################################################

step_login = 1
if step_login == 0:
    #第二步：检查当前界面是否账密登录界面，如果是则开始登录游戏
    wait(Template(r"tpl1720155205626.png", record_pos=(-0.026, -0.049), resolution=(1920, 1080)),120)

    wait(Template(r"tpl1720154166238.png", record_pos=(-0.083, -0.084), resolution=(1920, 1080)),120)##等待出现账号密码输入框
    kktouch(774,376,1)##点击账号输入框
    sleep(1)
    text(acc)
    sleep(1)
    kktouch(814,507,1)##点击密码输入框
    text(pss)
    sleep(1)
    kktouch(590,629,1)##点击 同意条款
    kktouch(962,764,1)##点击 进入游戏


    wait(Template(r"tpl1720154481227.png", record_pos=(-0.003, 0.241), resolution=(1920, 1080)),120) ##等待底部点击进入

    kktouch(955,999,1)##点击 进入游戏

    wait(Template(r"tpl1720155430321.png", record_pos=(0.012, 0.214), resolution=(1920, 1080)),120)
    print("正在加载游戏======================")
    touch((center_x, center_y))
    wait(Template(r"tpl1720155569384.png", record_pos=(0.428, -0.252), resolution=(1920, 1080)),120)
    
    kktouch(center_x, center_y,3)
    kktouch(center_x, center_y+300,1)
    kktouch(center_x, center_y+300,1)
    print("游戏加载成功，开始进入截图步骤==========================================")




###############主角信息 开始###################################
step_maoxiandengji = 0
if step_maoxiandengji == 0:
    ocr_text_zhujue_info_maoxiandengjie = ''
    kktouch(70,62,1.5)##进入人物
    save_cropped_screen("kkimg_zhujue_info")

    ocr_text_zhujue_info = ocr_text(778,195,875,247) ##冒险等级ocr
    for line in ocr_text_zhujue_info:
        if line is None:
            break
        for word_info in line:
            if word_info is None:
                break
            ocr_text_zhujue_info_maoxiandengjie = word_info[1][0]

    print(f"冒险等级：{ocr_text_zhujue_info_maoxiandengjie}")
    touch((58,58))
    sleep(1)
# # ##############点亮人物 结束##################################




#第三步：开始截图命之座和属性#####################################################
step_juese = 1
if step_juese == 0:
    touch((1828,54))##进入角色
    sleep(1.0)
    start_x = 95 
    start_y = 187
    end_y = 800

    swipe((start_x,start_y), (start_x,start_y+100)) ##向下滚动，确保第一个
    ##获取左上logo图像
    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen, (46, 5, 142, 120))
    touch((start_x,start_y))
    ##获取左上logo图像
    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, (46, 5, 142, 120))
    # 计算相似度  
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)  
    print("===计算初始相似度:", confidence)  
    if confidence < 0.9:
        swipe((start_x,start_y), (start_x,start_y+100)) ##向下滚动，确保第一个
        sleep(0.1)


    loop_step = 122
    is_over = 0
    juese_name_array = [] ##存储所有人物名称，例如：火元素/安
    juese_golden_name_array = [] ##存储所有人物名称，例如：火元素/安
    juese_name_set = set()
    # all_juese_names = config['DEFAULT']['all_juese_names'] 
    all_juese_golden_names = config['DEFAULT']['all_golden_juese_names'] 

    for j in range(6):
        pagePersonCount = 0
        for i in range(7):##一屏七个角色
            if is_over == 1:
                break
            pagePersonCount = i+1
            y = start_y + loop_step*i
            #点击左侧人物
            print(f'定位人物 横坐标：{start_x} 纵坐标：{y}')
            touch((start_x,y))

    #         ##获取左上logo图像
    #         start_screen = G.DEVICE.snapshot()
    #         cropped_start_screen = aircv.crop_image(start_screen, (46, 5, 142, 120))
    #         touch((start_x,y))

    #         ##获取左上logo图像
    #         end_screen = G.DEVICE.snapshot()
    #         cropped_end_screen = aircv.crop_image(end_screen, (46, 5, 142, 120))
    #         # 计算相似度  
    #         confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)  
    #         print("人物头像点击后计算，左上logo相似度:", confidence)  
    #         if i > 0 and confidence >= 0.9:
    #             is_over = 1
    #             break

            #一屏的最后一个不截图，等滚动到最上面再截图
            if i == 5:
                break
            ####################################属性截图开始
            kktouch(300,185,2) ##点击属性
            ocr_text_shuxin = ocr_text(1310,138,1694,227) ##属性页OCR
            if ocr_text_shuxin is None:
                continue
            now_juese_name = ""
    #         print(f"{ocr_text_shuxin}")
            is_skip = 0
            for line in ocr_text_shuxin:
                if line is None:
                    break
                for word_info in line:
                    if word_info is None:
                        break
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    now_juese_name = textinfo
    #                 if(textinfo in all_juese_names):
                    if(textinfo != ""):
                        print("识别到元素："+textinfo)
                        if textinfo in juese_name_set:
                            print(f"字符串 '{textinfo}' 已存在，未添加。")
                            is_over = 1
                            break
                        else:
                            juese_name_set.add(textinfo)
                            break
                    else:
                        is_skip = 1
            if is_skip == 1:
                print(f"角色不在白名单中，跳过：{now_juese_name}")
                continue
            ##点击详细信息
            kktouch(1554,619,1)

            save_cropped_screen("kkimg_juse_shuxing")
            kktouch(1821,54,0.5)##点击X返回
            ####################属性截图结束


            #####################命之座截图开始
            kktouch(300,454,1)##点击命之座
            ###############
            ming_count = 0
            ming_lock_count = 0
            findLockResult = find_all(Template(r"tpl1720263361614.png", record_pos=(0.228, 0.19), resolution=(1920, 1080)))
            if findLockResult is None:  
                ming_count = 6
            else:
                for line in findLockResult:
                    print(f"锁相似度:{line['confidence']}")
                    if line['confidence'] > 0.85:
                        ming_lock_count = ming_lock_count + 1
            ming_count = 6 - ming_lock_count
            print(f"=计算命之座解锁数量===={ming_count}")

            if now_juese_name != "":
                if now_juese_name in all_juese_golden_names:
                    add_to_json_array(juese_golden_name_array,now_juese_name,ming_count)
                else:
                    add_to_json_array(juese_name_array,now_juese_name,ming_count)
            #################
            print(f"搜集到的紫色角色：{juese_name_array}")
            print(f"搜集到的金色角色：{juese_golden_name_array}")

            sleep(0.3)
            ##截图命之座
            save_cropped_screen("kkimg_juese__mingzhizuo")

        if is_over == 1:
            print(f'检测到is_voer====={is_over},第{j}屏')
            break
        ##左侧人物滚动
        swipe((start_x,end_y), (start_x,start_y), duration=7,steps=30)
        print(f'左侧人物滚动=====第{j}次')
        sleep(0.1)

    dumpsJson = json.dumps(juese_name_array,ensure_ascii=False) 
    logger.info(f"{dumpsJson}")
    kktouch(1830,58,1)##点击右上退出人物面板
    print("命之座和属性结束")
    #####################################################


step_beibao = 1
if step_beibao == 0:
    print("背包截图开始")
    kktouch(1717,64,1)##点击右上 背包

    ######背包截图-武器开始################
    kktouch(99,195,1)##点击左侧 武器
    kktouch(352,996,1)##点击筛选框
    # ocr_touch("品质顺序")
    kktouch(314,875,1)##品质顺序
    kktouch(250,230,1)##点击第一个


    # 设定循环次数或条件，这里简单示例为循环10次  
    wuqi_name_array = [] ##存储所有人物名称，例如：火元素/安
    for i in range(2):
        findFiveStarResult = find_all(Template(r"tpl1720243142299.png", record_pos=(-0.274, -0.131), resolution=(1920, 1080)))
        if findFiveStarResult is None:  
            break
        fiveStarCount = 0
        for line in findFiveStarResult:
            if line['confidence'] > 0.8:
                fiveStarCount = fiveStarCount +1
        print(f"=====发现金色武器 五星数量===={fiveStarCount}")

        if fiveStarCount > 0:
            save_cropped_area_screen("kkimg_beibao_wuqi",176, 136, 1220, 924)
            sleep(1)

            wuqi_detail_start_x = 252
            wuqi_detail_start_y = 236
            for i in range(fiveStarCount):
                touch((wuqi_detail_start_x,wuqi_detail_start_y))
                sleep(0.5)
                ocr_text_wuqi = ocr_text(1254,125,1760,634) ##武器详情页ocr

                now_wuqi_name = ''
                now_wuqi_value = ''
                for line in ocr_text_wuqi:
                    if line is None:
                        break
                    for word_info in line:
                        if word_info is None:
                            break
                        #获取识别结果的文字信息
                        textinfo = word_info[1][0]
                        if now_wuqi_name == '':
                            now_wuqi_name = textinfo ##武器名称为ocr识别出来的第一个字符
                        if "精炼" in textinfo:
                            now_wuqi_value = textinfo

                print(f"发现精炼武器 {now_wuqi_name}：{now_wuqi_value}")
                if now_wuqi_name in config['DEFAULT']['all_wuqi_names']:
                    add_to_json_array(wuqi_name_array,now_wuqi_name,now_wuqi_value)
                else:
                    print(f"精炼武器 {now_wuqi_name}：{now_wuqi_value}，不在白名单中")
                wuqi_detail_start_x = wuqi_detail_start_x + 182
                if (i + 1) % 6 == 0 and i != fiveStarCount - 1:
                    wuqi_detail_start_x = 252
                    wuqi_detail_start_y = wuqi_detail_start_y + 214

            ##武器栏滚动
            swipe((1125,855), (1121,214), duration=7,steps=20)
        else:
            print(f'未发现金色武器')
            break
    dumpsJson = json.dumps(wuqi_name_array,ensure_ascii=False) 
    logger.info(f"{dumpsJson}")
    print(f'武器截图结束,相关材料在{snapImgPath}')
    ######武器 结束#####################################################


##test#################################################
step_hecheng_pic = 1
if step_hecheng_pic == 0:
    # 加载底图  
    background = Image.open(f'{projectPath}\\kk_yuanshen_bg2.jpg')  
    #增加文字
    font = ImageFont.truetype(f'{projectPath}\\font\\zxf.ttf', 20)
    draw = ImageDraw.Draw(background)  

    font_productSn = ImageFont.truetype(f'{projectPath}\\font\\zxf.ttf', 16)
    text_position = (30, 300)  # (x, y)  
    draw.text(text_position, "编号："+productSn, fill=(119, 92, 61), font=font_productSn) 


    text_position = (30, 340)  # (x, y)  
    draw.text(text_position, "黄数：45", fill=(119, 92, 61), font=font_productSn) 

    text_position = (30, 380)  # (x, y)  
    draw.text(text_position, f"冒险等级：{ocr_text_zhujue_info_maoxiandengjie}", fill=(119, 92, 61), font=font_productSn) 

    text_position = (30, 420)  # (x, y)  
    bindstate = productInfo['绑定状态']
    draw.text(text_position, f"邮箱状态：{bindstate}", fill=(119, 92, 61), font=font_productSn) 


    juese_start_x = 272
    juese_start_y = 147
    i =0
    # 使用extend()方法  
    juese_golden_name_array.extend(juese_name_array)  
    for word_info in juese_golden_name_array:
        try:
            image_name = word_info['name']
            image_juese = Image.open(f'{projectPath}\\roles\\{image_name}.png')

            position = (juese_start_x, juese_start_y)  # 第一个小图片的位置  
            background.paste(image_juese, position)  

            juese_value_position = (juese_start_x+3, juese_start_y+2)  # (x, y)  
            fontJuese = ImageFont.truetype(f'{projectPath}\\font\\gete.ttf', 16)
            juese_value = word_info['value']
            draw.text(juese_value_position, f"{juese_value}命", fill=(255, 255, 255), font=fontJuese) 

            juese_start_x = juese_start_x + 123
            if (i + 1) % 7 == 0 and i != len(juese_name_array) - 1:
                juese_start_x = 272
                juese_start_y = juese_start_y + 147
            # 将小图片粘贴到底图上  
            i = i +1
            if i == 35:
                break
        except Exception as e:
            continue


    wuqi_start_x = 1157
    wuqi_start_y = 147
    i =0
    for word_info in wuqi_name_array:
        try:
            image_name = word_info['name']
            image_wuqi = Image.open(f'{projectPath}\\wuqi\\{image_name}.png')

            position = (wuqi_start_x, wuqi_start_y)  # 第一个小图片的位置  
            background.paste(image_wuqi, position)  

            text_position = (wuqi_start_x+3, wuqi_start_y+2)  # (x, y)  
            font_jinlian = ImageFont.truetype(f'{projectPath}\\font\\gete.ttf', 16)
            draw.text(text_position, word_info['value'], fill=(255, 255, 255), font=font_jinlian) 

            wuqi_start_x = wuqi_start_x + 122
            if (i + 1) % 4 == 0 and i != len(wuqi_name_array) - 1:
                wuqi_start_x = 1157
                wuqi_start_y = wuqi_start_y + 122
            # 将小图片粘贴到底图上  
            i = i +1
        except Exception as e:
            continue

    # 保存合成的图像  
    background.save(f'{snapImgPath}\\bg_{productSn}.png')
    print("合成的图像结束")



###test################################################
step_shengyiwu = 1
if step_shengyiwu == 0:
    print("背包截图圣遗物开始")
    touch((97,310))##点击左侧 圣遗物
    sleep(1)
    touch((250,230))##点击第一个

    # 截图一张
    screen = G.DEVICE.snapshot()  
    cropped_screen = aircv.crop_image(screen, (1, 1, width, height-30))
    pil_img = cv2_2_pil(cropped_screen)
    pil_img.save(f'{snapImgPath}\\kkimg_beibao_shenyiwu_{i}.jpg')

    print(f'圣遗物截图结束,相关材料在{snapImgPath}')

    # 设定循环次数或条件，这里简单示例为循环10次  
    for i in range(10):
        touch((250,230))##点击第一个
        findFiveStarResult = find_all(Template(r"tpl1720243142299.png", record_pos=(-0.274, -0.131), resolution=(1920, 1080)))
        if findFiveStarResult is None:  
            break
        print(f"=====发现金色圣遗物 五星数量===={findFiveStarResult}")
        fiveStarCount = 0
        for line in findFiveStarResult:
            if line['confidence'] > 0.8:
                fiveStarCount = fiveStarCount +1
        print(f"=====发现金色圣遗物 五星数量===={fiveStarCount}")

        if fiveStarCount > 0:
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (1, 1, width, height-30))
            cropped_screen = Image.fromarray(cropped_screen)  
            cropped_screen.save(f'{snapImgPath}\\kkimg_beibao_shenyiwu_{i}.jpg')

            sleep(1)
            ##武器栏滚动
            swipe((1125,855), (1121,214), duration=7,steps=20)
        else:
            print(f'未发现圣遗物')
            break



step_yangcheng = 1
if step_yangcheng == 0:
    ######背包截图-养成道具开始------------------------
    touch((92,429))##点击左侧 养成道具
    sleep(1)
    touch((250,230))##点击第一个

    # 截图一张
    screen = G.DEVICE.snapshot()  
    cropped_screen = aircv.crop_image(screen, (1, 1, width, height-30))
    pil_img = cv2_2_pil(cropped_screen)
    pil_img.save(f'{snapImgPath}\\kkimg_beibao_yangchengdaoju_{i}.jpg')

    print(f'养成道具结束,相关材料在{snapImgPath}')
#########################################################
step_cailiao = 1
if step_cailiao == 0:
    ######背包截图-材料开始------------------------
    touch((92,671))##点击左侧 养成道具
    sleep(1)
    touch((250,230))##点击第一个

    # 截图一张
    screen = G.DEVICE.snapshot()  
    cropped_screen = aircv.crop_image(screen, (1, 1, width, height-30))
    pil_img = cv2_2_pil(cropped_screen)
    pil_img.save(f'{snapImgPath}\\kkimg_beibao_cailiao_{i}.jpg')

    print(f'材料结束')
    print(f"材料结束，相关材料在{snapImgPath}")
#########################################################
step_guizhongdaoju = 1
if step_guizhongdaoju == 0:
    ######背包截图-贵重道具开始------------------------
    swipe((89,674), (89,290), duration=5,steps=10)
    sleep(1)
    touch((92,671))##点击左侧 养成道具
    sleep(1)
    touch((250,230))##点击第一个

    # 截图一张
    screen = G.DEVICE.snapshot()  
    cropped_screen = aircv.crop_image(screen, (1, 1, width, height-30))
    pil_img = cv2_2_pil(cropped_screen)
    pil_img.save(f'{snapImgPath}\\kkimg_beibao_guizhongdaoju_{i}.jpg')

    print(f'贵重道具结束,相关材料在{snapImgPath}')
    ###################################
    print(f'退出背包')
    touch((1822,62))##退出背包
    sleep(1)
########################################################################
step_img_upload = 1
if step_img_upload == 0:
    ###oss上传文件##########################################
    sleep(1)
    print(f'开始上传')
    upload_kk_img_to_oss(f'{snapImgPath}')
    sleep(5)
    print(f'上传结束')
    ###oss上传文件##########################################

###################################################################
print(f"录号结束，相关材料在{snapImgPath}")



step_logout = 0
if step_logout == 0:
    print("退出游戏账号开始")
    sleep(2)
    kktouch(71,56,3)##主游戏页左上人物
    kktouch(59,1013,3)##左下退出
    kktouch(1231,800,3)##弹框，“确认退出游戏-退出”，
    wait(Template(r"tpl1720152292465.png", record_pos=(0.436, 0.218), resolution=(1920, 1080)),120)##等待退出按钮出现
    kktouch(1796,956,3)##点击右下角退出按钮
    wait(Template(r"tpl1720600146830.png", record_pos=(0.107, 0.032), resolution=(1920, 1080)),120)
    kktouch(1152,602,3)##点击弹窗 “确认”
    ##到此界面停留在手机短信验证码登录界面
    wait(Template(r"tpl1720600210978.png", record_pos=(0.002, 0.08), resolution=(1920, 1080)),120)

    kktouch(959,788,2)##点击登录其他账号
    wait(Template(r"tpl1720154070521.png", record_pos=(-0.082, 0.193), resolution=(1920, 1080)),120)##等待出现左下“账号密码”
    kktouch(827,912,2)##点击账号密码，进入账密输入页面
    #############退出 结束###################################

























