# -*- encoding=utf8 -*-
__author__ = "kkzhw016"

from airtest.core.api import *

auto_setup(__file__)
# -*- encoding=utf8 -*-
__author__ = "kk001"

from http.client import responses
from io import BytesIO

from airtest.core.api import *

auto_setup(__file__)
# -*- encoding=utf8 -*-
__author__ = "labugao"

# auto_setup(__file__)

# if 'auto_setup' in globals():
#     auto_setup(__file__)
# else:
#     from airtest.core.api import *
#     from airtest.cli.parser import cli_setup

#     if not cli_setup():
#         auto_setup(__file__, logdir=False, devices=[
#             "Android:///?cap_method=javacap&ori_method=adbori",
#     ])



from airtest.core.api import *
from airtest.aircv import *
from paddleocr import PaddleOCR
import requests
import json
from airtest.aircv.cal_confidence import cal_ccoeff_confidence  
from airtest.core.settings import Settings as ST
import oss2  
import os  
import numpy as np  
import configparser
from PIL import Image, ImageDraw, ImageFont,ImageFilter
import shutil  
import logging  
from urllib.parse import urlparse

import os
import sys
curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)
import configs
import product_util
import logger_config
import image_util
from server_client import PortalClient
from luhao_models import *
import sys_tool

from collections import defaultdict

#####################################################
# auto_setup(__file__, devices=["Android://127.0.0.1:5037/127.0.0.1:50000?ori_method=adbori"])

auto_setup(__file__)
# connect_device("Android://127.0.0.1:5037/127.0.0.1:5555?ori_method=adbori")
# 使用connect_device

# auto_setup(__file__,devices=["Android://127.0.0.1:5037/SJE5T17B17"])
# dev = connect_device("Android://127.0.0.1:50000/003/?cap_method=javacap&ori_method=adbori") 
#########################################################################
import datetime  
import random  
import string  
import re  
#############################################
#############################################################################全局变量
projectPath = "D:\\kkzhw\\airtest_log\\"
snapImgPath = ""
logger = None
logger2 = None
# 设置全局的截图精度为90
ST.SNAPSHOT_QUALITY = 99
# 获取当前设备的屏幕宽度和高度
width = 1
height = 1
# width, height = device().get_current_resolution()
center_x = 1
center_y = 1
product_meta = []
luhao_task = None
pic_url_arrays = []
tianniran_ocr_txt_arrays = set()
ocr_file = 1 #ocr 是否保存识别区域图片 1保存图片 0不保存
###############################################################################


##########################################


ocr = PaddleOCR(use_angle_cls=True, lang='ch',rec_model_dir="D:\\airtest_script\\ch_PP-OCRv4_rec_infer") 


portal_client = PortalClient()
###########################################################################

def remove_unwanted_chars(s):
    s = re.sub(r'\[[^\]]*\]', '', s) 
    return re.sub(r'[^·\u4e00-\u9fff]', '', s)
  
####################################################
def contains_digit(s):  
    return bool(re.search(r'\d', s)) 
######################################################################
def clear_folder(folder_path):
    # 文件夹路径  
    new_folder_name = f'{folder_path}_' + str(int(time.time()))
    if os.path.exists(folder_path):  
        shutil.move(folder_path, new_folder_name)  
        print(f"文件夹 '{folder_path}' 已重命名为 '{new_folder_name}'")
    os.makedirs(folder_path, exist_ok=True)
    print(f"新的空文件夹 '{folder_path}' 已创建")
######################################################################
def clear_folder2(folder_path):
    if os.path.exists(folder_path):
       return
    os.makedirs(folder_path, exist_ok=True)
    print(f"新的空文件夹 '{folder_path}' 已创建")
######################################################################
  
def generate_oss_path():  
    # 获取当前日期并格式化为 yyyyMMdd  
    today = datetime.datetime.now().strftime("%Y%m%d")  
    # 生成六位随机字符数字串  
    # 注意：这里使用了string.ascii_letters + string.digits来确保生成的是字母和数字的混合  
    # 如果只想要数字，可以去掉string.ascii_letters部分  
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=6))  
    # 获取当前毫秒时间戳  
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
    # 拼接路径  
    oss_path = f"{today}/"  
    return oss_path
###################################################################
def read_config(filename):  
    config = {}  
    with open(filename, 'r', encoding='utf-8') as file:  
        for line in file:  
            # 去除每行两端的空白字符，并检查是否为空行或注释行（假设以#开头的行为注释）  
            line = line.strip()  
            if not line or line.startswith('#'):  
                continue  
            # 使用等号分割键和值  
            key, value = line.split('=', 1)  
            # 去除值两端的引号（如果有的话）  
            value = value.strip('"').strip("'")  
            config[key] = value  
    return config  
########################################################################

def generate_oss_fileName():  
    # 生成六位随机字符数字串  
    # 注意：这里使用了string.ascii_letters + string.digits来确保生成的是字母和数字的混合  
    # 如果只想要数字，可以去掉string.ascii_letters部分  
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=7))  
      
    # 获取当前毫秒时间戳  
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
      
    # 拼接路径  
    oss_fileName = f"{random_str}_{current_millis}.jpg"  
      
    return oss_fileName
############################################################################


################################################
def resize_image_by_width(input_image_path, output_image_path, target_width):  
    # 打开原始图片  
    with Image.open(input_image_path) as img:  
        # 获取原始图片的宽度和高度  
        original_width, original_height = img.size  
          
        # 计算缩放比例  
        ratio = original_width / float(target_width)  
          
        # 根据缩放比例计算新的高度  
        new_height = int(original_height / ratio)  
          
        # 缩放图片  
        resized_img = img.resize((target_width, new_height), Image.LANCZOS)  
          
        # 保存缩放后的图片  
        resized_img.save(output_image_path)  
##############################################################################


#########################################################################

def upload_kk_img_to_oss(folder_path):
    image_path_set = set()
#     image_name_array = [] ##图片分类+url集合

    bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称  
    endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint  
    access_key_id = configs.OSS_ACCESS_KEY_ID
    access_key_secret = configs.OSS_ACCESS_KEY_SECRET
    logger.info(f"开始上传文件夹{folder_path} 内容")
    
    auth = oss2.Auth(access_key_id, access_key_secret)
    bucket = oss2.Bucket(auth, endpoint, bucket_name)  
    total_count = count_images(folder_path)
    up_count  = 0
    for filename in os.listdir(folder_path):
#         logger.info(f"发现图片：{filename}")
        local_file_path = os.path.join(folder_path, filename)  
        if filename.endswith('jpg') and "luhao_" not in filename:
            up_count = up_count + 1
            logger.info(f"上传图片：{up_count}/{total_count}")
            oss_file_path = "mall/images2/"+ generate_oss_path()  

            oss_fileName = oss_file_path+generate_oss_fileName()
            with open(local_file_path, 'rb') as fileobj:  
                bucket.put_object(oss_fileName, fileobj)
                image_path_set.add(oss_fileName)
                
                parts = filename.split('_')
                add_to_json_array(pic_url_arrays,parts[0],oss_fileName)
###############################################################################
def upload_one_img_to_oss(filename):
    image_path_set = set()
    bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称  
    endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint  
    access_key_id = configs.OSS_ACCESS_KEY_ID
    access_key_secret = configs.OSS_ACCESS_KEY_SECRET
    logger.info(f"开始上传文件夹{snapImgPath} 内容")
    
    auth = oss2.Auth(access_key_id, access_key_secret)
    bucket = oss2.Bucket(auth, endpoint, bucket_name)  

    local_file_path = os.path.join(snapImgPath, filename)  
    oss_file_path = "mall/images2/"+ generate_oss_path()  
    oss_fileName = oss_file_path+generate_oss_fileName()
    with open(local_file_path, 'rb') as fileobj:
        bucket.put_object(oss_fileName, fileobj)
    
    print(f"oss_filename:{oss_fileName}")
    return oss_fileName
    
#############################################################
def count_images(folder_path):  
    jpg_count = 0  
    png_count = 0  
    # 遍历指定文件夹及其所有子文件夹  
    for root, dirs, files in os.walk(folder_path):  
        for file in files:  
            # 检查文件扩展名  
            if file.lower().endswith('.jpg'):  
                jpg_count += 1  
#             elif file.lower().endswith('.png'):  
#                 png_count += 1  
    # 返回总数  
    total_images = jpg_count + png_count  
    return total_images  
###################################################################


############################################################################
#根据sn查询账号密码
def requestGameAccountInfo(productSn):  
    kk_url = 'https://api2.kkzhw.com/mall-portal/import/product/getProductAccountInfo?productSn='+productSn
    response = requests.get(kk_url)
    # 检查请求是否成功  
    if response.status_code == 200:  
        # 打印返回的内容  
        logger.info(response.text)
        responseObj = json.loads(response.text)
        product_dict = responseObj.get('data')
        return product_dict
    
########################################################################
#根据分类ID获取待录号productSn
def requestCategoryProductSn(categoryId):  
    kk_url = f'http://api2.kkzhw.com/mall-portal/import/product/getTheProductAccountInfo?categoryId={categoryId}'
    response = requests.get(kk_url)
    # 检查请求是否成功  
    if response.status_code == 200:  
        # 打印返回的内容  
#         print(response.text)
        responseObj = json.loads(response.text)
        productSn = responseObj.get('data')
        return productSn
    else:
        return ""
###########################################################################
def requestGameProductCategoryMeta(categoryId): 
    kk_url = f'https://api2.kkzhw.com/mall-portal/openapi/record/product/meta?categoryId={categoryId}'
    response = requests.get(kk_url)
    # 检查请求是否成功
    if response.status_code == 200:  
        # 打印返回的内容
        responseObj = json.loads(response.text)
        product_meta = responseObj.get('data')
        return product_meta
###################################################################根据sn查询账号密码


##OCR TOUCH#########################################ffffffffff##########
def ocr_touch(target_text) :
      # 截屏当前画面
    pic_path=f"{snapImgPath}\\now.png"
    snapshot(pic_path)
    
     # 使用PaddleOCR识别图片文字
    ocr_result = ocr.ocr(pic_path, cls=True)
    
    # 遍历识别结果，找到目标文字的坐标
    target_coords = None
    for line in ocr_result:
        for word_info in line:
            #获取识别结果的文字信息
            textinfo = word_info[1][0]
            logger.info(textinfo)
            if target_text in textinfo:
                # 获取文字的坐标（中心点）
                x1, y1 = word_info[0][0]
                x2, y2 = word_info[0][2]
                target_coords = ((x1 + x2) / 2, (y1 + y2) / 2)
                break
        if target_coords:
            break

    # 使用Airtest点击坐标
    if target_coords:
        touch(target_coords)
    else:
        logger.info(f"未找到目标文字：{target_text}")
#####################################################################


##ocr text####################################################################
def ocr_text_all():
    for i in range(5):
        try:
            sleep(1)
            pic_path=f"{snapImgPath}\\now.png"
            snapshot(pic_path)
            ocr_result = ocr.ocr(pic_path, cls=True)
            target_coords = None
            for line in ocr_result:
                for word_info in line:
                    textinfo = word_info[1][0]
            return ocr_result
        except Exception as e:
            logger.error(f"ocr_text_all 失败：{e}")  
            sleep(2)
            continue
    return None
############################################################

def find_all_subImg(temp, start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            find_result =  temp.match_all_in(local_screen)

            logger.info(f"find_all_subImg：{find_result}")

            if find_result:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_all_subImg_{current_millis}.png')
            return find_result
        except Exception as e:
            logger.error(f"find_all_subImg 失败：{e}")  
            sleep(2)
            continue
    return None
###################################################
def find_subImg(temp, start_x,start_y,end_x,end_y):
    sleep(1)
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            find_result = temp.match_in(local_screen)

            logger.info(f"find_subImg：{find_result}")

            if find_result:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_subImg_{current_millis}.png')
            return find_result
        except Exception as e:
            logger.error(f"find_subImg 失败：{e}")  
            sleep(2)
            continue
    return None
########################################################
def find_subImg_inscreen(temp,local_screen):
    sleep(1)
    for i in range(5):
        try:
            find_result = temp.match_in(local_screen)

            logger.info(f"find_subImg_inscreen：{find_result}")

            if find_result:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_subImg_inscreen_{current_millis}.png')
            return find_result
        except Exception as e:
            logger.error(f"find_subImg_inscreen 失败：{e}")  
            sleep(2)
            continue
    return None
#########################################################
def find_first_subImg(temp, start_x,start_y,end_x,end_y):
    sleep(1)
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            f_result = temp.match_all_in(local_screen)

            logger.info(f"find_first_subImg：{f_result}")

            if f_result:
                #保存为图片
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_first_subImg_{current_millis}.png')

                print(f"f_result:{f_result}")  
                min_y = f_result[0]['result'][1]  # 假设f_result的第一个元素的第二个值是y坐标  
                min_y_item = f_result[0]['result']  

                # 遍历f_result找到y坐标最小的匹配项  
                for item in f_result:  
                    # 假设item是一个包含(x, y, w, h)的元组  
                    current_y = item['result'][1]
                    if current_y < min_y:  
                        min_y = current_y
                        min_y_item = item['result']
                print("Y坐标最小的匹配项:", min_y_item)  
                print("Y坐标:", min_y)  
                return min_y_item
            else:
                return None
        except Exception as e:
            logger.error(f"find_first_subImg 失败：{e}")  
            sleep(2)
            continue  
    return None
#######################################################################

def ocr_text(start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()
            if screen is None:
                sleep(0.5)
                screen = G.DEVICE.snapshot()
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            ocr_result = ocr.ocr(cropped_screen, cls=True)

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\now_ocr_{current_millis}.png")

            return ocr_result
        except Exception as e:
            logger.error(f"ocr_text 失败：{e}")  
            sleep(2)
            continue
    return None
###############################################################################
def ocr_text_all(target_text,start_x,start_y,end_x,end_y) :
    for i in range(5):
        try:
            sleep(0.5)
              # 截屏当前画面
            pic_path=f"{snapImgPath}\\now_orc_all.png"
            screen = G.DEVICE.snapshot()  
            sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            pil_img = cv2_2_pil(cropped_screen)
            pil_img.save(f"{snapImgPath}\\now_orc_all.png")

             # 使用PaddleOCR识别图片文字
            ocr_result = ocr.ocr(pic_path, cls=True)
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            target_coords_list = []
            for line in ocr_result:
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    logger.info(f"ocr_text_all 识别到{textinfo}")
                    if target_text in textinfo:
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        target_coords_list.append(target_coords)
            logger.info(f"识别到{target_text},数量：{len(target_coords_list)}")
            return target_coords_list
        except Exception as e:
            logger.error(f"ocr_text_all 失败：{e}")  
            sleep(2)
            continue
    return None
################################################################################
def ocr_text_target_coords(target_text,start_x,start_y,end_x,end_y) :
    sleep(0.5)
    for i in range(5):
        try:  
            pic_path=f"{snapImgPath}\\now.png"
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\ocr_text_target_coords_{target_text}_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=True)
            if ocr_result is None:
                return None
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    logger.info(f"识别到文字：{textinfo}")
                    if target_text in textinfo:
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        break
                if target_coords:
                    break

            # 使用Airtest点击坐标
            if target_coords:
                return target_coords
            else:
                logger.info(f"未找到目标文字：{target_text}")
                return target_coords
        except Exception as e:
            sleep(2)
            logger.error(f"ocr_text_target_coords：{e}")  
            continue
            
    return None
#########################################################################
def ocr_text_target_coords_arrays(target_text,start_x,start_y,end_x,end_y) :
    sleep(0.5)
    for i in range(5):
        try:  
            target_coords_arrays = []
#             pic_path=f"{snapImgPath}\\now.png"
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\ocr_text_target_coords_arrays_{target_text}_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=True)
            if ocr_result is None:
                return None
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    logger.info(f"识别到文字：{textinfo}")
                    if target_text in textinfo:
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        target_coords_arrays.append(target_coords)

            # 使用Airtest点击坐标
            if len(target_coords_arrays) > 0:
                return target_coords_arrays
            else:
                logger.info(f"未找到目标文字：{target_text}")
                return None
        except Exception as e:
            sleep(2)
            logger.error(f"ocr_text_target_coords_arrays：{e}")  
            continue
            
    return None
##ocr text#################################################################
def ocr_text_has_number(start_x,start_y,end_x,end_y) :
    sleep(0.5)
    
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\ocr_text_has_number_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=True)
            if ocr_result is None:
                return None
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    if bool(re.search(r'\d', textinfo)):
                        logger.info(f"ocr_text_has_number查找到数字：{textinfo}")
                        return True

            return False
        except Exception as e:
            sleep(2)
            logger.error(f"ocr_text_has_number：{e}")  
            continue
            
    return False
############################
##全屏截图方法，指定区域打码，去掉UUID
def save_cropped_screen_with_blur(modename, x1, y1, x2, y2):  
    sleep(1)
    for i in range(5):
        try:  
            logger.info(f"全屏截图，部分打码：{modename}")
            blur_region = (x1, y1, x2, y2)
            screen = G.DEVICE.snapshot()  

            pil_img = cv2.cvtColor(screen, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(pil_img)

            region_to_blur = pil_img.crop(blur_region)

            region_to_blur = region_to_blur.filter(ImageFilter.GaussianBlur(radius=20))

            pil_img.paste(region_to_blur, blur_region)

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg', 'JPEG')
            break
        except Exception as e:
            sleep(2)
            logger.error(f"save_cropped_screen_with_mulblur：{e}")  
            continue
######################################################################                
def check_and_try_game_home(attempts=4,step=None):
    kkLogger().info(f"check_and_try_game_home: {attempts}")
    if attempts == 4 and step is None:
        check_account_login_other()
        check_yueka_jiazeng(1)
        check_game_update(1)
        check_game_alert_note()

    if attempts <= 0:  
        return False
    if ocr_text_target_coords("菜",1163,11,1268,70) is not None:
        logger.info(f"发现主页菜单")
        return True
    if exists(Template(r"tpl1727874508588.png", record_pos=(0.459, -0.255), resolution=(1280, 720))) or ocr_text_target_coords("单", 1169, 3, 1265, 64) is not None:  
        return True  
    else:
        logger.info(f"当前不在游戏主页，尝试回到主页")

        coords = ocr_text_target_coords("江湖",445, 613,773, 685)
        if coords is not None:
            touch(coords)
            print("点击江湖匆匆")
            sleep(2)

        target = exists(Template(r"tpl1727549637193.png", record_pos=(0.466, -0.252), resolution=(1280, 720)))
        if target is not False:
            kktouch2(target[0], target[1],1,"X")

        kktouch2(59, 31, 1, "左上返回")
        return check_and_try_game_home(attempts - 1)
###########################################################################
def save_cropped_screen_with_mulblur(modename, blur_regions):  
    sleep(1)
    for i in range(5):
        try:  
            logger.info(f"全屏截图，部分打码：{modename}")

            screen = G.DEVICE.snapshot()  
            pil_img = cv2.cvtColor(screen, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(pil_img)

            # 遍历所有需要模糊的区域  
            for blur_region in blur_regions:  
                x1, y1, x2, y2 = blur_region  
                # 确保区域在图片范围内  
                x1, y1, x2, y2 = max(0, x1), max(0, y1), min(width, x2), min(height, y2)  
                # 裁剪出需要模糊的区域  
                region_to_blur = pil_img.crop((x1, y1, x2, y2))  
                # 应用高斯模糊  
                region_to_blur = region_to_blur.filter(ImageFilter.GaussianBlur(radius=20))  
                # 将模糊后的区域粘贴回原图  
                pil_img.paste(region_to_blur, (x1, y1))  

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg', 'JPEG')
            break
        except Exception as e:
            sleep(2)
            logger.error(f"save_cropped_screen_with_mulblur：{e}")  
            continue
##全屏截图方法，去掉UUID
def save_cropped_screen(modename):
    sleep(1)
    for i in range(5):
        try:  
            logger.info(f"全屏截图：{modename}")
            screen = G.DEVICE.snapshot()  
            sleep(0.2)
#             cropped_screen = aircv.crop_image(screen, (1, 1, width, height-30))
            pil_img = cv2_2_pil(screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg')
            
            return f"{modename}_{current_millis}.jpg"
        except Exception as e:
            sleep(2)
            logger.error(f"截图失败：{e}")  
            continue
    
###################################################################
def save_cropped_area_screen(modename,start_x,start_y,end_x,end_y):
    sleep(1)
    for i in range(5):
        try:  
            logger.info(f"区域截图：{modename}")
            screen = G.DEVICE.snapshot()  
            sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg')
            break
        except Exception as e:
            sleep(2)
            logger.error(f"save_cropped_area_screen 截图失败：{e}")  
            continue
######################################################################################
def save_cropped_area_screen_dazao(modename,start_x,start_y,end_x,end_y):
    sleep(1)
    for i in range(5):
        try:  
            logger.info(f"区域截图：{modename}")
            screen = G.DEVICE.snapshot()  
            sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            
            tile_img_2 = np.array(pil_img)
            if contains_purple_in_corner(tile_img_2,(74,62)):
                pil_img.save(f'{snapImgPath}\\{modename}_1_{current_millis}.jpg')
            elif contains_lightred_in_corner(tile_img_2,(74,62)):
                pil_img.save(f'{snapImgPath}\\{modename}_2_{current_millis}.jpg')
            else:
                pil_img.save(f'{snapImgPath}\\{modename}_0_{current_millis}.jpg')
            break
        except Exception as e:
            sleep(2)
            logger.error(f"save_cropped_area_screen 截图失败：{e}")  
            continue
######################################################################################
# 计算图像的哈希值  
tianniran_saved_hashes = set()  
def save_cropped_area_screen_tianniran(modename,start_x,start_y,end_x,end_y):
    sleep(1)
    for i in range(5):
        try:  
            logger.info(f"save_cropped_area_screen_tianniran区域截图：{modename}")
            screen = G.DEVICE.snapshot()  
            sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)
            
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg')
            break
        except Exception as e:
            sleep(2)
            logger.error(f"save_cropped_area_screen_tianniran 截图失败：{e}")  
            continue
######################################################################
def save_cropped_area_screen_and_ocr_add_to_get(modename,start_x,start_y,end_x,end_y,split_num=None,temp=None):
    sleep(1)
    for i in range(5):
        try:  
            logger.info(f"区域截图：{modename}")
            screen = G.DEVICE.snapshot()  
            sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            if split_num is not None:
                # 获取图像的尺寸  
                img_width, img_height = pil_img.size  
                # 计算切割后的小图片尺寸（这里假设均匀切割）  
                tile_width = img_width // split_num[1]  
                tile_height = img_height // split_num[0]
                # 切割图像并保存  
                for row in range(split_num[0]):  
                    for col in range(split_num[1]): 
                        left = col * tile_width
                        upper = row * tile_height  
                        right = (col + 1) * tile_width
                        lower = (row + 1) * tile_height
                        tile_img = pil_img.crop((left, upper, right, lower))  
                        
                        tile_img_2 = np.array(tile_img)
                        if temp is not None and find_subImg_inscreen(temp,tile_img_2) is not None:
                            logger.info(f"find_subImg_inscreen 发现锁")
                            continue
                        
                        result = (True,False)
                        for i in range(2):
                            result = ocr_screen_add_to_get(tile_img_2)
                            if result[1]:
                                break
                            else:
                                logger.info(f"ocr_screen_add_to_get 未识别到文字，重试一次")
                        
                        if result[0] is False:
                            continue
                        current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
                        if contains_yellow_in_corner(tile_img_2,(15,15)):
                            # 保存切割后的小图片  
                            tile_img.save(f'{snapImgPath}\\{modename}_0_{current_millis}_{row}_{col}.jpg')
                        else:
                            tile_img.save(f'{snapImgPath}\\{modename}_1_{current_millis}_{row}_{col}.jpg')

            break
        except Exception as e:
            sleep(2)
            logger.error(f"save_cropped_area_screen_and_ocr_add_to_get 截图失败：{e}")  
            continue
###############################################################################################################
###############################################################################################################
def contains_lightred_in_corner(tile_img_np, region_size):  
    try:  
        height, width, _ = tile_img_np.shape  

        x_end = region_size[0]  # 左上角区域的右边界  
        y_end = region_size[1]  # 左上角区域的下边界  

        corner_img = tile_img_np[0:y_end, 0:x_end]  
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  

        lower_purple = np.array([200, 130, 94])  # 紫色下限（色调、饱和度、明度）  
        upper_purple = np.array([233, 142, 123])  # 紫色上限（注意：HSV色调是循环的，所以180接近0）  

        # 创建紫色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_purple, upper_purple)  

        return np.any(mask != 0)  
    except Exception as e:  
        return False
###############################################################################################################
def contains_purple_in_corner(tile_img_np, region_size):  
    try:  
        height, width, _ = tile_img_np.shape  

        x_end = region_size[0]  # 左上角区域的右边界  
        y_end = region_size[1]  # 左上角区域的下边界  

        corner_img = tile_img_np[0:y_end, 0:x_end]  
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  

        lower_purple = np.array([120, 30, 150])  # 紫色下限（色调、饱和度、明度）  
        upper_purple = np.array([160, 200, 255])  # 紫色上限（注意：HSV色调是循环的，所以180接近0）  

        # 创建紫色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_purple, upper_purple)  

        return np.any(mask != 0)  
    except Exception as e:  
        return False
###############################################################################################
def contains_yellow_in_corner(tile_img_np, region_size):   
    try:
        height, width, _ = tile_img_np.shape  
        # 计算右下角区域的左上角坐标  
        x_start = width - region_size[0]  
        y_start = height - region_size[1]  
        corner_img = tile_img_np[y_start:y_start+region_size[1], x_start:x_start+region_size[0]]  
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  
        lower_yellow = np.array([15, 100, 100])  
        upper_yellow = np.array([35, 255, 255])  

        # 创建黄色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_yellow, upper_yellow)  

        # 检查掩码中是否存在非零像素，以确定是否存在黄色  
        return np.any(mask != 0)  
    except Exception as e:
        return False
#################################################################
def contains_yellow(tile_img_np):  
    try:
        hsv_img = cv2.cvtColor(tile_img_np, cv2.COLOR_RGB2HSV)  

        lower_yellow = np.array([15, 100, 100])  
        upper_yellow = np.array([35, 255, 255])  

        # 创建黄色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_yellow, upper_yellow)  

        # 检查掩码中是否存在非零像素，以确定是否存在黄色  
        return np.any(mask != 0)  
    except Exception as e:
        return False
###############################################################################
#识别到的名称放入meta中
def add_to_get(ocr_text):
    return ocr_check_and_add(ocr_text)
    
#####################################################################
def add_to_json_array(json_array, name, value):
    new_json_object = {"name": name, "value": value}
    json_array.append(new_json_object)  
    return json_array
################################################

def kktouch(x, y, sleep_time):  
    touch((x,y))
    sleep(sleep_time)
###################
def kktouch3(x, y, sleep_time,msg):
    kktouch2(x, y, sleep_time, msg)
#################################################
def kktouch2(x, y, sleep_time,msg):
    logger.info(f"点击{msg}")
    if msg == "菜单":
        check_and_try_game_home()
    if msg == "菜单" or msg == "武功" or msg == "技能":
        sleep_time = 2
    touch((x,y))
    sleep(sleep_time)
    if msg == "菜单":
        coords = ocr_text_target_coords("外",946, 55,1174, 160)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224, 32,1,"菜单")
            
        return True
    if msg == "外观":
        coords = ocr_text_target_coords("时",201, 644,354, 708)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224, 32,1,"菜单")
            kktouch2(x, y,sleep_time,msg)
        sync_current_snapshot("外观")
        return True
    if msg == "角色":
        coords = ocr_text_target_coords("角色",11, 92,143, 347)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224, 32,1,"菜单")
            kktouch2(x, y,sleep_time,msg)
        sync_current_snapshot("角色")
        return True
    if msg == "打造":
        coords = ocr_text_target_coords("打",88, 9,177, 58)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224, 32,1,"菜单")
            kktouch2(x, y,sleep_time,msg)
        sync_current_snapshot("打造")
        return True
    if msg == "武功":
        coords = ocr_text_target_coords("功",12, 92,140, 499)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224, 32,1,"菜单")
            kktouch2(x, y,sleep_time,msg)
        sync_current_snapshot("武功")
        return True
    if msg == "设置":
        coords = ocr_text_target_coords("下",4, 85,147, 548)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224, 32,1,"菜单")
            kktouch2(x, y,sleep_time,msg)
        sync_current_snapshot("游戏设置")
        return True
    if msg == "开放世界":
#         coords = ocr_text_target_coords("",58, 606,58, 606)
#         if coords is None:
#             check_and_try_game_home()
#             kktouch2(1224, 32,1,"菜单")
#             kktouch2(x, y,sleep_time,msg)

        sync_current_snapshot("开放世界")
        return True
    if msg == "宠物":
        coords = ocr_text_target_coords("宠物",95, 9,194, 63)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224, 32,1,"菜单")
            kktouch2(x, y,sleep_time,msg)
        sync_current_snapshot("宠物")
        return True
    if msg == "群侠":
        coords = ocr_text_target_coords("群",8, 84,158, 344)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224, 32,1,"菜单")
            kktouch2(x, y,sleep_time,msg)
        sync_current_snapshot("群侠")
        return True
    if msg == "庄园":
        coords = ocr_text_target_coords("庄园",87, 3,182, 61)
        if coords is None:
            return False
            #沒有庄园
        sync_current_snapshot("庄园")
        return True

    return True
#########################################################
def swipe_to_end(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and (judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        logger.info(f"空白区域,无需滚动")
        return True

    for i in range(20):
        try:  
            start_screen = G.DEVICE.snapshot()
            cropped_start_screen = aircv.crop_image(start_screen, judge_area)
            swipe(swipe_start, swipe_end, duration=1)
            sleep(1.5)
            end_screen = G.DEVICE.snapshot()
            cropped_end_screen = aircv.crop_image(end_screen, judge_area)
            confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)  
            logger.info(f"拖动后计算目标区域相似度:{confidence}")
            if confidence > 0.8:
                logger.info(f"区域相似度:{confidence},结束滚动，共滚动{i}次")
                break
        except Exception as e:
            sleep(1)
            logger.error(f"swipe_to_end error：{e}")  
            continue
###############################################################

#########################################################
def swipe_to_end_slow(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and (judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        logger.info(f"空白区域,无需滚动")
        return True

    for i in range(40):
        try:  
            start_screen = G.DEVICE.snapshot()
            cropped_start_screen = aircv.crop_image(start_screen, judge_area)
            swipe(swipe_start, swipe_end, duration=2)
            sleep(3)
            end_screen = G.DEVICE.snapshot()
            cropped_end_screen = aircv.crop_image(end_screen, judge_area)
            confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)  
            logger.info(f"拖动后计算目标区域相似度:{confidence}")
            if confidence > 0.8:
                logger.info(f"区域相似度:{confidence},结束滚动，共滚动{i}次")
                break
        except Exception as e:
            logger.error(f"swipe_to_end_slow error：{e}")  
            continue

#########################################################################
def swipe_and_judge_end(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and ocr_text(judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        logger.info(f"空白区域,无需滚动")
        return True

    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen,judge_area)
    
    swipe(swipe_start, swipe_end,steps=35, duration=7)
    sleep(2)
    
    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, judge_area)
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)  
    logger.info(f"拖动后计算目标区域相似度:{confidence}")
    if confidence > 0.8:
        logger.info(f"区域相似度:{confidence},已经滚动到尽头")
        return True
    else:
        return False
##################################################################
def swipe_and_judge_end_fast(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and ocr_text(judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        logger.info(f"空白区域,无需滚动")
        return True

    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen,judge_area)
    
    swipe(swipe_start, swipe_end, duration=2)
    sleep(2)
    
    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, judge_area)
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)  
    logger.info(f"拖动后计算目标区域相似度:{confidence}")
    if confidence > 0.8:
        logger.info(f"区域相似度:{confidence},已经滚动到尽头")
        return True
    else:
        return False
###########################################################

# NSH616218134
# def init_nsh(l_task):
#     global luhao_task
#     luhao_task = l_task
#     productSn = luhao_task['productSn']
#     global snapImgPath
#     global projectPath
#     snapImgPath = f"{projectPath}\\{productSn}"
#
#     projectPath = "D:\\kkzhw\\airtest_log"
#
#     clear_folder(snapImgPath)
#
#     global logger
#     if logger is None:
#         logger = logger_config.setup_logger(os.path.basename(__file__), log_file_name=f'{snapImgPath}\\{productSn}_runtime.log')
#         logger.info(f"屏幕 宽：{width}，高：{height}")
#         logger.info(f"图片路径：{snapImgPath}")
#
#     global product_meta
#     product_meta = l_task['product_meta']
# NSH616218134
def init(text_widget=None):
    global logger
    logger = logger_config.setup_logger(os.path.basename(__file__), text_widget=text_widget)


def init_nsh(l_task):
    global width
    global height

#     connect_device("Android://127.0.0.1:5037")
    connect_device("Android:///")
    width, height = device().get_current_resolution()
    print(f"设备屏幕宽：{width} 高：{height}")
    global center_x
    global center_y
    center_x = width // 2
    center_y = height // 2

    global luhao_task
    luhao_task = l_task
    productSn = luhao_task['productSn']
    global snapImgPath
    global projectPath
    nowTime = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    projectPath = "D:\\kkzhw\\airtest_log"
    snapImgPath = f"{projectPath}\\{productSn}_{nowTime}"

    clear_folder2(snapImgPath)

    global logger
    if logger is None:
        logger = logger_config.setup_logger(os.path.basename(__file__),
                                            log_file_name=f'{snapImgPath}\\{productSn}_runtime.log')
        kkLogger_log(f"屏幕 宽：{width}，高：{height}")
        kkLogger_log(f"图片路径：{snapImgPath}")

    global product_meta
    product_meta = l_task['product_meta']
    #     category_meta = requestGameProductCategoryMeta(75)

    check_game_gonggao()

#########################################################

def get_head_pic():
    pic = None
    for item in pic_url_arrays:
        if item['name'] == "头图":
            pic = item['value']
            break
    if pic is None:
        pic = pic_url_arrays[0]['value']
    return pic
#######################################################################


##################################################################
def ocr_add_to_get(start_x,start_y,end_x,end_y):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    add_to_get(get_ocr_txt)
                    logger.info(f"ocr_add_to_get ocr_get：{get_ocr_txt}")
    else:
        logger.info("ocr nothing")
###########################################################
def ocr_screen_add_to_get(cropped_screen):
    try:
        has_text = False
        if cropped_screen is not None:
            ocr_result = ocr.ocr(cropped_screen, cls=True)
            if ocr_result:
                for line in ocr_result:
                    if line is None:
                        continue
                    for word_info in line:
                        if word_info is None:
                            continue
                        else:
                            get_ocr_txt = word_info[1][0]
                            has_text = True
                            if any(keyword in get_ocr_txt for keyword in configs.lock_keywords):
                                logger.info(f"ocr_screen_add_to_get 识别到非解锁字符 ：{get_ocr_txt}")
                                return (False,has_text)
                            add_to_get(get_ocr_txt)
                            logger.info(f"ocr_screen_add_to_get ：{get_ocr_txt}")
                return (True,has_text)
            else:
                logger.info("ocr_screen_add_to_get ocr nothing")
                return (False,has_text)
        else:
            logger.info("ocr_screen_add_to_get cropped_screen is none")
            return (False,has_text)
    except Exception as e:
        return (False,has_text)
        logger.error(f"ocr_screen_add_to_get error：{e}")

##################################################################
def ocr_add_to_get_attr(start_x,start_y,end_x,end_y,attr_names):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    for a_name in attr_names:
                        ocr_check_and_add_attr(get_ocr_txt,a_name)
                    logger.info(f"ocr_add_to_get_attr ocr_get：{get_ocr_txt}")
    else:
        logger.info("ocr nothing")

######################################################
################################################################################
def ocr_add_to_get_and_count_tianniran_number(start_x,start_y,end_x,end_y):
    tianniran_count = 0
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    
                    get_ocr_txt = get_ocr_txt.strip()
                    logger.info(f"ocr_add_to_get_and_count_tianniran_number ：{get_ocr_txt}")
                    if "/" in get_ocr_txt:
                        tianniran_count_str  = get_ocr_txt.split("/")[0]
                        try:
                            tianniran_count = int(tianniran_count_str)//2
                            if tianniran_count >= 1:
                                ocr_check_and_set("天霓染",str(tianniran_count))
                                logger.info(f"ocr_add_to_get_and_count_tianniran_number 天霓染数量 ：{tianniran_count}")
                        except Exception as e: 
                            logger.error(f"ocr_add_to_get_and_count_tianniran_number ：{e}")
                    
    else:
        logger.info("ocr_add_to_get_and_count_tianniran ocr nothing")
    return tianniran_count
###########################################################################################
def ocr_add_to_get_and_count_tianniran(start_x,start_y,end_x,end_y,skip_word):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    
                    get_ocr_txt = get_ocr_txt.strip()  
                    if(len(get_ocr_txt) == 1):
                        continue
#                     add_to_get(get_ocr_txt)
                    if skip_word not in get_ocr_txt and "点" not in get_ocr_txt and "案" not in get_ocr_txt:
                        tianniran_ocr_txt_arrays.add(get_ocr_txt)
                    logger.info(f"ocr_add_to_get ocr_get：{get_ocr_txt}")
    else:
        logger.info("ocr nothing")
    logger.info(f"tianniran_ocr_txt_arrays ：{tianniran_ocr_txt_arrays}")
######################################################



#############################################################
def ocr_check_and_add(ocr_txt):
    global product_meta
#     logger.info(f"ocr_check_and_add ocr_txt:{ocr_txt}")
    ocr_txt = ocr_txt.strip()  
    ocr_txt = remove_unwanted_chars(ocr_txt)
    
    if len(ocr_txt) == 1 or len(ocr_txt) == 0:
        return
    ocr_txt2 = configs.ERROR_TEXT.get(ocr_txt, ocr_txt)
    ocr_txt2 = configs.black_attr_value.get(ocr_txt2,ocr_txt2)
#     logger.info(f"ocr_check_and_add ocr_txt2:{ocr_txt}")
    hit_item_name_value = None
    for item in product_meta:
        if item["type"] not in [1,2]:
            continue
        if item["inputList"] and item["name"] not in configs.SKIP_ATTRI_NAME:
            input_arrays = item["inputList"].split(',')
            values = item.get("values", [])
            for input_value in input_arrays:
                input_value_2 = remove_unwanted_chars(input_value)
                if ocr_txt2 == input_value_2:
                    hit_item_name_value = item["name"]+":"+ocr_txt2
                    if input_value not in values:
                        values.append(input_value)
                    logger.info(f'get attr {item["name"]}:{values}')
#                     else:
#                         logger.info(f"{input_value} already in {item["name"]}:{values}")

            item["values"] = values  # 更新字典中的values键 
    return hit_item_name_value
##############################################################
def ocr_check_and_add_attr(ocr_txt,attr_name):
    global product_meta
    logger.info(f"ocr_check_and_add_attr ocr_txt:{ocr_txt}")
    ocr_txt = ocr_txt.strip()  
    ocr_txt = remove_unwanted_chars(ocr_txt)
    
    if len(ocr_txt) == 1 or len(ocr_txt) == 0:
        return
    ocr_txt2 = configs.ERROR_TEXT.get(ocr_txt, ocr_txt)
    ocr_txt2 = configs.black_attr_value.get(ocr_txt2,ocr_txt2)
    logger.info(f"ocr_check_and_add ocr_txt2:{ocr_txt}")
    for item in product_meta:
        if item["type"] not in [1,2]:
            continue
        if item["inputList"] and item["name"] == attr_name:
            input_arrays = item["inputList"].split(',')
            values = item.get("values", [])
            for input_value in input_arrays:
                input_value_2 = remove_unwanted_chars(input_value)
                if ocr_txt2 == input_value_2:
                    if input_value not in values:
                        values.append(input_value)
                        logger.info(f"{input_value} append in {values}")
                    else:
                        logger.info(f"{input_value} already in {values}")

            item["values"] = values  # 更新字典中的values键 
    return ocr_txt2
#############################################################
def category_meta_item(item_name):
    for item in product_meta:
        if item["name"] == item_name:
            return item
        else:
            return None
##############################################################################
def get_attr_ocr_number(attr_name,start_x,start_y,end_x,end_y):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    if contains_digit(get_ocr_txt):
                        ocr_check_and_set_number(attr_name,get_ocr_txt)
                        logger.info(f"get_attr_ocr_number {attr_name}：{get_ocr_txt}")
    else:
        logger.info(f"未获取到{attr_name}")


########################################################################
def ocr_check_and_set(attrName,ocr_txt):
    global product_meta
    for item in product_meta:
        if item["name"] == attrName:
            values = item.get("values", [])
            values.append(ocr_txt) 
            item["values"] = values
            logger.info(f"ocr_check_and_set: {item}")
            return ocr_txt
######################################################
def ocr_check_and_set_number(attrName,ocr_txt):
    global product_meta
    match = re.search(r'\d+', ocr_txt)  
    value_number = match.group(0) if match else ""
    
    for item in product_meta:  
        if item["name"] == attrName:
            values = item.get("values", [])
            values.append(value_number)
            item["values"] = values
            if attrName == "充值金额":
                ocr_check_and_set("充值称号",get_chongzhi_chenghao(value_number))
                print(f"充值称号================：{get_chongzhi_chenghao(value_number)}")
            logger.info(f"ocr_check_and_set_number {item}")
            return ocr_txt
################################################################
def get_chongzhi_chenghao(num):
    sorted_chongzhi_chenghao = sorted(configs.chongzhi_chenghao, key=lambda x: x[1], reverse=True)  
    for item in sorted_chongzhi_chenghao:
        if int(num) > item[1]:
            return item[0]
    return ""
################################################################
def get_luhao_snapshot():
    step_login_pic = save_cropped_screen("luhao_snapshot")
    oss_file = upload_one_img_to_oss(step_login_pic)
    the_state_img = configs.image_server_url+oss_file
    return the_state_img
#######################################################
#######################################################
def kkLogger():
    global logger
    global logger2
    if logger is None:
        if logger2 is None:
            logger2 = logger_config.setup_logger(os.path.basename(__file__), log_file_name=f'{projectPath}\\nsh_runtime.log')
        return logger2
    else:
        return logger
#####################################################
def kkLogger_log(msg,level="info"):
    try:
        if "info" == level:
            kkLogger().info(msg)
        elif "error" == level:
            kkLogger().error(msg)
        elif "debug" == level:
            kkLogger().debug(msg)
        else:
            print(msg)
    except Exception as e:
        print(f"kkLogger_log {msg} exception：:{e}")
###################################################################
def check_game_ready():
    if exists(Template(r"tpl1727453967853.png", record_pos=(-0.004, 0.105), resolution=(1280, 720))):
        if exists(Template(r"tpl1727259849115.png", record_pos=(-0.152, 0.159), resolution=(1280, 720))):
            print("已同意条款")
        else:
            kktouch2(1063, 885,1,"同意条款")
        return True
    else:
        return False
#####################################################
def sync_current_snapshot(msg="录号同步",now_status=None):
    try:
        global luhao_task
        luhao_task["msg"] = msg
        luhao_task["snapshot"] = get_luhao_snapshot()
        if now_status is not None:
            luhao_task["status"] = now_status
        else:
            luhao_task["status"] = 'IN_PROGRESS'
        portal_client.sync_task_info(luhao_task)
    except Exception as e:
        print(f"sync_current_snapshot：{e}")
##########################################################
def step_login(step_login):
    if step_login == 0:

        account = luhao_task['gameAccount']
        account_pass = luhao_task['gamePassword']
        task_id = luhao_task['id']
        
        if check_game_ready() is False:
            return get_return_taskEvent("step_login",EventStatus.FAILURE,"登录界面错误")

        if '@' in account:
            kktouch2(1555, 788,2,"网易邮箱")
            for i in range(10):
                kktouch2(1277, 429,1,"账号输入框")
                if exists(Template(r"tpl1727454319528.png", record_pos=(0.42, 0.214), resolution=(1280, 720))):
                    break
            kktouch2(1344, 953,1,"账号输入")
            text(account,enter=False)
            kktouch2(2369, 956,1,"下一步")
            
            kktouch2(1257, 569,1,"密码输入框")
            kktouch2(1270, 950,1,"密码输入")
            text(account_pass,enter=False)
            kktouch2(2350, 960,1,"下一步")
            kktouch2(1364, 711,2,"登录")
            sleep(5)
            ################################################################
            coords = ocr_text_target_coords("密码",353, 309,847, 382)
            if coords is not None:
                print(f"密码错误，停留在登录界面")
                kktouch2(341, 153,1,"返回主登录界面")
                
                return get_return_taskEvent("step_login",EventStatus.FAILURE,"密码错误")
            ##########################################
            coords = ocr_text_target_coords("安全风险",101, 335,1170, 465)
            if coords is not None:
                kktouch2(51, 95,1,"退出登录安全验证")
                kktouch2(341, 153,1,"返回主登录界面")
                
                return get_return_taskEvent("step_login",EventStatus.FAILURE,"需要安全验证")

            if is_login_success():
                for i in range(60):
                    sleep(10)
                    print(f"等待录号进入角色阶段，{i}")
                    task = portal_client.get_task_info(luhao_task["id"])
                    status = task.get("status", "none")
                    stage = task.get("stage", "none")
                    if status == "IN_PROGRESS" and i >30:
                        print("录号进入角色阶段，开始退登")
                        step_logout2(0)
                        break
                    else:
                        continue

                return get_return_taskEvent("step_login",EventStatus.SUCCESS,"登录成功")
            else:
                return get_return_taskEvent("step_login",EventStatus.SUCCESS,"登录成功")

        else:
            if exists(Template(r"tpl1727242753729.png", record_pos=(-0.002, -0.006), resolution=(1280, 720))):

                kktouch2(631, 302,1,"输入框")
                kktouch2(611, 637,1,"输入")
                text(account)
                kktouch2(634, 401,1,"下一步")
                
                step_login_pic = save_cropped_screen("luhao_step")
                oss_file = upload_one_img_to_oss(step_login_pic)

                the_state_img = configs.image_server_url+oss_file
                propertyBag = {
                    "state":"need_sms_code",
                    "state_img":the_state_img
                }
                luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
                portal_client.sync_task_info(luhao_task)
                
                sleep(5)
                kktouch2(437, 357, 1, "验证码")
                smscode = portal_client.get_sms_code(task_id=luhao_task['id'], timeout=120)
                if smscode:
                    text(smscode)
                    if wait_for_ocr_txt("手机", 2, 380, 188, 927, 348):
                        step_login_pic = save_cropped_screen("luhao_step")
                        oss_file = upload_one_img_to_oss(step_login_pic)

                        the_state_img = configs.image_server_url + oss_file
                        propertyBag = {
                            "state": "need_sms_code2",
                            "state_img": the_state_img
                        }
                        luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
                        portal_client.sync_task_info(luhao_task)
                        sleep(2)

                        for i in range(60):
                            smscode2 = portal_client.get_sms_code(task_id=luhao_task['id'], timeout=120)
                            print(f"获取到验证码：{smscode2}")
                            if smscode2 == smscode:
                                print(f"获取到重复验证码：{smscode2}")
                                sleep(1)
                                continue
                            else:
                                print(f"获取到新验证码：{smscode2}")
                                kktouch2(437, 357, 1, "验证码")
                                text(smscode2)
                                break

                if is_login_success():
                    for i in range(60):
                        sleep(10)
                        print(f"等待录号进入角色阶段，{i}")
                        task = portal_client.get_task_info(luhao_task["id"])
                        status = task.get("status", "none")
                        stage = task.get("stage", "none")
                        if status == "IN_PROGRESS" and i > 30:
                            print("录号进入角色阶段，开始退登")
                            step_logout2(0)
                            break
                        else:
                            continue

                    return get_return_taskEvent("step_login", EventStatus.SUCCESS, "登录成功")
            else:
                return get_return_taskEvent("step_login", EventStatus.FAILURE, "登录失败")



##############################
def is_login_success():
    for i in range(20):
        sleep(1)
        check_game_gonggao()
        
        step_login_pic = save_cropped_screen("luhao_step")
        oss_file = upload_one_img_to_oss(step_login_pic)
        the_state_img = configs.image_server_url+oss_file

        if exists(Template(r"tpl1727284372666.png", record_pos=(0.002, 0.158), resolution=(1280, 720))):
            propertyBag = {
                "state":"login_success",
                "state_img":the_state_img
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)  
            print("登录成功")
            return True
        else:
            print("未发现 闯荡江湖")
            continue
        
    propertyBag = {
        "state":"login_timeout",
        "state_img":the_state_img
    }
    luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
    portal_client.sync_task_info(luhao_task)  
    print("登录超时")
    return False

###########################################
def check_game_gonggao():
    try:
        coords = ocr_text_target_coords("本期上新",10,10,280,422)
        if coords is not None:
            kktouch2(1079, 101, 2, "关闭庄园公告")

        coords = ocr_text_target_coords("月", 10, 10, 280, 422)
        if coords is not None:
            kktouch2(1194, 113, 2, "关闭公告.")

        # kktouch2(1199, 113,1,"关闭公告")
        # if exists(Template(r"tpl1727356979747.png", record_pos=(0.433, -0.195), resolution=(1280, 720))):
        #     kktouch2(1198, 112,1,"关闭公告")
        # else:
        #     print("没有公告")
    except Exception as e:
        print("没有公告")

####################################
##########################################
def get_return_taskEvent(now_stage,now_status,now_msg=None):
    return TaskEvent(task_id=luhao_task['id'],
                             stage=now_stage,
                             status=now_status,
                             snapshot=get_luhao_snapshot(), 
                             data=product_meta,
                             msg=now_msg)

#######################################
def luhao_health_check():
    for i in range(3):
        try:  
            screen = G.DEVICE.snapshot()  
            sleep(0.5)
            pil_img = cv2_2_pil(screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            
            pil_img.save(f'{projectPath}\\device_health\\luhao_health_{current_millis}.jpg')
            
            filename = f'luhao_health_{current_millis}.jpg'
            snapImgPath = f'{projectPath}\\device_health'
            
            image_path_set = set()
            bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称  
            endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint  
            access_key_id = configs.OSS_ACCESS_KEY_ID
            access_key_secret = configs.OSS_ACCESS_KEY_SECRET

            auth = oss2.Auth(access_key_id, access_key_secret)
            bucket = oss2.Bucket(auth, endpoint, bucket_name)  

            local_file_path = os.path.join(snapImgPath, filename)  
            oss_file_path = "mall/images2/"+ generate_oss_path()  
            oss_fileName = oss_file_path+generate_oss_fileName()
            with open(local_file_path, 'rb') as fileobj:
                bucket.put_object(oss_fileName, fileobj)

            print(f"oss_filename:{oss_fileName}")
            
            return True
        except Exception as e:
            continue

    return False
##################################################################
#######################################################################
def check_yueka_jiazeng(timeout=5):
    kkLogger().info(f"check_yueka_jiazeng")
    target = wait_for_ocr_txt("领取",timeout,627, 400,1233, 697)
    if target:
        kktouch2(target[0], target[1],5,"领取")
        kktouch2(target[0], target[1],2,"领取")
###########################################
def step_qufu(is_skip):
    if is_skip == 0:
        wait_for_ocr_txt("闯荡江湖", 120, 407, 508, 793, 706)

        kktouch2(710, 477,2,"更换角色")
        wait_for_ocr_txt("最近角色", 10, 22, 270, 209, 601)
        sync_current_snapshot("选择录号角色")

        try:
            account_qufu = luhao_task['product_info']['product']['gameAccountQufu']
        except Exception as e:
            account_qufu = None
        if account_qufu:
            account_qufu_arrays = account_qufu.split('|')
            coords = ocr_text_target_coords(account_qufu_arrays[1],237, 40,1253, 605)
            if coords is not None:
                print(f"找到区服，点击{account_qufu}")
                touch(coords)
                sleep(1)
            else:
                print(f"没有找到区服： {account_qufu}")
                kktouch2(421, 274,2,"第一个区服")
        else:
            kktouch2(421, 274,2,"第一个区服")

        wait_for_ocr_txt("闯荡江湖", 120, 407, 508, 793, 706)
        kktouch2(641, 561,2,"点击闯荡江湖")
        result = wait_for_ocr_txt("场景设置",60, 22,620,226,728)
        if result:
            kktouch2(122, 149,1,"第一个角色")
            save_cropped_screen_with_mulblur("头图",[(145, 76,385, 590),(858, 520,1179, 570),(894, 653,1095, 694)])
            kktouch2(1058, 628,10,"进入游戏")

            sleep(10)

        check_and_try_game_home(100, "step_qufu")

        return get_return_taskEvent("step_qufu", EventStatus.SUCCESS,"角色选择完成")

############################
#################################################
def safe_step_gameset(is_skip):
    for i in range(3):
        try:
            return step_gameset(is_skip)
        except Exception as e:
            logger.error(f"safe_step_gameset error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_gameset", EventStatus.FAILURE)
###############################################################
###########################################
def wait_for_ocr_txt(ocr_txt,timeout,start_x,start_y,end_x,end_y):
    kkLogger().info(f"等待查找文字：{ocr_txt},超时时间：{timeout}")
    for i in range(timeout):
        coords = ocr_text_target_coords(ocr_txt,start_x, start_y,end_x, end_y)
        if coords is not None:
            kkLogger().info(f"识别到文字：{ocr_txt},时间：{i}")
            return coords
        else:
            if timeout > 1:
                sleep(1.5)
    kkLogger().info(f"没有找到文字：{ocr_txt},超时时间：{timeout}")
    return False

######################################################################
#######################################################################
def check_game_login_tiaokuan(timeout=5):
    kkLogger().info(f"check_game_login_tiaokuan")
    wait_for_ocr_txt("拒绝",timeout,10, 400,720, 500)

    coords = ocr_text_target_coords("接受",10, 400,720, 500)
    if coords is not None:
        kktouch2(coords[0]-10, coords[1]-37,5,"接受")
#######################################################################
def check_game_update(timeout=3):
    kkLogger().info(f"check_game_update")
    wait_for_ocr_txt("过旧",timeout,337, 206,909, 479)
    coords = ocr_text_target_coords("确定",337, 206,909, 479)
    if coords is not None:
        kktouch2(coords[0]-10, coords[1]-37,5,"确定")
#######################################################################
def check_game_alert_note():
    kkLogger().info(f"检查是否有提示弹窗")
    tip_target = ocr_text_target_coords("提示",239, 150,984, 578)
    if tip_target:
        cancel_target = ocr_text_target_coords("取消",239, 150,984, 578)
        if cancel_target:
            kktouch2(cancel_target[0],cancel_target[1],1,"check_game_alert_note 取消")
        else:
            confirm_target = ocr_text_target_coords("确定", 239, 150, 984, 578)
            if confirm_target:
                kktouch2(confirm_target[0], confirm_target[1], 1, "check_game_alert_note 确定")

#######################################################################
def check_account_login_other():
    kkLogger().info(f"判断是否有被顶号提示")
    account_logout = False
    coords = ocr_text_target_coords("已在其他设备",439, 316,934, 467)
    if coords is not None:
        sync_current_snapshot("被顶号，录号中断",EventStatus.FAILURE)
        kktouch2(1162,390,2,"顶号：确定")
        account_logout = True
    else:
        coords2 = ocr_text_target_coords("闯荡江湖", 475, 432, 870, 640)
        if coords2 is not None:
            sync_current_snapshot("被顶号，录号中断",EventStatus.FAILURE)
            account_logout = True
    if account_logout:
        kktouch2(1243-10,98-37,3,"右上账号")
        kktouch2(590-10,394-37,3,"切换账号")
        kktouch2(802 - 10, 160 - 37, 3, "切换账号")

    return account_logout
#################################
def step_gameset(step_gameset):
    if step_gameset == 0:
        check_yueka_jiazeng(1)
        check_game_update(1)

        kktouch2(1231, 38,2,"菜单")
        target = ocr_text_target_coords("设置", 1214, 236, 1278, 621)
        if target:
            kktouch2(target[0], target[1], 2, "设置")

            kktouch2(77,445,2,"视角")
            kktouch2(747,126,2,"2.5D")

            kktouch2(72, 190, 2, "界面")
            kktouch2(528, 213, 2, "红点")

            kktouch2(69, 320, 2, "左侧画面")
            kktouch2(713, 340, 1, "省电")

            sync_current_snapshot("游戏设置")

            kktouch2(71, 37,1,"左上退出游戏设定")
        return get_return_taskEvent("step_gameset", EventStatus.SUCCESS)
############################################################
def safe_step_juese(is_skip):
    for i in range(3):
        try:
            return step_juese(is_skip)
        except Exception as e:
            logger.error(f"safe_step_juese error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_juese", EventStatus.FAILURE)

#####
def step_juese(is_skip):
    if is_skip == 0:
        kktouch2(1231, 38,2,"菜单")
        kktouch2(1238, 100,2,"角色")
        kktouch2(68, 128,2,"左侧角色")
        save_cropped_screen_with_mulblur("头图",[(84, 6,407, 68),(866, 6,1261, 239),(884, 652,1101, 695)])
        get_attr_ocr_number("评分",236, 65,395, 136)

        kktouch2(277, 257,0.5,"点击角色-装备1")
        save_cropped_area_screen("角色-装备1",347, 37,700, 634)

        kktouch2(323, 296,0.5,"点击角色-装备2")
        save_cropped_area_screen("角色-装备2",388, 32,742, 631)

        kktouch2(341, 425,0.5,"点击角色-装备3")
        save_cropped_area_screen("角色-装备3",436, 112,787, 712)

        kktouch2(284, 512,0.5,"点击角色-装备4")
        save_cropped_area_screen("角色-装备4",346, 115,698, 709)

        kktouch2(286, 598,0.5,"点击角色-装备5")
        save_cropped_area_screen("角色-装备5",346, 113,700, 710)

        kktouch2(256, 296,0.5,"点击角色-装备6")
        save_cropped_area_screen("角色-装备6",301, 34,655, 632)

        kktouch2(290, 343,0.5,"点击角色-装备7")
        save_cropped_area_screen("角色-装备7",346, 56,700, 658)

        kktouch2(254, 385,0.5,"点击角色-装备8")
        save_cropped_area_screen("角色-装备8",302, 98,656, 701)

        kktouch2(254, 467,1,"点击角色-装备9")
        save_cropped_area_screen("角色-装备9",301, 112,655, 710)

        kktouch2(256, 556,0.5,"点击角色-装备10")
        save_cropped_area_screen("角色-装备10",304, 112,656, 713)

        kktouch2(215, 344,0.5,"点击角色-装备11")
        save_cropped_area_screen("角色-装备11",259, 59,613, 655)

        kktouch2(212, 508,0.5,"点击角色-装备12")
        save_cropped_area_screen("角色-装备12",259, 113,616, 709)

        ###################################

        logger.info("======旅途截图开始")
        ############开始旅途
        kktouch2(79, 257,1,"点击左侧旅途")
        save_cropped_area_screen("旅途",864, 80,1251, 687)  ##旅途右侧截图
        kktouch2(74, 317,1,"点击左侧修行")
        save_cropped_screen("修行")

        #############################################################
        kktouch2(73, 194,2,"左侧背包")
#         kktouch2(764, 674,2,"底部仓库")
#         save_cropped_screen("背包-仓库")
#         kktouch2(66, 35,2,"左上退出仓库")

        swipe_to_end((721, 175, 1146, 311),(929, 181),(934, 523))
        for i in range(6):
            save_cropped_area_screen("角色-背包",719, 175,1144, 639)

            is_end = swipe_and_judge_end((715, 147,1142, 243),(931, 597), (860, 154))
            if is_end:
                break

        logger.info(f'背包截图结束,相关材料在{snapImgPath}')
        
        #############################################################

        logger.info("======仓库截图开始")
        kktouch2(765, 671,2,"底部仓库")

        findcangkuResult = find_all_subImg(Template(r"tpl1727872265286.png"),169, 77,559, 151)#仓库数量
        cangkuCount = 0
        if findcangkuResult is None:  
            cangkuCount = 0
        else:
            for line in findcangkuResult:
                logger.info(line)
                if line['confidence'] > 0.83:
                    cangkuCount = cangkuCount +1
                    kktouch2(line['result'][0]+169,line['result'][1]+77,1,f"仓库第{cangkuCount}个包")
                    save_cropped_screen("仓库")

        logger.info(f"=====仓库数量===={cangkuCount}")
        kktouch2(65, 37,1,"退出仓库") ##推出仓库
        logger.info("======仓库截图结束")
        
        
         ##推出修行，回到主界面
        kktouch2(69, 42,1.5,"退出")

        return get_return_taskEvent("step_juese", EventStatus.SUCCESS)
    else:
        return get_return_taskEvent("step_juese", EventStatus.SUCCESS,"未执行")
###############################################################
def safe_step_jueji(is_skip):
    for i in range(3):
        try:
            return step_jueji(is_skip)
        except Exception as e:
            logger.error(f"safe_step_jueji error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_jueji", EventStatus.FAILURE)
###################
def step_jueji(step_jueji):
    if step_jueji == 0:
        logger.info("======绝技截图开始")
        kktouch2(1228,35,2,"菜单")#点击菜单
        kktouch2(1024, 115,2,"武功")#点击武功
        kktouch2(64, 121,2,"左侧技能")#点击左侧技能
        kktouch2(68, 245,2,"绝技")#点击升阶
        save_cropped_screen("绝技")
        kktouch2(70, 38,1,"退出武功")

        logger.info("======绝技截图完成")
        return get_return_taskEvent("step_jueji", EventStatus.SUCCESS)
####################################################
def safe_step_neigong(is_skip):
    for i in range(3):
        try:
            return step_neigong(is_skip)
        except Exception as e:
            logger.error(f"safe_step_neigong error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_neigong", EventStatus.FAILURE)
###################
def step_neigong(is_skip):
    if is_skip == 0:
        logger.info("======内功截图开始")
        kktouch2(1228,35,2,"菜单")#点击菜单
        kktouch2(1024, 115,1,"点击武功")#点击武功
        kktouch2(75, 123,1,"技能")
        kktouch2(75, 434,1,"内功")

        kktouch2(346, 658,1,"点击详细属性加成")
        save_cropped_area_screen("内功详细属性加成",399, 47,878, 662)
        kktouch2(270, 699,1,"取消详细属性加成")


        kktouch2(1001, 653,2,"全部内功")
        kktouch2(143, 107,2,"筛选")
        swipe_to_end_slow((764, 454, 1234, 618),(1008, 587),(976, 202))

        kktouch2(1003, 464,1.5,"词条")
        kktouch2(233, 238,2,"灵韵")
        kktouch2(1138, 655,2,"确定")



        start_x = 107
        start_y = 137
        end_x = 818
        end_y = 245
        loop_height = 125
        lingyun_count = 0
        save_cropped_screen("lingyun_all")
        for i in range(4):
            find_lingyun_result = find_all_subImg(Template(r"tpl1726237406954.png", threshold=0.6),start_x, start_y,end_x, end_y)
            neigong_count = 0
            if find_lingyun_result is None:  
#                 lingyun_count = 0
                break
            else:
    #             save_cropped_screen(f"灵韵内功")
                logger.info(f"第{i+1}行，find_lingyun_result数量：{len(find_lingyun_result)}")
                for line in find_lingyun_result:
                    neigong_count = neigong_count + 1
                    if line['confidence'] > 0.5:
                        x1 = line['result'][0]
                        y1 = line['result'][1]
                        kktouch2(x1+start_x,y1+start_y,1,f"第{neigong_count}个灵韵内功")

                        


                        swipe((1055, 505), (1021, 183), duration=0.5)
                        sleep(1)
                        find_lingyun_result = find_subImg(Template(r"tpl1726830336235.png"),846, 174,1033, 632)
                        if find_lingyun_result is not None:
                            save_cropped_area_screen("灵韵_0",864, 27,1266, 634)
                            ocr_add_to_get(933, 24,1166, 73)
                            lingyun_count = lingyun_count +1
                        else:
                            save_cropped_area_screen("灵韵_1",864, 27,1266, 634)
                start_y = start_y + loop_height
                end_y = end_y + loop_height
                if neigong_count < 7:
                    break
                        
        logger.info(f"=====灵韵数量===={lingyun_count}")
        ocr_check_and_set("灵韵数量",str(lingyun_count))
        kktouch2(70, 44,1,"左上退出灵韵武功")
        kktouch2(72, 38,1,"左上退出武功")
    return get_return_taskEvent("step_neigong", EventStatus.SUCCESS)
####################################################
def safe_step_dazao(is_skip):
    for i in range(3):
        try:
            return step_dazao(is_skip)
        except Exception as e:
            logger.error(f"safe_step_dazao error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_dazao", EventStatus.FAILURE)
###################
def step_dazao(step_dazao):
    if step_dazao == 0:
        kktouch2(1228,35,2,"菜单")
        kktouch2(1146,286,1,"打造")
        kktouch2(71,123,1,"左侧强化")
        save_cropped_screen("打造强化")

        kktouch2(75,195,2,"左侧打造")

        loop = 80
        start_x = 219
        start_y = 131
        for i in range(6):
            kktouch2(start_x,start_y,0.5,f"第{i+1}个打造")
            kktouch2(1227, 303,1,"已有特技库")
            
            if ocr_text_has_number(667, 57,1253, 673):
                swipe_to_end((781, 58, 1258, 170),(1032, 632),(992, 96))
                kktouch2(872, 643,1,"点击最后一个")
                swipe_to_end((651, 430,1233, 663), (975, 100),(1004, 583)) 
            else:
                logger.info("打造页面无数字，直接截图")
                
            target_arrays = ocr_text_target_coords_arrays("属性提升",656, 63,1271, 697)
            if target_arrays is not None:
                for item in target_arrays:
                    save_cropped_area_screen_dazao("打造属性",item[0]-121, item[1]-57,(item[0]-121)+444, (item[1]-57)+81)

            kktouch2(71, 40,1,"左上返回")
            start_y = start_y + loop

        kktouch2(71, 36,1,"左上返回")
        logger.info("打造结束")
    return get_return_taskEvent("step_dazao", EventStatus.SUCCESS)
#################################################


#################################################
def safe_step_waiguan(is_skip):
    for i in range(3):
        try:
            return step_waiguan(is_skip)
        except Exception as e:
            logger.error(f"safe_step_waiguan error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_waiguan", EventStatus.FAILURE)
###################
def waiguan_substep_guancang(is_skip):
    if is_skip == 0:
        kktouch2(1223, 86,2,"馆藏")
        kktouch2(1016, 42,2,"馆藏奖励")
        kktouch2(71, 131,2,"馆藏总等级")
        save_cropped_screen(f"馆藏总等级")

        kktouch2(68, 199,1,"时鉴赏一期")
        save_cropped_screen(f"时鉴赏一期")

        kktouch2(70, 272,1,"时鉴赏二期")
        save_cropped_screen("时鉴赏二期")

        kktouch2(1220, 79,1,"右侧X退出馆藏")

        kktouch2(486, 39,1,"衣品")
        save_cropped_area_screen("衣品",835, 41,1245, 618)
        get_attr_ocr_number("衣品",1086, 55,1261, 93)
        kktouch2(1238, 35,1,"退出衣品")

        kktouch2(642, 346,2,"青丝管霓")

        coords = ocr_text_target_coords("暂无",354, 98,855, 358)
        tianniran_count =  ocr_add_to_get_and_count_tianniran_number(104, 620,222, 659)
        
        if coords is None:
            swipe_to_end((54, 67, 554, 193),(302, 82),(302, 431),judge_blank=True)
            
            for i in range(5):
#                 save_cropped_area_screen_and_ocr_add_to_get("天霓染",51, 75,553, 473,[2,4],)
                save_cropped_screen(f"天霓染")
                ocr_add_to_get_and_count_tianniran(56, 66,555, 599,"种方")
                is_end = swipe_and_judge_end_fast((53, 64,557, 391),(305, 472), (307, 103))
                if is_end:
                    break

                    
#         logger.info(f"天霓染数量:{len(tianniran_ocr_txt_arrays)}")
#         ocr_check_and_set("天霓染",str(len(tianniran_ocr_txt_arrays)))
        
        for item in tianniran_ocr_txt_arrays:
            ocr_check_and_add_attr(item,"热门女号自染")
            ocr_check_and_add_attr(item,"热门男号自染")
        
        
        
        kktouch2(67, 42,1,"退出青丝管霓")


        kktouch2(143, 659,1,"国色")
        save_cropped_screen(f"国色")
        #获取国色值
        get_attr_ocr_number("国色值",926, 6,1125, 52)


        kktouch2(65, 35,1,"左侧退出国色")
        logger.info("馆藏结束")
######################################     
def waiguan_substep_shizhuang(is_skip):
    if is_skip == 0:
        logger.info("开始时装")
        kktouch2(255, 665,1,"底部时装")
        logger.info("开始套装")
        kktouch2(61, 124,1,"套装")
        kktouch2(428, 618,2,"套装回到顶部")
        swipe_to_end((108, 156, 468, 340),(275, 172),(294, 537))
        kktouch2(398, 231,2,"右上第一个套装")
        for i in range(60):

            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),106, 227,474, 551)

            if find_lock_result is None:
                logger.info(f"套装第{i}页")
#                 ocr_add_to_get(109, 291,467, 539)

                # ocr_add_to_get(108, 286,230, 538)
                # ocr_add_to_get(228, 287,351, 539)
                # ocr_add_to_get(350, 290,472, 540)

                if i % 2 == 0:
                    kktouch2(413, 563,0.2,"右下套装")
                else:
                    kktouch2(281, 563,0.2,"底中套装")

                save_cropped_area_screen_and_ocr_add_to_get("套装",107, 143,469, 542,[2,3])

                kktouch2(938, 680,0.5,"底部周边")
                kktouch2(256, 686,1,"底部时装")
            else:
                logger.info(f"套装识别到锁")
                
                save_cropped_area_screen_and_ocr_add_to_get("套装",100, 143,469, 542,[2,3],Template(r"tpl1727075488904.png", threshold=0.75))
                
#                 find_result = find_subImg(Template(r"tpl1726729052163.png", threshold=0.85),112, 133,395, 223)
#                 if find_result is not None: 
#                     save_cropped_area_screen("套装",107, 143,471, 340)
#                     ocr_add_to_get(104, 230,468, 340)
                kktouch2(516, 478,1,"套装重置")
                kktouch2(1157, 354,1,"确认套装重置")
                break
####################################################
def waiguan_substep_fashi(is_skip):
    if is_skip == 0:
        logger.info("开始时装-发式")
        kktouch2(62, 366,1,"左侧发式")
        #滚动到顶部
        kktouch2(434, 616,2,"发式回顶部")
        swipe_to_end((109, 249, 472, 605),(275, 172),(294, 537))
        kktouch2(428, 618,2,"发式回到顶部")
        kktouch2(406, 235,2,"右上第一个发式")


        for i in range(60):
            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),108, 146,468, 546)
            if find_lock_result is None:
                logger.info(f"发式第{i}页")
#                 ocr_add_to_get(106, 249,477, 543)

                # ocr_add_to_get(105, 283,229, 540)
                # ocr_add_to_get(226, 293,351, 539)
                # ocr_add_to_get(350, 274,473, 539)


                kktouch2(412, 577,0.2,"右下发式")
                save_cropped_area_screen_and_ocr_add_to_get("发式",106, 134,471, 541,[2,3])

                kktouch2(67, 125,0.5,"套装")
                kktouch2(56, 372,1,"发式")
            else:
                logger.info(f"发式识别到锁")
                
                save_cropped_area_screen_and_ocr_add_to_get("发式",100, 134,471, 541,[2,3],Template(r"tpl1727075488904.png", threshold=0.75))
                
                
#                 find_result = find_subImg(Template(r"tpl1726729052163.png", threshold=0.7),112, 133,395, 223)
#                 if find_result is not None: 
#                     save_cropped_area_screen("发式",106, 134,471, 541)
#                     ocr_add_to_get(104, 230,470, 338)
                kktouch2(516, 478,1,"发饰重置")
                kktouch2(1157, 354,1,"确认发饰重置")
                break            

####################################################
def waiguan_substep_wuqi(is_skip):
    if is_skip == 0:
        kktouch2(536, 676,1,"底部武器")#点击底部武器
        kktouch2(66, 121,1,"分块")
        kktouch2(56, 486,1,"套装")

        logger.info("开始武器")
        kktouch2(430, 618,2,"武器回到顶部")
        swipe_to_end((109, 138, 462, 417),(289, 170),(287, 565))
#         kktouch2(374, 209,2,"右上第一个武器")
        for i in range(11):
            find_lock_result = find_subImg(Template(r"tpl1727007667521.png", threshold=0.75),108, 206,462, 624)

            if find_lock_result is None:
                logger.info(f"武器第{i}页")
#                 ocr_add_to_get(107, 134,465, 627)
#                 ocr_add_to_get(107, 146,289, 647)
#                 ocr_add_to_get(286, 139,473, 654)

                save_cropped_area_screen_and_ocr_add_to_get("武器",105, 139,464, 561,[3,2])

                is_end = swipe_and_judge_end((101, 140,458, 423),(290, 561),(287, 173))
                if is_end:
                    break
                sleep(1)
            else:
                logger.info(f"武器识别到锁")
                save_cropped_area_screen_and_ocr_add_to_get("武器",101, 140,464, 565,[3,2],Template(r"tpl1727007667521.png", threshold=0.75))

#                 save_cropped_area_screen("武器",106, 135,464, 564)

#                 ocr_add_to_get(107, 134,465, find_lock_result[1]+206)

                kktouch2(513, 477,1,"武器重置")
                kktouch2(1151, 352,1,"确认武器重置")
                break            
####################################################
def waiguan_substep_zhuoqi(is_skip):
    if is_skip == 0:
        kktouch2(671, 679,1,"底部坐骑")#点击底部坐骑
        kktouch2(65, 126,1,"左侧坐骑")#点击坐骑
        logger.info("开始坐骑")
        kktouch2(430, 616,2,"坐骑回到顶部")
        swipe_to_end((114, 191, 473, 574),(250, 195),(308, 594))
        for i in range(40):

            find_lock_result = find_subImg(Template(r"tpl1726951381717.png", threshold=0.75),102, 258,467, 630)

            if find_lock_result is None:
                logger.info(f"坐骑第{i}页")
#                 ocr_add_to_get(112, 187,473, 629)
#                 ocr_add_to_get(107, 308,230, 589)
#                 ocr_add_to_get(229, 319,351, 591)
#                 ocr_add_to_get(350, 321,482, 591)



                kktouch2(368, 593,0.2,"右下坐骑")
                save_cropped_area_screen_and_ocr_add_to_get("坐骑",110, 178,480, 580,[2,3])

                kktouch2(72, 206,0.5,"祥瑞")
                kktouch2(67, 131,1,"坐骑")
            else:
                logger.info(f"坐骑识别到锁")
                
                save_cropped_area_screen_and_ocr_add_to_get("坐骑",102, 170,480, 580,[2,3],Template(r"tpl1726951381717.png", threshold=0.75))
                
#                 find_result = find_subImg(Template(r"tpl1726951961036.png", threshold=0.7),110, 178,467, 295)
#                 if find_result is not None: 
#                     save_cropped_area_screen("坐骑",110, 178,472, 575)
#                     ocr_add_to_get(112, 181,468, 373)

                kktouch2(509, 478,1,"坐骑重置")
                kktouch2(1157, 354,1,"确认坐骑重置")
                break   
########################################
def waiguan_substep_xiangrui(is_skip):
    if is_skip == 0:
        kktouch2(671, 679,1,"底部坐骑")#点击底部坐骑
        kktouch2(61, 210,1,"左侧祥瑞")#点击坐骑
        logger.info("开始祥瑞")
        kktouch2(431, 619,2,"祥瑞回到顶部")
        swipe_to_end((114, 191, 473, 574),(250, 195),(308, 594))
        kktouch2(410, 290,1,"右上第一个祥瑞")
        for i in range(60):

            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),102, 186,471, 598)

            if find_lock_result is None:
                logger.info(f"祥瑞第{i}页")
#                 ocr_add_to_get(110, 182,474, 622)
#                 ocr_add_to_get(107, 173,232, 585)
#                 ocr_add_to_get(228, 183,354, 582)
#                 ocr_add_to_get(351, 179,471, 594)

                kktouch2(367, 591,0.2,"右下祥瑞")
                save_cropped_area_screen_and_ocr_add_to_get("祥瑞",110, 179,470, 580,[2,3])

                kktouch2(67, 131,0.5,"坐骑")
                kktouch2(72, 206,1,"祥瑞")
            else:
                logger.info(f"武器识别到锁")
                save_cropped_area_screen_and_ocr_add_to_get("祥瑞",102, 179,470, 580,[2,3],Template(r"tpl1727075488904.png", threshold=0.75))

#                 find_result = find_subImg(Template(r"tpl1726951961036.png", threshold=0.7),110, 178,467, 295)
#                 if find_result is not None: 
#                     save_cropped_area_screen("祥瑞",110, 179,469, 577)
#                     ocr_add_to_get(112, 181,470, 376)
                kktouch2(514, 471,1,"祥瑞重置")
                kktouch2(1157, 354,1,"确认祥瑞重置")
                break           
#########################################################
def waiguan_substep_jueji(is_skip):
    if is_skip == 0:
        start_x = 1061
        start_y = 145
        end_x = 1238
        end_y = 254
        kktouch2(802, 687,1,"底部武功")#点击底部武功
        kktouch2(67, 240,1,"左侧绝技")#点击绝技
#             save_cropped_screen(f"武功绝技")

        kktouch2(191, 186,1,"冰火绝灭")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_冰火绝灭")
            save_cropped_area_screen("武功_绝技_冰火绝灭",start_x, start_y,end_x, end_y)
            logger.info("识别到 冰火绝灭")
            add_to_get("明河曙天·长鲸天斗")


        kktouch2(287, 186,1,"残心三绝剑")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_残心三绝剑")    
            save_cropped_area_screen("武功_绝技_残心三绝剑",start_x, start_y,end_x, end_y)
            logger.info("识别到 残心三绝剑")
            add_to_get("天曦四象·朱雀")        



        kktouch2(186, 306,1,"剑破乾坤")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_剑破乾坤")
            save_cropped_area_screen("武功_绝技_剑破乾坤",start_x, start_y,end_x, end_y)
            logger.info("识别到 剑破乾坤")
            add_to_get("日曜八荒·轩辕剑")        



        kktouch2(285, 309,1,"万剑绝")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_万剑绝")
            save_cropped_area_screen("武功_绝技_万剑绝",start_x, start_y,end_x, end_y)
            logger.info("识别到 万剑绝")
            add_to_get("山海祠神·阳升羲和")


        kktouch2(186, 433,1,"长歌献君")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_长歌献君")
            save_cropped_area_screen("武功_绝技_长歌献君",start_x, start_y,end_x, end_y)
            
            logger.info("识别到 长歌献君")
            add_to_get("重蝶化梦")        


        kktouch2(287, 435,1,"狂发一怒")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_狂发一怒")
            save_cropped_area_screen("武功_绝技_狂发一怒",start_x, start_y,end_x, end_y)
            logger.info("识别到 狂发一怒")
            add_to_get("青丘雪")        


        kktouch2(186, 554,1,"红莲焚夜")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_红莲焚夜")
            save_cropped_area_screen("武功_绝技_红莲焚夜",start_x, start_y,end_x, end_y)
            logger.info("识别到 红莲焚夜")
            add_to_get("天曦四象·白虎")        


        kktouch2(290, 558,1,"繁花一梦")

        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_繁花一梦")
            save_cropped_area_screen("武功_绝技_繁花一梦",start_x, start_y,end_x, end_y)
            logger.info("识别到 繁花一梦")
            add_to_get("天曦四象·青龙")        


        swipe_to_end((125, 497, 354, 618),(236, 594),(237, 258))


        kktouch2(187, 314,1,"九天雷引")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_九天雷引")
            save_cropped_area_screen("武功_绝技_九天雷引",start_x, start_y,end_x, end_y)
            logger.info("识别到 九天雷引")
            add_to_get("岁星行渡·千重焰")        


        kktouch2(284, 309,1,"法天象地")

        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_法天象地")
            save_cropped_area_screen("武功_绝技_法天象地",start_x, start_y,end_x, end_y)
            logger.info("识别到 法天象地")
            add_to_get("降魔破邪·金刚明王")        


        kktouch2(183, 431,1,"相夷太剑")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_相夷太剑")
            save_cropped_area_screen("武功_绝技_相夷太剑",start_x, start_y,end_x, end_y)
            logger.info("识别到 相夷太剑")
            add_to_get("明河曙天·醉卧西海")    
            

        kktouch2(287, 435,1,"星火漫天")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_星火漫天")
            save_cropped_area_screen("武功_绝技_星火漫天",start_x, start_y,end_x, end_y)
            logger.info("识别到 星火漫天")
            add_to_get("妖影寻灵·狐舞青丘")        



#         kktouch2(187, 562,1,"大闹天宫")
#         save_cropped_screen(f"kkimg_武功_绝技_大闹天宫")
#         find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
#         if find_result is not None:
#             add_to_get("大闹天宫")      


        kktouch2(282, 560,1,"终焉裁决")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),1065, 172,1104, 241)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_终焉裁决")
            save_cropped_area_screen("武功_绝技_终焉裁决",start_x, start_y,end_x, end_y)
            logger.info("识别到 终焉裁决")
            add_to_get("丹青纵横·龙潭墨云")       
            
            
        kktouch2(65, 334,2,"左侧江湖")#点击江湖
        coords = ocr_text_target_coords("雪寒",125, 175,380, 645)
        if coords is not None:
            touch(coords)
            logger.info("点击 辉雪寒瑛")
            sleep(1)
#             kktouch2(186, 558,1,"辉雪寒瑛")
        find_result = find_subImg(Template(r"tpl1726977527725.png", threshold=0.7),1050, 195,1121, 256)
        if find_result is None:
            logger.info("识别到 辉雪寒瑛")
#             save_cropped_screen("武功_江湖_辉雪寒瑛")
            save_cropped_area_screen("武功_江湖_辉雪寒瑛",start_x, start_y,end_x, end_y)
            add_to_get("山海祠神·赤凰重明")                



#             kktouch2(284, 556,1,"芳心妙愈")
        coords = ocr_text_target_coords("芳心",125, 175,380, 645)
        if coords is not None:
            touch(coords)
            logger.info("点击芳心妙愈")
            sleep(1)
        find_result = find_subImg(Template(r"tpl1726977628834.png", threshold=0.7),1054, 209,1119, 250)
        if find_result is None:
            logger.info("识别到 芳心妙愈")
#             save_cropped_screen("武功_江湖_芳心妙愈")
            save_cropped_area_screen("武功_江湖_芳心妙愈",start_x, start_y,end_x, end_y)
            add_to_get("新世华霓·喵到病除")
    ###############################################################
        kktouch2(65, 425,1,"左侧轻功")#点击轻功
#         save_cropped_screen(f"轻功")
        logger.info("开始轻功")
        
        swipe_to_end((881, 149, 1238, 484),(1053, 155),(1061, 491))
        save_cropped_area_screen_and_ocr_add_to_get("武功-轻功",870, 147,1234, 480,[3,2],Template(r"tpl1727017209837.png", threshold=0.6))
        swipe_to_end((881, 149, 1238, 484),(1061, 491),(1053, 155))
        save_cropped_area_screen_and_ocr_add_to_get("武功-轻功",872, 243,1236, 580,[3,2],Template(r"tpl1727017209837.png", threshold=0.6))
        
        
        
#         swipe_to_end((881, 149, 1238, 484),(1053, 155),(1061, 491))
#         for i in range(2):
#             find_lock_result = find_first_subImg(Template(r"tpl1727017209837.png", threshold=0.75),872, 137,1238, 575)
#             if find_lock_result is None:
#                 logger.info(f"轻功第{i}页")
# #                 ocr_add_to_get(869, 137,1242, 577)
#                 save_cropped_area_screen_and_ocr_add_to_get("武功-轻功",870, 147,1234, 473,[3,2])

#                 swipe((1058, 565),(1058, 223),duration=5)
#             else:
#                 logger.info(f"轻功识别到锁")
# #                 ocr_add_to_get(869, 137,1235, find_lock_result[1]+137)
#                 save_cropped_area_screen_and_ocr_add_to_get("武功-轻功",870, 147,1234, 473,[3,2],Template(r"tpl1727017209837.png", threshold=0.75))
#                 break 



################################################
def step_waiguan(is_skip):
    if is_skip == 0:
        kktouch2(1228,35,2,"菜单")#点击菜单
        kktouch2(1121, 115,1,"外观")

        waiguan_substep_guancang(0)


        waiguan_substep_shizhuang(0)
        sync_current_snapshot("外观-套装")


        waiguan_substep_fashi(0)
        sync_current_snapshot("外观-发式")


        waiguan_substep_wuqi(0)
        sync_current_snapshot("外观-武器")


        waiguan_substep_zhuoqi(0)
        sync_current_snapshot("外观-坐骑")


        waiguan_substep_xiangrui(0)
        sync_current_snapshot("外观-祥瑞")


        waiguan_substep_jueji(0)

        sync_current_snapshot("外观结束")
        
        logger.info("外观结束")

        kktouch2(64, 38,1,"左上退出外观")
    return get_return_taskEvent("step_waiguan", EventStatus.SUCCESS)
################################################

######################################################
#################################################
def safe_step_kaifang_shijie(is_skip):
    for i in range(3):
        try:
            return step_kaifang_shijie(is_skip)
        except Exception as e:
            logger.error(f"safe_step_kaifangshijie error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_kaifang_shijie", EventStatus.FAILURE)
###################
def step_kaifang_shijie(step_kaifang_shijie):
    if step_kaifang_shijie == 0:
        kktouch2(1231, 33,2,"菜单")#点击菜单
        kktouch2(1130, 489,2,"开放世界")#点击开放世界
        kktouch2(285, 587,1,"地名小三角")#点击小三角

        swipe_to_end((171, 14, 465, 385),(269, 9),(269, 536))
        save_cropped_screen("开放世界")
        swipe_to_end((171, 14, 465, 385),(269, 536),(269, 9))
        save_cropped_screen("开放世界")

        kktouch2(1177, 59,1,"右上X退出开放世界-地名")#点击推出X
        kktouch2(1178, 54,1,"右上X退出开放世界")#点击推出X

        logger.info("开放世界结束")
    return get_return_taskEvent("step_kaifang_shijie", EventStatus.SUCCESS)
##########################################################
def safe_step_qunxia(is_skip):
    for i in range(3):
        try:
            return step_qunxia(is_skip)
        except Exception as e:
            logger.error(f"safe_step_qunxia error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_qunxia", EventStatus.FAILURE)
##########################################################
def step_qunxia(step_qunxia):
    if step_qunxia == 0:
        kktouch2(1231, 33,2,"菜单")#点击菜单
        kktouch2(793, 537,1,"群侠")#点击群侠
        kktouch2(64, 126,1,"左侧群侠")#点击左侧群侠
        kktouch2(240, 353,1,"左侧第一个人")#点击第一个人物
        kktouch2(279, 395,1,"左侧展开箭头")#点击展开
        save_cropped_screen("群侠")
        for i in range(4):
            find_full_qunxia_result = find_all_subImg(Template(r"tpl1727019178804.png", threshold=0.89),37, 127,702, 668)
            full_qunxia_count = 0
            if find_full_qunxia_result is None:  
                full_qunxia_count = 0
            else:
                for line in find_full_qunxia_result:
                    logger.info(line)
                    if line['confidence'] > 0.89:
                        full_qunxia_count = full_qunxia_count +1
                        kktouch2(line['result'][0]+37,line['result'][1]+127,1,f"第{full_qunxia_count}个满级群侠")
                        ocr_add_to_get(89, 8,219, 72)

            is_end = swipe_and_judge_end_fast((37, 87,708, 262),(258, 529), (254, 109))
            if is_end:
                break


        kktouch2(69, 44,1,"左上退出")#点击推出
        kktouch2(70, 35,1,"左上退出")#点击推出

        logger.info("群侠结束")
    return get_return_taskEvent("step_qunxia", EventStatus.SUCCESS)
##########################################################
#################################################
def safe_step_zhuangyuan(is_skip):
    for i in range(3):
        try:
            return step_zhuangyuan(is_skip)
        except Exception as e:
            logger.error(f"safe_step_zhuangyuan error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_zhuangyuan", EventStatus.FAILURE)
###################
def step_zhuangyuan(step_zhuangyuan):
    if step_zhuangyuan == 0:
        kktouch2(1231, 33,1,"菜单")#点击菜单
        if kktouch2(794, 96,2,"庄园") is False:
            return
        
        save_cropped_screen_with_blur("庄园",228, 80, 680, 504)
        kktouch2(67, 38,1,"左上退出")

        logger.info("庄园结束")
    return get_return_taskEvent("step_zhuangyuan", EventStatus.SUCCESS)
#######################################################################
#################################################
def safe_step_lingcong(is_skip):
    for i in range(3):
        try:
            return step_lingcong(is_skip)
        except Exception as e:
            logger.error(f"safe_step_lingcong error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_lingcong", EventStatus.FAILURE)
###################
def step_lingcong(step_lingcong):
    if step_lingcong == 0:
        kktouch2(1231, 33,1,"菜单")
        kktouch2(1239, 371,2,"宠物")
        kktouch2(443, 222,2,"灵宠")
        find_lingcong_result = find_subImg(Template(r"tpl1727025466427.png"),1118, 4,1275, 97)
        if find_lingcong_result:
            save_cropped_screen("灵宠")
            kktouch2(65, 37,1,"退出灵宠详情")

        kktouch2(65, 37,1,"退出宠物图鉴")#点击推出
        step_8 = 1
        logger.info("宠物结束")
        
    step_chongzhi(0)
    step_tianshangshi(0)
    
    return get_return_taskEvent("step_lingcong", EventStatus.SUCCESS)
#######################################################################
def step_chongzhi(step_chongzhi):
    try:
        if step_chongzhi == 0:
            kktouch2(1231, 33,1,"菜单")
            kktouch2(1143, 680,2,"攻略")
            for i in range(2):
                kktouch2(505, 639,2,"输入框")
                for i in range(10):
                    keyevent("67")
                text("累计充值")
                kktouch2(1090, 639,1,"发送")
                sleep(5)
                coords = ocr_text_target_coords("元",263, 25,1219, 599)
                if coords is not None:
                    get_attr_ocr_number("充值金额",283, 12,1230, 527)
                    break
                
            kktouch2(37, 32,2,"退出小暖问答")
            logger.info("充值查询结束")
        return get_return_taskEvent("step_chongzhi", EventStatus.SUCCESS)
    except Exception as e:
        logger.error(f"step_chongzhi 失败：{e}")  
        kktouch2(37, 32,2,"退出小暖问答")
        return get_return_taskEvent("step_chongzhi", EventStatus.FAILURE)
##############################################################################
def step_tianshangshi(step_tianshangshi):
    try:
        if step_tianshangshi == 0:
            tss_value = 0
            for item in product_meta:
                if item["type"] not in [1,2]:
                    continue
                else:
                    item_values = item.get("values", [])
                    for item_value in item_values:
                        item_value_2 = remove_unwanted_chars(item_value)
                        tss_value = tss_value + configs.TSS_VALUE.get(item_value_2,0)
            logger.info(f"已使用天赏石：{tss_value}")
            ocr_check_and_set_number("已使用天赏石",str(tss_value))
        return get_return_taskEvent("step_tianshangshi", EventStatus.SUCCESS)
    except Exception as e:
        logger.error(f"step_tianshangshi 失败：{e}")  
        return get_return_taskEvent("step_tianshangshi", EventStatus.FAILURE)
########################################################################
def step_img_upload(is_skip):
    if is_skip == 0:
        sleep(1)
        logger.info("开始处理图片")
        image_util.collage(f'{snapImgPath}')

        logger.info(f'开始上传')
#         pic_url_arrays = upload_kk_img_to_oss(f'{snapImgPath}')
        upload_kk_img_to_oss(f'{snapImgPath}')

        logger.info(f'上传结束 pic_url_arrays:{pic_url_arrays}')
    return get_return_taskEvent("step_img_upload", EventStatus.SUCCESS)
###########################################################
def step_meta_upload(step_meta_upload):
    global product_meta
    if step_meta_upload == 0:
        logger.info(f"product_meta_json:{product_meta}")
        logger.info(f"pic_url_arrays:{pic_url_arrays}")
        portal_client.sync_product_info(luhao_task['productSn'], configs.image_server_url + get_head_pic(), pic_url_arrays, product_meta)
    return get_return_taskEvent("step_meta_upload", EventStatus.SUCCESS)
#####################################################
def step_logout(is_skip):
    if is_skip == 0:
        check_and_try_game_home()
        kktouch2(1227, 35,1,"菜单")#点击菜单
        kktouch2(1240, 547,2,"左侧设置")#点击设置
        kktouch2(1116, 287,2,"切换账号")#点击切换账号
        kktouch2(1045, 189,2,"切换账号")#点击切换账号
        # wait(Template(r"tpl1726752501676.png", record_pos=(-0.004, 0.139), resolution=(1280, 720)),timeout=120,interval=3)
        target = wait_for_ocr_txt("其他账号登录",5,359,393,940,562)
        if target:
            kktouch2(target[0], target[1],1,"其他账号登录")
            kktouch2(target[0], target[1], 1, "其他账号登录")

            coords = ocr_text_target_coords("输入手机号码", 466, 259, 796, 448)
            if coords is not None:
                kkLogger().info("退登成功")
                return get_return_taskEvent("step_logout", EventStatus.SUCCESS, "录号完成，退登成功")
            else:
                kkLogger().info("退登失败")
                return get_return_taskEvent("step_logout", EventStatus.MANUAL_REQUIRED, "异常，需要人工介入回到登录首页")
        else:
            kkLogger().info("退登失败.")
            return get_return_taskEvent("step_logout", EventStatus.MANUAL_REQUIRED, "异常，需要人工介入回到登录首页")


luhao_task = {
#     'gameAccount':'***********',
    'gameAccount':'<EMAIL>',
    'gamePassword':'Jun135790.',
    "id":19,
    "status":"PENDING",
    "productSn":"NSH76864033",
    "product_meta":requestGameProductCategoryMeta(75)
}
###############################################################################################################################
def step_logout2(is_skip):
    if is_skip == 0:

        width, height = device().get_current_resolution()
        kkLogger_log(f"step_logout2 设备屏幕宽：{width} 高：{height}")

        kktouch2(1240, 49,2,"右上账号")
        kktouch2(1045, 184,2,"切换账号")
        target = wait_for_ocr_txt("其他账号登录",5,359,393,940,562)
        if target:
            kktouch2(target[0], target[1],1,"其他账号登录")
            kktouch2(target[0], target[1], 1, "其他账号登录")

            coords = ocr_text_target_coords("输入手机号码", 466, 259, 796, 448)
            if coords is not None:
                kkLogger_log("退登成功")
                return get_return_taskEvent("step_logout", EventStatus.SUCCESS, "录号完成，退登成功")
            else:
                kkLogger_log("退登失败")
                return get_return_taskEvent("step_logout", EventStatus.MANUAL_REQUIRED, "登录端异常，需要人工介入回到登录首页")
        else:
            kkLogger_log("退登失败.")
            return get_return_taskEvent("step_logout", EventStatus.MANUAL_REQUIRED, "登录端异常，需要人工介入回到登录首页")
#####################################################################################

# 
if __name__ == '__main__':
    # 记录程序开始执行的时间  
    
    luhao_task = {
    #     'gameAccount':'***********',
        'gameAccount':'<EMAIL>',
        'gamePassword':'Jun135790.',
        "id":139,
        "status":"PENDING",
        "productSn":"NSH20822593",
        "product_meta":requestGameProductCategoryMeta(75),
        "product_info":{
             "product":{
                "productCategoryId": 75,
                "productSn": "NSH20822593",
                "productCategoryName": "逆水寒手游",
                "updateTime": "2024-09-29T06:36:15.000+00:00",
                "createTime": "2024-09-28T22:49:57.000+00:00",
                "gameActiveFlag": 0,
                "gameAccountQufu": "踏北尘|紫禁之巅"
            }   
        }
    }


    start_time = datetime.datetime.now()
    init_nsh(luhao_task)
    
    # 定义要执行的adb命令
    # adb_command = "adb push D:\\kkzhw\\airtest_log\\NSH49976883_20241018194236\\find_subImg_inscreen_1729252009.png /sdcard/Pictures/"
    #
    # # 在Python脚本中执行adb命令
    # output = os.popen(adb_command).read()
    # adb_command = ['adb']
    # adb_command.extend(['push', "D:\\kkzhw\\airtest_log\\NSH49976883_20241018194236\\find_subImg_inscreen_1729252009.png", '/sdcard/Pictures/'])

    step_login(0)

    # for i in range(120):
    #     dev2 = connect_device("windows:///?title_re=雷电模拟器")

    # step_qufu(0)
    # safe_step_gameset(0)
    # #
    # safe_step_juese(0)
    # safe_step_jueji(0)
    # safe_step_neigong(0)
    # safe_step_dazao(0)
    # safe_step_waiguan(0)
    # safe_step_kaifang_shijie(0)
    # safe_step_qunxia(0)
    # safe_step_zhuangyuan(0)
    # safe_step_lingcong(0)
    #
    # step_img_upload(0)
#     step_meta_upload(0)

    # step_logout(0)

    end_time = datetime.datetime.now()
    total_time = end_time - start_time
    minutes = total_time.total_seconds() / 60
    logger.info(f"录号执行了 {minutes} 分钟。")


        
        
     














