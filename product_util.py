# -*- coding: utf-8 -*-
import re

import requests

import configs
import sys_tool


def extract_number(input_string):
    # 使用正则表达式提取数字
    match = re.search(r'\d+', input_string)
    if match:
        return match.group(0)  # 返回找到的第一个数字
    return None


def sync_product_info(product_sn, pic, raw_pic_url, metadata):
    """
    同步商品信息到服务器
    :param product_sn:
    :param pic: 封面图
    :param raw_pic_url: 详情图片
    :param metadata: 扩展属性
    :return:
    """
    urls = []
    for item in raw_pic_url[:]:
        if item['name'] == '头图':
            raw_pic_url.remove(item)
        else:
            item["url"] = configs.image_server_url + item["value"]
            urls.append(item["url"])
            del item["value"]

    album_pics = ','.join(urls)
    album_pics_json = sys_tool.safe_json_dumps(raw_pic_url)

    meta_dict = {meta['name']: meta for meta in metadata}

    err_msg = ''

    attrs = []

    # 属性处理
    for attr in metadata:
        name = attr['name']

        origin_value = attr.get('values')

        if not origin_value:
            continue

        if name not in meta_dict:
            err_msg += f"1 attr_name【{name}】不存在"
            continue
        if name in ['换绑CD', '转职CD', '转性CD']:
            if origin_value == '是':
                origin_value = '有'
            elif origin_value == '否':
                origin_value = '无'

        meta = meta_dict.get(name)
        # select_type = meta.get('selectType')
        # input_type = meta.get('inputType')
        # input_list = meta.get('inputList', '').split(',')

        # input_type: 0 手工录入，1 列表选择
        # if input_type == 1:  # 从列表中选取
        #     # 先检查单选
        #     # select_type: 0->唯一；1->单选；2->多选
        #     if select_type == 1 and len(origin_value) > 1:  # 单选
        #         if len(origin_value) > 1:
        #             err_msg += f"2 attr_name【{name}】的 value【{origin_value}】为多选，不合法,"
        #             origin_value = origin_value[0]
        #     # 检查多选
        #     if select_type == 2:  # 多选
        #         for value_item in origin_value:
        #             if value_item and value_item not in input_list:  # 检查分割后的值是否非空且不在有效列表中
        #                 err_msg += f"3 attr_name【{name}】: value 【{value_item}】 不合法,"
        #         # 去重
        #         origin_value = list(set(origin_value))
        #     origin_value = ','.join(origin_value)

        # 去重
        origin_value = list(set(origin_value))
        # origin_value = 转成字符串
        origin_value = ','.join(str(item) for item in origin_value)

        if name == '国色值':
            origin_value = extract_number(origin_value)

        item = {
            "productAttributeId": meta_dict.get(name)['id'],
            "value": origin_value,
            "attriName": meta_dict.get(name)['name'],
            "sort": meta_dict.get(name)['sort'],
            "filterType": meta_dict.get(name)['filterType'],
            "searchType": meta_dict.get(name)['searchType'],
            "type": meta_dict.get(name)['type'],
            "searchSort": meta_dict.get(name)['searchSort'],
        }
        attrs.append(item)

    product_param = {
        "albumPics": album_pics,
        "albumPicsJson": album_pics_json,
        "productAttributeValueList": attrs,
        "brandId": None,
        "productAttributeCategoryId": None,  # 逆水寒手游是 17
        "gameAccountQufu": None,
        "description": None,
        "pic": pic,
        "price": None,
        "originalPrice": None,
        "stock": 9,
        "gameGoodsFangxin": None,
        "gameGoodsBukuan": 0,
        "gameGoodsJiangjia": 0,
        "gameGoodsYijia": None,
        "sort": 1,
        "publishTime": None,
        "publishStatus": 0,
        "gameGoodsYishou": None,
        "gameGoodsYuyue": 0,
        "gameGoodsSaletype": 1,
        "gameCareinfoPhone": None,
        "gameCareinfoTime": None,
        "gameCareinfoVx": None,
        "subjectProductRelationList": [],
        "recommandStatus": 0,
        "productCategoryId": None,
        "productCategoryName": None,
        "brandName": "逆水寒手游",
        "productSn": product_sn,
        "verifyStatus": 0,
        "verifyDetail": None,
        "createTime": None,
        "gameSysinfoReadcount": None,
        "gameSysinfoCollectcount": None,
        # "memberId": member_id,
        "pushType": None,
        "pushStatus": 2  # 录号完成
    }

    print(product_param)
    r = requests.post(configs.server_url + '/mall-portal/openapi/record/product/update', json=product_param,
                      headers={
                          'X-Token': configs.SERVER_API_TOKEN
                      })

    if r.json().get('code') == 200:
        return r.json()['data']
    else:
        print(('resp:', r.text))
