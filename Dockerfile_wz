# 使用官方 Python 3.9 镜像作为基础镜像
FROM python:3.9

# 设置时区为 Asia/Shanghai
ENV TZ=Asia/Shanghai

# 设置工作目录为 /usr/src/app
WORKDIR /app

# 复制 requirements.txt 文件
COPY ./wzry/requirements.txt .

# 安装依赖项
RUN pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 复制项目文件到容器中
COPY ./common /app/common
COPY ./wzry /app/wzry
COPY configs.py /app

# 设置 PYTHONPATH
ENV PYTHONPATH=/app

CMD ["python", "wzry/wzry_main.py"]
