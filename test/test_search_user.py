# -*- encoding=utf8 -*-
"""
掌上无畏契约用户搜索功能测试
测试通过掌瓦ID搜索用户并获取完整信息的功能

使用方法：
1. 配置真实的Cookie和掌瓦ID
2. 运行测试脚本
3. 查看搜索结果和用户信息
"""

import json
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from valorant_client import ValorantClient, search_user_simple, get_user_info_by_zhangwa_id


def test_search_user():
    """测试用户搜索功能"""
    print("🔍 掌上无畏契约用户搜索功能测试")
    print("=" * 50)
    
    # 示例数据（需要替换为真实数据）
    example_cookie = "clientType=9; uin=o3171828341; appid=*********; acctype=qc; openid=7681D046F73F1C438E7D3ABC127E9508; access_token=686D65F0B5211CBF686CF1F4B43DD820; userId=JA-0f9fe8d7526f41d3-a2a58f99ca691a99; accountType=5; tid=17C2FAEFC337E0C3725FF4BBAEA70DF3202E2295F55F7EDFAEFE3A0E1C0694ED691428A57672D9761EDAECD0620C749076DEF63985BF0F40358806437660256C56416C0AB688DFE736287AE4A09C4B63B5063578DFEBC6F2D23EC114DDE22C69B9DDB7A4526C6E366E53269808D1B0D5294FD2390449BBED985E1DFDF69B5B008D67911A3007325B546161D5EED851B5C90BDCA5E3F590729FACB88D1F8C3563FF60E0C37D1A6DE0DCB27923CC591FB5;"
    
    zhangwa_id = "*********"  # 掌瓦ID
    zhangwa_id = "*********"  # 掌瓦ID
    op_uuid = "JA-0f9fe8d7526f41d3-a2a58f99ca691a99"  # 查询者UUID
    
    print("📋 测试参数:")
    print(f"   掌瓦ID: {zhangwa_id}")
    print(f"   查询者UUID: {op_uuid}")
    print(f"   Cookie长度: {len(example_cookie)}")
    
    # 方法一：使用客户端类搜索
    print("\n📱 方法一：使用 ValorantClient 类搜索")
    client = ValorantClient()
    client.set_auth(example_cookie)
    
    print("🚀 发送用户搜索请求...")
    search_response = client.search_user_by_zhangwa_id(zhangwa_id)
    
    print(f"✅ 搜索请求完成")
    print(f"   成功状态: {search_response['success']}")
    print(f"   状态码: {search_response['status_code']}")
    print(f"   消息: {search_response['message']}")
    
    if search_response['success']:
        user_uuid = search_response.get('user_uuid')
        user_info = search_response.get('user_info', {})
        
        print(f"\n👤 搜索到的用户信息:")
        print(f"   用户UUID: {user_uuid}")
        print(f"   用户名: {user_info.get('userName', 'N/A')}")
        print(f"   头像: {user_info.get('userIcon', 'N/A')}")
        print(f"   描述: {user_info.get('userDesc', 'N/A')}")
        print(f"   掌瓦号: {user_info.get('userAppNum', 'N/A')}")
        
        # 保存搜索响应数据
        output_file = Path(__file__).parent / "search_response.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(search_response['data'], f, ensure_ascii=False, indent=2)
        print(f"\n💾 搜索响应数据已保存到: {output_file}")
        
        return user_uuid
    else:
        print(f"❌ 搜索失败: {search_response['message']}")
        return None


def test_search_simple():
    """测试便捷搜索函数"""
    print("\n📱 方法二：使用便捷搜索函数")
    
    example_cookie = "clientType=9; uin=o3171828341; appid=*********; acctype=qc; openid=7681D046F73F1C438E7D3ABC127E9508; access_token=686D65F0B5211CBF686CF1F4B43DD820; userId=JA-0f9fe8d7526f41d3-a2a58f99ca691a99; accountType=5; tid=17C2FAEFC337E0C3725FF4BBAEA70DF3202E2295F55F7EDFAEFE3A0E1C0694ED691428A57672D9761EDAECD0620C749076DEF63985BF0F40358806437660256C56416C0AB688DFE736287AE4A09C4B63B5063578DFEBC6F2D23EC114DDE22C69B9DDB7A4526C6E366E53269808D1B0D5294FD2390449BBED985E1DFDF69B5B008D67911A3007325B546161D5EED851B5C90BDCA5E3F590729FACB88D1F8C3563FF60E0C37D1A6DE0DCB27923CC591FB5;"
    zhangwa_id = "*********"
    
    print("🚀 发送便捷搜索请求...")
    response = search_user_simple(example_cookie, zhangwa_id)
    
    print(f"✅ 便捷搜索请求完成")
    print(f"   成功状态: {response['success']}")
    print(f"   消息: {response['message']}")
    
    return response.get('user_uuid') if response['success'] else None


def test_complete_workflow():
    """测试完整的工作流程：搜索+获取资料"""
    print("\n📱 方法三：一键获取完整用户信息")
    
    example_cookie = "clientType=9; uin=o3171828341; appid=*********; acctype=qc; openid=7681D046F73F1C438E7D3ABC127E9508; access_token=686D65F0B5211CBF686CF1F4B43DD820; userId=JA-0f9fe8d7526f41d3-a2a58f99ca691a99; accountType=5; tid=17C2FAEFC337E0C3725FF4BBAEA70DF3202E2295F55F7EDFAEFE3A0E1C0694ED691428A57672D9761EDAECD0620C749076DEF63985BF0F40358806437660256C56416C0AB688DFE736287AE4A09C4B63B5063578DFEBC6F2D23EC114DDE22C69B9DDB7A4526C6E366E53269808D1B0D5294FD2390449BBED985E1DFDF69B5B008D67911A3007325B546161D5EED851B5C90BDCA5E3F590729FACB88D1F8C3563FF60E0C37D1A6DE0DCB27923CC591FB5;"
    op_uuid = "JA-0f9fe8d7526f41d3-a2a58f99ca691a99"
    zhangwa_id = "*********"
    zhangwa_id = "*********"

    print("🚀 发送完整信息获取请求...")
    result = get_user_info_by_zhangwa_id(example_cookie, op_uuid, zhangwa_id)
    
    print(f"✅ 完整信息获取完成")
    print(f"   成功状态: {result['success']}")
    print(f"   消息: {result['message']}")
    
    if result['success']:
        search_result = result['search_result']
        profile_result = result['profile_result']
        
        print(f"\n🔍 搜索结果:")
        print(f"   找到UUID: {result['user_uuid']}")
        print(f"   掌瓦ID: {result['zhangwa_id']}")
        
        if profile_result and profile_result['success']:
            profile_data = profile_result['data']['data'][0]
            print(f"\n👤 用户详细信息:")
            print(f"   昵称: {profile_data.get('nickName', 'N/A')}")
            print(f"   社区等级: {profile_data.get('communityLevel', 'N/A')}")
            print(f"   最新位置: {profile_data.get('latestLocation', 'N/A')}")
            
            if 'gameInfoList' in profile_data and len(profile_data['gameInfoList']) > 0:
                game_info = profile_data['gameInfoList'][0]
                print(f"\n🎮 游戏信息:")
                print(f"   角色名: {game_info.get('roleName', 'N/A')}")
                print(f"   等级: {game_info.get('level', 'N/A')}")
                print(f"   段位: {game_info.get('tier', 'N/A')}")
                print(f"   积分: {game_info.get('league_point', 'N/A')}")
                print(f"   大区: {game_info.get('areaName', 'N/A')}")
            
            # 保存完整结果
            output_file = Path(__file__).parent / "complete_user_info.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n💾 完整用户信息已保存到: {output_file}")
    
    return result['success']


def show_search_api_structure():
    """展示搜索API结构"""
    print("\n📋 用户搜索API结构:")
    print("=" * 40)
    
    print("🔗 接口信息:")
    print("   URL: https://app.mval.qq.com/go/customize_search/search_all")
    print("   方法: GET")
    print("   参数: keyWord=掌瓦ID")
    print("   Accept: application/json")
    print("   Content-Type: application/text")
    
    print("\n📤 响应数据结构:")
    response_structure = {
        "data": {
            "result": 0,
            "searchInfo": [
                {
                    "feedBase": {"layoutType": "2004"},
                    "feedNews": {
                        "header": {"prefixText": "玩家"},
                        "body": {
                            "userList": [
                                {
                                    "userId": "JA-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
                                    "userName": "用户名",
                                    "userIcon": "头像URL",
                                    "userDesc": "掌瓦号: *********",
                                    "userAppNum": "*********"
                                }
                            ]
                        }
                    }
                }
            ]
        },
        "result": 0,
        "msg": "success"
    }
    print(json.dumps(response_structure, ensure_ascii=False, indent=2))


def main():
    """主函数"""
    print("🚀 掌上无畏契约用户搜索功能测试")
    print("=" * 60)
    
    # 显示API结构
    show_search_api_structure()
    
    # 执行测试
    print("\n" + "=" * 60)
    
    # 测试搜索功能
    user_uuid1 = test_search_user()
    user_uuid2 = test_search_simple()
    success3 = test_complete_workflow()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    
    if user_uuid1:
        print("✅ 客户端类搜索功能测试通过")
    else:
        print("❌ 客户端类搜索功能测试失败")
    
    if user_uuid2:
        print("✅ 便捷搜索函数测试通过")
    else:
        print("❌ 便捷搜索函数测试失败")
    
    if success3:
        print("✅ 完整工作流程测试通过")
    else:
        print("❌ 完整工作流程测试失败")
    
    print("\n💡 使用提示:")
    print("1. 需要真实的Cookie认证信息")
    print("2. 掌瓦ID格式: 数字字符串（如：*********）")
    print("3. 搜索成功后会返回用户UUID")
    print("4. 可以使用UUID进一步查询用户详细资料")
    print("5. 认证信息有时效性，过期需要重新获取")


if __name__ == "__main__":
    main()
