# -*- encoding=utf8 -*-
"""
掌上无畏契约API客户端
简单易用的API调用工具，用于获取用户资产数据和用户资料信息

API接口信息：
1. 资产查询接口:
   - URL: https://app.mval.qq.com/go/agame/asset/all
   - 方法: POST
   - 需要: Cookie认证 + Scene参数

2. 用户资料查询接口:
   - URL: https://app.mval.qq.com/go/user_profile/query/user
   - 方法: POST
   - 需要: Cookie认证 + 用户UUID参数

3. 用户搜索接口:
   - URL: https://app.mval.qq.com/go/customize_search/search_all
   - 方法: GET
   - 需要: Cookie认证 + 掌瓦ID参数

使用方法：
```python
from valorant_client import ValorantClient

# 创建客户端
client = ValorantClient()

# 设置认证信息
client.set_auth(cookie="你的Cookie字符串")

# 获取用户资产数据
response = client.get_assets(scene="你的Scene参数")

# 搜索用户获取UUID
search_response = client.search_user_by_zhangwa_id("017895864")

# 获取用户资料信息
profile_response = client.get_user_profile(
    op_uuid="查询者UUID",
    target_uuid="目标用户UUID"
)

if response['success']:
    data = response['data']
    # 处理数据...
else:
    print(f"获取失败: {response['message']}")
```
"""

import json
import requests
from typing import Dict, Any


class ValorantClient:
    """掌上无畏契约API客户端"""
    
    def __init__(self):
        """初始化客户端"""
        self.base_url = "https://app.mval.qq.com"
        self.session = requests.Session()
        self._setup_headers()
        self._setup_proxy()
    
    def _setup_headers(self):
        """设置默认请求头"""
        self.session.headers.update({
            "Host": "app.mval.qq.com",
            "Content-Type": "application/json",
            "User-Agent": "mval/1.10.0.10036 Channel/default Mozilla/5.0 (Linux; Android 12; NTH-AN00 Build/V417IR; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/101.0.4951.61 Mobile Safari/537.36"
        })

    def _setup_proxy(self):
        """设置代理配置"""
        import os

        # 清除环境变量中的代理设置
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
        for var in proxy_vars:
            if var in os.environ:
                del os.environ[var]

        # 禁用代理以避免连接问题
        self.session.proxies = {}
        self.session.trust_env = False

        # 禁用SSL验证（仅用于测试）
        self.session.verify = False

        # 禁用urllib3的SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    def set_auth(self, cookie: str):
        """
        设置认证Cookie
        
        Args:
            cookie: 从掌上无畏契约应用中获取的完整Cookie字符串
                   包含: clientType, uin, appid, acctype, openid, access_token, userId, accountType, tid等字段
        """
        self.session.headers.update({"Cookie": cookie})
    
    def get_assets(self, scene: str, show_unlock: int = 1) -> Dict[str, Any]:
        """
        获取用户资产数据
        
        Args:
            scene: 加密的场景参数，从应用中获取
            show_unlock: 是否显示未解锁内容 (1=显示, 0=不显示)
            
        Returns:
            响应数据字典:
            {
                "success": bool,        # 是否成功
                "data": dict,          # API返回的原始数据
                "message": str,        # 状态消息
                "status_code": int     # HTTP状态码
            }
        """
        url = f"{self.base_url}/go/agame/asset/all"
        
        payload = {
            "scene": scene,
            "show_unlock": show_unlock
        }
        
        try:
            response = self.session.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # 检查API业务状态
                    if data.get('result') == 0:  # 成功
                        return {
                            "success": True,
                            "data": data,
                            "message": "获取成功",
                            "status_code": 200
                        }
                    else:  # 业务错误
                        error_msg = data.get('msg', data.get('errMsg', '未知错误'))
                        return {
                            "success": False,
                            "data": data,
                            "message": f"API错误: {error_msg}",
                            "status_code": 200
                        }
                        
                except json.JSONDecodeError:
                    return {
                        "success": False,
                        "data": None,
                        "message": "响应数据格式错误",
                        "status_code": response.status_code
                    }
            else:
                return {
                    "success": False,
                    "data": None,
                    "message": f"HTTP错误: {response.status_code}",
                    "status_code": response.status_code
                }
                
        except requests.exceptions.Timeout:
            return {
                "success": False,
                "data": None,
                "message": "请求超时",
                "status_code": 0
            }
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "data": None,
                "message": f"网络错误: {str(e)}",
                "status_code": 0
            }
        except Exception as e:
            return {
                "success": False,
                "data": None,
                "message": f"未知错误: {str(e)}",
                "status_code": 0
            }

    def search_user_by_zhangwa_id(self, zhangwa_id: str) -> Dict[str, Any]:
        """
        通过掌瓦ID搜索用户，获取用户UUID

        Args:
            zhangwa_id: 掌瓦ID（如：017895864）

        Returns:
            响应数据字典:
            {
                "success": bool,        # 是否成功
                "data": dict,          # API返回的原始数据
                "message": str,        # 状态消息
                "status_code": int,    # HTTP状态码
                "user_uuid": str,      # 用户UUID（如果找到）
                "user_info": dict      # 用户基本信息（如果找到）
            }
        """
        url = f"{self.base_url}/go/customize_search/search_all"

        params = {
            "keyWord": zhangwa_id
        }

        # 设置特殊的请求头
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/text"
        }

        try:
            response = self.session.get(url, params=params, headers=headers, timeout=30)

            if response.status_code == 200:
                try:
                    data = response.json()

                    # 检查API业务状态
                    if data.get('result') == 0:  # 成功
                        # 解析搜索结果
                        search_info = data.get('data', {}).get('searchInfo', [])

                        if search_info and len(search_info) > 0:
                            # 查找用户信息
                            for item in search_info:
                                feed_news = item.get('feedNews', {})
                                body = feed_news.get('body', {})
                                user_list = body.get('userList', [])

                                if user_list and len(user_list) > 0:
                                    user = user_list[0]  # 取第一个用户
                                    user_uuid = user.get('userId', '')

                                    if user_uuid:
                                        return {
                                            "success": True,
                                            "data": data,
                                            "message": "搜索用户成功",
                                            "status_code": 200,
                                            "user_uuid": user_uuid,
                                            "user_info": {
                                                "uuid": user_uuid,
                                                "userName": user.get('userName', ''),
                                                "userIcon": user.get('userIcon', ''),
                                                "userDesc": user.get('userDesc', ''),
                                                "userAppNum": user.get('userAppNum', '')
                                            }
                                        }

                        # 没有找到用户
                        return {
                            "success": False,
                            "data": data,
                            "message": "未找到该掌瓦ID对应的用户",
                            "status_code": 200,
                            "user_uuid": None,
                            "user_info": None
                        }
                    else:  # 业务错误
                        error_msg = data.get('msg', data.get('errMsg', '未知错误'))
                        return {
                            "success": False,
                            "data": data,
                            "message": f"API错误: {error_msg}",
                            "status_code": 200,
                            "user_uuid": None,
                            "user_info": None
                        }

                except json.JSONDecodeError:
                    return {
                        "success": False,
                        "data": None,
                        "message": "响应数据格式错误",
                        "status_code": response.status_code,
                        "user_uuid": None,
                        "user_info": None
                    }
            else:
                return {
                    "success": False,
                    "data": None,
                    "message": f"HTTP错误: {response.status_code}",
                    "status_code": response.status_code,
                    "user_uuid": None,
                    "user_info": None
                }

        except requests.exceptions.Timeout:
            return {
                "success": False,
                "data": None,
                "message": "请求超时",
                "status_code": 0,
                "user_uuid": None,
                "user_info": None
            }
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "data": None,
                "message": f"网络错误: {str(e)}",
                "status_code": 0,
                "user_uuid": None,
                "user_info": None
            }
        except Exception as e:
            return {
                "success": False,
                "data": None,
                "message": f"未知错误: {str(e)}",
                "status_code": 0,
                "user_uuid": None,
                "user_info": None
            }

    def get_user_profile(self, op_uuid: str, target_uuid: str,
                        is_need_game_info: int = 1, is_need_medal: int = 1,
                        is_need_community_info: int = 1, is_need_main_info: int = 1,
                        client_type: int = 9, is_need_dress: int = 1,
                        is_need_remark: int = 1, scene: str = "") -> Dict[str, Any]:
        """
        获取用户资料信息

        Args:
            op_uuid: 操作者UUID（查询者的UUID）
            target_uuid: 目标用户UUID（被查询者的UUID）
            is_need_game_info: 是否需要游戏信息 (1=需要, 0=不需要)
            is_need_medal: 是否需要勋章信息 (1=需要, 0=不需要)
            is_need_community_info: 是否需要社区信息 (1=需要, 0=不需要)
            is_need_main_info: 是否需要主要信息 (1=需要, 0=不需要)
            client_type: 客户端类型 (默认9)
            is_need_dress: 是否需要装扮信息 (1=需要, 0=不需要)
            is_need_remark: 是否需要备注信息 (1=需要, 0=不需要)
            scene: 场景参数 (可选)

        Returns:
            响应数据字典:
            {
                "success": bool,        # 是否成功
                "data": dict,          # API返回的原始数据
                "message": str,        # 状态消息
                "status_code": int     # HTTP状态码
            }
        """
        url = f"{self.base_url}/go/user_profile/query/user"

        payload = {
            "opUuid": op_uuid,
            "isNeedGameInfo": is_need_game_info,
            "isNeedMedal": is_need_medal,
            "isNeedCommunityInfo": is_need_community_info,
            "isNeedMainInfo": is_need_main_info,
            "clientType": client_type,
            "isNeedDress": is_need_dress,
            "isNeedRemark": is_need_remark,
            "uuidSceneList": [
                {
                    "uuid": target_uuid,
                    "scene": scene
                }
            ]
        }

        try:
            response = self.session.post(url, json=payload, timeout=30)

            if response.status_code == 200:
                try:
                    data = response.json()

                    # 检查API业务状态
                    if data.get('result') == 0:  # 成功
                        return {
                            "success": True,
                            "data": data,
                            "message": "获取用户资料成功",
                            "status_code": 200
                        }
                    else:  # 业务错误
                        error_msg = data.get('msg', data.get('err_msg', '未知错误'))
                        return {
                            "success": False,
                            "data": data,
                            "message": f"API错误: {error_msg}",
                            "status_code": 200
                        }

                except json.JSONDecodeError:
                    return {
                        "success": False,
                        "data": None,
                        "message": "响应数据格式错误",
                        "status_code": response.status_code
                    }
            else:
                return {
                    "success": False,
                    "data": None,
                    "message": f"HTTP错误: {response.status_code}",
                    "status_code": response.status_code
                }

        except requests.exceptions.Timeout:
            return {
                "success": False,
                "data": None,
                "message": "请求超时",
                "status_code": 0
            }
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "data": None,
                "message": f"网络错误: {str(e)}",
                "status_code": 0
            }
        except Exception as e:
            return {
                "success": False,
                "data": None,
                "message": f"未知错误: {str(e)}",
                "status_code": 0
            }


# 便捷函数
def create_client() -> ValorantClient:
    """创建客户端实例"""
    return ValorantClient()


def get_user_assets(cookie: str, scene: str, show_unlock: int = 1) -> Dict[str, Any]:
    """
    一键获取用户资产数据的便捷函数

    Args:
        cookie: Cookie认证字符串
        scene: Scene参数
        show_unlock: 是否显示未解锁内容

    Returns:
        API响应数据
    """
    client = create_client()
    client.set_auth(cookie)
    return client.get_assets(scene, show_unlock)


def get_user_profile_simple(cookie: str, op_uuid: str, target_uuid: str, scene: str = "") -> Dict[str, Any]:
    """
    一键获取用户资料信息的便捷函数

    Args:
        cookie: Cookie认证字符串
        op_uuid: 操作者UUID（查询者的UUID）
        target_uuid: 目标用户UUID（被查询者的UUID）
        scene: 场景参数（可选）

    Returns:
        API响应数据
    """
    client = create_client()
    client.set_auth(cookie)
    return client.get_user_profile(op_uuid, target_uuid, scene=scene)


def search_user_simple(cookie: str, zhangwa_id: str) -> Dict[str, Any]:
    """
    一键搜索用户的便捷函数

    Args:
        cookie: Cookie认证字符串
        zhangwa_id: 掌瓦ID

    Returns:
        API响应数据
    """
    client = create_client()
    client.set_auth(cookie)
    return client.search_user_by_zhangwa_id(zhangwa_id)


def get_user_info_by_zhangwa_id(cookie: str, op_uuid: str, zhangwa_id: str) -> Dict[str, Any]:
    """
    通过掌瓦ID获取完整用户信息的便捷函数（搜索+资料查询）

    Args:
        cookie: Cookie认证字符串
        op_uuid: 操作者UUID（查询者的UUID）
        zhangwa_id: 掌瓦ID

    Returns:
        包含搜索结果和用户资料的完整信息
    """
    client = create_client()
    client.set_auth(cookie)

    # 第一步：搜索用户获取UUID
    search_result = client.search_user_by_zhangwa_id(zhangwa_id)

    if not search_result['success']:
        return {
            "success": False,
            "message": f"搜索用户失败: {search_result['message']}",
            "search_result": search_result,
            "profile_result": None
        }

    target_uuid = search_result.get('user_uuid')
    if not target_uuid:
        return {
            "success": False,
            "message": "未找到用户UUID",
            "search_result": search_result,
            "profile_result": None
        }

    # 第二步：获取用户详细资料
    profile_result = client.get_user_profile(op_uuid, target_uuid)

    return {
        "success": profile_result['success'],
        "message": "获取用户完整信息成功" if profile_result['success'] else f"获取用户资料失败: {profile_result['message']}",
        "search_result": search_result,
        "profile_result": profile_result,
        "user_uuid": target_uuid,
        "zhangwa_id": zhangwa_id
    }


# 使用示例
if __name__ == "__main__":
    print("掌上无畏契约API客户端")
    print("=" * 30)
    
    print("📖 使用方法:")
    print("1. 从掌上无畏契约应用中获取Cookie和相关参数")
    print("2. 使用ValorantClient类或便捷函数调用API")
    print("3. 处理返回的结构化数据")

    print("\n💡 资产查询示例:")
    print("```python")
    print("from valorant_client import ValorantClient")
    print("")
    print("client = ValorantClient()")
    print("client.set_auth('你的Cookie')")
    print("response = client.get_assets('你的Scene参数')")
    print("")
    print("if response['success']:")
    print("    data = response['data']")
    print("    # 处理资产数据...")
    print("```")

    print("\n💡 用户搜索示例:")
    print("```python")
    print("# 通过掌瓦ID搜索用户")
    print("search_response = client.search_user_by_zhangwa_id('017895864')")
    print("if search_response['success']:")
    print("    user_uuid = search_response['user_uuid']")
    print("    print(f\"找到用户UUID: {user_uuid}\")")
    print("```")

    print("\n💡 用户资料查询示例:")
    print("```python")
    print("# 查询用户资料")
    print("profile_response = client.get_user_profile(")
    print("    op_uuid='查询者UUID',")
    print("    target_uuid='目标用户UUID'")
    print(")")
    print("")
    print("if profile_response['success']:")
    print("    user_data = profile_response['data']['data'][0]")
    print("    print(f\"用户昵称: {user_data['nickName']}\")")
    print("    print(f\"游戏等级: {user_data['gameInfoList'][0]['level']}\")")
    print("    print(f\"游戏段位: {user_data['gameInfoList'][0]['tier']}\")")
    print("```")

    print("\n💡 一键获取完整信息示例:")
    print("```python")
    print("# 通过掌瓦ID直接获取完整用户信息")
    print("result = get_user_info_by_zhangwa_id(")
    print("    cookie='你的Cookie',")
    print("    op_uuid='查询者UUID',")
    print("    zhangwa_id='017895864'")
    print(")")
    print("")
    print("if result['success']:")
    print("    profile_data = result['profile_result']['data']['data'][0]")
    print("    print(f\"用户昵称: {profile_data['nickName']}\")")
    print("    print(f\"游戏段位: {profile_data['gameInfoList'][0]['tier']}\")")
    print("```")
    
    print("\n⚠️  重要提醒:")
    print("- Cookie和相关参数需要从真实应用中获取")
    print("- 认证信息有时效性，过期需要重新获取")
    print("- 请合理控制请求频率")
    print("- 妥善保护用户隐私数据")
    print("- UUID参数格式: JA-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx")
    print("- 掌瓦ID格式: 数字字符串（如：017895864）")
