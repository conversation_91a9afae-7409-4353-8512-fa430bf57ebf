# -*- encoding=utf8 -*-
"""
Valorant 客户端完整示例
展示如何获取和处理用户资产数据
"""

from valorant_client import ValorantClient, get_user_assets
import json
import time


# 真实认证信息
COOKIE = "clientType=9; uin=o3171828341; appid=*********; acctype=qc; openid=7681D046F73F1C438E7D3ABC127E9508; access_token=686D65F0B5211CBF686CF1F4B43DD820; userId=JA-0f9fe8d7526f41d3-a2a58f99ca691a99; accountType=5; tid=A4DD016CDEF26F8C7E3BD61DBA5A9BB73347A65ACE16BC39FC2B31F518E5F83BC50B9418C7EE9435508AB3659C0FA6C2CC0AFA8916ACAECC2F838DEAB03C2FF543BEBE22F7296848C7258E0CDBCEB962C2068AC1DFA3B254998A1661DEF4C8F304056F799D5E252DB8632E5216943CDB87C820C5B20FEC8725E9E406D7A571FA0CB4D70173D445091485FDDD5E95F06A5A14375C48EC192E420D9FB04414915703DEA5CC53E26C497C42A604C5974C31;"

SCENE = "v3_sJUHrAIELSAJyy_xkojaUQM1razOGDlnQnnJB-eEvk3DLCj1CS3vOA1CITMENNgTU7S6fEImJdIKTCDZXd1KQbgZzx2W5jLLkWuiq3eak4Ag1esMH7auuXFnc-79pA3QjZZmv_Acr4WHwgRWaKqQb681-rApoe4yyVSmO5eteR6n0hjTAND4aeSWbeFyNdoTjpwzpyAVjMC-bSVX7m5P1Q=="


def get_valorant_data():
    """获取 Valorant 用户数据"""
    print("🎮 获取 Valorant 用户数据")
    print("=" * 40)
    
    # 使用客户端获取数据
    client = ValorantClient()
    client.set_auth(COOKIE)
    response = client.get_assets(SCENE)
    
    if response['success']:
        print("✅ 数据获取成功")
        return response['data']
    else:
        print(f"❌ 获取失败: {response['message']}")
        return None


def analyze_user_assets(data):
    """分析用户资产数据"""
    if not data:
        return

    print("\n📊 用户资产分析")
    print("=" * 40)

    # 检查数据结构，如果有嵌套的 data 字段
    if 'data' in data:
        asset_data = data['data']
    else:
        asset_data = data

    # 分析资金情况
    if 'money' in asset_data:
        money = asset_data['money']
        print(f"💰 {money['title']}:")
        for item in money['list']:
            print(f"   {item['title']}: {item['value']}")

    # 分析英雄情况
    if 'agent' in asset_data:
        agent = asset_data['agent']
        print(f"\n🦸 {agent['title']}:")
        print(f"   总数: {agent['total_num']}")
        print(f"   已拥有: {agent['access_num']}")
        print(f"   拥有的英雄:")
        for hero in agent['list']:
            print(f"     - {hero['name']} (ID: {hero['id']})")

    # 分析皮肤情况
    if 'skin' in asset_data:
        skin = asset_data['skin']
        print(f"\n🎨 {skin['title']}:")
        print(f"   已拥有: {skin['access_num']}")
        if skin['list']:
            for item in skin['list']:
                print(f"     - {item['name']}")
        else:
            print("   暂无皮肤")

    # 分析喷漆情况
    if 'spray' in asset_data:
        spray = asset_data['spray']
        print(f"\n🎯 {spray['title']}:")
        print(f"   已拥有: {spray['access_num']}")
        for item in spray['list']:
            print(f"     - {item['name']} ({item['unlock_time']})")

    # 分析卡面情况
    if 'card' in asset_data:
        card = asset_data['card']
        print(f"\n🃏 {card['title']}:")
        print(f"   已拥有: {card['access_num']}")
        for item in card['list']:
            print(f"     - {item['name']} ({item['unlock_time']})")

    # 分析挂饰情况
    if 'charm' in asset_data:
        charm = asset_data['charm']
        print(f"\n🎀 {charm['title']}:")
        print(f"   已拥有: {charm['access_num']}")
        if charm['list']:
            for item in charm['list']:
                print(f"     - {item['name']}")
        else:
            print("   暂无挂饰")


def save_detailed_report(data):
    """保存详细报告"""
    if not data:
        return
    
    print("\n📝 生成详细报告")
    print("=" * 40)
    
    # 生成报告
    report = {
        "生成时间": time.strftime("%Y-%m-%d %H:%M:%S"),
        "用户资产统计": {},
        "详细数据": data
    }
    
    # 统计各类资产
    stats = {}

    # 检查数据结构
    if 'data' in data:
        asset_data = data['data']
    else:
        asset_data = data

    if 'money' in asset_data:
        money_info = {}
        for item in asset_data['money']['list']:
            money_info[item['title']] = item['value']
        stats['资金'] = money_info

    if 'agent' in asset_data:
        stats['英雄'] = {
            "总数": asset_data['agent']['total_num'],
            "已拥有": asset_data['agent']['access_num'],
            "拥有列表": [hero['name'] for hero in asset_data['agent']['list']]
        }

    if 'spray' in asset_data:
        stats['喷漆'] = {
            "已拥有": asset_data['spray']['access_num'],
            "列表": [item['name'] for item in asset_data['spray']['list']]
        }

    if 'card' in asset_data:
        stats['卡面'] = {
            "已拥有": asset_data['card']['access_num'],
            "列表": [item['name'] for item in asset_data['card']['list']]
        }
    
    report['用户资产统计'] = stats
    
    # 保存报告
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"valorant_report_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 详细报告已保存到: {filename}")
    
    # 生成简要文本报告
    txt_filename = f"valorant_summary_{timestamp}.txt"
    with open(txt_filename, 'w', encoding='utf-8') as f:
        f.write("Valorant 用户资产报告\n")
        f.write("=" * 30 + "\n")
        f.write(f"生成时间: {report['生成时间']}\n\n")
        
        for category, info in stats.items():
            f.write(f"{category}:\n")
            if isinstance(info, dict):
                for key, value in info.items():
                    if isinstance(value, list):
                        f.write(f"  {key}: {len(value)} 项\n")
                        for item in value:
                            f.write(f"    - {item}\n")
                    else:
                        f.write(f"  {key}: {value}\n")
            f.write("\n")
    
    print(f"✅ 简要报告已保存到: {txt_filename}")


def main():
    """主函数"""
    print("🎮 Valorant 完整示例")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 50)
    
    try:
        # 1. 获取数据
        data = get_valorant_data()
        
        if data:
            # 2. 分析数据
            analyze_user_assets(data)
            
            # 3. 保存报告
            save_detailed_report(data)
            
            print("\n🎉 所有操作完成!")
            print("💡 提示: 查看生成的 JSON 和 TXT 文件获取详细信息")
        else:
            print("❌ 无法获取数据，请检查认证信息")
    
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
