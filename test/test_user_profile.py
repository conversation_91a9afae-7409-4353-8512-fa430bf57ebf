# -*- encoding=utf8 -*-
"""
掌上无畏契约用户资料查询测试
测试新添加的用户资料查询接口功能

使用方法：
1. 配置真实的Cookie和UUID参数
2. 运行测试脚本
3. 查看用户资料查询结果
"""

import json
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from valorant_client import ValorantClient, get_user_profile_simple


def test_user_profile_query():
    """测试用户资料查询功能"""
    print("🎮 掌上无畏契约用户资料查询测试")
    print("=" * 50)
    
    # 示例数据（需要替换为真实数据）
    example_cookie = "clientType=9; uin=o3171828341; appid=*********; acctype=qc; openid=7681D046F73F1C438E7D3ABC127E9508; access_token=686D65F0B5211CBF686CF1F4B43DD820; userId=JA-0f9fe8d7526f41d3-a2a58f99ca691a99; accountType=5; tid=17C2FAEFC337E0C3725FF4BBAEA70DF3202E2295F55F7EDFAEFE3A0E1C0694ED691428A57672D9761EDAECD0620C749076DEF63985BF0F40358806437660256C56416C0AB688DFE736287AE4A09C4B63B5063578DFEBC6F2D23EC114DDE22C69B9DDB7A4526C6E366E53269808D1B0D5294FD2390449BBED985E1DFDF69B5B008D67911A3007325B546161D5EED851B5C90BDCA5E3F590729FACB88D1F8C3563FF60E0C37D1A6DE0DCB27923CC591FB5;"
    
    op_uuid = "JA-0f9fe8d7526f41d3-a2a58f99ca691a99"  # 查询者UUID
    target_uuid = "JA-e1948077d621465a-b1edadc609c28b32"  # 目标用户UUID
    
    print("📋 测试参数:")
    print(f"   查询者UUID: {op_uuid}")
    print(f"   目标用户UUID: {target_uuid}")
    print(f"   Cookie长度: {len(example_cookie)}")
    
    # 方法一：使用客户端类
    print("\n📱 方法一：使用 ValorantClient 类")
    client = ValorantClient()
    client.set_auth(example_cookie)
    
    print("🚀 发送用户资料查询请求...")
    response = client.get_user_profile(op_uuid, target_uuid)
    
    print(f"✅ 请求完成")
    print(f"   成功状态: {response['success']}")
    print(f"   状态码: {response['status_code']}")
    print(f"   消息: {response['message']}")
    
    if response['success'] and response['data']:
        data = response['data']
        print(f"   请求ID: {data.get('req_id', 'N/A')}")
        
        # 解析用户数据
        if 'data' in data and len(data['data']) > 0:
            user_info = data['data'][0]
            print(f"\n👤 用户信息:")
            print(f"   UUID: {user_info.get('uuid', 'N/A')}")
            print(f"   昵称: {user_info.get('nickName', 'N/A')}")
            print(f"   头像: {user_info.get('headUrl', 'N/A')}")
            print(f"   社区等级: {user_info.get('communityLevel', 'N/A')}")
            print(f"   最新位置: {user_info.get('latestLocation', 'N/A')}")
            
            # 游戏信息
            if 'gameInfoList' in user_info and len(user_info['gameInfoList']) > 0:
                game_info = user_info['gameInfoList'][0]
                print(f"\n🎮 游戏信息:")
                print(f"   角色名: {game_info.get('roleName', 'N/A')}")
                print(f"   等级: {game_info.get('level', 'N/A')}")
                print(f"   段位: {game_info.get('tier', 'N/A')}")
                print(f"   积分: {game_info.get('league_point', 'N/A')}")
                print(f"   大区: {game_info.get('areaName', 'N/A')}")
        
        # 保存响应数据
        output_file = Path(__file__).parent / "user_profile_response.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"\n💾 响应数据已保存到: {output_file}")
    
    # 方法二：使用便捷函数
    print("\n📱 方法二：使用便捷函数")
    print("🚀 发送用户资料查询请求...")
    response2 = get_user_profile_simple(example_cookie, op_uuid, target_uuid)
    
    print(f"✅ 便捷函数请求完成")
    print(f"   成功状态: {response2['success']}")
    print(f"   消息: {response2['message']}")
    
    return response['success']


def show_api_structure():
    """展示API请求结构"""
    print("\n📋 用户资料查询API结构:")
    print("=" * 40)
    
    print("🔗 接口信息:")
    print("   URL: https://app.mval.qq.com/go/user_profile/query/user")
    print("   方法: POST")
    print("   Content-Type: application/json")
    
    print("\n📝 请求体结构:")
    request_structure = {
        "opUuid": "查询者UUID",
        "isNeedGameInfo": 1,
        "isNeedMedal": 1,
        "isNeedCommunityInfo": 1,
        "isNeedMainInfo": 1,
        "clientType": 9,
        "isNeedDress": 1,
        "isNeedRemark": 1,
        "uuidSceneList": [
            {
                "uuid": "目标用户UUID",
                "scene": "场景参数(可选)"
            }
        ]
    }
    print(json.dumps(request_structure, ensure_ascii=False, indent=2))
    
    print("\n📤 响应数据结构:")
    response_structure = {
        "result": 0,
        "msg": "success",
        "err_msg": "",
        "req_id": "请求ID",
        "data": [
            {
                "uuid": "用户UUID",
                "nickName": "用户昵称",
                "headUrl": "头像URL",
                "communityLevel": "社区等级",
                "latestLocation": "最新位置",
                "gameInfoList": [
                    {
                        "roleName": "游戏角色名",
                        "level": "等级",
                        "tier": "段位",
                        "league_point": "积分",
                        "areaName": "大区名称"
                    }
                ]
            }
        ]
    }
    print(json.dumps(response_structure, ensure_ascii=False, indent=2))


def main():
    """主函数"""
    print("🚀 掌上无畏契约用户资料查询功能测试")
    print("=" * 60)
    
    # 显示API结构
    show_api_structure()
    
    # 执行测试
    print("\n" + "=" * 60)
    success = test_user_profile_query()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    if success:
        print("✅ 用户资料查询功能测试通过")
    else:
        print("❌ 用户资料查询功能测试失败")
    
    print("\n💡 使用提示:")
    print("1. 需要真实的Cookie认证信息")
    print("2. UUID格式: JA-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx")
    print("3. 查询者UUID通常是当前登录用户的UUID")
    print("4. 目标UUID是要查询的用户UUID")
    print("5. 认证信息有时效性，过期需要重新获取")


if __name__ == "__main__":
    main()
