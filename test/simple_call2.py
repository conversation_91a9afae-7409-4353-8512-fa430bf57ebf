# -*- encoding=utf8 -*-
"""
最简单的 Valorant API 调用示例
"""

from valorant_client import ValorantClient, get_user_assets
import json


# 真实认证信息
# COOKIE = "clientType=9; uin=o3171828341; appid=*********; acctype=qc; openid=7681D046F73F1C438E7D3ABC127E9508; access_token=686D65F0B5211CBF686CF1F4B43DD820; userId=JA-0f9fe8d7526f41d3-a2a58f99ca691a99; accountType=5; tid=A4DD016CDEF26F8C7E3BD61DBA5A9BB73347A65ACE16BC39FC2B31F518E5F83BC50B9418C7EE9435508AB3659C0FA6C2CC0AFA8916ACAECC2F838DEAB03C2FF543BEBE22F7296848C7258E0CDBCEB962C2068AC1DFA3B254998A1661DEF4C8F304056F799D5E252DB8632E5216943CDB87C820C5B20FEC8725E9E406D7A571FA0CB4D70173D445091485FDDD5E95F06A5A14375C48EC192E420D9FB04414915703DEA5CC53E26C497C42A604C5974C31;"
COOKIE = "clientType=9; uin=o1560254921; appid=*********; acctype=qc; openid=82C452629A65F50CA86CA3DA11D4AB16; access_token=686D65F0B5211CBF686CF1F4B43DD820; userId=JA-0f9fe8d7526f41d3-a2a58f99ca691a99; accountType=5; tid=A4DD016CDEF26F8C7E3BD61DBA5A9BB73347A65ACE16BC39FC2B31F518E5F83BC50B9418C7EE9435508AB3659C0FA6C2CC0AFA8916ACAECC2F838DEAB03C2FF543BEBE22F7296848C7258E0CDBCEB962C2068AC1DFA3B254998A1661DEF4C8F304056F799D5E252DB8632E5216943CDB87C820C5B20FEC8725E9E406D7A571FA0CB4D70173D445091485FDDD5E95F06A5A14375C48EC192E420D9FB04414915703DEA5CC53E26C497C42A604C5974C31;"

SCENE = "v3_sJUHrAIELSAJyy_xkojaUQM1razOGDlnQnnJB-eEvk3DLCj1CS3vOA1CITMENNgTU7S6fEImJdIKTCDZXd1KQbgZzx2W5jLLkWuiq3eak4Ag1esMH7auuXFnc-79pA3QjZZmv_Acr4WHwgRWaKqQb681-rApoe4yyVSmO5eteR6n0hjTAND4aeSWbeFyNdoTjpwzpyAVjMC-bSVX7m5P1Q=="


def method1_client_class():
    """方法1: 使用客户端类"""
    print("方法1: 使用 ValorantClient 类")
    
    client = ValorantClient()
    client.set_auth(COOKIE)
    response = client.get_assets(SCENE)
    
    if response['success']:
        print("✅ 成功")
        print(f"数据大小: {len(json.dumps(response['data']))} 字符")
        return response['data']
    else:
        print(f"❌ 失败: {response['message']}")
        return None


def method2_convenient_function():
    """方法2: 使用便捷函数"""
    print("\n方法2: 使用便捷函数")
    
    response = get_user_assets(COOKIE, SCENE)
    
    if response['success']:
        print("✅ 成功")
        return response['data']
    else:
        print(f"❌ 失败: {response['message']}")
        return None


def save_data(data):
    """保存数据到文件"""
    if data:
        with open("valorant_data2.json", "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print("💾 数据已保存到 valorant_data.json")


if __name__ == "__main__":
    print("🎮 Valorant API 简单调用")
    print("=" * 30)
    
    # 尝试两种方法
    data = method1_client_class() or method2_convenient_function()
    
    # 保存数据
    save_data(data)
    
    print("\n✅ 完成")
