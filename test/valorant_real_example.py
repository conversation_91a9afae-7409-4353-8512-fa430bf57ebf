# -*- encoding=utf8 -*-
"""
Valorant 客户端真实调用示例
使用真实的认证信息进行 API 调用
"""

import json
import time
from valorant_client import ValorantClient, get_user_assets


def real_api_call():
    """使用真实认证信息调用 API"""
    
    # 真实的 Cookie 信息
    cookie = "clientType=9; uin=o3171828341; appid=*********; acctype=qc; openid=7681D046F73F1C438E7D3ABC127E9508; access_token=686D65F0B5211CBF686CF1F4B43DD820; userId=JA-0f9fe8d7526f41d3-a2a58f99ca691a99; accountType=5; tid=A4DD016CDEF26F8C7E3BD61DBA5A9BB73347A65ACE16BC39FC2B31F518E5F83BC50B9418C7EE9435508AB3659C0FA6C2CC0AFA8916ACAECC2F838DEAB03C2FF543BEBE22F7296848C7258E0CDBCEB962C2068AC1DFA3B254998A1661DEF4C8F304056F799D5E252DB8632E5216943CDB87C820C5B20FEC8725E9E406D7A571FA0CB4D70173D445091485FDDD5E95F06A5A14375C48EC192E420D9FB04414915703DEA5CC53E26C497C42A604C5974C31;"
    
    # 真实的 Scene 参数
    scene = "v3_sJUHrAIELSAJyy_xkojaUQM1razOGDlnQnnJB-eEvk3DLCj1CS3vOA1CITMENNgTU7S6fEImJdIKTCDZXd1KQbgZzx2W5jLLkWuiq3eak4Ag1esMH7auuXFnc-79pA3QjZZmv_Acr4WHwgRWaKqQb681-rApoe4yyVSmO5eteR6n0hjTAND4aeSWbeFyNdoTjpwzpyAVjMC-bSVX7m5P1Q=="
    
    print("🎮 Valorant API 真实调用测试")
    print("=" * 50)
    
    # 方法一：使用客户端类
    print("📱 方法一：使用 ValorantClient 类")
    client = ValorantClient()
    client.set_auth(cookie)
    
    print("🚀 发送 API 请求...")
    response = client.get_assets(scene)
    
    print(f"✅ 请求完成")
    print(f"   成功状态: {response['success']}")
    print(f"   状态码: {response['status_code']}")
    print(f"   消息: {response['message']}")
    
    if response['success']:
        data = response['data']
        print(f"📊 数据获取成功")
        print(f"   数据类型: {type(data)}")
        
        if isinstance(data, dict):
            print(f"   数据字段: {list(data.keys())}")
            print(f"   数据大小: {len(json.dumps(data))} 字符")
        
        # 保存完整响应数据
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"valorant_response_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(response, f, ensure_ascii=False, indent=2)
        
        print(f"💾 完整响应已保存到: {filename}")
        
        return data
    else:
        print(f"❌ 请求失败: {response['message']}")
        return None


def convenient_function_call():
    """使用便捷函数调用"""
    
    print("\n" + "=" * 50)
    print("🚀 方法二：使用便捷函数")
    
    cookie = "clientType=9; uin=o3171828341; appid=*********; acctype=qc; openid=7681D046F73F1C438E7D3ABC127E9508; access_token=686D65F0B5211CBF686CF1F4B43DD820; userId=JA-0f9fe8d7526f41d3-a2a58f99ca691a99; accountType=5; tid=A4DD016CDEF26F8C7E3BD61DBA5A9BB73347A65ACE16BC39FC2B31F518E5F83BC50B9418C7EE9435508AB3659C0FA6C2CC0AFA8916ACAECC2F838DEAB03C2FF543BEBE22F7296848C7258E0CDBCEB962C2068AC1DFA3B254998A1661DEF4C8F304056F799D5E252DB8632E5216943CDB87C820C5B20FEC8725E9E406D7A571FA0CB4D70173D445091485FDDD5E95F06A5A14375C48EC192E420D9FB04414915703DEA5CC53E26C497C42A604C5974C31;"
    
    scene = "v3_sJUHrAIELSAJyy_xkojaUQM1razOGDlnQnnJB-eEvk3DLCj1CS3vOA1CITMENNgTU7S6fEImJdIKTCDZXd1KQbgZzx2W5jLLkWuiq3eak4Ag1esMH7auuXFnc-79pA3QjZZmv_Acr4WHwgRWaKqQb681-rApoe4yyVSmO5eteR6n0hjTAND4aeSWbeFyNdoTjpwzpyAVjMC-bSVX7m5P1Q=="
    
    print("🔄 使用便捷函数调用...")
    response = get_user_assets(cookie, scene)
    
    if response['success']:
        print("✅ 便捷函数调用成功")
        return response['data']
    else:
        print(f"❌ 便捷函数调用失败: {response['message']}")
        return None


def process_data(data):
    """处理返回的数据"""
    if not data:
        return
    
    print("\n" + "=" * 50)
    print("📊 数据处理示例")
    
    try:
        # 如果数据是字典格式，尝试提取关键信息
        if isinstance(data, dict):
            print("🔍 数据结构分析:")
            
            # 显示所有顶级字段
            for key in data.keys():
                value = data[key]
                if isinstance(value, (list, dict)):
                    print(f"   {key}: {type(value).__name__} (长度: {len(value)})")
                else:
                    print(f"   {key}: {value}")
            
            # 尝试提取用户信息
            if 'user' in data or 'userInfo' in data:
                user_info = data.get('user') or data.get('userInfo')
                print(f"\n👤 用户信息:")
                for k, v in user_info.items():
                    print(f"   {k}: {v}")
            
            # 尝试提取资产信息
            asset_fields = ['weapons', 'agents', 'skins', 'currency', 'gun_list', 'agent_list']
            for field in asset_fields:
                if field in data:
                    asset_data = data[field]
                    if isinstance(asset_data, list):
                        print(f"\n🎯 {field}: {len(asset_data)} 项")
                        if asset_data:
                            print(f"   示例: {asset_data[0] if len(str(asset_data[0])) < 100 else str(asset_data[0])[:100] + '...'}")
                    else:
                        print(f"\n💰 {field}: {asset_data}")
        
        else:
            print(f"数据类型: {type(data)}")
            print(f"数据内容: {str(data)[:200]}...")
    
    except Exception as e:
        print(f"❌ 数据处理出错: {e}")


def main():
    """主函数"""
    print("🎮 Valorant 真实 API 调用示例")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 60)
    
    try:
        # 使用客户端类调用
        data1 = real_api_call()
        
        # 使用便捷函数调用
        data2 = convenient_function_call()
        
        # 处理数据（使用第一个成功的结果）
        data = data1 or data2
        if data:
            process_data(data)
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("✅ 示例执行完成")


if __name__ == "__main__":
    main()
