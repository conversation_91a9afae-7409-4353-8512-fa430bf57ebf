import requests

url = "https://app.mval.qq.com/go/agame/asset/gun_skin"

headers = {
    "Host": "app.mval.qq.com",
    "Cookie": "clientType=9; uin=o1560254921; appid=*********; acctype=qc; openid=82C452629A65F50CA86CA3DA11D4AB16; access_token=2430D391A2467E1B380BB9A1783E4769; userId=JA-b10c3854beeb41fa-b3ae284ccb9de519; accountType=5; tid=2AE52CB4812060A464734624B3E4DBA3BA9B23860F96FD5FC6B0D9A96551893FA6128603E8C61E9EC82A8020AF82BC41C1ABD99C6288D564D739B26CFD1C064ADE4F17676521A4179990187269BA35B68F1EB72D50A2CDE9F04058782C6E2B51C9F72BC0E8BB715E4BED086EAED263D172901F356D45AE53A4142170491256E0D218ECB3F63F307A33AD50563DAA9E94135916E9C12CEFC4727F8E8DFE28FE4E6D237B25EFC3B4DAE536193F9F75AC20;",
    "content-type": "application/json",
    "user-agent": "mval/1.10.0.10036 Channel/default Mozilla/5.0 (Linux; Android 12; NTH-AN00 Build/V417IR; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/101.0.4951.61 Mobile Safari/537.36"
}

data = {
    # "scene": "v3_sJUHrAIELSAJyy_xkojaUQM1razOGDlnQnnJB-eEvk3DLCj1CS3vOA1CITMENNgTU7S6fEImJdIKTCDZXd1KQbgZzx2W5jLLkWuiq3eak4Ag1esMH7auuXFnc-79pA3QjZZmv_Acr4WHwgRWaKqQb681-rApoe4yyVSmO5eteR6n0hjTAND4aeSWbeFyNdoTjpwzpyAVjMC-bSVX7m5P1Q==",
    "scene": "v3_sJUHrAIELSAJyy_xkojaUX35RCsbaJALnPb8_XiRkzfAnm4Gl9l3K6N5YVaqd49V9M38w4yglc8MngRUVT8QWwdFIsCK0cf6-m9RC_FWLu2WayTOrZl7g_1yy-_NsmEFtqKXO8z56wSraYRVdvnrcGwbDgGzzpxOl9c11F7MPcq1UfGFiS8zbRyUQDUKYIehz61gndc7YDTrPeSRHVNgSQ==",
    "show_unlock": 1
}

try:
    response = requests.post(url, headers=headers, json=data)
    response.raise_for_status()  # 检查HTTP错误

    # 打印响应信息
    print(f"状态码: {response.status_code}")
    print("响应头:")
    for header, value in response.headers.items():
        print(f"{header}: {value}")

    print("\n响应内容:")
    try:
        print(response.json())  # 尝试解析为JSON
    except ValueError:
        print(response.text)  # 如果不是JSON则打印原始文本

except requests.exceptions.RequestException as e:
    print(f"请求失败: {e}")
except Exception as e:
    print(f"发生意外错误: {e}")
