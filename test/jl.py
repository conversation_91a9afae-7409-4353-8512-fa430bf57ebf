import tkinter as tk
from tkinter import messagebox
import pyautogui
import keyboard
import threading
import time

# === 设定 ===
color_check_interval = 0.05  # 检测间隔秒
color_tolerance = 15         # 色差容忍范围

running = False

# 比较颜色（带容差）
def is_color_match(c1, c2, tol=10):
    return all(abs(a - b) <= tol for a, b in zip(c1, c2))

# 取鼠标位置和颜色
def get_mouse_info():
    x, y = pyautogui.position()
    rgb = pyautogui.screenshot().getpixel((x, y))
    return x, y, rgb

# 自动连招函数
def auto_combo(x, y, target_rgb):
    global running
    while running:
        current_rgb = pyautogui.screenshot().getpixel((x, y))
        if is_color_match(current_rgb, target_rgb, color_tolerance):
            print("[√] Color matched! Executing combo...")
            keyboard.press_and_release('1')
            time.sleep(0.05)
            keyboard.press_and_release('2')
            time.sleep(0.05)
            pyautogui.click()
            time.sleep(0.1)
        time.sleep(color_check_interval)

# 启动宏线程
def start_macro():
    global running
    try:
        x = int(entry_x.get())
        y = int(entry_y.get())
        rgb_str = entry_color.get()
        target_rgb = tuple(int(rgb_str[i:i+2], 16) for i in (0, 2, 4))
    except Exception as e:
        messagebox.showerror("错误", f"参数错误：{e}")
        return
    if not running:
        running = True
        threading.Thread(target=auto_combo, args=(x, y, target_rgb), daemon=True).start()
        status_label.config(text="状态：运行中", fg="green")

# 停止宏
def stop_macro():
    global running
    running = False
    status_label.config(text="状态：已停止", fg="red")

# 实时更新取色信息
def update_color():
    if not running:
        x, y, rgb = get_mouse_info()
        color_hex = '{:02X}{:02X}{:02X}'.format(*rgb)
        label_mouse.config(text=f"坐标: ({x}, {y})")
        label_color.config(text=f"颜色: RGB{rgb} / HEX#{color_hex}")
    root.after(100, update_color)

# 将当前鼠标信息写入输入框
def set_current_position():
    x, y, rgb = get_mouse_info()
    entry_x.delete(0, tk.END)
    entry_y.delete(0, tk.END)
    color_hex = '{:02X}{:02X}{:02X}'.format(*rgb)
    entry_color.delete(0, tk.END)
    entry_color.insert(0, color_hex)

# GUI 初始化
root = tk.Tk()
root.title("剑灵 · 取色宏工具（连招版）")
root.geometry("350x300")
root.resizable(False, False)

# UI 布局
label_mouse = tk.Label(root, text="坐标: ", font=("Arial", 12))
label_mouse.pack(pady=5)

label_color = tk.Label(root, text="颜色: ", font=("Arial", 12))
label_color.pack(pady=5)

tk.Button(root, text="使用当前鼠标点", command=set_current_position).pack(pady=5)

frame = tk.Frame(root)
frame.pack(pady=10)

tk.Label(frame, text="X:").grid(row=0, column=0)
entry_x = tk.Entry(frame, width=6)
entry_x.grid(row=0, column=1)

tk.Label(frame, text="Y:").grid(row=0, column=2)
entry_y = tk.Entry(frame, width=6)
entry_y.grid(row=0, column=3)

tk.Label(frame, text="目标颜色 (HEX):").grid(row=1, column=0, columnspan=2)
entry_color = tk.Entry(frame, width=10)
entry_color.grid(row=1, column=2, columnspan=2)

status_label = tk.Label(root, text="状态：已停止", font=("Arial", 12), fg="red")
status_label.pack(pady=5)

tk.Button(root, text="▶ 开始连招宏", bg="lightgreen", command=start_macro).pack(pady=5)
tk.Button(root, text="■ 停止宏", bg="tomato", command=stop_macro).pack(pady=5)

tk.Label(root, text="按 ESC 关闭程序", fg="gray").pack(pady=10)

# 绑定退出键
keyboard.add_hotkey('esc', lambda: root.destroy())

# 启动色值更新线程
update_color()
root.mainloop()
