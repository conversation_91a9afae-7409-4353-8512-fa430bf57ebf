
import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui
import keyboard
import threading
import time
import json
import os

# 读取技能预设
preset_path = "macro_presets.json"
if os.path.exists(preset_path):
    with open(preset_path, "r", encoding="utf-8") as f:
        preset_macros = json.load(f)
else:
    preset_macros = {}

# 状态控制
running = False
selected_macro = None
macro_thread = None
color_targets = []  # [(x, y, hex_string)]

# 颜色比较函数
def is_color_match(c1, c2, tol=15):
    return all(abs(a - b) <= tol for a, b in zip(c1, c2))

def hex_to_rgb(hex_str):
    return tuple(int(hex_str[i:i+2], 16) for i in (0, 2, 4))

# 宏执行线程
def macro_loop():
    global running
    while running and selected_macro:
        matched = False
        screenshot = pyautogui.screenshot()
        for x, y, hex_color in color_targets:
            target_rgb = hex_to_rgb(hex_color)
            screen_rgb = screenshot.getpixel((x, y))
            if is_color_match(screen_rgb, target_rgb):
                matched = True
                break
        if matched:
            for key in selected_macro["sequence"]:
                if not running: break
                if key == "LMB":
                    pyautogui.click()
                elif key == "RMB":
                    pyautogui.click(button="right")
                else:
                    keyboard.press_and_release(key)
                time.sleep(selected_macro.get("interval", 100) / 1000)
        time.sleep(0.05)

# 启动宏
def start_macro():
    global running, selected_macro, macro_thread
    if not macro_combo.get():
        messagebox.showerror("错误", "请选择一个技能宏")
        return
    if not color_targets:
        messagebox.showerror("错误", "请至少设置一个取色点")
        return
    selected_macro = preset_macros[macro_combo.get()]
    running = True
    macro_thread = threading.Thread(target=macro_loop, daemon=True)
    macro_thread.start()
    status_var.set("运行中")

# 停止宏
def stop_macro():
    global running
    running = False
    status_var.set("已停止")

# 添加当前鼠标颜色坐标
def add_color_point():
    x, y = pyautogui.position()
    rgb = pyautogui.screenshot().getpixel((x, y))
    hex_str = '{:02X}{:02X}{:02X}'.format(*rgb)
    color_targets.append((x, y, hex_str))
    color_list.insert(tk.END, f"{x},{y} #{hex_str}")

# GUI
root = tk.Tk()
root.title("剑灵取色宏工具（复刻版）")
root.geometry("420x420")
root.attributes('-topmost', True)

# 宏选择
ttk.Label(root, text="选择技能预设:").pack(pady=5)
macro_combo = ttk.Combobox(root, values=list(preset_macros.keys()), state="readonly")
macro_combo.pack()

# 状态显示
status_var = tk.StringVar(value="已停止")
ttk.Label(root, textvariable=status_var, foreground="green").pack(pady=5)

# 操作按钮
frame = ttk.Frame(root)
frame.pack(pady=10)
ttk.Button(frame, text="▶ 启动宏", command=start_macro).grid(row=0, column=0, padx=5)
ttk.Button(frame, text="■ 停止宏", command=stop_macro).grid(row=0, column=1, padx=5)
ttk.Button(frame, text="➕ 添加取色点", command=add_color_point).grid(row=0, column=2, padx=5)

# 取色点列表
ttk.Label(root, text="当前取色点（坐标 + 颜色）:").pack()
color_list = tk.Listbox(root, height=6)
color_list.pack(fill="x", padx=10)

# 热键提示
ttk.Label(root, text="按 ESC 可关闭程序", foreground="gray").pack(pady=10)
keyboard.add_hotkey('esc', lambda: root.destroy())

root.mainloop()
