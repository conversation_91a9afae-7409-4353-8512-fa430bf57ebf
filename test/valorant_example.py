# -*- encoding=utf8 -*-
"""
Valorant 客户端调用示例
简单演示如何使用 ValorantClient 获取用户资产数据

使用步骤：
1. 从掌上无畏契约应用中获取 Cookie 和 Scene 参数
2. 创建客户端实例
3. 设置认证信息
4. 调用 API 获取数据
5. 处理返回结果
"""

import json
import time
from valorant_client import ValorantClient, get_user_assets


def basic_example():
    """基础使用示例"""
    print("=" * 50)
    print("🎮 Valorant 客户端基础使用示例")
    print("=" * 50)
    
    # 1. 创建客户端
    client = ValorantClient()
    print("✅ 客户端创建成功")
    
    # 2. 设置认证信息（需要替换为真实的 Cookie）
    cookie = "clientType=android; uin=你的uin; appid=*********; acctype=qc; openid=你的openid; access_token=你的token; userId=你的userId; accountType=1; tid=你的tid"
    client.set_auth(cookie)
    print("✅ 认证信息设置完成")
    
    # 3. 设置 Scene 参数（需要替换为真实的 Scene）
    scene = "你的Scene参数"
    
    # 4. 获取用户资产数据
    print("🚀 开始获取用户资产数据...")
    response = client.get_assets(scene)
    
    # 5. 处理返回结果
    if response['success']:
        print("✅ 数据获取成功！")
        print(f"   状态码: {response['status_code']}")
        print(f"   消息: {response['message']}")
        
        # 显示部分数据结构
        data = response['data']
        if data:
            print("📊 返回数据概览:")
            print(f"   数据类型: {type(data)}")
            if isinstance(data, dict):
                print(f"   数据字段: {list(data.keys())}")
        
        return data
    else:
        print("❌ 数据获取失败")
        print(f"   错误信息: {response['message']}")
        print(f"   状态码: {response['status_code']}")
        return None


def convenient_function_example():
    """便捷函数使用示例"""
    print("\n" + "=" * 50)
    print("🚀 便捷函数使用示例")
    print("=" * 50)
    
    # 使用便捷函数一键获取数据
    cookie = "你的完整Cookie字符串"
    scene = "你的Scene参数"
    
    print("🔄 使用便捷函数获取数据...")
    response = get_user_assets(cookie, scene)
    
    if response['success']:
        print("✅ 便捷函数调用成功！")
        return response['data']
    else:
        print("❌ 便捷函数调用失败")
        print(f"   错误: {response['message']}")
        return None


def error_handling_example():
    """错误处理示例"""
    print("\n" + "=" * 50)
    print("⚠️  错误处理示例")
    print("=" * 50)
    
    client = ValorantClient()
    
    # 测试无效的认证信息
    print("🧪 测试无效认证信息...")
    client.set_auth("invalid_cookie")
    response = client.get_assets("invalid_scene")
    
    print(f"   返回状态: {'成功' if response['success'] else '失败'}")
    print(f"   错误信息: {response['message']}")
    print(f"   状态码: {response['status_code']}")


def data_processing_example():
    """数据处理示例"""
    print("\n" + "=" * 50)
    print("📊 数据处理示例")
    print("=" * 50)
    
    # 模拟成功的响应数据
    mock_response = {
        'success': True,
        'data': {
            'user_info': {
                'username': 'TestUser',
                'level': 50,
                'rank': 'Gold'
            },
            'currency': {
                'vp': 1000,
                'rp': 500
            },
            'weapons': [
                {'name': 'Vandal', 'skin': 'Prime'},
                {'name': 'Phantom', 'skin': 'Ion'}
            ]
        },
        'message': '获取成功',
        'status_code': 200
    }
    
    print("🔍 处理模拟数据...")
    data = mock_response['data']
    
    # 提取用户信息
    user_info = data.get('user_info', {})
    print(f"👤 用户信息:")
    print(f"   用户名: {user_info.get('username', '未知')}")
    print(f"   等级: {user_info.get('level', 0)}")
    print(f"   段位: {user_info.get('rank', '未知')}")
    
    # 提取货币信息
    currency = data.get('currency', {})
    print(f"💰 货币信息:")
    print(f"   VP: {currency.get('vp', 0)}")
    print(f"   RP: {currency.get('rp', 0)}")
    
    # 提取武器信息
    weapons = data.get('weapons', [])
    print(f"🔫 武器信息 (共{len(weapons)}把):")
    for weapon in weapons:
        print(f"   - {weapon.get('name', '未知')} ({weapon.get('skin', '默认皮肤')})")


def save_data_example(data):
    """保存数据示例"""
    if not data:
        print("\n⚠️  没有数据可保存")
        return
    
    print("\n" + "=" * 50)
    print("💾 数据保存示例")
    print("=" * 50)
    
    # 生成文件名
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"valorant_data_{timestamp}.json"
    
    try:
        # 保存为 JSON 文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 数据已保存到: {filename}")
        print(f"   文件大小: {len(json.dumps(data, ensure_ascii=False))} 字符")
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")


def main():
    """主函数 - 运行所有示例"""
    print("🎮 Valorant 客户端调用示例")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 60)
    
    # 重要提醒
    print("⚠️  重要提醒:")
    print("1. 需要从真实的掌上无畏契约应用中获取 Cookie 和 Scene 参数")
    print("2. Cookie 包含敏感认证信息，请妥善保管")
    print("3. Scene 参数具有时效性，过期需要重新获取")
    print("4. 请合理控制请求频率，避免被限制")
    print()
    
    # 运行示例
    try:
        # 1. 基础使用示例
        data = basic_example()
        
        # 2. 便捷函数示例
        # convenient_function_example()
        #
        # # 3. 错误处理示例
        # error_handling_example()
        #
        # # 4. 数据处理示例
        # data_processing_example()
        #
        # # 5. 数据保存示例
        # save_data_example(data)
        
    except Exception as e:
        print(f"\n❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("✅ 示例运行完成")
    print("💡 提示: 修改认证信息后可进行真实测试")


if __name__ == "__main__":
    main()
