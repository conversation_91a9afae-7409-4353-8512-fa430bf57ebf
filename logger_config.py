import logging
import os
import sys


class TkinterHandler(logging.Handler):
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget

    def emit(self, record):
        log_entry = self.format(record)
        self.text_widget.insert('end', f'{log_entry}\n')
        self.text_widget.see('end')  # Scroll to the end
        self.text_widget.update()  # Update the widget


def setup_logger(module_name, log_file_name='app.log', text_widget=None):
    logger = logging.getLogger(module_name)

    # 避免重复添加处理器
    if not logger.hasHandlers():
        logger.setLevel(logging.INFO)

        # 获取当前 exe 文件所在的目录
        if getattr(sys, 'frozen', False):  # 如果是打包后的exe
            exe_dir = os.path.dirname(sys.executable)
        else:  # 普通脚本运行
            exe_dir = os.path.dirname(os.path.abspath(__file__))

        # 日志文件路径为 exe 所在目录下
        log_file_path = os.path.join(exe_dir, log_file_name)

        file_handler = logging.FileHandler(log_file_name)
        file_handler.setLevel(logging.INFO)

        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        formatter = logging.Formatter('%(asctime)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        if text_widget:
            tk_handler = TkinterHandler(text_widget)
            tk_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', '%Y-%m-%d %H:%M:%S')
            tk_handler.setFormatter(tk_formatter)
            logger.addHandler(tk_handler)

    return logger


if __name__ == '__main__':
    logger = setup_logger('luhao', 'luhao.log')
    logger.error('test')
    logger.info('info test')
    logger.warning('warning test')
