import hashlib
import hmac
import os
# 添加项目根目录到 sys.path
import sys
import time

import requests

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

from common import logger_config
from common import notify_util
from common import settings

logger = logger_config.setup_logger(os.path.basename(__file__))

app_key = settings.app_key
app_secret = settings.app_secret


def get_signature(message, secret):
    # algorithm = 'HmacSHA256'
    try:
        hmac_sha256 = hmac.new(secret.encode('utf-8'), message.encode('utf-8'), hashlib.sha256)
        return hmac_sha256.hexdigest()
    except Exception as e:
        raise Exception(f"签名计算错误: {e}")


def post(url, data=''):
    timestamp = str(int(time.time()) * 1000)
    message = timestamp + data
    signature = get_signature(message, app_secret)

    headers = {
        'X-KK-APIKEY': app_key,
        'X-KK-TIME': timestamp,
        'X-KK-SIGNATURE': signature,
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36"
    }

    r = requests.post(url, data=data, headers=headers)
    if r.status_code != 200:
        logger.error('请求接口失败，url: %s，body: %s，resp: %s', url, data, r.text)
        # notify_util.send_notify('请求接口失败，url: {}，body: {}，resp: {}'.format(url, data, r.text))
        raise Exception('请求接口失败')
    return r
