# -*- encoding=utf8 -*-

from airtest.core.api import *
from poco.drivers.android.uiautomation import AndroidUiautomationPoco

from luhao_models import EventStatus


class WzryHelper:
    def __init__(self, device_ip, app_package):
        self.device_ip = device_ip
        self.app_package = app_package
        self.poco = None

    def connect_dev(self):
        dev = connect_device(f"Android://127.0.0.1:5037/{self.device_ip}?cap_method=JAVACAP")
        self.poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
        return dev

    def init_app(self):
        stop_app(self.app_package)
        sleep(1)
        start_app(self.app_package)
        sleep(5)
        wait(Template(r"tpl1730882892385.png", record_pos=(-0.385, 0.857), resolution=(720, 1280)),
             timeout=100, interval=1)

    def search_user(self, uid):
        self.poco("com.tencent.gamehelper.smoba:id/title_search").wait(10).click()
        sleep(1)
        self.poco("android.widget.EditText").wait(15).click()
        text(uid)
        self.poco("com.tencent.gamehelper.smoba:id/searchEdit").wait(10).click()
        self.poco("android.widget.EditText").wait(10).click()
        self.poco("搜索").wait(10).click()
        sleep(1)
        # 等待搜索结果列表出现，最多等待10秒
        if not self.poco("com.tencent.gamehelper.smoba:id/user_list").wait(10).exists():
            print('营地ID不存在')
            return {
                'status': EventStatus.FAILURE,
                'msg': '营地ID不存在'
            }

        # 如果存在，点击进入用户信息
        self.poco("com.tencent.gamehelper.smoba:id/user_list").click()

        items = self.poco("com.tencent.gamehelper.smoba:id/mine_battle_record_list").child("android.widget.LinearLayout")

        # 战斗力、MVP、总场次、英雄、胜率、皮肤
        values = []
        for item in items:
            values.append(item.child()[0].get_text())

        print('战绩：', values)

        if len(values) < 6:
            print('未找到战绩信息')
            return {
                'status':EventStatus.FAILURE,
                'msg':'未找到战绩信息'
            }
        if '*' in values[5]:
            print('王者营地隐藏了皮肤信息，无法自动录号')
            return {
                'status': EventStatus.FAILURE,
                'msg': '王者营地隐藏了皮肤信息，无法录号，请取消隐藏后重新上号。'
            }

        self.poco(text="更多").wait(5).click()

        sleep(0.2)
        wait(Template(r"tpl1730881600441.png", record_pos=(-0.031, -0.326), resolution=(720, 1280)))
        touch(Template(r"tpl1730881600441.png", record_pos=(-0.031, -0.326), resolution=(720, 1280)))
        sleep(1)
        wait(Template(r"tpl1731085583893.png", record_pos=(-0.006, -0.779), resolution=(720, 1280)))
        return EventStatus.SUCCESS

    def stop_app(self):
        stop_app(self.app_package)


if __name__ == '__main__':
    device_ip = "127.0.0.1:16384"
    app_package = "com.tencent.gamehelper.smoba"

    game_helper = WzryHelper(device_ip, app_package)

    try:
        game_helper.connect_dev()
        game_helper.init_app()

        uid = "1685394019"
        # uid = "1681528269"

        game_helper.search_user(uid)

    except Exception as e:
        print(f"[ERROR] {e}")

    finally:
        game_helper.stop_app()
