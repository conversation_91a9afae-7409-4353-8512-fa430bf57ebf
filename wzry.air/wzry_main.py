import multiprocessing
import os
import queue
import sys
import threading
import time
import traceback
from contextlib import contextmanager

from utils.crypto_util import AESCryptoUtil

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import configs
import sys_tool
from luhao_models import TaskEvent, EventStatus
from server_client import PortalClient
from wzry import W<PERSON>ryHelper
from wzry_image_util import create_wzry_cover
from wzry_proxy import run_server

device_id = os.environ.get('DEVICE_ID', '39')

# 排序优先级
priority_order = {
    "贵族限定": 1,
    "荣耀典藏": 2,
    "传说品质": 3,
    "史诗品质": 4,
    "限定": 5,
    "勇者品质": 6
}


def get_skin_priority(skin):
    class_types = skin.get("classTypeName", [])
    # 默认优先级是无类别的最低优先级
    return min([priority_order.get(class_type, float('inf')) for class_type in class_types], default=float('inf'))

aes_util = AESCryptoUtil("aEUAzI8eCEgdo02pfkLY+w==")

# Worker类，用于执行录号任务，并通过队列接收指令
class Worker(multiprocessing.Process):
    def __init__(self, task, result_queue, capture_result_queue, params):
        super().__init__()
        self.result_queue = result_queue  # 用于返回结果。 任务进度通过result_queue返回master，master进入后续的处理
        # 清空result_queue
        while not self.result_queue.empty():
            self.result_queue.get()
        self.capture_result_queue = capture_result_queue  # 用于监听抓包结果的队列
        self.current_task = task  # 存储当前任务的信息
        self.params = params

        self.portal_client = PortalClient()
        self.daemon = True  # 守护进程

    def run(self):
        # self.log_queue.put("【worker】任务开始")
        try:
            result = self.execute_long_task()
            if result == EventStatus.SUCCESS:
                # 等待抓包结果
                capture_result = self.capture_result_queue.get(timeout=30)  # 阻塞，直到获取到抓包结果
                print(f"[INFO] 收到抓包结果: {capture_result}")

                if result and capture_result.status == EventStatus.SUCCESS:
                    obj = capture_result.data
                    role_job_name = obj.get('roleJobName')
                    if role_job_name:
                        role_job_name = role_job_name[:4]
                    glory_collection = 0
                    skin_names = []
                    skin_ids = []

                    sorted_skin_list = sorted(obj['skinList'], key=get_skin_priority)

                    for skin in sorted_skin_list:
                        skin_names.append(skin['skinName'])
                        skin_ids.append(skin['skinId'])
                        if '荣耀典藏' in skin['classTypeName']:
                            glory_collection = glory_collection + 1

                    product_meta = self.portal_client.get_product_category_meta(82)
                    account_type = '*'
                    noble_level = '*'
                    real_name_type = '*'

                    for item in product_meta:
                        if item['name'] == '营地ID':
                            item['values'] = [obj.get('encrypted_user_id')]
                        if item['name'] == '段位':
                            if role_job_name in item['inputList']:
                                item['values'] = [role_job_name]
                        elif item['type'] == 2:
                            input_list = item['inputList'].split(',')
                            for skin in skin_names:
                                if skin in input_list:
                                    if item.get('values') is None:
                                        item['values'] = []
                                    item['values'].append(skin)

                    skin_images = [f'skin/{str(skin_id)}.jpeg' for skin_id in skin_ids]

                    product = self.current_task['product_info']
                    attr_list = product.get('productAttributeValueList')
                    for attr in attr_list:
                        if attr.get('productAttributeName') == '账号类型':
                            account_type = attr.get('value')
                        elif attr.get('productAttributeName') == '贵族等级':
                            noble_level = attr.get('value')
                            if len(noble_level) >= 3:
                                noble_level = noble_level[2:]
                        elif attr.get('productAttributeName') == '实名类型':
                            real_name_type = attr.get('value')


                    text_info = {
                        'real_name_type': real_name_type,
                        'noble_level': noble_level,
                        'hero_count': len(obj.get('heroList')),
                        'skin_count': len(obj.get('skinList')),
                        'glory_collection': glory_collection
                    }

                    text_info2 = {
                        'product_sn': self.current_task['productSn'],
                        'account_type': account_type
                    }

                    cover = configs.image_server_url + create_wzry_cover(skin_images[:14], text_info, text_info2)
                    # skin_images = []
                    # for skin in sorted_skin_list:
                    #     skin_images.append(f'skin/{str(skin["skinId"])}.jpg')

                    # todo 自动审核
                    self.portal_client.sync_product_info(self.current_task['productSn'], cover,
                                                         [], product_meta)

                    self.result_queue.put(
                        TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FINISHED))
                else:
                    self.result_queue.put(
                        TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FAILURE))
            else:
                # todo 同步失败原因到审核说明
                self.result_queue.put(
                    TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FAILURE, msg=result['msg']))
        except Exception as e:
            traceback.print_exc()
            # 将异常信息和堆栈信息放入 log_queue
            error_message = f"【worker】任务出错: {e}\n{traceback.format_exc()}"
            # self.log_queue.put(error_message)
            self.result_queue.put(
                TaskEvent(self.current_task['id'], stage=0, status=EventStatus.FAILURE))
        # self.log_queue.put("【worker】任务完成")
        self.current_task = None  # 任务完成后清除任务信息

    # 耗时任务的具体实现
    def execute_long_task(self):
        product_category_id = self.current_task.get('productCategoryId')
        metadata = self.portal_client.get_product_category_meta(product_category_id)
        self.current_task['product_meta'] = metadata
        product = self.portal_client.get_product_detail(self.current_task['productId'])
        self.current_task['product_info'] = product

        attr_list = product.get('productAttributeValueList')

        if (product_category_id == 82):
            for attr in attr_list:
                if attr.get('productAttributeName') == '营地ID':
                    self.current_task['uid'] = attr.get('value')
                    if not self.current_task['uid'].isdigit():
                        self.current_task['uid'] = aes_util.decrypt(self.current_task['uid'])
            app_package = "com.tencent.gamehelper.smoba"
        elif (product_category_id == 98):
            for attr in attr_list:
                if attr.get('productAttributeName') == '掌瓦ID':
                    self.current_task['uid'] = attr.get('value')
                    if not self.current_task['uid'].isdigit():
                        self.current_task['uid'] = aes_util.decrypt(self.current_task['uid'])
            app_package = "com.tencent.apps.valorant"


        wzry_helper = WzryHelper("127.0.0.1:16384", app_package)
        wzry_helper.connect_dev()
        wzry_helper.init_app()

        result = None
        uid = self.current_task['uid']
        try:
            result = wzry_helper.search_user(uid)
        except Exception as e:
            traceback.print_exc()
            # if app crashed, restart worker
            os.execv(sys.executable, ['python'] + sys.argv)
            # result = EventStatus.FAILURE
        wzry_helper.stop_app()
        return result

    def execute_task_stage(self, stage_function, stage, param=0):
        with self.time_logger(stage):  # 开始计时

            try:
                self.result_queue.put(TaskEvent(task_id=self.current_task['id'],
                                                stage=stage,
                                                status=EventStatus.IN_PROGRESS,
                                                snapshot=None,
                                                data=None,
                                                msg=None))

                stage_event = stage_function(param)
                self.result_queue.put(stage_event)
            except Exception as e:
                # self.log_queue.put(f"【worker】任务出错: {e}")
                self.handle_failure(self.current_task, stage)
                traceback.print_exc()
                return False  # 表示失败
        return True  # 表示成功

    def handle_failure(self, task, stage):
        step_login_event = TaskEvent(task['id'], stage=stage, status=EventStatus.FAILURE)
        self.result_queue.put(step_login_event)

    @contextmanager
    def time_logger(self, stage):
        start_time = time.time()  # 记录开始时间
        # self.log_queue.put(f"开始执行阶段: {stage}")
        try:
            yield  # 执行阶段操作
        finally:
            end_time = time.time()  # 记录结束时间
            duration = end_time - start_time  # 计算耗时
            # self.log_queue.put(f"阶段 {stage} 耗时: {duration:.2f} 秒")


# Master类，发送任务指令，管理Worker，并处理心跳
class Master:
    def __init__(self, status_queue, capture_result_queue, device_addr=None):
        self.task_queue = multiprocessing.Queue()
        self.result_queue = multiprocessing.Queue()
        self.capture_result_queue = capture_result_queue

        self.worker = None
        self.running = True
        self.check_flag = True
        self.portal_client = PortalClient()
        self.last_instruction = None
        self.test_queue = queue.Queue()  # 测试队列

        # self.gui_queue = queue.Queue()
        self.status_queue = status_queue
        self.device_id = device_id

    # 启动Worker
    def start_worker(self, task):
        if self.worker is None or not self.worker.is_alive():
            print("【master】启动新任务...")
            # capture_result_queue = multiprocessing.Queue()
            while not self.capture_result_queue.empty():
                self.capture_result_queue.get()
            self.worker = Worker(task, self.result_queue, self.capture_result_queue, None)
            self.worker.start()

    def stop_worker_now(self, is_canceled=False):
        if self.worker is not None:
            print("【master】停止worker...")
            try:
                task_id = self.worker.current_task['id']
                self.worker.terminate()  # 立即停止
                time.sleep(2)
                self.worker.kill()
                self.worker = None
                print("【master】停止worker成功")
                self.status_queue.put({'status': '任务已停止'})
                if is_canceled:
                    task = {
                        'id': task_id,
                        'status': 'CANCELLED',
                        'msg': '取消任务'
                    }
                    self.portal_client.sync_task_info(task)
                self.check_flag = True
            except Exception as e:
                error_message = f"【worker】停止任务出错: {e}\n{traceback.format_exc()}"
                print(error_message)

    def heartbeat(self):
        while self.running:
            print("heartbeat... ")
            time.sleep(10)
            if self.worker is not None:
                self.send_heartbeat(device_status='IN_PROGRESS')
            else:
                self.send_heartbeat(device_status='IDLE')
                self.check_new_task()

    def check_worker_result(self):
        # 检查任务状态, 如果worker在执行中，才检查任务状态
        while self.running:
            try:
                if self.worker is None:
                    time.sleep(5)
                    continue
                result = self.result_queue.get_nowait()
                current_task = self.worker.current_task
                if result is None:
                    continue
                if result.status == EventStatus.FINISHED:
                    print('【master】同步录号任务信息...FINISHED')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'COMPLETED',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    # 自动审核
                    time.sleep(1)
                    self.portal_client.product_auto_verify(current_task['productId'],1,None)

                    self.status_queue.put({'status': result.status, 'stage': result.stage})
                    self.stop_worker_now()
                if result.status == EventStatus.SUCCESS:
                    print('【master】同步录号任务信息...SUCCESS')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'IN_PROGRESS',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    product_sn = None
                    if current_task:
                        product_sn = current_task['productSn']
                    self.status_queue.put({'status': result.status, 'stage': result.stage,
                                           'product_sn': product_sn})
                if result.status == EventStatus.FAILURE:
                    print('【master】同步录号任务信息...FAILURE')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'FAILED',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    # 自动审核
                    time.sleep(1)
                    self.portal_client.product_auto_verify(current_task['productId'], 2, result.msg)
                    self.status_queue.put(
                        {'status': result.status, 'stage': result.stage, 'product_sn': current_task['productSn']})
                    self.stop_worker_now()
                if result.status == EventStatus.MANUAL_REQUIRED:
                    print('【master】同步录号任务信息...MANUAL_REQUIRED')
                    task = {
                        'id': result.task_id,
                        'stage': result.stage,
                        'status': 'MANUAL_REQUIRED',
                        'snapshot': result.snapshot,
                        'productMeta': sys_tool.safe_json_dumps(result.data),
                        'msg': result.msg
                    }
                    self.portal_client.sync_task_info(task)
                    self.status_queue.put(
                        {'status': result.status, 'stage': result.stage, 'product_sn': current_task['product.Sn']})
                if result.status == EventStatus.IN_PROGRESS:
                    product_sn = None
                    if current_task:
                        product_sn = current_task['productSn']
                    self.status_queue.put({'status': result.status, 'stage': result.stage,
                                           'product_sn': product_sn})
            except queue.Empty:
                time.sleep(5)

    def check_new_task(self):
        if self.running and self.check_flag is True:
            try:
                task = self.portal_client.get_todo_task_from_kk(device_id)

                if task is None:
                    return
                self.check_flag = False

                product_sn = task['productSn']
                print(f"【master】有新任务: {product_sn}")
                self.start_worker(task)
                self.status_queue.put(
                    {'status': EventStatus.IN_PROGRESS, 'stage': None, 'product_sn': product_sn})
            except queue.Empty:
                pass
            except Exception as e:
                print(f"获取任务错误: {e}")
            finally:
                print('等待新任务')

    def send_heartbeat(self, device_status='IDLE'):
        instruction = {
            'deviceId': self.device_id,
            'deviceStatus': device_status,
            'timestamp': int(round(time.time() * 1000))
        }
        return self.portal_client.send_heartbeat(instruction)

    # 关闭Master
    def shutdown(self):
        self.stop_worker_now()
        self.running = False
        self.send_heartbeat(device_status='OFFLINE')
        print("Master 关闭")


# 主程序
def main(status_queue=None, device_addr=None):
    status_queue = queue.Queue()
    capture_result_queue = multiprocessing.Queue()

    # 启动服务器的线程
    server_thread = threading.Thread(target=run_server, args=(capture_result_queue,), daemon=True)
    server_thread.start()

    master = Master(status_queue, capture_result_queue, device_addr)

    if master.device_id is None:
        print(f"【master】device_id is None, exit")
        return None

    # 启动心跳线程
    heartbeat_thread = threading.Thread(target=master.heartbeat, daemon=True)
    heartbeat_thread.start()

    # 检查worker执行结果
    check_worker_result_thread = threading.Thread(target=master.check_worker_result, daemon=True)
    check_worker_result_thread.start()

    heartbeat_thread.join()
    return master


if __name__ == "__main__":
    main()
