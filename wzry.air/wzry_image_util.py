import json
import os
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
from io import BytesIO

import requests
from PIL import Image, ImageDraw, ImageFont

from utils import oss_util


def add_rounded_corners_with_border(image, radius, border_size=2, border_color=(255, 255, 255)):
    img_width, img_height = image.size
    new_img = Image.new('RGBA', (img_width + 2 * border_size, img_height + 2 * border_size), (0, 0, 0, 0))

    # 创建圆角遮罩
    mask = Image.new('L', (img_width, img_height), 0)
    draw = ImageDraw.Draw(mask)
    draw.rounded_rectangle([0, 0, img_width, img_height], radius=radius, fill=255)

    # 贴上圆角图片
    new_img.paste(image, (border_size, border_size), mask=mask)

    # 创建边框图层
    border_img = Image.new('RGBA', (img_width + 2 * border_size, img_height + 2 * border_size), (0, 0, 0, 0))
    border_draw = ImageDraw.Draw(border_img)
    border_draw.rounded_rectangle([border_size, border_size, img_width + border_size, img_height + border_size],
                                  radius=radius, outline=border_color, width=border_size)

    # 将边框图层贴到图像上
    new_img.paste(border_img, (0, 0), border_img)

    return new_img


def add_text_to_image(image, text, position, font_path=None, font_size=40, color=(255, 255, 255)):
    draw = ImageDraw.Draw(image)

    if font_path:
        font = ImageFont.truetype(font_path, font_size)
    else:
        font = ImageFont.load_default()

    draw.text(position, text, font=font, fill=color)
    return image


def create_collage(image_paths, background_path, output_path, images_per_row=8, spacing=10, left_margin=20,
                   right_margin=20, row_spacing=10, radius=10, border_size=2, border_color=(255, 255, 255),
                   text_info=None, text_info2=None, font_path=None, font_size=40):
    # 打开背景图并获取其尺寸
    background = Image.open(background_path).convert("RGBA")
    bg_width, bg_height = background.size

    # 打开所有小图并获取其原始大小
    images = [Image.open(img_path).convert("RGBA") for img_path in image_paths]

    # 计算每行的图片数量
    total_images = len(images)
    num_rows = (total_images + images_per_row - 1) // images_per_row

    # 计算可用的宽度和每张小图的宽度
    total_spacing = (images_per_row - 1) * spacing  # 总的图片间距
    total_left_right_margin = left_margin + right_margin  # 左右边距总宽度
    total_width_available = bg_width - total_left_right_margin - total_spacing  # 可用于放置小图的宽度
    small_img_width = total_width_available // images_per_row  # 每个小图的宽度

    # 目标宽高比为9:14
    target_aspect_ratio = 9 / 14
    small_img_height = int(small_img_width / target_aspect_ratio)  # 小图的高度根据9:14的比例计算

    # 计算垂直方向的总高度（包含图片高度和间距）
    total_height_needed = num_rows * small_img_height + (num_rows - 1) * row_spacing

    # 确保总高度不超过背景高度
    if total_height_needed > bg_height:
        raise ValueError(f"Background height is too small. Needed: {total_height_needed}, but got: {bg_height}")

    # 计算拼图的Y轴起点，确保上下居中
    top_margin = (bg_height - total_height_needed) // 2

    # 如果有文本信息，先在背景图上绘制文本
    if text_info:
        text = f"实名：{text_info['real_name_type']} 贵族等级：{text_info['noble_level']} 英雄：{text_info['hero_count']} 皮肤：{text_info['skin_count']} 荣耀典藏：{text_info['glory_collection']}"
        background = add_text_to_image(background, text, (left_margin + 20, 40), font_path=font_path,
                                       font_size=font_size,
                                       color=(255, 255, 255))
    if text_info2:
        text = f"编号：{text_info2['product_sn']} | {text_info2['account_type']}                              kkzhw.com"
        background = add_text_to_image(background, text, (left_margin + 20, bg_height - 120), font_path=font_path,
                                       font_size=font_size,
                                       color=(255, 255, 255))

    # 调整每张小图的大小，并添加圆角和边框
    resized_images = []
    for img in images:
        # 计算图片的宽高比
        img_width, img_height = img.size
        img_aspect_ratio = img_width / img_height

        # 如果宽高比小于目标比例，说明太高了，需要裁剪高度
        if img_aspect_ratio < target_aspect_ratio:
            # 按宽度缩放，保持宽度为 small_img_width
            new_width = small_img_width
            new_height = int(new_width / img_aspect_ratio)
            resized_image = img.resize((new_width, new_height), Image.LANCZOS)

            # 裁剪上下多余部分，使高度符合9:14比例
            top = (new_height - small_img_height) // 2
            bottom = top + small_img_height
            resized_image = resized_image.crop((0, top, new_width, bottom))

        # 如果宽高比大于等于目标比例，说明太宽了，需要裁剪宽度
        else:
            # 按高度缩放，保持高度为 small_img_height
            new_height = small_img_height
            new_width = int(new_height * img_aspect_ratio)
            resized_image = img.resize((new_width, new_height), Image.LANCZOS)

            # 裁剪左右多余部分，使宽度符合9:14比例
            left = (new_width - small_img_width) // 2
            right = left + small_img_width
            resized_image = resized_image.crop((left, 0, right, new_height))

        # 添加圆角和边框
        rounded_img = add_rounded_corners_with_border(resized_image, radius=radius, border_size=border_size,
                                                      border_color=border_color)
        resized_images.append(rounded_img)

    # 拼接图像到背景上（确保有指定的上下左右边距）
    # y_offset = top_margin + 20  # 留出文字空间
    y_offset = top_margin
    for index, img in enumerate(resized_images):
        x = left_margin + (index % images_per_row) * (small_img_width + spacing)
        y = y_offset + (index // images_per_row) * (small_img_height + row_spacing)
        background.paste(img, (x, y), img)

    # 保存结果
    background = background.convert("RGB")  # 去除透明背景（如果需要）
    background.save(output_path)
    print(f'Collage saved at {output_path}')


@lru_cache(maxsize=1)
def get_skin_dict(file_path='all_hero_and_skin.json'):
    with open(file_path, 'r', encoding='utf-8') as f:
        skin_json = json.load(f)
    skin_list = skin_json.get('allSkinConf')
    #
    skin_dict = {skin['skinId']: skin for skin in skin_list}
    return skin_dict

@lru_cache(maxsize=1)
def get_hero_dict(file_path='all_hero_and_skin.json'):
    with open(file_path, 'r', encoding='utf-8') as f:
        hero_json = json.load(f)
    hero_list = hero_json.get('allHeroConf')
    #
    hero_dict = {hero['heroId']: hero for hero in hero_list}
    return hero_dict


def download_skin_img():
    # 获取皮肤字典
    skin_dict = get_skin_dict()

    # 创建存放图片的文件夹
    folder_path = 'skin'
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

    def download_image(key, skin, folder_path):
        img_url = skin['imgUrl']
        label_url = skin['classLabel']
        img_name = f"{key}.jpeg"
        img_path = os.path.join(folder_path, img_name)

        try:
            # 下载图片
            img_response = requests.get(img_url, timeout=10)  # 设置超时
            img_response.raise_for_status()  # 检查请求是否成功

            label = None
            if label_url and img_url.startswith(('http://', 'https://')):  # 如果有 label 图片
                label_response = requests.get(label_url, timeout=10)
                label_response.raise_for_status()
                label = Image.open(BytesIO(label_response.content))

            # 打开主图（imgUrl）图片
            img = Image.open(BytesIO(img_response.content))

            # 将 img 图片转换为 RGBA，以便支持透明背景
            img_rgba = img.convert("RGBA")

            # 创建一个新的空白图像，宽度和原图相同，高度和原图一致
            new_img = Image.new('RGBA', img.size)

            new_img.paste(img_rgba, (0, 0), mask=img_rgba)

            # 如果有 label 图片，则贴在上面
            if label:
                # 确保 label 是 RGBA 格式
                label = label.convert("RGBA")

                # 将 label 贴在上方，确保它不会超出原图的高度
                new_img.paste(label, (img.width - label.width - 15 , 8), mask=label)


            final_img = new_img.convert("RGB")  # 转换为 RGB 模式以便保存为 JPEG
            final_img.save(img_path, quality=95, optimize=True)  # 高质量 JPEG

            print(f"成功下载并合成图片: {img_name}")

        except requests.exceptions.RequestException as e:
            print(f"下载图片失败: {img_url} 错误: {e}")
            traceback.print_exc()

    # 使用线程池并发下载
    with ThreadPoolExecutor(max_workers=10) as executor:  # 设置最大线程数

        # 为每个皮肤提交下载任务
        for key, skin in skin_dict.items():
            executor.submit(download_image, key, skin, folder_path)

def download_hero_img():
    # 获取皮肤字典
    hero_dict = get_hero_dict()

    # 创建存放图片的文件夹
    folder_path = 'hero'
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

    def download_image(key, hero, folder_path):
        img_url = hero['heroIcon']
        img_name = f"{key}.jpeg"
        img_path = os.path.join(folder_path, img_name)

        try:
            # 下载图片
            img_response = requests.get(img_url, timeout=10)  # 设置超时
            img_response.raise_for_status()  # 检查请求是否成功

            # 打开主图（imgUrl）图片
            img = Image.open(BytesIO(img_response.content))

            final_img = img.convert("RGB")  # 转换为 RGB 模式以便保存为 JPEG
            final_img.save(img_path, quality=95, optimize=True)  # 高质量 JPEG

            print(f"成功下载hero图片: {img_name}")

        except requests.exceptions.RequestException as e:
            print(f"下载图片失败: {img_url} 错误: {e}")
            traceback.print_exc()

    # 使用线程池并发下载
    with ThreadPoolExecutor(max_workers=10) as executor:  # 设置最大线程数

        # 为每个皮肤提交下载任务
        for key, skin in hero_dict.items():
            executor.submit(download_image, key, skin, folder_path)


def create_wzry_cover(image_paths, text_info, text_info2):
    background_path = 'wz_bg2.jpg'
    output_path = 'output_collage.jpg'

    # 提供贵族等级、英雄数、皮肤数、荣耀典藏等信息

    font_path = "SourceHanSerifCN-Bold-2.otf"
    font_size = 60

    create_collage(image_paths, background_path, output_path, images_per_row=7, spacing=20, left_margin=50,
                   right_margin=50, row_spacing=40, radius=15, border_size=5, border_color=(255, 255, 255),
                   text_info=text_info, text_info2=text_info2, font_path=font_path, font_size=font_size)
    oss_object_name = oss_util.upload_one_img_to_oss(output_path)
    return oss_object_name


if __name__ == '__main__':
    start_time = time.time()
    download_skin_img()
    # download_hero_img()
    end_time = time.time()
    print(f"download_skin_img cost {end_time - start_time:.2f} seconds")
