import json
import logging
import time
import traceback
from time import sleep

import requests

from common.configs import current_config as config
from common.crypto_util import AESCryptoUtil
from common.luhao_models import FuncResponse

aes_util = AESCryptoUtil("aEUAzI8eCEgdo02pfkLY+w==")

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 皮肤品质翻译
translation = {
    'exclusive': '限定',
    'premium': '高级',
    'deluxe': '豪华',
    'select': '精选',
    'ultra': '终极',
    'standard': '标准'
}

class WwqyClient(object):
    def __init__(self):
        self.CONFIG = {
            'token': '',
            'user_id': '',
            'headers': {
                'Host': 'game.qq.com',
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 QQ/8.8.50.634 V1_IPH_SQ_8.8.50_1_APP_A',
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Cookie': '',  # 需要从掌上无畏契约APP获取
                'Referer': 'https://game.qq.com/'
            }
        }

    def get_player_info(self, username, tag):
        """获取玩家基本信息"""
        try:
            headers = self.CONFIG['headers'].copy()
            response = requests.get(
                f'https://game.qq.com/mall/valorant/api/player/info',
                params={
                    'username': username,
                    'tag': tag
                },
                headers=headers,
                timeout=10
            )
            response.raise_for_status()
            result = response.json()

            if result.get('code') == 0:
                data = result.get('data', {})
                return FuncResponse.ok({
                    'username': username,
                    'tag': tag,
                    'current_rank': data.get('currentRank'),
                    'current_rank_points': data.get('currentRankPoints'),
                    'peak_rank': data.get('peakRank'),
                    'mmr_change': data.get('mmrChange')
                })
            else:
                logging.error(f"接口错误: {result.get('message')}")
                return FuncResponse.fail_need_retry("接口错误")

        except requests.exceptions.RequestException as e:
            logging.error(f"请求失败: {traceback.format_exc()}")
            raise e

    def get_player_loadout(self, username, tag):
        """获取玩家装备信息"""
        try:
            headers = self.CONFIG['headers'].copy()
            response = requests.get(
                f'https://game.qq.com/mall/valorant/api/player/loadout',
                params={
                    'username': username,
                    'tag': tag
                },
                headers=headers,
                timeout=10
            )
            response.raise_for_status()
            result = response.json()

            if result.get('code') == 0:
                data = result.get('data', {})
                return FuncResponse.ok({
                    'sprays': data.get('sprays', []),
                    'cards': data.get('cards', []),
                    'skins': data.get('skins', []),
                    'buddies': data.get('buddies', [])
                })
            else:
                logging.error(f"接口错误: {result.get('message')}")
                return FuncResponse.fail_need_retry("接口错误")

        except requests.exceptions.RequestException as e:
            logging.error(f"请求失败: {traceback.format_exc()}")
            raise e

    def get_player_inventory(self, username, tag):
        """获取玩家库存信息"""
        try:
            headers = self.CONFIG['headers'].copy()
            response = requests.get(
                f'https://game.qq.com/mall/valorant/api/player/inventory',
                params={
                    'username': username,
                    'tag': tag
                },
                headers=headers,
                timeout=10
            )
            response.raise_for_status()
            result = response.json()

            if result.get('code') == 0:
                data = result.get('data', {})
                return FuncResponse.ok({
                    'skins': data.get('skins', []),
                    'buddies': data.get('buddies', []),
                    'cards': data.get('cards', []),
                    'sprays': data.get('sprays', []),
                    'titles': data.get('titles', [])
                })
            else:
                logging.error(f"接口错误: {result.get('message')}")
                return FuncResponse.fail_need_retry("接口错误")

        except requests.exceptions.RequestException as e:
            logging.error(f"请求失败: {traceback.format_exc()}")
            raise e

    def get_player_stats(self, username, tag):
        """获取玩家统计数据"""
        try:
            headers = self.CONFIG['headers'].copy()
            response = requests.get(
                f'https://game.qq.com/mall/valorant/api/player/stats',
                params={
                    'username': username,
                    'tag': tag
                },
                headers=headers,
                timeout=10
            )
            response.raise_for_status()
            result = response.json()

            if result.get('code') == 0:
                data = result.get('data', {})
                return FuncResponse.ok({
                    'kills': data.get('kills', 0),
                    'deaths': data.get('deaths', 0),
                    'assists': data.get('assists', 0),
                    'kd_ratio': data.get('kdRatio', 0),
                    'headshots': data.get('headshots', 0),
                    'headshot_percentage': data.get('headshotPercentage', 0),
                    'wins': data.get('wins', 0),
                    'losses': data.get('losses', 0),
                    'win_percentage': data.get('winPercentage', 0)
                })
            else:
                logging.error(f"接口错误: {result.get('message')}")
                return FuncResponse.fail_need_retry("接口错误")

        except requests.exceptions.RequestException as e:
            logging.error(f"请求失败: {traceback.format_exc()}")
            raise e

    def save_to_json_file(self, data, file_path):
        """保存数据到JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logging.info(f"数据成功保存到文件: {file_path}")
        except Exception as error:
            logging.error(f"保存数据到文件失败: {error}") 