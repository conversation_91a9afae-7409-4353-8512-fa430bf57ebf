import datetime
import json
import multiprocessing
import os
import queue
import threading
import time
import traceback
from contextlib import contextmanager
from pathlib import Path

from common import common_utils
from common import oss_util
from common.blacklist_util import KejinBlacklistAPI
from common.configs import current_config
from common.crypto_util import AESCryptoUtil
from common.luhao_models import TaskEvent, EventStatus
from common.server_client import PortalClient
from wwqy import wwqy_image_util
from wwqy.wwqy_client import WwqyClient

# 环境变量，DEVICE_ID
device_id = os.environ.get('DEVICE_ID', '51')

# 获取当前脚本所在目录
script_dir = Path(__file__).resolve().parent

# 皮肤品质优先级
priority_order = {
    "限定": 1,
    "终极": 2,
    "高级": 3,
    "豪华": 4,
    "精选": 5,
    "标准": 6
}

def get_skin_priority(skin):
    """获取皮肤优先级"""
    quality = skin.get("quality", "标准")
    return priority_order.get(quality, float('inf'))

def save_to_json_file(data, file_path):
    """保存数据到JSON文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"[INFO] 数据成功保存到文件: {file_path}")
    except Exception as error:
        print(f"[ERROR] 保存数据到文件失败: {error}")

aes_util = AESCryptoUtil("aEUAzI8eCEgdo02pfkLY+w==")

class Worker(multiprocessing.Process):
    def __init__(self, task, result_queue, capture_result_queue, params):
        super().__init__()
        self.result_queue = result_queue
        while not self.result_queue.empty():
            self.result_queue.get()
        self.capture_result_queue = capture_result_queue
        self.current_task = task
        self.params = params
        self.portal_client = PortalClient()
        self.daemon = True
        self.wwqy_client = WwqyClient()

    def run(self):
        try:
            result = self.execute_long_task(device_id=device_id)
            if result['status'] == EventStatus.SUCCESS:
                obj = result.get('data')
                
                # 处理皮肤数据
                sorted_skin_list = sorted(obj['skins'], key=get_skin_priority)
                skin_names = []
                skin_ids = []
                exclusive_skins = 0

                for skin in sorted_skin_list:
                    skin_names.append(skin['name'])
                    skin_ids.append(skin['id'])
                    if skin['quality'] == '限定':
                        exclusive_skins += 1

                # 获取产品元数据
                product_meta = self.portal_client.get_product_category_meta(83)  # 假设83是无畏契约的分类ID

                # 填充元数据
                for item in product_meta:
                    if item['name'] == '游戏ID':
                        item['values'] = [f"{obj.get('username')}#{obj.get('tag')}"]
                    elif item['name'] == '段位':
                        item['values'] = [obj.get('current_rank', '未定级')]
                    elif item['type'] == 2:  # 多选属性
                        if item['name'] in ['限定皮肤', '终极皮肤', '高级皮肤', '豪华皮肤', '精选皮肤', '标准皮肤']:
                            item['values'] = [skin['name'] for skin in sorted_skin_list if skin['quality'] == item['name'].replace('皮肤', '')]
                    elif item['type'] == 1:  # 单选属性
                        if item['name'] == '限定皮肤数量':
                            item['values'] = [str(exclusive_skins)]
                        elif item['name'] == '皮肤总数':
                            item['values'] = [str(len(sorted_skin_list))]
                        elif item['name'] == 'K/D比':
                            item['values'] = [str(obj.get('kd_ratio', '0'))]
                        elif item['name'] == '胜率':
                            item['values'] = [str(obj.get('win_percentage', '0'))]

                # 保存数据
                now = datetime.datetime.now()
                filename = f"result-{now.strftime('%Y%m%d-%H%M%S')}.json"
                save_to_json_file(obj, filename)
                
                # 上传到OSS
                oss_file_name = oss_util.upload_file_to_oss(filename, 'json', 'mall/statics/wwqy/')
                data_url = current_config.image_server_url + oss_file_name
                
                # 添加元数据URL
                for item in product_meta:
                    if item['name'] == '账号元数据':
                        item['values'] = [data_url]

                # 更新产品信息
                product = self.current_task['product_info']
                self.portal_client.update_product_meta(product['id'], product_meta)

                self.result_queue.put({
                    'status': EventStatus.SUCCESS,
                    'data': {
                        'product_id': product['id'],
                        'meta': product_meta
                    }
                })
            else:
                self.result_queue.put(result)
        except Exception as e:
            print(f"执行任务失败: {str(e)}")
            traceback.print_exc()
            self.result_queue.put({
                'status': EventStatus.FAILED,
                'message': str(e)
            })

    def execute_long_task(self, device_id=None):
        """执行长时间运行的任务"""
        try:
            # 获取任务参数
            task_params = self.current_task.get('params', {})
            username = task_params.get('username')
            tag = task_params.get('tag')

            if not username or not tag:
                return {
                    'status': EventStatus.FAILED,
                    'message': '缺少必要参数'
                }

            # 获取玩家信息
            player_info = self.wwqy_client.get_player_info(username, tag)
            if not player_info.is_success():
                return {
                    'status': EventStatus.FAILED,
                    'message': player_info.message
                }

            # 获取玩家装备
            loadout_info = self.wwqy_client.get_player_loadout(username, tag)
            if not loadout_info.is_success():
                return {
                    'status': EventStatus.FAILED,
                    'message': loadout_info.message
                }

            # 获取玩家库存
            inventory_info = self.wwqy_client.get_player_inventory(username, tag)
            if not inventory_info.is_success():
                return {
                    'status': EventStatus.FAILED,
                    'message': inventory_info.message
                }

            # 获取玩家统计
            stats_info = self.wwqy_client.get_player_stats(username, tag)
            if not stats_info.is_success():
                return {
                    'status': EventStatus.FAILED,
                    'message': stats_info.message
                }

            # 合并所有数据
            result = {
                **player_info.data,
                **loadout_info.data,
                **inventory_info.data,
                **stats_info.data
            }

            return {
                'status': EventStatus.SUCCESS,
                'data': result
            }

        except Exception as e:
            print(f"执行任务失败: {str(e)}")
            traceback.print_exc()
            return {
                'status': EventStatus.FAILED,
                'message': str(e)
            }

    @contextmanager
    def time_logger(self, stage):
        """记录执行时间"""
        start_time = time.time()
        try:
            yield
        finally:
            end_time = time.time()
            print(f"[INFO] {stage} 执行时间: {end_time - start_time:.2f}秒")

class Master:
    def __init__(self, status_queue, capture_result_queue, device_addr=None):
        self.status_queue = status_queue
        self.capture_result_queue = capture_result_queue
        self.device_addr = device_addr
        self.worker = None
        self.portal_client = PortalClient()
        self.running = True

    def start_worker(self, task):
        """启动工作进程"""
        if self.worker is not None:
            self.stop_worker_now()
        
        self.worker = Worker(task, self.status_queue, self.capture_result_queue, {})
        self.worker.start()

    def stop_worker_now(self, is_canceled=False):
        """立即停止工作进程"""
        if self.worker is not None:
            self.worker.terminate()
            self.worker.join()
            self.worker = None

    def heartbeat(self):
        """心跳检测"""
        while self.running:
            try:
                self.send_heartbeat()
                time.sleep(30)
            except Exception as e:
                print(f"心跳检测失败: {str(e)}")
                time.sleep(5)

    def check_worker_result(self):
        """检查工作进程结果"""
        if self.worker is None or not self.worker.is_alive():
            return

        try:
            if not self.status_queue.empty():
                result = self.status_queue.get()
                if result['status'] == EventStatus.SUCCESS:
                    print(f"任务执行成功: {result['data']}")
                else:
                    print(f"任务执行失败: {result['message']}")
                self.stop_worker_now()
        except Exception as e:
            print(f"检查工作进程结果失败: {str(e)}")

    def check_new_task(self):
        """检查新任务"""
        try:
            task = self.portal_client.get_task(device_id)
            if task:
                print(f"获取到新任务: {task}")
                self.start_worker(task)
        except Exception as e:
            print(f"检查新任务失败: {str(e)}")

    def send_heartbeat(self, device_status='IDLE'):
        """发送心跳"""
        try:
            self.portal_client.heartbeat(device_id, device_status)
        except Exception as e:
            print(f"发送心跳失败: {str(e)}")

    def shutdown(self):
        """关闭主进程"""
        self.running = False
        self.stop_worker_now()

def main(status_queue=None, device_addr=None):
    """主函数"""
    try:
        master = Master(status_queue or multiprocessing.Queue(), 
                       multiprocessing.Queue(), 
                       device_addr)
        
        # 启动心跳线程
        heartbeat_thread = threading.Thread(target=master.heartbeat)
        heartbeat_thread.daemon = True
        heartbeat_thread.start()

        # 主循环
        while True:
            try:
                master.check_worker_result()
                master.check_new_task()
                time.sleep(1)
            except KeyboardInterrupt:
                print("收到中断信号，正在关闭...")
                break
            except Exception as e:
                print(f"主循环异常: {str(e)}")
                time.sleep(5)

    except Exception as e:
        print(f"程序异常: {str(e)}")
        traceback.print_exc()
    finally:
        if 'master' in locals():
            master.shutdown()

if __name__ == '__main__':
    main() 