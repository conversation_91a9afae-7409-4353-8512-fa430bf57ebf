import os
from PIL import Image, ImageDraw, ImageFont
import json
import logging
from pathlib import Path

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class WwqyImageUtil:
    def __init__(self):
        self.script_dir = Path(__file__).resolve().parent
        self.font_path = os.path.join(self.script_dir, "SourceHanSansSC-Bold-2.otf")
        self.template_path = os.path.join(self.script_dir, "tpl_cover.png")
        self.detail_template_path = os.path.join(self.script_dir, "tpl_detail.png")

    def create_cover_image(self, data, output_path):
        """创建封面图片"""
        try:
            # 加载模板图片
            template = Image.open(self.template_path)
            draw = ImageDraw.Draw(template)

            # 设置字体
            title_font = ImageFont.truetype(self.font_path, 40)
            normal_font = ImageFont.truetype(self.font_path, 30)

            # 绘制标题
            title = f"{data['username']}#{data['tag']}"
            draw.text((50, 50), title, font=title_font, fill=(255, 255, 255))

            # 绘制段位信息
            rank_info = f"当前段位: {data.get('current_rank', '未定级')}"
            draw.text((50, 120), rank_info, font=normal_font, fill=(255, 255, 255))

            # 绘制皮肤统计
            skin_count = len(data.get('skins', []))
            skin_info = f"皮肤数量: {skin_count}"
            draw.text((50, 170), skin_info, font=normal_font, fill=(255, 255, 255))

            # 绘制K/D比
            kd_info = f"K/D比: {data.get('kd_ratio', '0')}"
            draw.text((50, 220), kd_info, font=normal_font, fill=(255, 255, 255))

            # 绘制胜率
            win_info = f"胜率: {data.get('win_percentage', '0')}%"
            draw.text((50, 270), win_info, font=normal_font, fill=(255, 255, 255))

            # 保存图片
            template.save(output_path)
            logging.info(f"封面图片已保存: {output_path}")

        except Exception as e:
            logging.error(f"创建封面图片失败: {str(e)}")
            raise e

    def create_detail_image(self, data, output_path):
        """创建详情图片"""
        try:
            # 加载模板图片
            template = Image.open(self.detail_template_path)
            draw = ImageDraw.Draw(template)

            # 设置字体
            title_font = ImageFont.truetype(self.font_path, 40)
            normal_font = ImageFont.truetype(self.font_path, 30)
            small_font = ImageFont.truetype(self.font_path, 25)

            # 绘制标题
            title = f"{data['username']}#{data['tag']} - 账号详情"
            draw.text((50, 50), title, font=title_font, fill=(255, 255, 255))

            # 绘制基本信息
            y_offset = 120
            info_items = [
                f"当前段位: {data.get('current_rank', '未定级')}",
                f"段位点数: {data.get('current_rank_points', '0')}",
                f"最高段位: {data.get('peak_rank', '未定级')}",
                f"MMR变化: {data.get('mmr_change', '0')}"
            ]

            for item in info_items:
                draw.text((50, y_offset), item, font=normal_font, fill=(255, 255, 255))
                y_offset += 50

            # 绘制皮肤列表
            y_offset += 30
            draw.text((50, y_offset), "皮肤列表:", font=normal_font, fill=(255, 255, 255))
            y_offset += 50

            for skin in data.get('skins', [])[:10]:  # 只显示前10个皮肤
                skin_info = f"{skin['name']} ({skin['quality']})"
                draw.text((70, y_offset), skin_info, font=small_font, fill=(255, 255, 255))
                y_offset += 40

            # 保存图片
            template.save(output_path)
            logging.info(f"详情图片已保存: {output_path}")

        except Exception as e:
            logging.error(f"创建详情图片失败: {str(e)}")
            raise e

    def process_images(self, data, output_dir):
        """处理所有图片"""
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)

            # 创建封面图片
            cover_path = os.path.join(output_dir, "cover.png")
            self.create_cover_image(data, cover_path)

            # 创建详情图片
            detail_path = os.path.join(output_dir, "detail.png")
            self.create_detail_image(data, detail_path)

            return {
                'cover_path': cover_path,
                'detail_path': detail_path
            }

        except Exception as e:
            logging.error(f"处理图片失败: {str(e)}")
            raise e 