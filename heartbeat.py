import random
import threading
import time


class HeartbeatReporter:
    def __init__(self, interval=1):
        self.interval = interval
        self.running = True
        self.thread = threading.Thread(target=self.report_heartbeat)

    def start(self):
        self.thread.start()

    def stop(self):
        self.running = False
        self.thread.join()

    def report_heartbeat(self):
        while self.running:
            # 模拟心跳上报
            print(f"Heartbeat at {time.time()}")
            time.sleep(self.interval)


def main():
    reporter = HeartbeatReporter(interval=2)
    reporter.start()

    try:
        # 模拟主线程的工作
        while True:
            # 这里可以放入主线程的逻辑
            time.sleep(5)
            if random.random() < 0.1:  # 随机触发主线程挂掉
                print("Main thread is exiting unexpectedly!")
                raise Exception("Main thread crash!")
    except Exception as e:
        print(e)
    finally:
        reporter.stop()
        print("Heartbeat reporting stopped.")


if __name__ == "__main__":
    main()
