import hashlib
import hmac
import os
# 添加项目根目录到 sys.path
import sys
import threading
import time

import requests

from bm.import_bm_product_handler import portal_client

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

from common import logger_config
from common import mysql_util, notify_util
from common import settings

logger = logger_config.setup_logger(os.path.basename(__file__))

portal_api_url_prefix = settings.portal_api_url_prefix
admin_api_url_prefix = settings.admin_api_url_prefix
search_api_url_prefix = settings.search_api_url_prefix

# 定义一个锁和一个上次执行时间的变量用于限流
delete_es_lock = threading.Lock()
last_delete_es_time = 0


def delete_es(product_id):
    global last_delete_es_time

    with delete_es_lock:
        current_time = time.time()
        # 确保上次调用后至少间隔0.1秒
        if current_time - last_delete_es_time < 0.2:
            time.sleep(0.2 - (current_time - last_delete_es_time))

        timestamp = str(int(time.time()) * 1000)
        message = timestamp
        signature = get_signature(message, app_secret)

        headers = {
            'X-KK-APIKEY': app_key,
            'X-KK-TIME': timestamp,
            'X-KK-SIGNATURE': signature,
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36"
        }

        r = requests.post(admin_api_url_prefix + f'esBuild/delete/{product_id}', headers=headers)
        logger.info('删除es:product_id: %s,  %s', product_id, r.text)
        if r.json()['code'] != 200:
            logger.error('删除es失败: %s', r.json())
            notify_util.send_notify('导入product出错', 'code:{}, resp:{}'.format(r.json.get('code'), r.text))
            raise Exception('删除es失败')
        # 更新上次执行时间
        last_delete_es_time = time.time()


def get_signature(message, secret):
    # algorithm = 'HmacSHA256'
    try:
        hmac_sha256 = hmac.new(secret.encode('utf-8'), message.encode('utf-8'), hashlib.sha256)
        return hmac_sha256.hexdigest()
    except Exception as e:
        raise Exception(f"签名计算错误: {e}")


app_key = settings.app_key
app_secret = settings.app_secret

from common.server_client import PortalClient

portal_client = PortalClient()


def delete_cache(product_id):
    # 删除缓存
    # r = http_client.post(portal_api_url_prefix + '/deleteProductCache2', data=json.dumps({'productId': product_id}))

    r = portal_client.delete_product_cache(product_id)
    logger.info('删除product缓存, result: %s', r)


if __name__ == "__main__":

    # 清除数据，173212，YmqruNhrVENaEsTb
    with open("operation_backup-20250514-九神.txt", "a") as log_file:
        products = mysql_util.execute_sql_dict(
            "select id,product_sn from pms_product where member_id=1022098 and delete_status=0 and publish_status=1 and stock=9;")

        index = 0
        for product in products:
            index += 1
            print('## ', index)
            # 执行数据库更新操作
            mysql_util.execute_sql("update pms_product set publish_status=0 where id = %s", (product['id'],))

            # 删除缓存和 Elasticsearch 记录
            delete_cache(product['id'])
            time.sleep(0.05)
            delete_es(product['id'])

            # 打印当前产品信息
            print(product)

            # 将操作记录写入文件
            log_file.write(f"{product['id']},{product['product_sn']}\n")

    # sync_es_all()
    # sync_es_by_category_id(112)
