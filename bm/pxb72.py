import requests
import json
import time
import csv
import random
import logging
from typing import List, Tuple
from requests.adapters import HTT<PERSON>dapter
from urllib3.util.retry import Retry

# === 配置 ===
BASE_URL = 'https://www.pxb7.com/api/search/product/selectSearchPageList'
HEADERS = {
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'client_type': '0',
    'px-authorization-merchant': '<YOUR_MERCHANT_TOKEN>',
    'px-authorization-user': '<YOUR_USER_TOKEN>',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko)'
}
FILTERS: List[dict] = []  # 筛选条件
PAGE_SIZE = 16  # 每页大小
MIN_DELAY = 1.0  # 最小延迟 秒
MAX_DELAY = 3.0  # 最大延迟 秒
MAX_RETRIES = 3  # 重试次数
CSV_FILE = 'products2.csv'  # 输出文件
LOG_FILE = 'crawl.log'   # 日志文件

# === 日志配置 ===
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# === HTTP 会话和重试策略 ===
session = requests.Session()
retry_strategy = Retry(
    total=MAX_RETRIES,
    status_forcelist=[405, 429, 500, 502, 503, 504],
    allowed_methods=["POST"],
    backoff_factor=1
)
adapter = HTTPAdapter(max_retries=retry_strategy)
session.mount("https://", adapter)

# === 请求单页数据 ===
def fetch_page(game_id: str, page_index: int, page_size: int = PAGE_SIZE, filters: List[dict] = None) -> List[dict]:
    payload = {
        'gameId': game_id,
        'pageIndex': page_index,
        'pageSize': page_size,
        'bizProd': 1,
        'type': '4',
        'posType': 1,
        'filterDTOList': filters or []
    }
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            response = session.post(BASE_URL, headers=HEADERS, json=payload, timeout=10)
            response.raise_for_status()
            data = response.json()
            if not data.get('success'):
                raise RuntimeError(f"API 返回错误: {data.get('errMessage')} (code {data.get('errCode')})")
            return data.get('data', [])
        except Exception as e:
            logger.warning(f"第{page_index}页 请求第{attempt}次失败: {e}")
            time.sleep(MIN_DELAY * (2 ** (attempt - 1)))
    logger.error(f"第{page_index}页 多次重试失败，跳过此页。")
    return []

# === 爬取逻辑 ===
def crawl_all_products(game_id: str, filters: List[dict] = FILTERS) -> None:
    seen_ids = set()
    page_index = 1
    total = 0

    # 初始化 CSV
    with open(CSV_FILE, 'w', encoding='utf-8', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['productId', 'productUniqueNo'])

    while True:
        logger.info(f"开始抓取 第{page_index}页 数据...")
        items = fetch_page(game_id, page_index, PAGE_SIZE, filters)
        if not items:
            logger.info("无数据或全部失败，停止爬取。")
            break

        new_count = 0
        # 逐条处理并实时写入
        with open(CSV_FILE, 'a', encoding='utf-8', newline='') as csvfile:
            writer = csv.writer(csvfile)
            for item in items:
                pid = item.get('productId')
                unique_no = item.get('productUniqueNo')
                if pid and unique_no:
                    if pid not in seen_ids:
                        seen_ids.add(pid)
                        writer.writerow([pid, unique_no])
                        total += 1
                        new_count += 1
                        logger.info(f"新增: productId={pid}, uniqueNo={unique_no}")
                    else:
                        logger.debug(f"跳过重复: {pid}")

        # 判断终止条件
        if new_count == 0 and len(items) == PAGE_SIZE:
            logger.info(f"第{page_index}页 全为重复，停止爬取。")
            break
        if len(items) < PAGE_SIZE:
            logger.info("最后一页 数据不足，停止爬取。")
            break

        delay = random.uniform(MIN_DELAY, MAX_DELAY)
        logger.info(f"睡眠 {delay:.2f} 秒 后继续...")
        time.sleep(delay)
        page_index += 1

    logger.info(f"爬取完毕，共抓取到 {total} 条 唯一商品数据。")

if __name__ == '__main__':
    GAME_ID = '10013'  # 替换为实际 gameId
    crawl_all_products(GAME_ID)
