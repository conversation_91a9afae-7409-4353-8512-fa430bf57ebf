import hashlib
import hmac
import json
import os
import random
import re
import sys
import threading
import time
import traceback
from urllib.parse import urlparse

from cache_util import SimpleCache
from common.server_client import PortalClient

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

from client import http_client
from common import logger_config
from common import notify_util
from common import settings

logger = logger_config.setup_logger(os.path.basename(__file__))

admin_api_url_prefix = settings.admin_api_url_prefix

# 定义一个锁和一个上次执行时间的变量用于限流
delete_es_lock = threading.Lock()
last_delete_es_time = 0

# 自动上架游戏分类id
auto_publish_cate_ids = settings.auto_publish_category_ids
portal_client = PortalClient()

device_list = [54, 55, 56, 58]


def import_one_data(product, metadata, args):
    meta_dict = {meta['name']: meta for meta in metadata}

    qufu = None
    for meta in metadata:
        if meta.get('ename') == 'gameAccountQufu':
            qufu = meta
            break

    ext_attributes = json.loads(product.get('extGameAttributeArrays', '[]'))

    product_sn = product.get('productSn')
    err_msg = ''

    qufu_value = product['gameAccountQufu']  # 区服信息

    attrs = []

    # 如果 区服 不存在，检查 大区 是否存在
    if qufu is None:
        logger.error(f"1 attr_name '区服' 不存在，productSn %s", product_sn)
        notify_util.send_notify('导入product出错', "attr_name '区服' 不存在, product_sn: " + product_sn)
        raise Exception(f"1 attr_name '区服' 不存在")

    item = {
        "productAttributeId": qufu['id'],
        "value": qufu_value,
        "attriName": qufu['name'],
        "sort": qufu.get('sort', 0),
        "filterType": qufu['filterType'],
        "searchType": qufu['searchType'],
        "type": qufu['type'],
        "searchSort": qufu['searchSort'],
    }
    attrs.append(item)

    push_type = 2
    need_record = False
    # 属性处理
    for attr in ext_attributes:
        name = attr['name']

        origin_value = attr['value']
        if name == '账号描述':
            continue

        if product['categoryId'] == 82:
            # 王者荣耀
            if name == '营地ID':
                # 录号
                push_type = 1
                need_record = True
            elif name in ['珍品传说', '无双皮肤', '荣耀典藏皮肤', '年限皮肤', '内测皮肤', '传说皮肤', '史诗皮肤',
                          '赛季皮肤',
                          '战令限定', '勇者皮肤', '贵族限定皮肤', '其他皮肤', '赛年皮肤', '珍稀皮肤']:
                attr['value'] = ''
                origin_value = attr['value']
            elif name in ['大国标', '小国标', '小兵皮肤', '星元皮肤']:
                meta = meta_dict.get(name)
                input_list = meta.get('inputList', '').split(',')

                ori_values = [x.strip() for x in origin_value.split(',') if x.strip()]

                # 过滤掉不在input_list中的值
                filtered_values = [value for value in ori_values if value in input_list]

                # 重新组合成逗号分隔的字符串
                origin_value = ','.join(filtered_values)

        if name not in meta_dict:
            # logger.warning(f"1 attr_name【{name}】不存在")
            err_msg += f"1 attr_name【{name}】不存在"
            # invalid_names.add(name)
            continue
        if product['categoryId'] == 75 and origin_value and len(origin_value) > 0:
            # 逆水寒正手游特殊处理
            if name in ['换绑CD', '转职CD', '转性CD']:
                if origin_value == '是':
                    origin_value = '有'
                elif origin_value == '否':
                    origin_value = '无'

        meta = meta_dict.get(name)
        select_type = meta.get('selectType')
        input_type = meta.get('inputType')
        input_list = meta.get('inputList', '').split(',')

        # input_type: 0 手工录入，1 列表选择
        if input_type == 1:  # 从列表中选取
            # 先检查单选
            # select_type: 0->唯一；1->单选；2->多选
            if select_type == 1 and len(origin_value) > 1:  # 单选
                if ',' in origin_value:  # 逗号表示多选
                    # logger.warning(f"2 attr_name【{name}】的 value【{origin_value}】为多选，不合法")
                    err_msg += f"2 attr_name【{name}】的 value【{origin_value}】为多选，不合法,"
                    # invalid_single_values.add(name + ':' + origin_value)
            # 检查多选
            if select_type == 2:  # 多选
                # 先去除两端空格
                origin_value = origin_value.strip()
                # 处理所有可能的分隔符
                if '，' in origin_value or ',' in origin_value or ' ' in origin_value or '、' in origin_value:
                    split_value_list = re.split(r'[，,、 ]+', origin_value)  # 使用正则表达式同时分割多种分隔符
                else:
                    split_value_list = [origin_value]  # 没有分隔符，直接使用原始值

                for split_value in split_value_list:
                    if split_value and split_value not in input_list:  # 检查分割后的值是否非空且不在有效列表中
                        # logger.warning(f"3 attr_name【{name}】: value 【{split_value}】 不合法")
                        err_msg += f"3 attr_name【{name}】: value 【{split_value}】 不合法,"
                        # invalid_multiple_values.add(name + ':' + split_value)
        # [高价值]->[钱]、[绝版]->[绝]、[核心]->[核]
        # 如果 value 包含 [高价值]，则替换为 [钱]
        if '[高价值]' in origin_value:
            origin_value = origin_value.replace('[高价值]', '[钱]')
        # 如果 value 包含 [绝版]，则替换为 [绝]
        if '[绝版]' in origin_value:
            origin_value = origin_value.replace('[绝版]', '[绝]')
        # 如果 value 包含 [核心]，则替换为 [核]
        if '[核心]' in origin_value:
            origin_value = origin_value.replace('[核心]', '[核]')

        # origin_value 兼容多种分隔符
        # 将分隔符转换成英文逗号
        origin_value = re.sub(r'[，、 ]+', ',', origin_value)

        item = {
            "productAttributeId": meta_dict.get(name)['id'],
            "value": origin_value,
            "attriName": meta_dict.get(name)['name'],
            "sort": meta_dict.get(name)['sort'],
            "filterType": meta_dict.get(name)['filterType'],
            "searchType": meta_dict.get(name)['searchType'],
            "type": meta_dict.get(name)['type'],
            "searchSort": meta_dict.get(name)['searchSort'],
        }
        if len(origin_value) > 0:
            attrs.append(item)

    if product['categoryId'] == 82 and not need_record:
        # 没有营地ID
        print('无营地ID，该商品不导入')
        mark_import_state(product['id'], -1)
        pass

    publish_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    if product['categoryId'] in auto_publish_cate_ids:
        verify_status = 1
        publish_status = 1
    else:
        verify_status = 0
        publish_status = 0
    stock = 9
    if need_record:
        # 需要录号
        verify_status = 0
        publish_status = 0
        product['albumPics'] = ''
        product['albumPicsJson'] = '[]'
        product['pic'] = ''

    # 如果 albumPics 为空，从 albumPicsJson 读取，组装成 albumPicsJson
    # 逆水寒端游, 前端隐藏“全部”
    if (product.get('albumPics') is None or product.get('albumPics') == '') and product.get(
            'albumPicsJson') not in (
            None, '[]', ''):
        urls = [urlparse(item['url']).scheme + "://" + urlparse(item['url']).netloc + urlparse(item['url']).path
                for item in json.loads(product['albumPicsJson'])]

        product['albumPics'] = ",".join(urls)

    member_id = cache.get(product['userPhone'])
    if not member_id:
        member = portal_client.get_member_by_username(product['userPhone'])
        if member:
            member_id = member['id']
        cache.set(product['userPhone'], member_id)
    if member_id is None:
        mark_import_state(product['id'], -1)

    description = product.get('description')

    sort = 1
    if product['categoryId'] == 82 and not need_record:
        # todo weight 王者荣耀，没有营地ID，权重降低
        sort = 0

    product_param = {
        "albumPics": product.get('albumPics'),
        "albumPicsJson": product.get('albumPicsJson'),
        "productAttributeValueList": attrs,
        # "brandId": args['brandId'],
        "productAttributeCategoryId": args['attributeCategoryId'],  # 逆水寒手游是 17
        "gameAccountQufu": product.get('gameAccountQufu'),
        "description": description,
        "pic": product.get('pic'),
        "price": float(product.get('price')),
        "originalPrice": float(product.get('originalPrice')),
        "stock": stock,
        "gameGoodsFangxin": None,
        "gameGoodsBukuan": 0,
        "gameGoodsJiangjia": 0,
        "gameGoodsYijia": product.get('gameGoodsYijia'),
        "sort": sort,
        "publishTime": publish_time,
        "publishStatus": publish_status,
        "gameGoodsYishou": product.get('gameGoodsYishou'),
        "gameGoodsYuyue": 0,
        "gameGoodsSaletype": 1,
        "gameCareinfoPhone": product.get('gameCareinfoPhone'),
        "gameCareinfoTime": product.get('gameCareinfoTime'),
        "gameCareinfoVx": product.get('gameCareinfoVx'),
        "subjectProductRelationList": [],
        "recommandStatus": 0,
        "productCategoryId": args['categoryId'],
        "productCategoryName": args['categoryName'],
        "brandName": None,
        "productSn": product.get('productSn'),
        "verifyStatus": verify_status,
        "verifyDetail": "",
        # "createTime": str(product.get('createTime')),
        "gameSysinfoReadcount": random.randint(0, 50),
        "gameSysinfoCollectcount": product.get('gameSysinfoCollectcount'),
        "memberId": member_id,
        "pushType": push_type,  # 自主截图 2
        "pushStatus": 0  # 录号状态
    }
    print('## create params: ', product_param)
    r = portal_client.bm_create(product_param)

    if r is not None:
        if need_record:
            # 创建录号任务
            product_sn = r
            device_id = random.choice(device_list)
            create_task_req = {
                'productSn': product_sn,
                'operateMan': 'API',
                'deviceId': device_id
            }
            task = portal_client.create_task(create_task_req)
            print('create task:', task)
            pass
        return r
    else:
        logger.error('resp:', r)


def delete_es(product_id):
    global last_delete_es_time

    with delete_es_lock:
        current_time = time.time()
        # 确保上次调用后至少间隔0.1秒
        if current_time - last_delete_es_time < 0.1:
            time.sleep(0.1 - (current_time - last_delete_es_time))

        r = http_client.post(admin_api_url_prefix + f'esBuild/delete/{product_id}')

        # 更新上次执行时间
        last_delete_es_time = time.time()


def get_signature(message, secret):
    # algorithm = 'HmacSHA256'
    try:
        hmac_sha256 = hmac.new(secret.encode('utf-8'), message.encode('utf-8'), hashlib.sha256)
        return hmac_sha256.hexdigest()
    except Exception as e:
        raise Exception(f"签名计算错误: {e}")


app_key = settings.app_key
app_secret = settings.app_secret


def mark_import_state(import_id, dispose_state):
    portal_client.mark_import_state({
        'id': import_id,
        'disposeState': dispose_state
    })


def delete_cache(product_id):
    # 删除缓存
    # r = http_client.post(portal_api_url_prefix + '/deleteProductCache2', data=json.dumps({'productId': product_id}))
    r = portal_client.delete_product_cache(product_id)
    logger.info('删除product缓存, result: %s', r)


# def fetch_all_products(dispose_state=0):
#     """
#     获取所有商品
#     """
#     all_products = []
#     page_num = 1
#     page_size = 100
#
#     while True:
#         logger.info(f"正在获取第 {page_num} 页数据")
#         import_products = mysql_util.execute_sql_dict(
#             f'select * from pms_bm_import_product where dispose_state = {dispose_state} limit {page_size} offset {(page_num - 1) * page_size}')
#         if len(import_products) == 0:
#             break
#         all_products.extend(import_products)
#         page_num += 1
#     return all_products


cache = SimpleCache(maxsize=256, ttl=7200)


def main():
    # import_products = fetch_all_products(dispose_state=0)
    # import_products.extend(fetch_all_products(dispose_state=2))

    import_products = portal_client.get_all_import_products(dispose_state=0)
    import_products.extend(portal_client.get_all_import_products(dispose_state=2))

    for index, import_product in enumerate(import_products, 1):
        print(import_product)
        try:
            logger.info(f'Processing import_product #{index}: ' + import_product['productSn'])

            # pre_product = mysql_util.execute_sql_dict(
            #     "select id, publish_status, delete_status, push_type, product_category_id from pms_product where product_sn = %s",
            #     (import_product['product_sn'],))

            pre_product = portal_client.get_product_detail_by_sn(import_product['productSn'])
            #
            if pre_product and len(pre_product) > 0 and pre_product['product']['publishStatus'] == 1:
                # 如果之前是上架状态，更新需要下架，删除es
                time.sleep(1)
                delete_es(product_id=pre_product['product'].get('id'))

            category_name = import_product.get('categoryName')
            category_id = import_product.get('categoryId')

            metadata = cache.get(f'metadata:{category_name}')
            if metadata is None:
                metadata = portal_client.get_product_category_meta(category_id)
                cache.set(f'metadata:{category_name}', metadata)

            # 获取category_name, brand_id, attribute_category_id
            category_info = cache.get(f'category:{category_id}')
            if category_info is None:
                # query_category_sql = "SELECT name, attri_cate_id FROM pms_product_category WHERE id = %s;"
                # category_info = mysql_util.execute_sql_dict(query_category_sql, (category_id,))[0]
                category_info = portal_client.get_product_category_info(category_id)
                cache.set(f'category:{category_id}', category_info)
            category_name = category_info.get('name')
            attribute_category_id = category_info.get('attriCateId')

            # brand_info = cache.get(f'brand:{category_id}')
            # if brand_info is None:
            #     query_brand_sql = "SELECT id FROM pms_brand WHERE product_category_id = %s;"
            #     brand_info = mysql_util.execute_sql_dict(query_brand_sql, (category_id,))
            #     cache.set(f'brand:{category_id}', brand_info)
            # brand_id = brand_info[0]['id'] if brand_info else None

            args = {
                'categoryName': category_name,
                'categoryId': category_id,
                # 'brandId': brand_id,
                'attributeCategoryId': attribute_category_id,
            }

            import_one_data(import_product, metadata, args)

            # 待审核状态，不同步es
            # sync_es(import_product['productSn'])
            # 标记转换完成
            # delete_cache(import_product['product_sn'])
            mark_import_state(import_product['id'], 1)
        except Exception as e:
            logger.error(f"An error occurred while processing import_product #{index}: {e}")
            traceback.print_exc()
            logger.error(traceback.format_exc())
            notify_util.send_notify('导入product出错', 'exception{}'.format(e))
            mark_import_state(import_product['id'], 2)


if __name__ == "__main__":
    main()
