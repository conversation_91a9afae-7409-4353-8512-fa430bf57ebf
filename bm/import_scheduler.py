from apscheduler.executors.pool import ThreadPoolExecutor
import logging
from logging.handlers import TimedRotatingFileHandler

import import_bm_product_handler
from apscheduler.schedulers.blocking import BlockingScheduler

from common import notify_util

# 配置日志，按天分隔输出到文件
file_handler = TimedRotatingFileHandler('task_log.log', when='midnight', interval=1, backupCount=30)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 添加处理器
logger.addHandler(file_handler)
logger.addHandler(console_handler)


def task5():
    job_name = 'import_bm_product_handler'
    logging.info(f"==================== 开始执行任务, {job_name} ====================")
    import_bm_product_handler.main()
    logging.info(f"==================== 任务{job_name}执行完成 ====================")


# 创建调度器
scheduler = BlockingScheduler(executors={'default': ThreadPoolExecutor(1)})  # 单线程执行

# 添加第一个任务
scheduler.add_job(task5, 'cron', minute='2,7,12,17,22,27,32,37,42,47,52,57', max_instances=1)

# 启动调度器
try:
    logging.info("调度器开始运行...")
    scheduler.start()
except Exception as e:
    logging.info("调度器停止运行")
    notify_util.send_notify2('告警通知', f'调度器发生异常: {e}')
