import requests
import json

# 模拟的 API 地址（请替换成实际的接口 URL）
url = "https://act-api-takumi-static.mihoyo.com/common/blackboard/ys_obc/v1/home/<USER>/list?app_sn=ys_obc&channel_id=25"

# 发起请求获取数据
response = requests.get(url)
data = response.json()  # 解析返回的 JSON 数据

# 提取角色数据
characters = data['data']['list'][0]['list']
character_mapping = {}

for char in characters:
    title = char['title']
    ext = json.loads(char['ext'])  # 将 ext 转为字典
    star_info_str = ext['c_25']['filter']['text']  # 这是一个字符串

    # 将 star_info_str 转换为列表
    star_info = json.loads(star_info_str)

    # 提取星级信息
    star = None
    for item in star_info:
        if "星级" in item:
            star = item.split("/")[1]  # 获取 "四星" 或 "五星"

    # 将角色名称和星级记录到字典中
    if star:
        character_mapping[title] = star

# 保存角色与星级的映射关系到文件
output_file = "role_mapping.json"
with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(character_mapping, f, ensure_ascii=False, indent=4)

print(f"角色与星级映射关系已保存至 {output_file}")
