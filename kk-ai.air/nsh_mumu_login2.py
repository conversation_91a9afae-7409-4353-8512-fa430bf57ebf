# -*- encoding=utf8 -*-
__author__ = "kkzhw016"

import io
from logging import exception
from os import times
from time import sleep

from airtest.core.android import Android
from airtest.core.api import *


# -*- encoding=utf8 -*-
__author__ = "labugao"

# auto_setup(__file__)

# if 'auto_setup' in globals():
#     auto_setup(__file__)
# else:
#     from airtest.core.api import *
#     from airtest.cli.parser import cli_setup

#     if not cli_setup():
#         auto_setup(__file__, logdir=False, devices=[
#             "Android:///?cap_method=javacap&ori_method=adbori",
#     ])
import os
import sys

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

from airtest.core.api import *
from airtest.aircv import *
from paddleocr import PaddleOCR
import requests
import json
from airtest.aircv.cal_confidence import cal_ccoeff_confidence  
from airtest.core.settings import Settings as ST
import oss2  
import os  
import numpy as np  
import configparser
from PIL import Image, ImageDraw, ImageFont,ImageFilter
import shutil  
import logging  
from urllib.parse import urlparse



import sys_tool
from sys_tool import restart_nshm


import configs
import product_util
import logger_config
import image_util
from server_client import PortalClient
import models
from models import *
from collections import defaultdict
from luhao_models import EventStatus, TaskEvent

#####################################################
# auto_setup(__file__, devices=["Android://127.0.0.1:5037/127.0.0.1:50000?ori_method=adbori"])

# auto_setup(__file__)
# connect_device("Android://127.0.0.1:5037/127.0.0.1:50000?ori_method=adbori")
auto_setup(__file__)
# connect_device("Windows:///?title_re=MuMu模拟器12.*")

#########################################################################
import datetime  
import random  
import string  
import re
#############################################
#############################################################################全局变量
projectPath = "D:\\kkzhw\\airtest_log\\"
snapImgPath = ""
logger = None
logger2 = None
# 设置全局的截图精度为90
ST.SNAPSHOT_QUALITY = 99
# 获取当前设备的屏幕宽度和高度  
width = 1
height = 1
# width, height = device().get_current_resolution()  
center_x = 1
center_y = 1
product_meta = []
luhao_task = None
luhao_failed = False
pic_url_arrays = []
tianniran_ocr_txt_arrays = set()
ocr_file = 1 #ocr 是否保存识别区域图片 1保存图片 0不保存
touch_fast = True
start_time = datetime.datetime.now()
end_time = datetime.datetime.now()
is_local = False
save_original_pic = False
is_debug = True
logger_queue=None

android = None
###############################################################################


##########################################
ocr = PaddleOCR(use_angle_cls=True, lang='ch',
                det_model_dir="models\\det\\ch_PP-OCRv4_det_infer",
                rec_model_dir="models\\rec\\ch_PP-OCRv4_rec_infer",
                cls_model_dir="models\\cls\\ch_ppocr_mobile_v2.0_cls_infer"
                )


portal_client = PortalClient()
###########################################################################

def remove_unwanted_chars(s):
    s = re.sub(r'\[[^\]]*\]', '', s) 
    return re.sub(r'[^·\u4e00-\u9fff]', '', s)
  
####################################################
def contains_digit(s):  
    return bool(re.search(r'\d', s)) 
######################################################################
def clear_folder(folder_path):
    # 文件夹路径  
    new_folder_name = f'{folder_path}_' + str(int(time.time()))
    if os.path.exists(folder_path):  
        shutil.move(folder_path, new_folder_name)  
        print(f"文件夹 '{folder_path}' 已重命名为 '{new_folder_name}'")
    os.makedirs(folder_path, exist_ok=True)
    print(f"新的空文件夹 '{folder_path}' 已创建")
##############################################################
def clear_folder2(folder_path):
    if os.path.exists(folder_path):
       return
    os.makedirs(folder_path, exist_ok=True)
    print(f"新的空文件夹 '{folder_path}' 已创建")
######################################################################
  
def generate_oss_path():  
    # 获取当前日期并格式化为 yyyyMMdd  
    today = datetime.datetime.now().strftime("%Y%m%d")
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=6))  
    # 获取当前毫秒时间戳  
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
    # 拼接路径  
    oss_path = f"{today}/"  
    return oss_path
###################################################################
def read_config(filename):  
    config = {}  
    with open(filename, 'r', encoding='utf-8') as file:  
        for line in file:  
            # 去除每行两端的空白字符，并检查是否为空行或注释行（假设以#开头的行为注释）  
            line = line.strip()  
            if not line or line.startswith('#'):  
                continue  
            # 使用等号分割键和值  
            key, value = line.split('=', 1)  
            # 去除值两端的引号（如果有的话）  
            value = value.strip('"').strip("'")  
            config[key] = value  
    return config  
########################################################################

def generate_oss_fileName():  
    # 生成六位随机字符数字串  
    # 注意：这里使用了string.ascii_letters + string.digits来确保生成的是字母和数字的混合  
    # 如果只想要数字，可以去掉string.ascii_letters部分  
    random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=7))  
      
    # 获取当前毫秒时间戳  
    # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
      
    # 拼接路径  
    oss_fileName = f"{random_str}_{current_millis}.jpg"  
      
    return oss_fileName
############################################################################


################################################
def resize_image_by_width(input_image_path, output_image_path, target_width):  
    # 打开原始图片  
    with Image.open(input_image_path) as img:  
        # 获取原始图片的宽度和高度  
        original_width, original_height = img.size  
          
        # 计算缩放比例  
        ratio = original_width / float(target_width)  
          
        # 根据缩放比例计算新的高度  
        new_height = int(original_height / ratio)  
          
        # 缩放图片  
        resized_img = img.resize((target_width, new_height), Image.LANCZOS)  
          
        # 保存缩放后的图片  
        resized_img.save(output_image_path)  
##############################################################################


#########################################################################

def upload_kk_img_to_oss(folder_path):
    image_path_set = set()
    global pic_url_arrays
#     image_name_array = [] ##图片分类+url集合

    bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称  
    endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint  
    access_key_id = configs.OSS_ACCESS_KEY_ID
    access_key_secret = configs.OSS_ACCESS_KEY_SECRET
    kkLogger_log(f"开始上传文件夹{folder_path} 内容")
    
    auth = oss2.Auth(access_key_id, access_key_secret)
    bucket = oss2.Bucket(auth, endpoint, bucket_name)  
    total_count = count_images(folder_path)
    up_count  = 0
    for filename in os.listdir(folder_path):
#         kkLogger_log(f"发现图片：{filename}")
        local_file_path = os.path.join(folder_path, filename)  
        if filename.endswith('jpg') and "luhao_" not in filename:
            up_count = up_count + 1
            kkLogger_log(f"上传图片：{up_count}/{total_count}")
            oss_file_path = "mall/images2/"+ generate_oss_path()  

            oss_fileName = oss_file_path+generate_oss_fileName()
            with open(local_file_path, 'rb') as fileobj:  
                bucket.put_object(oss_fileName, fileobj)
                image_path_set.add(oss_fileName)
                
                parts = filename.split('_')
                kkLogger_log(f"oss 文件:{oss_fileName}")
                # new_json_object = {"name": parts[0], "value": oss_fileName}
                if "头图" in filename:
                    new_json_object = {"name": "头图", "value": oss_fileName}
                    pic_url_arrays.append(new_json_object)

                    new_json_object = {"name": parts[1], "value": oss_fileName}
                    pic_url_arrays.append(new_json_object)
                else:
                    new_json_object = {"name": parts[1], "value": oss_fileName}
                    pic_url_arrays.append(new_json_object)
###############################################################################
def upload_one_img_to_oss(filename):
    image_path_set = set()
    bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称  
    endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint  
    access_key_id = configs.OSS_ACCESS_KEY_ID
    access_key_secret = configs.OSS_ACCESS_KEY_SECRET
    print(f"开始上传文件夹{snapImgPath} 内容")
    
    auth = oss2.Auth(access_key_id, access_key_secret)
    bucket = oss2.Bucket(auth, endpoint, bucket_name)  

    local_file_path = os.path.join(snapImgPath, filename)  
    oss_file_path = "mall/images2/"+ generate_oss_path()  
    oss_fileName = oss_file_path+generate_oss_fileName()
    with open(local_file_path, 'rb') as fileobj:
        bucket.put_object(oss_fileName, fileobj)
    
    print(f"oss_filename:{oss_fileName}")
    return oss_fileName
    
#############################################################
def count_images(folder_path):  
    jpg_count = 0  
    png_count = 0  
    # 遍历指定文件夹及其所有子文件夹  
    for root, dirs, files in os.walk(folder_path):  
        for file in files:  
            # 检查文件扩展名  
            if file.lower().endswith('.jpg'):  
                jpg_count += 1  
#             elif file.lower().endswith('.png'):  
#                 png_count += 1  
    # 返回总数  
    total_images = jpg_count + png_count  
    return total_images  
###################################################################


############################################################################
#根据sn查询账号密码
def requestGameAccountInfo(productSn):  
    kk_url = 'https://api2.kkzhw.com/mall-portal/import/product/getProductAccountInfo?productSn='+productSn
    response = requests.get(kk_url)
    # 检查请求是否成功  
    if response.status_code == 200:  
        # 打印返回的内容  
        kkLogger_log(response.text)
        responseObj = json.loads(response.text)
        product_dict = responseObj.get('data')
        return product_dict
    
########################################################################
#根据分类ID获取待录号productSn
def requestCategoryProductSn(categoryId):  
    kk_url = f'http://api2.kkzhw.com/mall-portal/import/product/getTheProductAccountInfo?categoryId={categoryId}'
    response = requests.get(kk_url)
    # 检查请求是否成功  
    if response.status_code == 200:  
        # 打印返回的内容  
#         print(response.text)
        responseObj = json.loads(response.text)
        productSn = responseObj.get('data')
        return productSn
    else:
        return ""
###########################################################################
def requestGameProductCategoryMeta(categoryId): 
    kk_url = f'https://api2.kkzhw.com/mall-portal/openapi/record/product/meta?categoryId={categoryId}'
    response = requests.get(kk_url)
    # 检查请求是否成功
    if response.status_code == 200:  
        # 打印返回的内容
        responseObj = json.loads(response.text)
        product_meta = responseObj.get('data')
        return product_meta
###################################################################根据sn查询账号密码


##OCR TOUCH#########################################ffffffffff##########
def ocr_touch(target_text) :
      # 截屏当前画面
    pic_path=f"{snapImgPath}\\now.png"
    snapshot(pic_path)
    
     # 使用PaddleOCR识别图片文字
    ocr_result = ocr.ocr(pic_path, cls=True)
    
    # 遍历识别结果，找到目标文字的坐标
    target_coords = None
    for line in ocr_result:
        for word_info in line:
            #获取识别结果的文字信息
            textinfo = word_info[1][0]
            kkLogger_log(textinfo)
            if target_text in textinfo:
                # 获取文字的坐标（中心点）
                x1, y1 = word_info[0][0]
                x2, y2 = word_info[0][2]
                target_coords = ((x1 + x2) / 2, (y1 + y2) / 2)
                break
        if target_coords:
            break

    # 使用Airtest点击坐标
    if target_coords:
        touch(target_coords)
    else:
        kkLogger_log(f"未找到目标文字：{target_text}")
#####################################################################


##ocr text####################################################################
def ocr_text_all():
    for i in range(5):
        try:
            sleep(1)
            pic_path=f"{snapImgPath}\\now.png"
            snapshot(pic_path)
            ocr_result = ocr.ocr(pic_path, cls=True)
            target_coords = None
            for line in ocr_result:
                for word_info in line:
                    textinfo = word_info[1][0]
            return ocr_result
        except Exception as e:
            kkLogger_log(f"ocr_text_all 失败：{e}")  
            sleep(2)
            continue
    return None
############################################################

def find_all_subImg(temp, start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            find_result =  temp.match_all_in(local_screen)

            kkLogger_log(f"find_all_subImg：{find_result}")

            if find_result:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_all_subImg_{current_millis}.png')
            return find_result
        except Exception as e:
            kkLogger_log(f"find_all_subImg 失败：{e}")  
            sleep(2)
            continue
    return None
###################################################
def find_subImg(temp, start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            # sleep(0.5)
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            find_result = temp.match_in(local_screen)

            kkLogger_log(f"find_subImg：{find_result}")

            if ocr_file == 1:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\plog_find_subImg_{current_millis}.png')
            return find_result
        except Exception as e:
            kkLogger_log(f"find_subImg 失败：{e}")  
            sleep(2)
            continue
    return None
########################################################
def find_subImg_inscreen(temp,local_screen):

    for i in range(5):
        try:
            # sleep(0.5)
            find_result = temp.match_in(local_screen)
            kkLogger_log(f"find_subImg_inscreen：{find_result}")
            if find_result:
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_subImg_inscreen_{current_millis}.png')
            return find_result
        except Exception as e:
            kkLogger_log(f"find_subImg_inscreen 失败：{e}")  
            sleep(2)
            continue
    return None
#########################################################
def find_first_subImg(temp, start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            sleep(0.5)
            screen = G.DEVICE.snapshot(quality=ST.SNAPSHOT_QUALITY)
            local_screen = aircv.crop_image(screen,(start_x,start_y,end_x,end_y))
            f_result = temp.match_all_in(local_screen)
            kkLogger_log(f"find_first_subImg：{f_result}")

            if f_result:
                #保存为图片
                pil_img = cv2_2_pil(local_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f'{snapImgPath}\\find_first_subImg_{current_millis}.png')

                print(f"f_result:{f_result}")  
                min_y = f_result[0]['result'][1]  # 假设f_result的第一个元素的第二个值是y坐标  
                min_y_item = f_result[0]['result']  

                # 遍历f_result找到y坐标最小的匹配项  
                for item in f_result:  
                    # 假设item是一个包含(x, y, w, h)的元组  
                    current_y = item['result'][1]
                    if current_y < min_y:  
                        min_y = current_y
                        min_y_item = item['result']
                print("Y坐标最小的匹配项:", min_y_item)  
                print("Y坐标:", min_y)  
                return min_y_item
            else:
                return None
        except Exception as e:
            kkLogger_log(f"find_first_subImg 失败：{e}")  
            sleep(2)
            continue  
    return None
#######################################################################

def ocr_text(start_x,start_y,end_x,end_y):
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()
            if screen is None:
                sleep(0.5)
                screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            ocr_result = ocr.ocr(cropped_screen, cls=True)

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_now_ocr_{current_millis}.png")

            return ocr_result
        except Exception as e:
            kkLogger_log(f"ocr_text 失败：{e}")  
            sleep(2)
            continue
    return None
###############################################################################
def ocr_text_all(target_text,start_x,start_y,end_x,end_y) :
    for i in range(5):
        try:
            if touch_fast is False:
                sleep(0.5)
              # 截屏当前画面
            pic_path=f"{snapImgPath}\\now_orc_all.png"
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            pil_img = cv2_2_pil(cropped_screen)
            pil_img.save(f"{snapImgPath}\\now_orc_all.png")

             # 使用PaddleOCR识别图片文字
            ocr_result = ocr.ocr(pic_path, cls=True)
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            target_coords_list = []
            for line in ocr_result:
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"ocr_text_all 识别到{textinfo}")
                    if target_text in textinfo:
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        target_coords_list.append(target_coords)
            kkLogger_log(f"识别到{target_text},数量：{len(target_coords_list)}")
            return target_coords_list
        except Exception as e:
            kkLogger_log(f"ocr_text_all 失败：{e}")  
            sleep(2)
            continue
    return None
################################################################################
def ocr_text_target_coords(target_text,start_x,start_y,end_x,end_y,is_wait=True) :
    if touch_fast is False and is_wait is True:
        sleep(0.2)
    for i in range(5):
        try:  
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_target_coords_{target_text}_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=True)
            if ocr_result is None:
                return None
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"识别到文字：{textinfo}")
                    if target_text in textinfo:
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        break
                if target_coords:
                    break

            # 使用Airtest点击坐标
            if target_coords:
                return target_coords
            else:
                kkLogger_log(f"未找到目标文字：{target_text}")
                return target_coords
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_target_coords：{e}")  
            continue
            
    return None
#########################################################################
def ocr_text_target_coords_arrays(target_text,start_x,start_y,end_x,end_y,hit_txts=None) :
    if touch_fast is False:
        sleep(0.5)
    target_coords_arrays = []
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))
            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_target_coords_arrays_{target_text}_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=True)
            if ocr_result is None:
                return target_coords_arrays
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"识别到文字：{textinfo}","info")
                    if target_text in textinfo:
                        if hit_txts is not None:
                            hit_txts.append(textinfo)
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        target_coords_arrays.append(target_coords)

            # 使用Airtest点击坐标
            if len(target_coords_arrays) > 0:
                return target_coords_arrays
            else:
                kkLogger_log(f"未找到目标文字：{target_text}")
                return target_coords_arrays
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_target_coords_arrays：{e}")  
            continue
            
    return target_coords_arrays
##ocr text#################################################################
def ocr_text_has_txt(start_x, start_y, end_x, end_y):
    if touch_fast is False:
        sleep(0.5)

    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_has_txt_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=True)
            if ocr_result is None or ocr_result is [None]:
                return False
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    if word_info is not None:
                        return True
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_txt：{e}")
            continue

    return False
#################################################
def ocr_text_has_number(start_x,start_y,end_x,end_y) :
    if touch_fast is False:
        sleep(0.5)
    
    for i in range(5):
        try:
            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x, end_y))

            if ocr_file == 1:
                pil_img = cv2_2_pil(cropped_screen)
                current_millis = int(round(datetime.datetime.now().timestamp()))
                pil_img.save(f"{snapImgPath}\\plog_ocr_text_has_number_{current_millis}.png")

            ocr_result = ocr.ocr(cropped_screen, cls=True)
            if ocr_result is None:
                return False
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    if bool(re.search(r'\d', textinfo)):
                        kkLogger_log(f"ocr_text_has_number查找到数字：{textinfo}")
                        return True

            return False
        except Exception as e:
            sleep(2)
            kkLogger_log(f"ocr_text_has_number：{e}")  
            continue
            
    return False
############################
##全屏截图方法，指定区域打码，去掉UUID
def save_cropped_screen_with_blur(modename, x1, y1, x2, y2):  
    sleep(1)
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    for i in range(5):
        try:  
            kkLogger_log(f"全屏截图，部分打码：{modename}")
            blur_region = (x1, y1, x2, y2)
            screen = G.DEVICE.snapshot()  
            
            cropped_screen = aircv.crop_image(screen, (1, 40, width, height))

            pil_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(pil_img)

            region_to_blur = pil_img.crop(blur_region)

            region_to_blur = region_to_blur.filter(ImageFilter.GaussianBlur(radius=20))

            pil_img.paste(region_to_blur, blur_region)

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg', 'JPEG')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_screen_with_mulblur：{e}")  
            continue
#########################################################
def kk_keyevent(key,times=1,sleep_time=0.8,msg=None):
    for i in range(times):
        keyevent(key)
        kkLogger_log(f"kk_keyevent：{key},msg:{msg}")
        sleep(sleep_time)
################################################################
def kk_scroll(coords,wheel_dist,sleep_time= 0.9,msg=None):
    device().mouse.scroll(coords=coords, wheel_dist=wheel_dist)
    sleep(sleep_time)
    if msg is not None:
        kkLogger_log(f"kk_scroll wheel_dist：{coords},wheel_dist:{wheel_dist},msg:{msg}")
##################################################
is_gonggao_checked = False
is_zhuangyuan_gonggao_checked = False
is_game_update_checked = False
def check_and_try_game_home(attempts=4,step=None):
    kkLogger_log(f"check_and_try_game_home: {attempts}")
    kk_keyevent("{ESC}", 1, 1, "回主页")
    kk_keyevent("{ESC}", 1, 1, "回主页")
    if is_local is False and step is None:
        check_yueka_jiazeng(1)
        check_huodong()
        check_account_login_other()
        check_game_update(1)
        check_game_alert_note()

    if attempts <= 0:
        kkLogger_log(f"尝试回到主页次数用完")
        return False

    if find_subImg(Template(r"tpl1728385666525.png"),1163+15,11+30,1268+10,70+20):
        kkLogger_log(f"当前正在游戏主页")
        return True

    else:
        kkLogger_log(f"当前不在游戏主页，尝试回到主页")
        kk_keyevent("{ESC}", 1, 1, "回主页")
        if local_ocr_text_target_coords("角色",1191,30,1275,300) is not None:
            kk_keyevent("{ESC}", 1, 1, "回主页2")
            return True
        return check_and_try_game_home(attempts - 1,step)
###########################################################################
def save_cropped_screen_with_mulblur(modename, blur_regions):  
    sleep(1)
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    for i in range(5):
        try:  
            kkLogger_log(f"全屏截图，部分打码：{modename}")

            screen = G.DEVICE.snapshot()  
            cropped_screen = aircv.crop_image(screen, (1, 39, width, height))
            
            pil_img = cv2.cvtColor(cropped_screen, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(pil_img)

            # 遍历所有需要模糊的区域  
            for blur_region in blur_regions:  
                x1, y1, x2, y2 = blur_region  
                # 确保区域在图片范围内  
                x1, y1, x2, y2 = max(0, x1), max(0, y1), min(width, x2), min(height, y2)  
                # 裁剪出需要模糊的区域  
                region_to_blur = pil_img.crop((x1, y1, x2, y2))  
                # 应用高斯模糊  
                region_to_blur = region_to_blur.filter(ImageFilter.GaussianBlur(radius=20))  
                # 将模糊后的区域粘贴回原图  
                pil_img.paste(region_to_blur, (x1, y1))  

            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg', 'JPEG')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_screen_with_mulblur：{e}")  
            continue
##全屏截图方法，去掉UUID
def save_cropped_screen(modename,file_endfix=".jpg"):
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    sleep(0.5)
    for i in range(2):
        try:  
            kkLogger_log(f"全屏截图：{modename}")
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            if width > 1290:
                cropped_screen = aircv.crop_image(screen, (1, 40, 1280, 720))
            else:
                cropped_screen = aircv.crop_image(screen, (1, 40, width, height))
            pil_img = cv2_2_pil(cropped_screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}{file_endfix}')
            
            return f"{modename}_{current_millis}{file_endfix}"
        except Exception as e:
            sleep(2)
            kkLogger_log(f"截图失败：{e},尺寸:{width},{height}")
            try:
                current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
                image_util.capture_screen(f"{snapImgPath}\\{modename}_{current_millis}{file_endfix}",region=(1, 40, 1280, 720))
                return f"{modename}_{current_millis}{file_endfix}"
            except Exception as e:
                kkLogger_log(f"capture_screen截图失败：{e}")
                continue


    
###################################################################
def save_cropped_area_screen(modename,start_x,start_y,end_x,end_y,sleep_time=0.8,file_endfix=".jpg"):
    if sleep_time > 0:
        sleep(sleep_time)
    if "_" not in modename:
        modename = f"{modename}_其他物品"
    for i in range(5):
        try:  
            kkLogger_log(f"区域截图：{modename}")
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}{file_endfix}')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen 截图失败：{e}")  
            continue
#############################################################################################
def save_cropped_area_screen_dazao(modename,start_x,start_y,end_x,end_y):
    # sleep(1)
    for i in range(5):
        try:  
            kkLogger_log(f"区域截图：{modename}")
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒  
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            
            tile_img_2 = np.array(pil_img)
            if contains_purple_in_corner(tile_img_2,(55,29)):
                pil_img.save(f'{snapImgPath}\\{modename}_1_{current_millis}.jpg')
            elif contains_gold_in_corner(tile_img_2,(55,29)) and not contains_pink_in_corner(tile_img_2,(55,29)):
                pil_img.save(f'{snapImgPath}\\{modename}_0_{current_millis}.jpg')
            else:
                pil_img.save(f'{snapImgPath}\\{modename}_2_{current_millis}.jpg')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen 截图失败：{e}")  
            continue
######################################################################################
# 计算图像的哈希值  
tianniran_saved_hashes = set()  
def save_cropped_area_screen_tianniran(modename,start_x,start_y,end_x,end_y):
    sleep(1)
    for i in range(5):
        try:  
            kkLogger_log(f"save_cropped_area_screen_tianniran区域截图：{modename}")
            screen = G.DEVICE.snapshot()
            if touch_fast is False:
                sleep(0.5)
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)
            
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            pil_img.save(f'{snapImgPath}\\{modename}_{current_millis}.jpg')
            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen_tianniran 截图失败：{e}")  
            continue
######################################################################
def save_cropped_area_screen_and_ocr_add_to_get(modename,start_x,start_y,end_x,end_y,split_num=None,temp=None):
    if touch_fast is False:
        sleep(0.5)
    else:
        pass
    for i in range(5):

        # tianniran_ocr_txt_arrays.add("野月")
        # tianniran_ocr_txt_arrays.add("无常白袍")
        # if modename == "发式":
        #     for item in tianniran_ocr_txt_arrays:
        #         target = ocr_text_target_coords(item, start_x, start_y, end_x, end_y)
        #         if target is not None:
        #             kkLogger_log(f"发现自染头{item}")
        #             kktouch2(target[0], target[1], 1, "自染头")
        #             save_cropped_area_screen("天赏发式", 566, 43, 982, 692)
        #         else:
        #             kkLogger_log(f"未发现自染头{item}")


        try:  
            kkLogger_log(f"区域截图：{modename}")
            screen = G.DEVICE.snapshot()
            cropped_screen = aircv.crop_image(screen, (start_x, start_y, end_x,end_y))
            pil_img = cv2_2_pil(cropped_screen)

            if split_num is not None:
                # 获取图像的尺寸  
                img_width, img_height = pil_img.size  
                # 计算切割后的小图片尺寸（这里假设均匀切割）  
                tile_width = img_width // split_num[1]  
                tile_height = img_height // split_num[0]
                # 切割图像并保存  
                for row in range(split_num[0]):  
                    for col in range(split_num[1]): 
                        left = col * tile_width
                        upper = row * tile_height  
                        right = (col + 1) * tile_width
                        lower = (row + 1) * tile_height
                        tile_img = pil_img.crop((left, upper, right, lower))  
                        
                        tile_img_2 = np.array(tile_img)
                        if temp is not None and find_subImg_inscreen(temp,tile_img_2) is not None:
                            kkLogger_log(f"find_subImg_inscreen 发现锁")
                            continue
                        
                        result = (True,False)
                        for i in range(2):
                            result = ocr_screen_add_to_get(tile_img_2)
                            if result[1]:
                                break
                            else:
                                kkLogger().debug(f"ocr_screen_add_to_get 未识别到文字，重试一次")
                        
                        if result[0] is False:
                            continue
                        current_millis = int(round(datetime.datetime.now().timestamp() * 1000))  
                        if contains_yellow_in_corner(tile_img_2,(15,15)):
                            # 保存切割后的小图片  
                            tile_img.save(f'{snapImgPath}\\{modename}_天赏外观_0_{current_millis}{row}{col}.jpg')
                        else:
                            tile_img.save(f'{snapImgPath}\\{modename}_普通外观_1_{current_millis}{row}{col}.jpg')

            break
        except Exception as e:
            sleep(2)
            kkLogger_log(f"save_cropped_area_screen_and_ocr_add_to_get 截图失败：{e}")  
            continue
###############################################################################################################
def contains_pink_in_corner(tile_img_np, region_size):  
    try:  
        height, width, _ = tile_img_np.shape  

        x_end = region_size[0] 
        y_end = region_size[1]  
        corner_img = tile_img_np[22:y_end, 10:x_end]
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  

        lower_gold = np.array([233, 103, 94])  # 金黄色的下限（色调、饱和度、明度）  
        upper_gold = np.array([234, 108, 102])  # 金黄色的上限  

        mask = cv2.inRange(hsv_img, lower_gold, upper_gold)

        return np.any(mask != 0)  
    except Exception as e:  
        return False
########################################################
def contains_gold_in_corner(tile_img_np, region_size):  
    try:  
        height, width, _ = tile_img_np.shape  

        x_end = region_size[0] 
        y_end = region_size[1]  
        corner_img = tile_img_np[22:y_end, 10:x_end]
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  

        lower_gold = np.array([15, 100, 100])  # 金黄色的下限（色调、饱和度、明度）  
        upper_gold = np.array([45, 255, 255])  # 金黄色的上限  

        mask = cv2.inRange(hsv_img, lower_gold, upper_gold)

        return np.any(mask != 0)  
    except Exception as e:  
        return False
###############################################################################
def contains_purple_in_corner(tile_img_np, region_size):  
    try:  
        height, width, _ = tile_img_np.shape  

        x_end = region_size[0]  # 左上角区域的右边界  
        y_end = region_size[1]  # 左上角区域的下边界  

        corner_img = tile_img_np[0:y_end, 0:x_end]  
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  

        lower_purple = np.array([120, 30, 150])  # 紫色下限（色调、饱和度、明度）  
        upper_purple = np.array([160, 200, 255])  # 紫色上限（注意：HSV色调是循环的，所以180接近0）  

        # 创建紫色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_purple, upper_purple)  

        return np.any(mask != 0)  
    except Exception as e:  
        return False

###############################################################################
def contains_yellow_in_corner(tile_img_np, region_size):   
    try:
        height, width, _ = tile_img_np.shape  
        # 计算右下角区域的左上角坐标  
        x_start = width-10 - region_size[0]
        y_start = height-10 - region_size[1]
        corner_img = tile_img_np[y_start:y_start+region_size[1], x_start:x_start+region_size[0]]  
        hsv_img = cv2.cvtColor(corner_img, cv2.COLOR_RGB2HSV)  
        lower_yellow = np.array([15, 100, 100])  
        upper_yellow = np.array([35, 255, 255])  

        # 创建黄色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_yellow, upper_yellow)  

        # 检查掩码中是否存在非零像素，以确定是否存在黄色  
        return np.any(mask != 0)  
    except Exception as e:
        return False
#################################################################
def contains_yellow(tile_img_np):  
    try:
        hsv_img = cv2.cvtColor(tile_img_np, cv2.COLOR_RGB2HSV)  

        lower_yellow = np.array([15, 100, 100])  
        upper_yellow = np.array([35, 255, 255])  

        # 创建黄色区域的掩码  
        mask = cv2.inRange(hsv_img, lower_yellow, upper_yellow)  

        # 检查掩码中是否存在非零像素，以确定是否存在黄色  
        return np.any(mask != 0)  
    except Exception as e:
        return False
###############################################################################
#识别到的名称放入meta中
def add_to_get(ocr_text):
    return ocr_check_and_add(ocr_text)
    
#####################################################################
def add_to_json_array(json_array, name, value):
    new_json_object = {"name": name, "value": value}
    json_array.append(new_json_object)  
    return json_array
################################################

def kktouch(x, y, sleep_time):  
    touch((x,y))
    sleep(sleep_time)
######################################################

############################################################################
def kktouch_step(x, y, sleep_time,msg):
    if msg == "外观":
        kk_keyevent("{F12}")
        if ocr_text_target_coords("时", 198, 690, 358, 740) is not None:
            kkLogger_log(f"快捷键前往：{msg}，成功")
            return True
        else:
            kkLogger_log(f"快捷键前往：{msg}，失败，尝试点击前往")
            return kktouch2(x, y, sleep_time, msg)
    if msg == "角色":
        return kktouch2(x, y, sleep_time, msg)
    if msg == "打造":
        kk_keyevent("{F10}")
        kktouch2(106,684,1,"快捷打造")
        sync_current_snapshot("开始打造。")
        if ocr_text_target_coords("打", 95, 42,187, 102) is not None:
            kkLogger_log(f"快捷键前往：{msg}，成功")
            return True
        else:
            kkLogger_log(f"快捷键前往：{msg}，失败，尝试点击前往")
            return kktouch2(x, y, sleep_time, msg)
####################################################################
step_names = ["菜单","外观","角色","打造","武功","设置","开放世界","宠物","群侠","庄园"]
##########################################
##############################################
def kktouch2(x, y, sleep_time,msg,attempts=4):

    if is_debug:
        save_cropped_area_screen(f"点击：{msg}_{x}_{y}", x-30, y-30, x+30, y+30, 0.2,".png")

    global  touch_fast
    if attempts <= 0:
        return False

    if attempts <2:
        kkLogger_log(f"点击[{msg}],attempts:{attempts}，转换为普通模式")
        touch_fast = False

    if msg not in step_names and sleep_time < 1.1:
        sleep_time = 0.3

    if msg == "菜单":
        kk_keyevent("{ESC}",1,2,"菜单")
        return True

    kkLogger_log(f"点击[{msg}],attempts:{attempts}")
    touch((x,y))
    sleep(sleep_time)
    if msg == "菜单":
        coords = local_ocr_text_target_coords("外",1010, 129,1210, 305)
        sync_current_snapshot(f"菜单{attempts}")
        if coords is None:
            check_and_try_game_home()
            return kktouch2(1224+10, 32+37, 1, "菜单",attempts-1)
        return True
    if msg == "外观":
        sync_current_snapshot(f"外观{attempts}")
        coords = local_ocr_text_target_coords("时",198, 690,358, 740)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")

            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "角色":
        sync_current_snapshot(f"角色-背包{attempts}")
        coords = local_ocr_text_target_coords("角色",11, 92,143, 347)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 , 32 , 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "打造":
        sync_current_snapshot(f"打造{attempts}")
        coords = local_ocr_text_target_coords("打",95, 42,187, 102)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts - 1)
        return True
    if msg == "武功":
        sync_current_snapshot(f"武功{attempts}")
        coords = local_ocr_text_target_coords("功",12, 92,140, 499)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "设置":
        sync_current_snapshot(f"设置{attempts}")
        coords = local_ocr_text_target_coords("下",8, 196,160, 676)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "开放世界":
        sync_current_snapshot(f"开放世界{attempts}")
        return True
    if msg == "宠物":
        sync_current_snapshot(f"宠物{attempts}")
        coords = local_ocr_text_target_coords("宠物",105, 45,194, 98)
        if coords is None:
            check_and_try_game_home()
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            return kktouch2(x, y,sleep_time,msg,attempts-1)
        return True
    if msg == "群侠":
        sync_current_snapshot(f"群侠{attempts}")
        coords = local_ocr_text_target_coords("群",8, 84,158, 344)
        if coords is None:
            return False
        return True
    if msg == "庄园":
        sync_current_snapshot(f"庄园{attempts}")
        coords = local_ocr_text_target_coords("庄园",98, 45,183, 97)
        if coords is None:
            return False
        return True

    return True
#########################################################
def swipe_to_end(judge_area,swipe_start,swipe_end,judge_blank=False):
    if swipe_start[1] < swipe_end[1]:
        kk_scroll(swipe_start, 20,2,"swipe_to_end")
    else:
        kk_scroll(swipe_start, -20,2,"swipe_to_end")
###############################################################

#########################################################
def swipe_to_end_slow(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and (judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    for i in range(40):
        try:
            start_screen = G.DEVICE.snapshot()
            cropped_start_screen = aircv.crop_image(start_screen, judge_area)
            swipe(swipe_start, swipe_end, duration=2)
            sleep(3)
            end_screen = G.DEVICE.snapshot()
            cropped_end_screen = aircv.crop_image(end_screen, judge_area)
            confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
            kkLogger_log(f"{confidence},times:{i}")
            if confidence > 0.8:
                break
        except Exception as e:
            kkLogger_log(f"swipe_to_end_slow error：{e}")
            continue

#########################################################################
def swipe_and_judge_end(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and ocr_text(judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen,judge_area)

    # swipe(swipe_start, swipe_end,steps=35, duration=7)
    swipe(swipe_start, swipe_end, steps=15, duration=3)
    sleep(2)

    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, judge_area)
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
    kkLogger_log(f"confidence：{confidence}")
    if confidence > 0.8:
        return True
    else:
        return False
##################################################################
def scroll_and_judge_end(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and ocr_text(judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen,judge_area)


    # swipe(swipe_start, swipe_end)
    # sleep(2)
    height = abs(swipe_end[1] - swipe_start[1])
    kkLogger_log(f"================height:{height}")
    scroll_count = height // (575 - 285)
    coords = ( (judge_area[2] + judge_area[0]) //2, (judge_area[3] + judge_area[1]) //2)
    kkLogger_log(f"================scroll_count:{scroll_count}，coords：{coords}")
    if swipe_end[1] > swipe_start[1]:
        kk_scroll(coords,scroll_count,2,"scroll_and_judge_end")
    else:
        kk_scroll(coords, -scroll_count, 2,"scroll_and_judge_end")

    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, judge_area)
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
    kkLogger_log(f"confidence：{confidence}")

    # sleep(3)
    if confidence > 0.8:
        return True
    else:
        return False

#####################################################
def swipe_and_judge_end_fast(judge_area,swipe_start,swipe_end,judge_blank=False):
    if judge_blank and ocr_text(judge_area[0],judge_area[1],judge_area[2],judge_area[3]) == [None]:
        kkLogger_log(f"空白区域,无需滚动")
        return True

    start_screen = G.DEVICE.snapshot()
    cropped_start_screen = aircv.crop_image(start_screen,judge_area)


    swipe(swipe_start, swipe_end)
    sleep(2)

    end_screen = G.DEVICE.snapshot()
    cropped_end_screen = aircv.crop_image(end_screen, judge_area)
    confidence = cal_ccoeff_confidence(cropped_start_screen, cropped_end_screen)
    kkLogger_log(f"confidence：{confidence}")
    if confidence > 0.8:
        return True
    else:
        return False
###########################################################
def init(log_queue=None, fast=False, save_original=False):
    if log_queue:
        global logger_queue
        logger_queue = log_queue
    global touch_fast
    touch_fast = fast

    global save_original_pic
    save_original_pic = save_original
########################################################################################
################################################
def init_local_nsh(l_task):
    global center_x
    global center_y
    global width
    global height
    global start_time
    global snapImgPath
    global projectPath
    global luhao_task
    global logger
    global product_meta
    global is_local

    is_local = True

    center_x = width // 2
    center_y = height // 2

    luhao_task = l_task
    productSn = luhao_task['productSn']


    nowTime = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    projectPath = "D:\\kkzhw\\airtest_log"
    snapImgPath = f"{projectPath}\\{productSn}_{nowTime}"

    clear_folder2(snapImgPath)

    logging.getLogger("airtest").setLevel(logging.INFO)
    if logger is None:
        logger = logger_config.setup_logger(os.path.basename(__file__),
                                            log_file_name=f'{snapImgPath}\\{productSn}_runtime.log')


    for i in range(120):
        sleep(2)
        try:
            connect_device("windows:///?title_re=MuMu模拟器12")
            kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
            width, height = device().get_current_resolution()
            if width < 1280:
                print(f"设备错误或非初始界面，请调整，第{i}/{120}次")
                continue
            else:
                break
        except Exception as e:
            print(e)
            continue

    check_game_home(1000000)

    print(f"设备屏幕宽：{width} 高：{height}")
    print(f"窗口标题 {G.DEVICE.get_title()}")
    G.DEVICE.move((0, 0))

    center_x = width // 2
    center_y = height // 2

    kkLogger_log(f"屏幕 宽：{width}，高：{height}")
    kkLogger_log(f"图片路径：{snapImgPath}")

    if logger is None:
        logger = logger_config.setup_logger(os.path.basename(__file__),
                                            log_file_name=f'{snapImgPath}\\{productSn}_runtime.log')
        kkLogger_log(f"屏幕 宽：{width}，高：{height}")
        kkLogger_log(f"图片路径：{snapImgPath}")

    product_meta = l_task['product_meta']

    check_game_gonggao()
###################################################
def init_env():
    global center_x
    global snapImgPath
    global logger
    global logger2
    global width
    global height

    global center_x
    global center_y
    global product_meta
    global luhao_task
    global luhao_failed
    global pic_url_arrays
    global tianniran_ocr_txt_arrays
    global ocr_file
    global touch_fast
    global start_time
    global end_time
    global is_local
    global save_original_pic
    global is_debug



    snapImgPath = ""
    logger = None
    logger2 = None
    # 设置全局的截图精度为90
    ST.SNAPSHOT_QUALITY = 99
    # 获取当前设备的屏幕宽度和高度
    width = 1
    height = 1
    # width, height = device().get_current_resolution()
    center_x = 1
    center_y = 1
    product_meta = []
    luhao_task = None
    luhao_failed = False
    pic_url_arrays = []
    tianniran_ocr_txt_arrays = set()
    ocr_file = 1  # ocr 是否保存识别区域图片 1保存图片 0不保存
    start_time = datetime.datetime.now()
    end_time = datetime.datetime.now()
    is_debug = True

    # try:
    #     android_dev = connect_device("Android://127.0.0.1:5037/127.0.0.1:7555?ori_method=adbori")
    #     kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
    # except Exception as e:
    #     print(f"adb连接mumu模拟器失败,退出录号，{e}")
    #     return False

    # is_ok = sys_tool.restart_nsh_luhao()
    # if not is_ok:
    #     print(f"录号工具启动失败退出录号。。。。")
    #     return False
    # try:
    #     sleep(5)
    #     connect_device("windows:///?title_re=看看账号网逆水寒手游录号工具.*")
    # except Exception as e:
    #     print(f"录号工具连接失败退出录号,{e}")
    #     return False

    return True

#################################################################
def safe_step_gui_task(is_skip = 0):

    for i in range(3):
        try:
            task = portal_client.get_task_info(luhao_task["id"])
            kkLogger_log(f"获取到任务：{task}")
            task_id = task["id"]
            device_id = task["deviceId"]

            connect_device("windows:///?title_re=看看账号网逆水寒手游录号工具.*")

            kktouch2(116, 101, 1, "客服账号输入框")
            keyevent("^a")
            keyevent("{DELETE}")
            kktouch2(116, 101, 1, "客服账号输入框")
            text(f"DID:{device_id}")



            kktouch2(147, 171, 1, "编号输入框")
            keyevent("^a")
            keyevent("{DELETE}")
            kktouch2(147, 171, 1, "编号输入框")
            text(f"TID:{task_id}")
            kktouch2(291, 170, 2, "开始任务")
            connect_device("windows:///?title_re=MuMu模拟器12")
            break
        except Exception as e:
            kkLogger_log(f"safe_step_gui_task, {e}")
            sleep(5)
            continue

    for i in range(240): #最多40分钟
        try:
            task = portal_client.get_task_info(luhao_task["id"])
            if task is None:
                sleep(5)
                continue
            task_status = task.get("status", "None")

            global end_time
            end_time = datetime.datetime.now()
            total_time = end_time - start_time
            minutes = total_time.total_seconds() // 60

            kkLogger_log(f"当前任务状态：{task_status}，已执行：{minutes}分钟")
            if task_status == "COMPLETED":
                break
            elif task_status == "FAILED":
                break
            elif task_status == "CANCELLED":
                break
            else:
                sleep(10)
                continue
        except Exception as e:
            kkLogger_log(f"safe_step_gui_task, {e}")
            sleep(5)
            continue
#################################################################
def init_nsh(l_task):
    init_success = init_env()
    if not init_success:
        return get_return_taskEvent("step_init", EventStatus.FAILURE, "初始化失败")

    global center_x
    global center_y
    global width
    global height
    global start_time
    global snapImgPath
    global projectPath
    global luhao_task
    global logger
    global product_meta

    start_time = datetime.datetime.now()

    connect_device("windows:///?title_re=MuMu模拟器12.*")
    kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
    width, height = device().get_current_resolution()
    print(f"设备屏幕宽：{width} 高：{height}")
    print(f"窗口标题 {G.DEVICE.get_title()}")
    G.DEVICE.move((0,0))



    center_x = width // 2
    center_y = height // 2


    luhao_task = l_task
    productSn = luhao_task['productSn']


    nowTime = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    projectPath = "D:\\kkzhw\\airtest_log"
    snapImgPath = f"{projectPath}\\{productSn}_{nowTime}"

    clear_folder2(snapImgPath)

    logging.getLogger("airtest").setLevel(logging.INFO)
    if logger is None:
        logger = logger_config.setup_logger(os.path.basename(__file__), log_file_name=f'{snapImgPath}\\{productSn}_runtime.log')
        kkLogger_log(f"屏幕 宽：{width}，高：{height}")
        kkLogger_log(f"图片路径：{snapImgPath}")


    product_meta = l_task['product_meta']

    # check_game_gonggao()
    # check_game_window()
    # check_game_gonggao()
    # check_game_window()
    kkLogger_log(f"touch_false:{touch_fast},ocr_file:{ocr_file}")

    kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")


    return get_return_taskEvent("step_init", EventStatus.SUCCESS, "初始化")

########################################################################################
def kkLogger():
    global logger
    global logger2
    if logger is None:
        if logger2 is None:
            # logger2 = logger_config.setup_logger(os.path.basename(__file__), log_file_name=f'{projectPath}\\nsh_runtime.log')
            logger2 = logger_config.setup_logger(os.path.basename(__file__), log_file_name=f'{snapImgPath}\\nsh_runtime.log')

        return logger2
    else:
        return logger
########################################################################
def kkLogger_log(msg,level="info"):
    print(f"{msg}")

    global logger_queue
    try:
        if "info" == level:
            kkLogger().info(msg)
            if logger_queue:
                logger_queue.put(msg)
        elif "error" == level:
            kkLogger().error(msg)
        elif "debug" == level:
            kkLogger().debug(msg)
        else:
            print(msg)
            if logger_queue:
                logger_queue.put(msg)
    except Exception as e:
        print(f"kkLogger_log {msg} exception：:{e}")
######################################################################
def check_game_home(counts):
    for i in range(counts):
        sleep(2)
        if exists(Template(r"tpl1728385666525.png", record_pos=(0.471, -0.26), resolution=(1278, 720))):
            touch((1240,60))
            sleep(1)
            coords = local_ocr_text_target_coords("角色", 1198, 91, 1280, 236)
            if coords is not None:
                touch((1249,63))
                sleep(1)
                print(f"当前正在游戏主页")
                return True

            print(f"当前不在游戏登录成功角色主页，第{i}次")
            continue
        else:
            print(f"当前不在游戏登录成功角色主页，第{i}次")
            continue
    return False

#########################################################

def get_head_pic():
    pic = None
    for item in pic_url_arrays:
        if item['name'] == "头图":
            pic = item['value']
            break
    if pic is None:
        pic = pic_url_arrays[0]['value']
    return pic
#######################################################################


##################################################################
def ocr_add_to_get(start_x,start_y,end_x,end_y):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    add_to_get(get_ocr_txt)
                    kkLogger_log(f"ocr_add_to_get ocr_get：{get_ocr_txt}")
    else:
        kkLogger_log("ocr nothing")
###########################################################

def ocr_screen_add_to_get(cropped_screen):
    try:
        has_text = False
        if cropped_screen is not None:
            ocr_result = ocr.ocr(cropped_screen, cls=True)
            if ocr_result:
                for line in ocr_result:
                    if line is None:
                        continue
                    for word_info in line:
                        if word_info is None:
                            continue
                        else:
                            get_ocr_txt = word_info[1][0]
                            has_text = True
                            if any(keyword in get_ocr_txt for keyword in configs.lock_keywords):
                                kkLogger_log(f"ocr_screen_add_to_get 识别到非解锁字符 ：{get_ocr_txt}")
                                return (False,has_text)
                            add_to_get(get_ocr_txt)
                            kkLogger_log(f"ocr_screen_add_to_get ：{get_ocr_txt}")
                return (True,has_text)
            else:
                kkLogger_log("ocr_screen_add_to_get ocr nothing")
                return (False,has_text)
        else:
            kkLogger_log("ocr_screen_add_to_get cropped_screen is none")
            return (False,has_text)
    except Exception as e:
        return (False,has_text)
        kkLogger_log(f"ocr_screen_add_to_get error：{e}")

##################################################################
def ocr_add_to_get_attr(start_x,start_y,end_x,end_y,attr_names):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    for a_name in attr_names:
                        ocr_check_and_add_attr(get_ocr_txt,a_name)
                    kkLogger_log(f"ocr_add_to_get_attr ocr_get：{get_ocr_txt}")
    else:
        kkLogger_log("ocr nothing")

################################################################################
def ocr_add_to_get_and_count_tianniran_number(start_x,start_y,end_x,end_y):
    tianniran_count = 0
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]

                    get_ocr_txt = get_ocr_txt.strip()
                    kkLogger_log(f"ocr_add_to_get_and_count_tianniran_number ：{get_ocr_txt}")
                    if "/" in get_ocr_txt:
                        tianniran_count_str  = get_ocr_txt.split("/")[0]
                        try:
                            tianniran_count = int(tianniran_count_str)//2
                            if tianniran_count >= 1:
                                ocr_check_and_set("天霓染",str(tianniran_count))
                                kkLogger_log(f"ocr_add_to_get_and_count_tianniran_number 天霓染数量 ：{tianniran_count}")
                            break
                        except Exception as e:
                            kkLogger_log(f"ocr_add_to_get_and_count_tianniran_number ：{e}")
                    else:
                        kkLogger_log(f"ocr_add_to_get_and_count_tianniran_number [{get_ocr_txt}]，未识别到 /")

    else:
        kkLogger_log("ocr_add_to_get_and_count_tianniran ocr nothing")
    return tianniran_count
##################################################################################
def ocr_add_to_get_and_count_tianniran(start_x,start_y,end_x,end_y,skip_word):
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]

                    get_ocr_txt = get_ocr_txt.strip()
                    if(len(get_ocr_txt) == 1):
                        continue
                    if skip_word not in get_ocr_txt and "点" not in get_ocr_txt and "案" not in get_ocr_txt:
                        tianniran_ocr_txt_arrays.add(get_ocr_txt)
                    kkLogger_log(f"ocr_add_to_get_and_count_tianniran ocr_get：{get_ocr_txt}")
    else:
        kkLogger_log("ocr_add_to_get_and_count_tianniran ocr nothing")
    kkLogger_log(f"tianniran_ocr_txt_arrays ：{tianniran_ocr_txt_arrays}")
#################################################################################



#################################################################################
def ocr_check_and_add(ocr_txt):
    global product_meta
#     kkLogger_log(f"ocr_check_and_add ocr_txt:{ocr_txt}")
    ocr_txt = ocr_txt.strip()
    ocr_txt = remove_unwanted_chars(ocr_txt)

    if len(ocr_txt) == 1 or len(ocr_txt) == 0:
        return
    ocr_txt2 = configs.ERROR_TEXT.get(ocr_txt, ocr_txt)
    ocr_txt2 = configs.black_attr_value.get(ocr_txt2,ocr_txt2)
#     kkLogger_log(f"ocr_check_and_add ocr_txt2:{ocr_txt}")
    hit_item_name_value = None
    for item in product_meta:
        if item["type"] not in [1,2]:
            continue
        if item["inputList"] and item["name"] not in configs.SKIP_ATTRI_NAME:
            input_arrays = item["inputList"].split(',')
            values = item.get("values", [])
            for input_value in input_arrays:
                input_value_2 = remove_unwanted_chars(input_value)
                if ocr_txt2 == input_value_2:
                    hit_item_name_value = item["name"]+":"+ocr_txt2
                    if input_value not in values:
                        values.append(input_value)
                    kkLogger_log(f'get attr {item["name"]}:{values}')
#                     else:
#                         kkLogger_log(f"{input_value} already in {item["name"]}:{values}")

            item["values"] = values
    return hit_item_name_value
################################################################################
def ocr_check_and_add_attr(ocr_txt,attr_name):
    global product_meta
    kkLogger_log(f"ocr_check_and_add_attr ocr_txt:{ocr_txt}")
    ocr_txt = ocr_txt.strip()
    ocr_txt = remove_unwanted_chars(ocr_txt)

    if len(ocr_txt) == 1 or len(ocr_txt) == 0:
        return
    ocr_txt2 = configs.ERROR_TEXT.get(ocr_txt, ocr_txt)
    ocr_txt2 = configs.black_attr_value.get(ocr_txt2,ocr_txt2)
    kkLogger_log(f"ocr_check_and_add ocr_txt2:{ocr_txt}")
    for item in product_meta:
        if item["type"] not in [1,2]:
            continue
        if item["inputList"] and item["name"] == attr_name:
            input_arrays = item["inputList"].split(',')
            values = item.get("values", [])
            for input_value in input_arrays:
                input_value_2 = remove_unwanted_chars(input_value)
                if ocr_txt2 == input_value_2:
                    if input_value not in values:
                        values.append(input_value)
                        kkLogger_log(f"{input_value} append in {values}")
                    else:
                        kkLogger_log(f"{input_value} already in {values}")

            item["values"] = values  # 更新字典中的values键
    return ocr_txt2
#############################################################
def category_meta_item(item_name):
    for item in product_meta:
        if item["name"] == item_name:
            return item
        else:
            return None
##############################################################################
def get_attr_ocr_number(attr_name,start_x,start_y,end_x,end_y):
    has_yipin = False
    ocr_result = ocr_text(start_x,start_y,end_x,end_y)
    if ocr_result:
        for line in ocr_result:
            if line is None:
                continue
            for word_info in line:
                if word_info is None:
                    continue
                else:
                    get_ocr_txt = word_info[1][0]
                    if contains_digit(get_ocr_txt):
                        ocr_check_and_set_number(attr_name,get_ocr_txt)
                        kkLogger_log(f"get_attr_ocr_number {attr_name}：{get_ocr_txt}")
                        has_yipin = True
    else:
        kkLogger_log(f"未获取到{attr_name}")

    return has_yipin


########################################################################
def ocr_check_and_set(attrName,ocr_txt):
    global product_meta
    for item in product_meta:
        if item["name"] == attrName:
            values = item.get("values", [])
            values.append(ocr_txt)
            item["values"] = values
            kkLogger_log(f"ocr_check_and_set: {item}")
            return ocr_txt
######################################################
def ocr_check_and_set_number(attrName,ocr_txt):
    global product_meta
    match = re.search(r'\d+', ocr_txt)
    value_number = match.group(0) if match else ""

    for item in product_meta:
        if item["name"] == attrName:
            values = item.get("values", [])
            values.append(value_number)
            item["values"] = values
            if attrName == "充值金额":
                ocr_check_and_set("充值称号",get_chongzhi_chenghao(value_number))
                print(f"充值称号================：{get_chongzhi_chenghao(value_number)}")
            kkLogger_log(f"ocr_check_and_set_number {item}")
            return ocr_txt
################################################################
def get_chongzhi_chenghao(num):
    sorted_chongzhi_chenghao = sorted(configs.chongzhi_chenghao, key=lambda x: x[1], reverse=True)
    for item in sorted_chongzhi_chenghao:
        if int(num) > item[1]:
            return item[0]
    return ""
################################################################
def get_luhao_snapshot():
    step_login_pic = save_cropped_screen("luhao_snapshot",".png")
    oss_file = upload_one_img_to_oss(step_login_pic)
    the_state_img = configs.image_server_url+oss_file
    return the_state_img
#######################################################
def check_game_window(check_login=False):
    connect_device("windows:///?title_re=MuMu模拟器12")
    kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")

    width2, height2 = device().get_current_resolution()
    if width2 > 1280:

        #判断是否存在扫码弹窗
        coords = local_ocr_text_target_coords("扫码登录", 472, 212, 804, 552,False)
        if coords is not None:
            kktouch2(802 , 163 , 1, "关闭登录弹窗")
            return True
        # 判断是否存在扫码超时弹窗
        coords = local_ocr_text_target_coords("登录失败", 472, 212, 804, 552,False)
        if coords is not None:
            kktouch2(802 , 163 , 1, "关闭登录弹窗")
            return True
        # 判断是否上一个游戏登录状态
        if check_login and check_and_try_game_home(1,"init"):
            kk_keyevent("{ESC}", 1, 1, "回主页")
            if local_ocr_text_target_coords("角色", 1191, 30, 1275, 300,False) is not None:
                step_logout(0)
                connect_device("windows:///?title_re=MuMu模拟器12")
                kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
            return True
    else:
        sleep(2)
        connect_device("windows:///")
        kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
        coords = local_ocr_text_target_coords("扫码登录",472,212,804,552,False)
        if coords is not None:
            kktouch2(802,163,1,"关闭登录弹窗")
        return True
###########################################################################################################
def check_mumu_game_ready():
    for i in range(5):
        # sleep(1)
        target = ocr_text_target_coords("详细", 399, 456, 910, 634,False)
        if target is not None:
            kktouch2(467, 579, 1, "同意")
            return True
        #登录状态则退出
        target = ocr_text_target_coords("退出", 872, 20, 1270, 214,False)
        if target is not None:
            kktouch2(target[0], target[1]-10, 1.2, "右上退出")
            kktouch2(746,461-33,1.1,"弹窗退出")

        target = ocr_text_target_coords("其他账号登录", 330, 417, 920, 626,False)
        if target is not None:
            kktouch2(target[0],target[1],1.2,"其他账号登录")

        target = ocr_text_target_coords("详细", 399, 456, 910, 634,False)
        if target is not None:
            kktouch2(467,579,1,"同意")
            return True
        else:
            continue
    return False

######################################################################################################3
def check_game_ready():
    for i in range(600):
        # check_game_window()
        if device().get_current_resolution()[0] < 1280:
            kkLogger_log(f"游戏非初始界面:{device().get_current_resolution()}")
            # sync_current_snapshot(f"游戏非初始界面。尺寸：{device().get_current_resolution()}")
            connect_device("windows:///?title_re=MuMu模拟器12")
            kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
            continue
        coords = local_ocr_text_target_coords("选择",468, 422,858, 574,False)
        if coords is not None :
            kktouch2(1240,100,1,"右上账号")
            kktouch2(647,508,1,"使用其他账号登录")
            # kktouch2(634, 406, 2, "进入游戏")
            return True
        else:
            # sync_current_snapshot("游戏非初始界面，未识别到：选择服务器")
            kkLogger_log(f"游戏非初始界面，未识别到选择服务器")
            sleep(1)
            continue
    return False
######################################################
def sync_login_success(msg="登录成功"):
    for i in range(3):
        try:
            global luhao_task
            # luhao_task["snapshot"] = get_luhao_snapshot()
            # luhao_task["stage"] = "step_login"
            # luhao_task["msg"] = msg
            # propertyBag = {
            #     "state": "login_success",
            #     "state_img": get_luhao_snapshot()
            # }
            luhao_task["snapshot"] = ""
            luhao_task["stage"] = "step_login"
            luhao_task["msg"] = msg
            propertyBag = {
                "state": "login_success",
                "state_img": ""
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            break
        except Exception as e:
            print(f"sync_login_success：{e}")
            sleep(1)
            continue
################################################################
def sync_login_fail(msg="登录失败"):
    for i in range(3):
        try:
            global luhao_task
            luhao_task["snapshot"] = get_luhao_snapshot()
            luhao_task["stage"] = "step_login"
            luhao_task["msg"] = msg
            propertyBag = {
                "state": "login_fail",
                "state_img": get_luhao_snapshot()
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            break
        except Exception as e:
            print(f"sync_login_fail：{e}")
            sleep(1)
            continue
#####################################################
def sync_current_snapshot(msg="录号同步",now_status=None,is_snapshot=True):
    try:
        return True

        global luhao_failed
        global luhao_task
        luhao_task["msg"] = msg
        if is_snapshot:
            luhao_task["snapshot"] = get_luhao_snapshot()
        if now_status is not None:
            luhao_task["status"] = now_status
        else:
            luhao_task["status"] = 'IN_PROGRESS'
        portal_client.sync_task_info(luhao_task)

        if "FAILED" == now_status:
            luhao_failed = True

    except Exception as e:
        print(f"sync_current_snapshot：{e}")
###########################################################################
def up_qrcode_pic():

    current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
    image_util.capture_screen(f"{snapImgPath}\\luhao_step_{current_millis}.png",
                                             region=(476, 180, 818, 614))
    oss_file = upload_one_img_to_oss(f"{snapImgPath}\\luhao_step_{current_millis}.png")
    the_state_img = configs.image_server_url + oss_file
    if ocr_text_target_coords("成功", 476, 180, 818, 614) is not None:
        propertyBag = {
            "state": "need_show_qrcode_done",
            "state_img": the_state_img
        }
        luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
        luhao_task["qrcode"] = the_state_img
        luhao_task["status"] = "IN_PROGRESS"
        portal_client.sync_task_info(luhao_task)
    else:
        propertyBag = {
            "state": "need_show_qrcode",
            "state_img": the_state_img
        }

        luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
        luhao_task["qrcode"] = the_state_img
        luhao_task["status"] = "PENDING"
        portal_client.sync_task_info(luhao_task)
#############################################################
def step_mumuclick_login():
    # connect_device("Android://127.0.0.1:5037/127.0.0.1:7555?ori_method=adbori")
    set_current(0)
    width, height = device().get_current_resolution()
    kkLogger_log(f"step_mumuclick_login 设备屏幕宽：{width} 高：{height}")
    for i in range(5):
        target = ocr_text_target_coords("其他账号", 178, 75, 931, 709)
        if target:
            kktouch2(target[0], target[1] - 84, 1, "授权登录")
            kkLogger_log(f"step_mumuclick_login 点击登录")
            return True
        else:
            kkLogger_log(f"未识别到 其他账号，继续等待")
            sleep(2)
            continue
    #不论是否识别到登录，都点击这个位置
    kktouch2(651,460-37,1.5,"授权登录。")
    kktouch2(651, 460 - 37, 1.5, "授权登录。")
    return False

def start_login_mumu(l_task):
    if True:
        kkLogger_log("start step_login_mumu")
        luhao_task = l_task
        connect_device("Windows:///?title_re=MuMu模拟器12")
        kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
        # sleep(2)

        set_current(0)
        # connect_device("Android://127.0.0.1:5037/127.0.0.1:7555?ori_method=adbori")
        print(f"step_login_mumu 设备屏幕宽：{width} 高：{height}")

        account = luhao_task['gameAccount']
        account_pass = luhao_task['gamePassword']
        task_id = luhao_task['id']

        if check_mumu_game_ready() is False:
            return False

        kktouch2(464,578-33,1,"同意条款")
        if '@' in account:
            kktouch2(653, 414, 1, "网易邮箱")
            if ocr_text_target_coords("输入",295,154,1116,674) is None:
                kktouch2(464,578-33,1,"同意条款")
                kktouch2(653, 414, 1, "网易邮箱")

            # for i in range(10):
            #     kktouch2(581, 259, 1, "账号输入框")
            #     if exists(Template(r"tpl1727454319528.png", record_pos=(0.42, 0.214), resolution=(1280, 720))):
            #         break
            kktouch2(581, 259, 1, "账号输入框")
            kktouch2(614, 680, 1, "账号输入")
            text(account, enter=False)
            # kktouch2(1189, 680, 1, "下一步")

            kktouch2(546, 346, 1, "密码输入框")
            kktouch2(600, 672-33, 1, "密码输入")
            text(account_pass, enter=False)
            # kktouch2(1193, 675, 1, "下一步")
            kktouch2(642, 430, 1.5, "登录")
            # sleep(5)
            ################################################################
            coords = ocr_text_target_coords("二步", 10, 10, 900, 200,False)
            if coords is not None:
                kkLogger_log(f"需要二步验证")
                kktouch2(40, 114 - 37, 1.5, "退出安全验证")
                kktouch2(40, 114 - 37, 1.5, "退出安全验证")
                kktouch2(369, 218 - 37, 2, "返回登录框")
                sync_current_snapshot("需要二步验证",None,False)
                return False
            coords = ocr_text_target_coords("发送", 338, 531, 1034, 718,False)
            if coords is not None:
                kkLogger_log(f"需要安全验证")
                kktouch2(40, 114-37, 1.5, "退出安全验证")
                kktouch2(40, 114-37, 1.5, "退出安全验证")
                kktouch2(369, 218-37, 2, "返回登录框")
                sync_current_snapshot("需要安全验证",None,False)
                return False
            coords = ocr_text_target_coords("密码", 201, 98, 980, 604,False)
            if coords is not None:
                kkLogger_log(f"密码错误，停留在登录界面")
                kktouch2(341, 153, 2, "返回主登录界面")
                # kktouch2(369, 214 - 33, 2, "退出登录框")
                sync_current_snapshot("密码错误",None,False)
                return False

            if is_mumulogin_success():
                return True
            else:
                return False

        else:
            kktouch2(642,381-36,2,"手机账号")
            if ocr_text_target_coords("输入",295,154,1116,674,False) is None:
                kktouch2(464,578-36,2,"同意条款")
                kktouch2(642,381-36,2,"手机账号")

            kktouch2(614,352-36,2,"请输入手机号码")
            kktouch2(577, 714-36, 2, "输入框")
            text(account)
            kktouch2(1195, 645 - 36, 2, "下一步")


            #判断短信是否用完
            if ocr_text_target_coords("发送",400,415,883,617,False) is not None:
                kktouch2(885,226-37,1.5,"关闭短信发送次数限制弹窗")
                kktouch2(941,223-37,3,"关闭登录弹窗")
                kktouch2(941, 226 - 37, 2, "关闭登录弹窗")
                kktouch2(640, 559 - 36, 2, "其他账号登录")

                kkLogger_log("短信次数达到上限，取消本次录号，开始恢复初始页面")
                sync_current_snapshot("短信验证码次数达到上限")
                return False

            step_login_pic = save_cropped_screen("luhao_step")
            oss_file = upload_one_img_to_oss(step_login_pic)
            the_state_img = configs.image_server_url + oss_file
            propertyBag = {
                "state": "need_sms_code",
                "state_img": the_state_img
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            sleep(5)

            kktouch2(460, 395-36, 1, "验证码")
            kktouch2(577, 714-36, 1, "输入框")

            smscode = None
            for i in range(2):
                smscode = portal_client.get_sms_code(task_id=luhao_task['id'], timeout=120)
                if smscode is not None:
                    break
                else:
                    kktouch2(751,321,1,"重新发送")
                    kktouch2(577, 714 - 36, 1, "输入框")
                    continue


            if smscode is None or "扫码" in smscode:
                kkLogger_log("未获取到短信验证码，取消本次录号，开始恢复初始页面")
                kktouch2(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch2(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch2(640, 559 - 36, 1, "其他账号登录")
                return False
            if len(smscode) == 6:
                text(smscode)
                sleep(1)
                # 判断验证码是否正确，不正确或者超时则要求重新输入
                if wait_for_ocr_txt("手机", 2, 380, 188, 927, 348):
                    step_login_pic = save_cropped_screen("luhao_step")
                    oss_file = upload_one_img_to_oss(step_login_pic)

                    the_state_img = configs.image_server_url + oss_file
                    propertyBag = {
                        "state": "need_sms_code2",
                        "state_img": the_state_img
                    }
                    luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
                    portal_client.sync_task_info(luhao_task)
                    sleep(2)

                    for i in range(60):
                        smscode2 = portal_client.get_sms_code(task_id=luhao_task['id'], timeout=120)
                        print(f"获取到验证码：{smscode2}")
                        if smscode2 == smscode:
                            print(f"获取到重复验证码：{smscode2}")
                            sleep(1)
                            continue
                        else:
                            print(f"获取到新验证码：{smscode2}")
                            kktouch2(437, 357, 1, "验证码")
                            text(smscode2)
                            break

                if is_mumulogin_success():
                    kkLogger_log("短信登录成功")
                    return True
                else:
                    kkLogger_log("云逆水寒登录失败，取消本次录号，开始恢复初始页面")
                    return False
            else:
                kkLogger_log("未获取到短信验证码，取消本次录号，开始恢复初始页面")
                kktouch2(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch2(941, 226 - 37, 1, "关闭登录弹窗")
                kktouch2(640, 559 - 36, 1, "其他账号登录")
                return False

############################################################
def step_mumu_login():
    is_mumu_login_success = start_login_mumu(luhao_task)

    if is_mumu_login_success is not True:
        # account = luhao_task.get('gameAccount', "None")
        kkLogger_log("云逆水寒登录失败，开始启动用户扫码登录")
        return step_qrcode_login()

        # return get_return_taskEvent("step_login", EventStatus.FAILURE, "登录失败")
    else:
        sync_login_success("云逆水寒登录成功")
        kkLogger_log("云逆水寒登录成功并开启扫码")

    connect_device("Windows:///?title_re=MuMu模拟器12")
    kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
    (width2, height2) = device().get_current_resolution()
    kkLogger_log(f"尝试连接MuMu模拟器12:{(width2, height2)}")

    check_game_ready()

    is_scan = wait_qrcode_scan_desk(36)
    if not is_scan:
        # kktouch2(483, 159, 2, "退出二维码")
        # kktouch2(634, 406, 2, "进入游戏")
        kktouch2(802, 162, 1.1, "关闭扫码弹窗")
        return get_return_taskEvent("step_login", EventStatus.FAILURE, "登录失败")

    is_click_login_success = step_mumuclick_login()
    # if not is_click_login_success:
    #     kkLogger_log(f"is_click_login_success:{is_click_login_success}")

    is_ok = wait_qrcode_ok(300)
    if is_ok:
        # sync_login_success("扫码成功")
        for i in range(120):
            check_game_login_tiaokuan(2)
            check_game_gonggao()
            coords = local_ocr_text_target_coords("接受", 389, 555, 1000, 718,False)
            coords2 = local_ocr_text_target_coords("江湖", 389, 555, 952, 627,False)
            if coords is not None:
                #登录条款
                kktouch2(coords[0], coords[1], 1, "同意")
                break
            elif coords2 is not None:
                break
            else:
                print(f"没有发现同意按钮，继续等待 {i}")
                sleep(1)
                connect_device("windows:///?title_re=MuMu模拟器12")
                continue
    else:
        sync_login_fail("扫码超时，录号终止")
        kktouch2(802,162,1.1,"关闭扫码弹窗")
        return get_return_taskEvent("step_login", EventStatus.FAILURE, "登录扫码超时")

    if is_login_success():
        return get_return_taskEvent("step_login", EventStatus.SUCCESS, "逆水寒模拟器登录成功")
    else:
        kktouch2(802, 162, 1.1, "关闭扫码弹窗")
        sync_login_fail("逆水寒模拟器登录失败，终止录号")
        return get_return_taskEvent("step_login", EventStatus.FAILURE, "逆水寒模拟器登录失败")
#############################################################
def step_qrcode_login():
    connect_device("windows:///?title_re=MuMu模拟器12")
    if check_game_ready() is False:
        return get_return_taskEvent("step_login",EventStatus.MANUAL_REQUIRED,"登录界面错误")
    else:
        up_qrcode_pic()
        sleep(1)
        print('需要扫码登录')
        sync_current_snapshot("需要扫码")


        is_scan = wait_qrcode_scan_desk(36)
        if not is_scan:
            kktouch2(483, 159, 2, "退出二维码")
            kktouch2(634, 406, 2, "进入游戏")
            return get_return_taskEvent("step_login", EventStatus.FAILURE, "登录失败")


        is_ok = wait_qrcode_ok(300)
        if is_ok:
            sync_login_success()
            for i in range(120):
                check_game_gonggao()
                coords = local_ocr_text_target_coords("接受",389, 555,952, 627)
                coords2 = local_ocr_text_target_coords("江湖",389, 555,952, 627)
                if coords is not None:
                    kktouch2(coords[0], coords[1],1,"同意")
                    break
                elif coords2 is not None:
#                    kktouch2(724, 509,1,"更换")
                    break
                else:
                    print(f"没有发现同意按钮，继续等待 {i}")
                    sleep(1)
                    continue
        else:
            sync_login_fail()
            return get_return_taskEvent("step_login",EventStatus.FAILURE,"登录扫码超时")
    if is_login_success():

        # ##################
        # kktouch2(1239, 78, 1, "右上账号")
        # kktouch2(578, 398, 1, "切换账号")
        # kktouch2(804, 160, 1, "X")
        #
        # ##################

        return get_return_taskEvent("step_login",EventStatus.SUCCESS,"登录成功")
    else:
        sync_login_fail()
        return get_return_taskEvent("step_login",EventStatus.FAILURE,"登录失败")

################################################################################################
def step_login(is_skip):
    if is_skip == 0:
        login_type = luhao_task.get("loginType", "None")
        account = luhao_task.get('gameAccount',"None")
        if "None" == account:
            sync_login_fail()
            return get_return_taskEvent("step_login", EventStatus.FAILURE, "账号异常")
        # elif login_type == "QR_CODE" or '@' in account:
        elif login_type == "QR_CODE":
            return step_qrcode_login()
        else:
            return step_mumu_login()

#######################################################
def wait_qrcode_scan_desk(wait_times):
    for i in range(wait_times):
        try:
            connect_device("Windows:///")
            width,height = device().get_current_resolution()
            kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
            kkLogger_log(f"等待扫码，窗口尺寸: {width},{height}")
            if local_ocr_text_target_coords("扫码成功",389,223,853,567,False) is not None:
                kkLogger_log("扫码成功。。。。。")
                up_qrcode_pic()
                # sleep(1)
                sync_login_success()
                return True
            elif local_ocr_text_target_coords("更换",462,426,781,567,False) is not None:
                kkLogger_log("识别到主页更换角色，扫码成功")
                sync_login_success()
                return True
            elif local_ocr_text_target_coords("接受",462,426,781,567,False) is not None:
                kkLogger_log("识别到条款，扫码成功")
                sync_login_success()
                return True
            else:
                up_qrcode_pic()
                kkLogger_log(f"等待扫码,第{i}/{wait_times}次")
                # if wait_times % 5 == 0:
                #     kktouch2(485,161,1.2,"关闭二维码")
                #     kktouch2(641,504,1.2,"其他账号登录")
                #     sleep(3)
                # else:
                #     swipe((641,162), (641+40,162),steps=1, duration=0.01)
                #     sleep(0.03)
                #     swipe((641+40, 162), (641,162),steps=1, duration=0.01)
                #     sleep(0.03)
                #     swipe((641, 162), (641-40, 162), steps=1, duration=0.01)
                #     sleep(0.03)
                #     swipe((641-40, 162), (641, 162), steps=1, duration=0.01)
                #     sleep(0.03)
                # sleep(10)

                sleep(3)
                continue
        except Exception as e:
            kkLogger_log(f"wait_qrcode_scan error: {e}")
            sleep(5)
            continue

    kkLogger_log(f"未完成扫码,共计等待{wait_times}次")
    return False
#######################################################
def wait_qrcode_ok(wait_seconde):
    for i in range(wait_seconde):
        try:
            connect_device("Windows:///?title_re=MuMu模拟器12.*")
            width,height = device().get_current_resolution()
            kkLogger_log(f"MuMu模拟器12：等待扫码，窗口尺寸: {width},{height}")
            kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
            if width > 700:
                check_game_login_tiaokuan(2)

            if width >= 1280:
                sync_current_snapshot("扫码登录成功")
                return True
            else:
                sleep(3)
                up_qrcode_pic()
                continue
        except Exception as e:
            return True

    return False
###################################################################
def local_ocr_text_target_coords(target_text,start_x,start_y,end_x,end_y,is_wait=True):
    for i in range(5):
        try:
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))
            image_region = image_util.capture_screen(f"{snapImgPath}\\plog_local_ocr_text_target_coords_{target_text}_{current_millis}.png", region=(start_x, start_y, end_x, end_y))
            if touch_fast and is_wait is True:
                sleep(0.5)
            ocr_result = ocr.ocr(f"{snapImgPath}\\plog_local_ocr_text_target_coords_{target_text}_{current_millis}.png", cls=True)
            if ocr_result is None:
                return None
            # 遍历识别结果，找到目标文字的坐标
            target_coords = None
            for line in ocr_result:
                if line is None:
                    continue
                for word_info in line:
                    #获取识别结果的文字信息
                    textinfo = word_info[1][0]
                    kkLogger_log(f"local_ocr_text_target_coords识别到文字：{textinfo}")
                    if target_text in textinfo:
                        # 获取文字的坐标（中心点）
                        x1, y1 = word_info[0][0]
                        x2, y2 = word_info[0][2]
                        target_coords = (start_x+(x1 + x2) / 2, start_y+(y1 + y2) / 2)
                        break
                if target_coords:
                    break

            # 使用Airtest点击坐标
            if target_coords:
                return target_coords
            else:
                kkLogger_log(f"local_ocr_text_target_coords未找到目标文字：{target_text}")
                return target_coords
        except Exception as e:
            sleep(2)
            kkLogger_log(f"local_ocr_text_target_coords：{e}")
            continue

    return None
##############################
def is_login_success():
    for i in range(20):
        sleep(1)
        check_game_gonggao()
        check_yueka_jiazeng(1)
        check_game_update(1)

        step_login_pic = save_cropped_screen("luhao_step")
        oss_file = upload_one_img_to_oss(step_login_pic)
        the_state_img = configs.image_server_url+oss_file
        if local_ocr_text_target_coords("江湖",466,476,847,639,False) is not None:
        # if exists(Template(r"tpl1727284372666.png", record_pos=(0.002, 0.158), resolution=(1280, 720))):
            propertyBag = {
                "state":"login_success",
                "state_img":the_state_img
            }
            luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
            portal_client.sync_task_info(luhao_task)
            kkLogger_log("is_login_success 登录成功")
            return True
        else:
            kkLogger_log("未发现 闯荡江湖")
            continue

    propertyBag = {
        "state":"login_timeout",
        "state_img":the_state_img
    }
    luhao_task["propertyBag"] = sys_tool.safe_json_dumps(propertyBag)
    portal_client.sync_task_info(luhao_task)
    print("登录超时")
    return False
###########################################################################################
def is_mumulogin_success():
    for i in range(120):
        sleep(1)
        target = ocr_text_target_coords("扫码", 872, 20, 1259, 106)
        if target is not None:
            sync_login_success("云逆水寒登录成功")

            kktouch2(target[0], target[1] - 10, 1, "扫码")
            sleep(1)
            for i in range(3):
                try:
                    kkLogger_log("连接MuMu模拟器")
                    connect_device("Windows:///?title_re=MuMu模拟器12")
                    kkLogger_log("连接桌面")
                    kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")

                    dev2 = connect_device("windows:///")
                    kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
                    sleep(1)
                    break
                except Exception as e:
                    kkLogger_log(f"连接桌面异常：{e}")
                    sleep(2)
                    continue

            # kktouch2(604, 397, 1, "实时获取屏幕")
            kktouch2(604, 467, 1, "摄像头")

            # return scan_qrcode()
            return True
        else:
            kkLogger_log("未发现 扫码按钮")
            continue

    kkLogger_log("登录超时")
    return False
###########################################
#############################################################################
def scan_qrcode():
    # sleep(3)
    coords = ocr_text_target_coords("扫码", 872, 20, 1259, 106)
    if coords is not None:
        # kktouch2(1233, 126, 1, "扫码")
        kktouch2(coords[0], coords[1]-10, 1, "扫码")
        sleep(1)
        for i in range(3):
            try:
                kkLogger_log("连接MuMu模拟器")
                connect_device("Windows:///?title_re=MuMu模拟器12")
                kkLogger_log("连接桌面")
                kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")

                dev2 = connect_device("windows:///")
                kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
                sleep(1)
                break
            except Exception as e:
                kkLogger_log(f"连接桌面异常：{e}")
                sleep(2)
                continue

        # kktouch2(604, 397, 1, "实时获取屏幕")
        kktouch2(604, 467, 1, "摄像头")
        return True
    else:
        return False


####################################################################
def check_game_gonggao():
    try:
        global is_gonggao_checked
        global is_zhuangyuan_gonggao_checked
        if is_local:
            return
        coords = local_ocr_text_target_coords("本期上新",10,10,280,422)
        if coords is not None and is_zhuangyuan_gonggao_checked is False:
            kktouch2(1089, 138, 2, "关闭庄园公告")
            is_zhuangyuan_gonggao_checked = True

        coords = local_ocr_text_target_coords("月", 10, 10, 80, 653)
        if coords is not None and is_gonggao_checked is False:
            kktouch2(1199, 144, 2, "关闭公告.")
            is_gonggao_checked = True

    except Exception as e:
        print(f"没有公告:{e}")

####################################
##########################################
def get_return_taskEvent(now_stage,now_status,now_msg=None):
    return TaskEvent(task_id=luhao_task['id'],
                             stage=now_stage,
                             status=now_status,
                             snapshot=get_luhao_snapshot(),
                             data=product_meta,
                             msg=now_msg)

#######################################
def luhao_health_check():
    for i in range(3):
        try:
            screen = G.DEVICE.snapshot()
            if touch_fast:
                sleep(0.5)
            pil_img = cv2_2_pil(screen)
            # 注意：Python的datetime模块直接提供的是秒级时间戳，我们需要将其转换为毫秒
            current_millis = int(round(datetime.datetime.now().timestamp() * 1000))

            pil_img.save(f'{projectPath}\\device_health\\luhao_health_{current_millis}.jpg')

            filename = f'luhao_health_{current_millis}.jpg'
            snapImgPath = f'{projectPath}\\device_health'

            image_path_set = set()
            bucket_name = 'kkzhw-mall'      # 替换为你的OSS Bucket名称
            endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 替换为你的OSS Endpoint
            access_key_id = configs.OSS_ACCESS_KEY_ID
            access_key_secret = configs.OSS_ACCESS_KEY_SECRET

            auth = oss2.Auth(access_key_id, access_key_secret)
            bucket = oss2.Bucket(auth, endpoint, bucket_name)

            local_file_path = os.path.join(snapImgPath, filename)
            oss_file_path = "mall/images2/"+ generate_oss_path()
            oss_fileName = oss_file_path+generate_oss_fileName()
            with open(local_file_path, 'rb') as fileobj:
                bucket.put_object(oss_fileName, fileobj)

            print(f"oss_filename:{oss_fileName}")

            return True
        except Exception as e:
            continue

    return False

###########################################
def wait_for_ocr_txt(ocr_txt,timeout,start_x,start_y,end_x,end_y):
    kkLogger_log(f"等待查找文字：{ocr_txt},超时时间：{timeout}")
    for i in range(timeout):
        coords = local_ocr_text_target_coords(ocr_txt,start_x, start_y,end_x, end_y)
        if coords is not None:
            kkLogger_log(f"识别到文字：{ocr_txt},时间：{i}","debug")
            return coords
        else:
            if timeout > 1:
                sleep(1.5)
    kkLogger_log(f"没有找到文字：{ocr_txt},超时时间：{timeout}")
    return False

######################################################################
def check_huodong():
    if is_local:
        return
    kkLogger_log(f"检查是否有活动弹窗")
    coords = ocr_text_target_coords("参与",427, 300,1233, 734)
    if coords is not None:
        kktouch2(1150, 149,1.5,"关闭活动")


#######################################################################
is_jiazheng_checked = False
jiazheng_check_count = 0
def check_yueka_jiazeng(timeout=5):
    global is_jiazheng_checked
    global jiazheng_check_count

    if is_local:
        return
    kkLogger_log(f"第 {jiazheng_check_count} 次检查check_yueka_jiazeng：{is_jiazheng_checked}")
    if is_jiazheng_checked:
        return
    coords = wait_for_ocr_txt("领取",timeout,627, 400,1233, 734)
    if coords:
        kktouch2(coords[0], coords[1],2,"领取")
        kktouch2(coords[0], coords[1],2,"领取")
        kktouch2(coords[0], coords[1], 2, "领取")
        kktouch2(coords[0], coords[1], 2, "领取")
        is_jiazheng_checked = True
        return

    now_time = datetime.datetime.now()
    use_time = now_time - start_time
    use_minutes = use_time.total_seconds() // 60
    if use_minutes > 15:
        is_jiazheng_checked = True

#######################################################################

#########################################################################
def step_qufu(is_skip):
    connect_device("windows:///?title_re=MuMu模拟器12")
    if is_skip == 0:
        wait_for_ocr_txt("江湖",120, 407,508,793,706)

        kktouch2(719, 514,2,"更换角色")
        wait_for_ocr_txt("最近角色", 10, 22, 270, 209, 601)
        sync_current_snapshot("选择录号角色")
        try:
            account_qufu = luhao_task['product_info']['product']['gameAccountQufu']
        except Exception as e:
            account_qufu = None
        # if account_qufu:
        #     try:
        #         account_qufu_arrays = account_qufu.split('|')
        #         coords = ocr_text_target_coords(account_qufu_arrays[1],256, 218,1253, 626)
        #         if coords is not None:
        #             kkLogger_log(f"找到区服 {account_qufu}")
        #             kktouch2(coords[0],coords[1],2,f"{account_qufu}")
        #         else:
        #             kkLogger_log(f"没有找到区服： {account_qufu}")
        #             kktouch2(426, 310,2,"第一个区服")
        #     except Exception as e:
        #         kkLogger_log(f"{e}，区服异常： {account_qufu}")
        #         kktouch2(426, 310,2,"第一个区服")
        # else:
        #     kktouch2(426, 310,2,"第一个区服")

        kktouch2(426, 310, 2, "第一个区服")

        wait_for_ocr_txt("江湖",120, 407,508,793,706)
        kktouch2(641, 595,2,"闯荡江湖")

        result = wait_for_ocr_txt("场景设置",60, 22,620,226,728)
        if result:
            save_cropped_screen_with_mulblur("头图_面板属性",[(145, 76,385, 590),(858, 520,1179, 570),(894, 653,1095, 694)])
            kktouch2(170, 175,2,"第一个角色")
            # kktouch2(170, 175,2,"第一个角色")
            # kktouch2(1066, 660,2,"进入游戏")
            kktouch2(1066, 660,2,"进入游戏")
            sleep(10)

        # check_and_try_game_home(100,"step_qufu")
        check_and_try_game_home(100)

        return get_return_taskEvent("step_qufu", EventStatus.SUCCESS,"角色选择完成")

###################################################################################################################################
#########################################################################################################################################
def safe_step_gameset(is_skip):
    # safe_step_fuben_in(0)
    return step_gameset(is_skip)
########################################################################
#######################################################################
def check_game_login_tiaokuan(timeout=5):
    if is_local:
        return
    kkLogger_log(f"check_game_login_tiaokuan")
    wait_for_ocr_txt("拒绝",timeout,288, 426,999, 648)
    coords = local_ocr_text_target_coords("接受",288, 426,999, 648)
    if coords is not None:
        connect_device("windows:///")
        kkLogger_log(f"连接桌面,尺寸:{device().get_current_resolution()}")
        kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")

        sync_current_snapshot("处理登录条款")
        kktouch2(coords[0], coords[1],5,"接受")

        connect_device("windows:///?title_re=MuMu模拟器12")
        kkLogger_log(f"连接MuMu模拟器12,尺寸:{device().get_current_resolution()}")
        kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
#######################################################################
def check_game_update(timeout=5):
    return
    # if is_local:
    #     return
    # kkLogger_log(f"check_game_update")
    # wait_for_ocr_txt("过旧",timeout,337, 206,909, 479)
    # coords = ocr_text_target_coords("确定",337, 206,909, 479)
    # if coords is not None:
    #     sync_current_snapshot("提示版本过旧，需要升级客户端","FAILED")
    #     kktouch2(coords[0], coords[1],5,"确定")
#######################################################################
def check_game_alert_note():
    if is_local:
        return
    kkLogger_log(f"检查是否有提示弹窗")
    tip_target = ocr_text_target_coords("提示",239, 150,984, 578)
    if tip_target:
        cancel_target = ocr_text_target_coords("取消",239, 150,984, 578)
        if cancel_target:
            kktouch2(cancel_target[0],cancel_target[1],1,"check_game_alert_note 取消")
        else:
            confirm_target = ocr_text_target_coords("确定", 239, 150, 984, 578)
            if confirm_target:
                kktouch2(confirm_target[0], confirm_target[1], 1, "check_game_alert_note 确定")
#######################################################################
def check_account_login_other():
    if is_local:
        return
    kkLogger_log(f"判断是否有被顶号提示")
    account_logout = False
    coords = ocr_text_target_coords("已在其他设备",439, 316,934, 467)
    if coords is not None:
        sync_current_snapshot("被顶号，录号中断","FAILED")
        kktouch2(1162+10,390+37,2,"顶号：确定")
        account_logout = True
    else:
        coords2 = ocr_text_target_coords("江湖", 475, 432, 870, 640)
        if coords2 is not None:
            sync_current_snapshot("被顶号，录号中断。","FAILED")
            account_logout = True

    if account_logout:
        kktouch2(1243,98,3,"右上账号")
        kktouch2(590,394,3,"切换账号。")
        kktouch2(802 , 160 , 3, "切换账号。。")

    return account_logout

###################################################################################
def safe_step_fuben_in(is_skip):
    if is_skip == 0:
        try:
            kkLogger_log(f"开始进入副本")
            check_and_try_game_home(4)
            # kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            kk_keyevent("{ESC}",1,1,"菜单")
            kktouch2(1027,364,2,"副本")

            kktouch2(689,432,2,"千机")

            kktouch2(1104,80,2,"秋岚画院")

            kktouch2(937,567,2,"单双人")

            kktouch2(1148,688,2,"开始挑战")

            kktouch2(1030,614,10,"就位")

            if check_and_try_game_home(4):
                sync_current_snapshot("进入副本成功")
                return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS,"进入副本完成")
            else:
                sync_current_snapshot("进入副本失败")
                return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS, "进入副本失败")
        except Exception as e:
            kkLogger_log(f"进入副本异常 {e}")
###########################################################################
# def safe_step_fuben_out(is_skip):
#     if is_skip == 0:
#         try:
#             kkLogger_log(f"开始退出副本")
#             check_and_try_game_home(4)
#             kktouch2(947,177,2,"退出副本")
#             if ocr_text_target_coords("确认",1043,343,1250,458,) is not None:
#                 kktouch2(1157,386,10,"确认")
#
#             sync_current_snapshot("退出副本")
#             if check_and_try_game_home(4):
#                 return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS, "退出副本完成")
#             else:
#                 return get_return_taskEvent("safe_step_fuben_in", EventStatus.SUCCESS, "退出副本失败")
#         except Exception as e:
#             kkLogger_log(f"退出副本异常 {e}")
########################################################################
def step_gameset(step_gameset):
    if step_gameset == 0:
        connect_device("windows:///?title_re=MuMu模拟器12")
        check_yueka_jiazeng(1)
        check_game_update(1)

        sync_current_snapshot("游戏设置开始")
        for i in range(6):
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            target = local_ocr_text_target_coords("设置", 1214, 236, 1278, 621)
            if target:
                kktouch2(target[0], target[1],2,"设置")

                kktouch2(77+10, 445+37, 1, "视角")
                kktouch2(747+10, 126+37, 1, "2.5D")

                kktouch2(76, 359,1,"左侧画面")
                kktouch2(805, 319, 1, "流畅")

                sync_current_snapshot("游戏设置完成")

                kktouch2(70, 73,1,"左上退出游戏设定")
                return get_return_taskEvent("step_gameset", EventStatus.SUCCESS,"游戏设置完成回到主页")
            else:
                check_and_try_game_home(4)
                continue
        return get_return_taskEvent("step_gameset", EventStatus.SUCCESS,"游戏设置完成回到主页")
##################################################################################
def safe_step_juese(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=MuMu模拟器12")
            return step_juese(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_juese error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_juese", EventStatus.FAILURE)

#####
def step_juese(is_skip):
    if is_skip == 0:
        kktouch2(1224 + 10, 32 + 37, 1.5, "菜单")

        kktouch2(1250,187,1,"主页")
        sleep(5)
        save_cropped_screen_with_mulblur("主页_其他物品",[(17, 49,317, 357)])
        kk_keyevent("{ESC}",1,1,"返回主页")
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")

        # if kktouch_step(1247, 124, 2, "角色") is False:
        if kktouch2(1247, 124, 2, "角色") is False:
            return get_return_taskEvent("step_juese", EventStatus.SUCCESS, "未执行")
        kktouch2(68+10, 128+37,2,"左侧角色")
        save_cropped_screen_with_mulblur("头图_面板属性",[(49, 41-40,449, 95),(866, 6,1261+10, 239+40),(884, 652,1101+10, 695+40),(515,41,788,186)])
        get_attr_ocr_number("评分",229, 103,441, 182)

        kktouch2(277+10, 257+37,1,"点击角色-装备1")
        save_cropped_area_screen("角色-装备1_面板属性",347+10, 37+40,700+10, 634+40,0.2)

        kktouch2(323+10, 296+37,1,"点击角色-装备2")
        save_cropped_area_screen("角色-装备2_面板属性",388+10, 32+40,742, 631+40,0.2)

        kktouch2(341+10, 425+37,1,"点击角色-装备3")
        save_cropped_area_screen("角色-装备3_面板属性",436+10, 112+40,787, 712+40,0.2)

        kktouch2(284+10, 512+37,1,"点击角色-装备4")
        save_cropped_area_screen("角色-装备4_面板属性",346+10, 115+40,698, 709+40,0.2)

        kktouch2(286+10, 598+37,1,"点击角色-装备5")
        save_cropped_area_screen("角色-装备5_面板属性",346+10, 113+40,700, 710+40,0.2)

        kktouch2(256+10, 296+37,1,"点击角色-装备6")
        save_cropped_area_screen("角色-装备6_面板属性",301+10, 34+40,655, 632+40,0.2)

        kktouch2(290+10, 343+37,1,"点击角色-装备7")
        save_cropped_area_screen("角色-装备7_面板属性",346+10, 56+40,700, 658+40,0.2)

        kktouch2(254+10, 385+37,1,"点击角色-装备8")
        save_cropped_area_screen("角色-装备8_面板属性",302+10, 98+40,656, 701+40,0.2)

        kktouch2(254+10, 467+37,1,"点击角色-装备9")
        save_cropped_area_screen("角色-装备9_面板属性",301+10, 112+40,655, 710+40,0.2)

        kktouch2(256+10, 556+37,1,"点击角色-装备10")
        save_cropped_area_screen("角色-装备10_面板属性",304+10, 112+40,656, 713+40,0.2)

        kktouch2(215+10, 344+37,1,"点击角色-装备11")
        save_cropped_area_screen("角色-装备11_面板属性",259+10, 59+40,613, 655+40,0.2)

        kktouch2(212+10, 508+37,1,"点击角色-装备12")
        save_cropped_area_screen("角色-装备12_面板属性",259+10, 113+40,616+10, 709+40,0.2)

        #############################################################
        kktouch2(73+10, 194+37,2,"左侧背包")
        kktouch2(869, 716,2,"底部仓库")
#         save_cropped_screen("背包-仓库")
        kktouch2(66+10, 35+37,2,"左上退出仓库")

        swipe_to_end((832, 175, 1170, 287),(1058, 181),(934, 523))
        for i in range(6):
            save_cropped_area_screen("角色-背包_其他物品",832, 172,1181, 693,0.2)

            is_end = swipe_and_judge_end_fast((832, 175, 1170, 287),(945, 677), (945, 149))
            # is_end = scroll_and_judge_end((832, 175, 1170, 287),(1003, 684), (1003, 177))
            if is_end:
                break

        kkLogger_log(f'背包截图结束,相关材料在{snapImgPath}')

        #############################################################

        kkLogger_log("======仓库截图开始")
        kktouch2(869, 716,2,"底部仓库")
#         kktouch2(869, 716,2,"底部仓库")

        findcangkuResult = find_all_subImg(Template(r"tpl1727872265286.png", threshold=0.4),191, 105,444, 160)
        cangkuCount = 0
        if findcangkuResult is None:
            cangkuCount = 0
        else:
            for line in findcangkuResult:
                kkLogger_log(line)
                if line['confidence'] > 0.5:
                    cangkuCount = cangkuCount +1
                    kktouch2(line['result'][0]+191,line['result'][1]+105,1,f"仓库第{cangkuCount}个包")
                    if ocr_text_target_coords("取消", 57, 294, 300, 465):
                        kktouch2(67,390,1,"取消")
                    else:
                        # save_cropped_screen("仓库")
                        save_cropped_area_screen("仓库_其他物品", 105, 118, 450, 460)

        kkLogger_log(f"=====仓库数量===={cangkuCount}")

        kktouch2(65+10, 37+37,1,"退出仓库") ##推出仓库

        kkLogger_log("======仓库截图结束")

        ###################################

        kkLogger_log("======旅途截图开始")
        ############开始旅途
        kktouch2(79+10, 257+37,1,"点击左侧旅途")
        save_cropped_area_screen("旅途",864, 80,1251, 687)  ##旅途右侧截图
        kktouch2(74+10, 317+37,1,"点击左侧修行")
        save_cropped_screen("修行_其他物品")
        step_2 =1
        ##推出修行，回到主界面
        # kktouch2(69+10, 42+37,1.5,"退出修行")
        kk_keyevent("{ESC}",1,0.2)
        kkLogger_log("======旅途截图完成")
        return get_return_taskEvent("step_juese", EventStatus.SUCCESS,"角色信息完成")
    else:
        return get_return_taskEvent("step_juese", EventStatus.SUCCESS,"未执行")
###############################################################
def safe_step_jueji(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=MuMu模拟器12")
            return step_jueji(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_jueji error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_jueji", EventStatus.FAILURE)
###################
def step_jueji(step_jueji):
    if step_jueji == 0:
        kkLogger_log("======武功-绝技截图开始")
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1065, 193,3,"武功") is False:
            return get_return_taskEvent("step_jueji", EventStatus.SUCCESS, "绝技未执行")
        kktouch2(64+10, 121+37,1,"左侧技能")
        kktouch2(68+10, 245+37,1,"绝技")
        save_cropped_screen("绝技_其他物品")

        if ocr_text_target_coords("升",877, 678,1056, 719) is not None:
            kktouch2(933,700,1,"绝技-升阶")
            sleep(2)
            save_cropped_screen("绝技升阶_其他物品")

        kk_keyevent("{ESC}",2,0.2)

        kkLogger_log("======武功-绝技截图完成")
        return get_return_taskEvent("step_jueji", EventStatus.SUCCESS,"绝技完成")
####################################################
def safe_step_neigong(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=MuMu模拟器12")
            return step_neigong(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_neigong error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_neigong", EventStatus.FAILURE)
###################

def step_neigong(is_skip):
    if is_skip == 0:
        kkLogger_log("======内功截图开始")
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1065, 193,3,"武功") is False:
            return get_return_taskEvent("step_neigong", EventStatus.SUCCESS, "内功未执行")

        kktouch2(75+10, 123+37,1,"技能")
        kktouch2(75+10, 434+37,1,"内功")

        kktouch2(346+10, 658+37,1,"点击详细属性加成")
        # if ocr_text_has_number(399, 47, 878, 662):
        if ocr_text_target_coords("加成",399, 47,878, 662) is not None:
            save_cropped_area_screen("内功详细属性加成_打造内功",399, 47,878, 662,0)
            kktouch2(270+10, 699+37,1,"取消详细属性加成")


        kktouch2(1001+10, 653+37,2,"全部内功")
        kktouch2(143+10, 107+37,2,"筛选")

        # swipe((1008, 587),(1008, 350),duration=0.2,steps=2)
        kk_scroll((1008, 587), -5, 0.5, "词条搜索")

        kktouch2(1054, 546,1.5,"词条")
        kktouch2(440, 219,2,"灵韵")
        kktouch2(1169, 701,2,"确定")



        start_x = 109
        start_y = 170
        end_x = 823
        end_y = 286
        loop_height = 127
        lingyun_count = 0
        save_cropped_screen("lingyunall_打造内功")
        for i in range(4):
            find_lingyun_result = find_all_subImg(Template(r"tpl1726237406954.png", threshold=0.6),start_x, start_y,end_x, end_y)
            neigong_count = 0
            if find_lingyun_result is None:
                break
            else:
                kkLogger_log(f"第{i+1}行，find_lingyun_result数量：{len(find_lingyun_result)}")
                for line in find_lingyun_result:
                    neigong_count = neigong_count + 1
                    if line['confidence'] > 0.5:
                        x1 = line['result'][0]
                        y1 = line['result'][1]
                        kktouch2(x1+start_x,y1+start_y,1,f"第{neigong_count}个灵韵内功")

                        # swipe((1055, 505), (1055, 450),duration=0.2,steps=2)
                        kk_scroll((1055, 505), -3, 0.5, "灵韵")
                        sleep(0.5)
                        find_lingyun_result = find_subImg(Template(r"tpl1726830336235.png"),868,407,1263, 651)
                        if find_lingyun_result is not None:
                            save_cropped_area_screen("灵韵_打造内功_0",857, 59,1266, 669,0)
                            ocr_add_to_get(857, 59,1129, 105)
                            lingyun_count = lingyun_count +1
                        else:
                            save_cropped_area_screen("灵韵_打造内功_1",857, 59,1266, 669,0)
                start_y = start_y + loop_height
                end_y = end_y + loop_height
                if neigong_count < 7:
                    break

        kkLogger_log(f"=====灵韵数量===={lingyun_count}")
        ocr_check_and_set("灵韵数量",str(lingyun_count))
        # kktouch2(70+10, 44+37,1,"左上退出灵韵武功")
        # kktouch2(72+10, 38+37,1,"左上退出武功")
        kk_keyevent("{ESC}", 2, int(0.2))
    return get_return_taskEvent("step_neigong", EventStatus.SUCCESS,"内功完成")
####################################################
def safe_step_dazao(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=MuMu模拟器12")
            return step_dazao(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_dazao error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_dazao", EventStatus.FAILURE)
###################
def step_dazao(is_skip):
    if is_skip == 0:
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        # if kktouch_step(1172 , 329 , 1, "打造") is False:
        if kktouch2(1172 , 329 , 1, "打造") is False:
            return get_return_taskEvent("step_dazao", EventStatus.SUCCESS, "无打造，未执行")
        kktouch2(77,160,1,"左侧强化")
        save_cropped_screen("打造强化_打造内功")

        kktouch2(71,224,2,"左侧打造")

        sync_current_snapshot("打造强化")

        loop = 80
        start_x = 221
        start_y = 167
        for i in range(6):
            kktouch2(start_x,start_y,0.5,f"第{i+1}个打造")

            if ocr_text_target_coords("已",1201,268,1270,421) is None and ocr_text_target_coords("有",1201,268,1270,421) is None:
                sleep(1)
                start_y = start_y + loop
                continue

            kktouch2(1238, 336,1,"已有特技库")

            if ocr_text_has_number(659, 98,1266, 726):
                # swipe_to_end((781, 58, 1258, 170),(1032, 632),(992, 96))
                kk_scroll((1032, 632), -10, 1.5, "打造滚动底部")
                kktouch2(872+10, 643+37,1,"点击最后一个")
                kk_scroll((1032, 632), 10, 1.5, "打造滚动顶部")
                # swipe_to_end((651, 430,1233, 663), (975, 100),(1004, 583))
            else:
                kkLogger_log("打造页面无数字，直接截图")

            target_arrays = ocr_text_target_coords_arrays("属性提升",659, 98,1266, 726)
            if target_arrays is not None:
                for item in target_arrays:
                    save_cropped_area_screen_dazao("打造属性_打造内功",item[0]-121, item[1]-57,(item[0]-121)+444, (item[1]-57)+81)

            kktouch2(71+10, 40+37,1,"左上返回")
            start_y = start_y + loop

        # kktouch2(71+10, 36+37,1,"左上返回")
        kk_keyevent("{ESC}", 1, 0.2)
        kkLogger_log("打造结束")
    return get_return_taskEvent("step_dazao", EventStatus.SUCCESS,"打造完成")
###########################################################################################################

#####################################################################################################################
def safe_step_waiguan(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=MuMu模拟器12")
            return step_waiguan(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_waiguan error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_waiguan", EventStatus.FAILURE)
##############################################################################
def waiguan_substep_guancang1025(is_skip):
    if is_skip == 0:

        kktouch2(1094, 123, 2, "衣品")
        sync_current_snapshot("外观-衣品")
        save_cropped_area_screen("衣品", 835, 81, 1245, 651)
        has_yipin = get_attr_ocr_number("衣品", 1009, 84, 1254, 125)
        kk_keyevent("{ESC}", 1, 2, "退出衣品")


        kktouch2(1217, 124,2,"馆藏")
        target = ocr_text_target_coords("跳过", 954, 54, 1273, 177)
        if target is not None:
            sleep(5)
            kk_keyevent("{ESC}", 1, 2, "跳过引导")
            sleep(3)


        save_cropped_screen(f"馆藏总等级_其他物品")
        sync_current_snapshot("馆藏")


        kktouch2(640,616,1.5,"时鉴赏")
        kktouch2(67, 313,1,"时鉴赏一期")
        save_cropped_screen(f"时鉴赏一期_其他物品")
        kktouch2(67, 374,1,"时鉴赏二期")
        save_cropped_screen("时鉴赏二期_其他物品")
        kktouch2(67, 444, 1, "时鉴赏三期")
        save_cropped_screen("时鉴赏三期_其他物品")
        kk_keyevent("{ESC}", 1, 1.5, "退出时鉴赏")

        ###天赏
        tianniran_count = ocr_add_to_get_and_count_tianniran_number(268, 409, 338, 448)
        if tianniran_count == 0:
            tianniran_count = ocr_add_to_get_and_count_tianniran_number(268, 409, 338, 448)
            kkLogger_log(f"天赏数量==================={tianniran_count}")
            tianniran_count = 4
        kktouch2(374, 464,2,"天赏")
        kktouch2(89, 242, 2, "青丝馆霓")
        kktouch2(334,140,1,"分类展示")

        if tianniran_count > 0:
            start_x = 177
            start_y = 175
            end_x = 295
            end_y = 372
            save_ziran_pic(start_x,start_y,end_x,end_y)

            for i in range(10):
                swipe_and_judge_end((165, 310, 544, 372), (418, 571), (418, 335))
                save_ziran_pic(start_x,start_y,end_x,end_y)

                if ocr_text_has_txt(172, 577, 234, 618  ) is False:
                    break
            save_ziran_pic(177, 375, 293, 573)
            # sleep(15)

        save_cropped_screen("自染_天赏外观")
        sync_current_snapshot("外观-自染")
        kk_keyevent("{ESC}", 1, 1.5, "退出天赏")

        kktouch2(143, 689,1.2,"国色")
        save_cropped_screen(f"国色_其他物品")
        get_attr_ocr_number("国色值",843, 47,1130, 95)
        kk_keyevent("{ESC}", 1, 1.5, "退出国色")

        kkLogger_log("馆藏结束")

#################################################################################
def save_ziran_pic(start_x,start_y,end_x,end_y):
    loop_x = 299 - 175
    for i in range(3):
        if ocr_text_has_txt(start_x, start_y, end_x, end_y):
            kktouch2(start_x + 50, start_y + 50, 0.5, "左侧自染头")
            save_cropped_area_screen(f"自染头_天赏外观", start_x, start_y, end_x, end_y)
            pic_width = 149
            pic_height = 115
            save_cropped_area_screen(f"自染方案_天赏外观", 845, 242, 845 + pic_width, 242 + pic_height)

            start_x = start_x + loop_x
            end_x = end_x + loop_x
##################################################################################
def waiguan_substep_guancang(is_skip):
    if is_skip == 0:
        kktouch2(1223+10, 86+37,2,"馆藏")
        kktouch2(1013, 84,2,"馆藏奖励")
        kktouch2(71+10, 131+37,2,"馆藏总等级")
        save_cropped_screen(f"馆藏总等级_其他物品")

        sync_current_snapshot("馆藏等级")

        kktouch2(68+10, 199+37,1,"时鉴赏一期")
        save_cropped_screen(f"时鉴赏一期_其他物品")

        kktouch2(70+10, 272+37,1,"时鉴赏二期")
        save_cropped_screen("时鉴赏二期_其他物品")

        # kktouch2(1220+10, 79+37,2,"右侧X退出时鉴赏")
        kk_keyevent("{ESC}",1,2,"馆藏奖励")

        kktouch2(486+10, 39+37,2,"衣品")
        sync_current_snapshot("外观-衣品")

        save_cropped_area_screen("衣品",835, 81,1245, 651)
        has_yipin = get_attr_ocr_number("衣品",1009, 84,1254, 125)
        # kktouch2(1238+10, 35+37,1,"退出衣品")
        if has_yipin:
            kk_keyevent("{ESC}",1,2,"退出衣品")

        kktouch2(542,688,2,"天赏主题")

        kktouch2(642+10, 346+37,2,"青丝管霓")
        sync_current_snapshot("外观-自染")

        save_cropped_screen("自染_天赏外观")

        tianniran_count = ocr_add_to_get_and_count_tianniran_number(106, 653,229, 720)
        if tianniran_count == 0:
            tianniran_count = ocr_add_to_get_and_count_tianniran_number(106, 653, 229, 720)

        if tianniran_count == 0 and ocr_text_target_coords("暂无", 542, 345, 741, 389) is None:
            tianniran_count = 16

        if tianniran_count > 0:
            hit_txt_arrays = []
            targets = ocr_text_target_coords_arrays("点", 44, 101, 568, 184,hit_txt_arrays)
            snapshot_ziran(targets, hit_txt_arrays)

        if tianniran_count > 4:
            hit_txt_arrays = []
            targets = ocr_text_target_coords_arrays("点", 53, 314, 554, 387,hit_txt_arrays)
            snapshot_ziran(targets, hit_txt_arrays)

        ######################################################################
        if tianniran_count > 8:
            # swipe_to_end((54, 67, 554, 193), (302, 431), (302, 182),True)
            kk_scroll((302, 431), -10, 1, "自染滚动底部")
        #######################################################################
        if tianniran_count > 8:
            hit_txt_arrays = []
            targets = ocr_text_target_coords_arrays("点", 49, 234, 559, 295, hit_txt_arrays)
            snapshot_ziran(targets,hit_txt_arrays)

        if tianniran_count > 12:
            hit_txt_arrays = []
            targets = ocr_text_target_coords_arrays("点", 51, 434, 559, 494, hit_txt_arrays)
            snapshot_ziran(targets, hit_txt_arrays)


        for item in tianniran_ocr_txt_arrays:
            ocr_check_and_add_attr(item,"热门女号自染")
            ocr_check_and_add_attr(item,"热门男号自染")

        kktouch2(67+10, 42+37,1,"退出青丝管霓")

        kktouch2(143+10, 659+37,1,"国色")
        save_cropped_screen(f"国色_其他物品")
        #获取国色值
        get_attr_ocr_number("国色值",843, 47,1130, 95)


        # kktouch2(65+10, 35+37,1,"左侧退出国色")
        kk_keyevent("{ESC}", 1, 0.2,"左侧退出国色")
        kkLogger_log("馆藏结束")
#######################################################################################################################
def snapshot_ziran(targets,hit_txt_arrays):
    i = 0
    for item in targets:
        kktouch2(item[0], item[1], 1, "方案")
        save_cropped_area_screen(f"自染头_天赏外观", item[0] - 77, item[1] - 154, item[0] + 43, item[1] + 45)
        kkLogger_log(f"hit_txt_arrays:{hit_txt_arrays[i]}")
        pic_width=149
        pic_height=115
        if "2点" == hit_txt_arrays[i]:
            save_cropped_area_screen(f"自染方案_天赏外观", 845, 242, 845+pic_width, 242+pic_height)
        elif "4点" == hit_txt_arrays[i]:
            save_cropped_area_screen(f"自染方案_天赏外观", 845, 242, 845+pic_width, 242+pic_height)
            save_cropped_area_screen(f"自染方案_天赏外观", 700, 427, 962+pic_width, 352+pic_height)
        else:
            save_cropped_area_screen(f"超过4点自染方案_天赏外观", 664, 104, 664+pic_width, 104+pic_height)
        i = i + 1

# def snapshot_ziran(targets,hit_txt_arrays):
#     i = 0
#     for item in targets:
#         kktouch2(item[0], item[1], 1, "点")
#         save_cropped_area_screen(f"自染头_天赏外观", item[0] - 28, item[1] - 20, item[0] + 93, item[1] + 177)
#         kkLogger_log(f"hit_txt_arrays:{hit_txt_arrays[i]}")
#         pic_width=149
#         pic_height=115
#         if "2点" == hit_txt_arrays[i]:
#             save_cropped_area_screen(f"自染方案_天赏外观", 845, 242, 845+pic_width, 242+pic_height)
#         elif "4点" == hit_txt_arrays[i]:
#             save_cropped_area_screen(f"自染方案_天赏外观", 845, 242, 845+pic_width, 242+pic_height)
#             save_cropped_area_screen(f"自染方案_天赏外观", 700, 427, 962+pic_width, 352+pic_height)
#         else:
#             save_cropped_area_screen(f"超过4点自染方案_天赏外观", 664, 104, 664+pic_width, 104+pic_height)
#         i = i + 1
############################################################################
def waiguan_substep_shizhuang(is_skip):
    if is_skip == 0:
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1156, 193, 1, "外观") is False:
            return

        kkLogger_log("开始时装")
        kktouch2(255+10, 665+37,1,"底部时装")
        kkLogger_log("开始套装")
        kktouch2(61+10, 124+37,1,"套装")
        kktouch2(428+10, 618+37,2,"套装回到顶部")
        swipe_to_end((108, 156, 468, 340),(294, 200),(294, 537))

        kktouch2(398+10, 231+37,2,"右上第一个套装")

        sync_current_snapshot("外观-套装")
        for i in range(60):

            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),106, 227,474, 551)

            if find_lock_result is None:
                kkLogger_log(f"套装第{i}页")

                if i % 2 == 0:
                    kktouch2(413+10, 563+37,0.1,"右下套装")
                else:
                    kktouch2(281+10, 563+37,0.1,"底中套装")

                save_cropped_area_screen_and_ocr_add_to_get("套装",112, 178,470, 575,[2,3])

                kktouch2(938+10, 680+37,0.1,"底部周边")
                kktouch2(256+10, 686+37,0.1,"底部时装")
            else:
                kkLogger_log(f"套装识别到锁")

                save_cropped_area_screen_and_ocr_add_to_get("套装",112, 178,470, 575,[2,3],Template(r"tpl1727075488904.png", threshold=0.75))

                kktouch2(516+10, 478+37,1,"套装重置")
                kktouch2(1157+10, 354+37,1,"确认套装重置")
                break
####################################################
def waiguan_substep_fashi(is_skip):
    if is_skip == 0:
        kkLogger_log("开始时装-发式")
        kktouch2(62+10, 366+37,1,"左侧发式")
        sync_current_snapshot("外观-发式")
        #滚动到顶部
        kktouch2(434+10, 616+37,2,"发式回顶部")
        swipe_to_end((109, 249, 472, 605),(294, 200),(294, 500))
        kktouch2(428+10, 618+37,2,"发式回到顶部")
        kktouch2(406+10, 235+37,2,"右上第一个发式")

        # for i in range(6):
        #     kk_scroll((799,406),1,0.5,"发式滚动")

        for i in range(60):
            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),108, 146,468, 546)
            if find_lock_result is None:
                kkLogger_log(f"发式第{i}页")

                if i % 2 == 0:
                    kktouch2(281 + 10, 563 + 37, 0.1, "底中发式")
                else:
                    kktouch2(413 + 10, 563 + 37, 0.1, "右下发式")

                save_cropped_area_screen_and_ocr_add_to_get("发式",112, 178,470, 575,[2,3])

                # if i % 2 == 0:
                #     kktouch2(413 + 10, 563 + 37, 0.1, "右下发式")
                # else:
                #     kktouch2(281 + 10, 563 + 37, 0.1, "底中发式")

                kktouch2(67+10, 125+37,0.1,"套装")
                kktouch2(56+10, 372+37,0.1,"发式")
            else:
                kkLogger_log(f"发式识别到锁")
                save_cropped_area_screen_and_ocr_add_to_get("发式",112, 178,470, 575,[2,3],Template(r"tpl1727075488904.png", threshold=0.7))
                kktouch2(516+10, 478+37,1,"发饰重置")
                kktouch2(1157+10, 354+37,1,"确认发饰重置")
                break

####################################################
def waiguan_substep_wuqi(is_skip):
    if is_skip == 0:
        kktouch2(536+10, 676+37,1,"底部武器")#点击底部武器
        kktouch2(66+10, 121+37,1,"分块")
        kktouch2(56+10, 486+37,1,"套装")

        sync_current_snapshot("外观-武器")

        kkLogger_log("开始武器")
        kktouch2(430+10, 618+37,2,"武器回到顶部")
        swipe_to_end((110, 178,466, 604),(287, 200),(287, 500))
        for i in range(11):
            find_lock_result = find_subImg(Template(r"tpl1727007667521.png", threshold=0.7),108, 206,462, 624)

            if find_lock_result is None:
                kkLogger_log(f"武器第{i}页")
                save_cropped_area_screen_and_ocr_add_to_get("武器",110, 178,470, 604,[3,2])
                is_end = swipe_and_judge_end((110, 178,466, 604),(287, 656),(287, 199))
                if is_end:
                    break
                sleep(1)
            else:
                kkLogger_log(f"武器识别到锁")
                save_cropped_area_screen_and_ocr_add_to_get("武器",110, 178,466, 604,[3,2],Template(r"tpl1727007667521.png", threshold=0.7))
                kktouch2(513+10, 477+37,1,"武器重置")
                kktouch2(1151+10, 352+37,1,"确认武器重置")
                break
####################################################
def waiguan_substep_zhuoqi(is_skip):
    if is_skip == 0:
        kktouch2(671+10, 679+37,1,"底部坐骑")#点击底部坐骑
        kktouch2(65+10, 126+37,1,"左侧坐骑")#点击坐骑

        sync_current_snapshot("外观-坐骑")
        kkLogger_log("开始坐骑")
        kktouch2(430+10, 616+37,2,"坐骑回到顶部")
        swipe_to_end((110, 215,470, 615),(230, 215+30),(230, 500))
        for i in range(40):

            find_lock_result = find_subImg(Template(r"tpl1726951381717.png", threshold=0.75),102, 258,467, 630)

            if find_lock_result is None:
                kkLogger_log(f"坐骑第{i}页")
                kktouch2(365, 637,0.1,"右下坐骑")
                save_cropped_area_screen_and_ocr_add_to_get("坐骑",116, 215,470, 615,[2,3])

                kktouch2(63, 237,0.1,"祥瑞")
                kktouch2(67, 161,0.1,"坐骑")
            else:
                kkLogger_log(f"坐骑识别到锁")
                save_cropped_area_screen_and_ocr_add_to_get("坐骑",116, 215,470, 615,[2,3],Template(r"tpl1726951381717.png", threshold=0.75))
                kktouch2(509+10, 478+37,1,"坐骑重置")
                kktouch2(1157+10, 354+37,1,"确认坐骑重置")
                break
########################################
def waiguan_substep_xiangrui(is_skip):
    if is_skip == 0:
        kktouch2(671+10, 679+37,1,"底部坐骑")#点击底部坐骑
        kktouch2(61+10, 210+37,1,"左侧祥瑞")#点击坐骑
        kkLogger_log("开始祥瑞")
        sync_current_snapshot("外观-祥瑞")
        kktouch2(431+10, 619+37,2,"祥瑞回到顶部")
        swipe_to_end((110, 215,470, 615),(230, 215+30),(230, 615))
        kktouch2(410+10, 290+37,2,"右上第一个祥瑞")
        for i in range(40):

            find_lock_result = find_subImg(Template(r"tpl1727075488904.png", threshold=0.75),102, 186,471, 598)

            if find_lock_result is None:
                kkLogger_log(f"祥瑞第{i}页")
                kktouch2(308, 629,0.1,"右下祥瑞")
                save_cropped_area_screen_and_ocr_add_to_get("祥瑞",116, 215,470, 615,[2,3])
                kktouch2(67, 161,0.1,"坐骑")
                kktouch2(63, 237,0.1,"祥瑞")
            else:
                kkLogger_log(f"祥瑞识别到锁")
                save_cropped_area_screen_and_ocr_add_to_get("祥瑞",116, 215,470, 615,[2,3],Template(r"tpl1727075488904.png", threshold=0.75))
                kktouch2(514+10, 471+37,1,"祥瑞重置")
                kktouch2(1157+10, 354+37,1,"确认祥瑞重置")
                break
#########################################################
def waiguan_substep_jueji(is_skip):
    if is_skip == 0:
        start_x = 1066
        start_y = 183
        end_x = 1243
        end_y = 288
        kktouch2(802+10, 687+37,1,"底部武功")#点击底部武功
        kktouch2(67+10, 240+37,1,"左侧绝技")#点击绝技

        sync_current_snapshot("外观-绝技")

        kktouch2(191+10, 186+37,1,"冰火绝灭")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:
            save_cropped_area_screen("武功绝技冰火绝灭_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 冰火绝灭")
            add_to_get("明河曙天·长鲸天斗")


        kktouch2(287+10, 186+37,1,"残心三绝剑")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:
            save_cropped_area_screen("武功绝技残心三绝剑_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 残心三绝剑")
            add_to_get("天曦四象·朱雀")



        kktouch2(186+10, 306+37,1,"剑破乾坤")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:
            save_cropped_area_screen("武功绝技剑破乾坤_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 剑破乾坤")
            add_to_get("日曜八荒·轩辕剑")



        kktouch2(285+10, 309+37,1,"万剑绝")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:
            save_cropped_area_screen("武功绝技万剑绝_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 万剑绝")
            add_to_get("山海祠神·阳升羲和")


        kktouch2(186+10, 433+37,1,"长歌献君")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:
            save_cropped_area_screen("武功绝技长歌献君_天赏外观",start_x, start_y,end_x, end_y)

            kkLogger_log("识别到 长歌献君")
            add_to_get("重蝶化梦")


        kktouch2(287+10, 435+37,1,"狂发一怒")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:
            save_cropped_area_screen("武功绝技狂发一怒_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 狂发一怒")
            add_to_get("青丘雪")


        kktouch2(186+10, 554+37,1,"红莲焚夜")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:

            save_cropped_area_screen("武功绝技红莲焚夜_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 红莲焚夜")
            add_to_get("天曦四象·白虎")


        kktouch2(290+10, 558+37,1,"繁花一梦")

        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:

            save_cropped_area_screen("武功绝技繁花一梦_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 繁花一梦")
            add_to_get("天曦四象·青龙")


        swipe_to_end((142, 181, 353, 403),(244, 601),(244, 258))
        sleep(5)

        kktouch2(187+10, 314+37,1,"九天雷引")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:

            save_cropped_area_screen("武功绝技九天雷引_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 九天雷引")
            add_to_get("岁星行渡·千重焰")

        kktouch2(284+10, 309+37,1,"法天象地")

        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:

            save_cropped_area_screen("武功绝技法天象地_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 法天象地")
            add_to_get("降魔破邪·金刚明王")


        kktouch2(183+10, 431+37,1,"相夷太剑")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_相夷太剑")
            save_cropped_area_screen("武功绝技相夷太剑_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 相夷太剑")
            add_to_get("明河曙天·醉卧西海")


        kktouch2(287+10, 435+37,1,"星火漫天")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:

            save_cropped_area_screen("武功绝技星火漫天_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 星火漫天")
            add_to_get("妖影寻灵·狐舞青丘")


        kktouch2(282+10, 560+37,1,"终焉裁决")
        find_result = find_subImg(Template(r"tpl1726664464434.png"),start_x, start_y,end_x, end_y)
        if find_result is not None:
#             save_cropped_screen(f"武功_绝技_终焉裁决")
            save_cropped_area_screen("武功绝技终焉裁决_天赏外观",start_x, start_y,end_x, end_y)
            kkLogger_log("识别到 终焉裁决")
            add_to_get("丹青纵横·龙潭墨云")


        kktouch2(65+10, 334+37,3,"左侧江湖")#点击江湖
        sleep(2)
        coords = ocr_text_target_coords("雪寒",117, 420,380, 663)
        if coords is not None:
            touch(coords)
            kkLogger_log("点击 辉雪寒瑛")
            sleep(1)

        find_result = find_subImg(Template(r"tpl1726977527725.png", threshold=0.7),start_x, start_y,end_x, end_y)
        if find_result is None:
            kkLogger_log("识别到 辉雪寒瑛")
#             save_cropped_screen("武功_江湖_辉雪寒瑛")
            save_cropped_area_screen("武功江湖辉雪寒瑛_天赏外观",start_x, start_y,end_x, end_y)
            add_to_get("山海祠神·赤凰重明")


        coords = ocr_text_target_coords("芳心",117, 420,380, 663)
        if coords is not None:
            touch(coords)
            kkLogger_log("点击芳心妙愈")
            sleep(1)
        find_result = find_subImg(Template(r"tpl1726977628834.png", threshold=0.7),start_x, start_y,end_x, end_y)
        if find_result is None:
            kkLogger_log("识别到 芳心妙愈")

            save_cropped_area_screen("武功江湖芳心妙愈_天赏外观",start_x, start_y,end_x, end_y)
            add_to_get("新世华霓·喵到病除")
    ###############################################################
        kktouch2(65+10, 425+37,1,"左侧轻功")#点击轻功
#         save_cropped_screen(f"轻功")
        kkLogger_log("开始轻功")

        swipe_to_end((876, 181,1238, 511),(1055, 179),(1055, 492))
        save_cropped_area_screen_and_ocr_add_to_get("武功-轻功",876, 181,1238, 511,[3,2],Template(r"tpl1727017209837.png", threshold=0.6))

        swipe_to_end((876, 181,1238, 511),(1055, 492),(1055, 179))
        save_cropped_area_screen_and_ocr_add_to_get("武功-轻功",876, 275,1240, 608,[3,2],Template(r"tpl1727017209837.png", threshold=0.6))


################################################
def step_waiguan(is_skip):
    if is_skip == 0:
        kktouch2(1224 + 10, 32 + 37, 1, "菜单")
        if kktouch2(1156 , 193 , 1, "外观") is False:
            return

        # waiguan_substep_guancang(0)
        waiguan_substep_guancang1025(0)
        #
        check_and_try_game_home(4)
        waiguan_substep_shizhuang(0)

        waiguan_substep_fashi(0)

        waiguan_substep_wuqi(0)

        waiguan_substep_zhuoqi(0)

        waiguan_substep_xiangrui(0)

        waiguan_substep_jueji(0)

        kkLogger_log("外观结束")

        # kktouch2(64+10, 38+37,1,"左上退出外观")
        kk_keyevent("{ESC}", 1, 0.2, "左上退出外观")
    return get_return_taskEvent("step_waiguan", EventStatus.SUCCESS,"外观完成")
################################################

######################################################
#################################################
def safe_step_kaifang_shijie(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=MuMu模拟器12")
            return step_kaifang_shijie(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_kaifangshijie error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_kaifang_shijie", EventStatus.FAILURE)
###################
def step_kaifang_shijie(step_kaifang_shijie):
    if step_kaifang_shijie == 0:
        kktouch2(1231+10, 33+37,2,"菜单")#点击菜单
        if kktouch2(1161, 496,5,"开放世界") is False:
            return

        kktouch2(284, 621,1,"地名小三角")#点击小三角

        swipe_to_end((220, 60, 465, 385),(302, 58),(302, 536))
        save_cropped_screen("开放世界")

        kk_keyevent("{ESC}", 1, 0.2, "退出开放世界")

        kkLogger_log("开放世界结束")
    return get_return_taskEvent("step_kaifang_shijie", EventStatus.SUCCESS)
##########################################################
def safe_step_qunxia(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=MuMu模拟器12")
            return step_qunxia(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_qunxia error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_qunxia", EventStatus.FAILURE)
##########################################################
def step_qunxia(step_qunxia):
    if step_qunxia == 0:
        kktouch2(1231+10, 33+37,2,"菜单")#点击菜单

        if kktouch2(894, 541,1,"群侠") is False:
            return

        kktouch2(64+10, 126+37,1,"左侧群侠")#点击左侧群侠
        kktouch2(240+10, 353+37,1,"左侧第一个人")#点击第一个人物
        kktouch2(279+10, 395+37,1,"左侧展开箭头")#点击展开
        save_cropped_screen("群侠")
        for i in range(4):
            find_full_qunxia_result = find_all_subImg(Template(r"tpl1727019178804.png", threshold=0.89),37, 127,702, 668)
            full_qunxia_count = 0
            if find_full_qunxia_result is None:
                full_qunxia_count = 0
            else:
                for line in find_full_qunxia_result:
                    kkLogger_log(line)
                    if line['confidence'] > 0.89:
                        full_qunxia_count = full_qunxia_count +1
                        kktouch2(line['result'][0]+37,line['result'][1]+127,1,f"第{full_qunxia_count}个满级群侠")
                        ocr_add_to_get(96, 45,225, 105)

            is_end = swipe_and_judge_end_fast((37, 87,708, 262),(258, 529), (254, 109))
            if is_end:
                break

        kk_keyevent("{ESC}", 2, 0.2, "退出群侠")

        kkLogger_log("群侠结束")
    return get_return_taskEvent("step_qunxia", EventStatus.SUCCESS,"群侠完成")
##########################################################
#################################################
def safe_step_zhuangyuan(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=MuMu模拟器12")
            return step_zhuangyuan(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_zhuangyuan error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_zhuangyuan", EventStatus.FAILURE)
###################
def step_zhuangyuan(step_zhuangyuan):
    if step_zhuangyuan == 0:
        kktouch2(1231+10, 33+37,1,"菜单")#点击菜单
        if kktouch2(892, 181,2,"庄园") is False:
            return

        save_cropped_screen_with_blur("庄园",230, 80, 680, 504)
        # kktouch2(67+10, 38+37,1,"左上退出")
        kk_keyevent("{ESC}", 1, 0.2, "退出庄园")

        kkLogger_log("庄园结束")
    return get_return_taskEvent("step_zhuangyuan", EventStatus.SUCCESS,"庄园完成")
#######################################################################
#################################################
def safe_step_lingcong(is_skip):
    for i in range(3):
        try:
            # connect_device("windows:///?title_re=MuMu模拟器12")
            return step_lingcong(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_lingcong error,{e}")
            sleep(10)
            check_and_try_game_home()

    return get_return_taskEvent("step_lingcong", EventStatus.FAILURE)
#########################################################################
def step_lingcong(step_lingcong):
    if step_lingcong == 0:
        kktouch2(1231+10, 33+37,1,"菜单")
        if kktouch2(1246, 333,2,"宠物") is False:
            return get_return_taskEvent("step_lingcong", EventStatus.SUCCESS,"没有宠物")

        if ocr_text_target_coords("跳过",1124-100,10,1124+100,63+100) is not None:
            kk_keyevent("{ESC}", 1, 1, "跳过教学")
            # kktouch2(1124,63,1.2,"跳过教学")
            kktouch2(796, 470, 1.2, "确定")
            # kk_keyevent("{ESC}", 1, 0.2, "退出灵宠")

        kktouch2(443+10, 222+37,2,"灵宠")
        if ocr_text_target_coords("跳过",1124-100,10,1124+100,63+100) is not None:
            # kktouch2(1124,63,1.2,"跳过教学")
            kk_keyevent("{ESC}", 1, 1, "跳过教学")
            kktouch2(796, 470, 1.2, "确定")
        find_lingcong_result = find_subImg(Template(r"tpl1727025466427.png"),1136, 41,1269, 111)
        if find_lingcong_result:
            # save_cropped_screen("灵宠")
            save_cropped_screen_with_mulblur("灵宠_天赏外观",
                                             [(418, 110-40, 668, 170-40), (63, 273-40, 160, 311-40)])
            if ocr_text_target_coords("天狼",600,80,1000,400) is not None:
                ocr_check_and_set("天赏宠物","灵宠·天狼星")
            # kktouch2(65+10, 37+37,1,"退出灵宠详情")
        # kktouch2(65+10, 37+37,1,"退出宠物图鉴")#点击推出

        kk_keyevent("{ESC}", 1, 0.2, "退出灵宠")
        kkLogger_log("宠物结束")

    step_tianshangshi(0)
    step_chongzhi(0)
    step_shenqi(0)
    return get_return_taskEvent("step_lingcong", EventStatus.SUCCESS,"灵宠完成")
##################################################################################
def step_shenqi(is_skip):
    try:
        if is_skip == 0:
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            kktouch2(1114, 331,0.5,"千机玄甲")
            sleep(1)
            kktouch2(618,661,1.2,"我知道啦")
            save_cropped_screen("神器")

            kk_keyevent("{ESC}", 1, 0.2, "神器")

        return get_return_taskEvent("step_shenqi", EventStatus.SUCCESS)
    except Exception as e:
        kkLogger_log(f"step_shenqi 失败：{e}")
        kk_keyevent("{ESC}")
        return get_return_taskEvent("step_shenqi", EventStatus.FAILURE)
#######################################################################
def step_chongzhi(is_skip):
    try:
        if is_skip == 0:
            kktouch2(1224 + 10, 32 + 37, 1, "菜单")
            kktouch2(1167, 722,2,"攻略")
            target = local_ocr_text_target_coords("小",58,46,229,112)
            sleep(1)
            target2 = local_ocr_text_target_coords("小", 58, 46, 229, 112)
            if target is None and target2 is None:
                connect_device("windows:///?title_re=MuMu模拟器12")
                check_and_try_game_home(4)
                kktouch2(1224 + 10, 32 + 37, 1, "菜单")
                kktouch2(1167, 722, 2, "攻略")

            for i in range(2):
                kktouch2(505+10, 639+37,1,"输入框")
                text("累计充值")
                kktouch2(1090+10, 639+37,1,"发送")
                sleep(1)
                coords = ocr_text_target_coords("元",263, 25,1219, 599)
                if coords is not None:
                    get_attr_ocr_number("充值金额",283, 12,1230, 527)
                    break

            for i in range(2):
                kktouch2(505+10, 639+37,1,"输入框")
                text("赠礼额度")
                kktouch2(1090+10, 639+37,1,"发送")
                sleep(1)
                coords = ocr_text_target_coords("赠礼额度查询",296, 269,1219, 599)
                if coords is not None:
                    kktouch2(coords[0],coords[1],1.2,"赠礼额度查询")
                    save_cropped_screen("赠礼额度")
                    break

            # kktouch2(37, 72,2,"退出小暖问答")
            kk_keyevent("{ESC}", 1, 0.2, "退出小暖问答")
            kkLogger_log("充值查询结束")
        return get_return_taskEvent("step_chongzhi", EventStatus.SUCCESS)
    except Exception as e:
        kkLogger_log(f"step_chongzhi 失败：{e}")
        kktouch2(37+10, 32+37,2,"退出小暖问答")
        return get_return_taskEvent("step_chongzhi", EventStatus.FAILURE)
###################################################################################
def step_zengli_edu(is_skip):
    try:
        if is_skip == 0:
            kktouch2(1231+10, 33+37,1,"菜单")
            kktouch2(1143+10, 680+37,2,"攻略")
            for i in range(2):
                kktouch2(505+10, 639+37,2,"输入框")
                text("赠礼额度")
                kktouch2(1090+10, 639+37,1,"发送")
                sleep(5)
                coords = ocr_text_target_coords("赠礼额度查询",296, 269,1219, 599)
                if coords is not None:
                    kktouch2(coords[0],coords[1],2,"赠礼额度查询")
                    save_cropped_screen("赠礼额度_其他物品")
                    break

            kktouch2(37, 72,2,"退出小暖问答")
            kkLogger_log("赠礼额度查询结束")
        return get_return_taskEvent("step_chongzhi", EventStatus.SUCCESS)
    except Exception as e:
        kkLogger_log(f"step_zengli_edu 失败：{e}")
        kktouch2(37+10, 32,2,"退出小暖问答")
        return get_return_taskEvent("step_zengli_edu", EventStatus.FAILURE)
##############################################################################
def step_tianshangshi(step_tianshangshi):
    try:
        if step_tianshangshi == 0:
            tss_value = 0
            for item in product_meta:
                if item["type"] not in [1,2]:
                    continue
                else:
                    item_values = item.get("values", [])
                    for item_value in item_values:
                        item_value_2 = remove_unwanted_chars(item_value)
                        tss_value = tss_value + configs.TSS_VALUE.get(item_value_2,0)
            kkLogger_log(f"已使用天赏石：{tss_value}")
            ocr_check_and_set_number("已使用天赏石",str(tss_value))
        return get_return_taskEvent("step_tianshangshi", EventStatus.SUCCESS)
    except Exception as e:
        kkLogger_log(f"step_tianshangshi 失败：{e}")
        return get_return_taskEvent("step_tianshangshi", EventStatus.FAILURE)
########################################################################
def step_img_upload(is_skip):
    if is_skip == 0:
        sleep(1)
        for i in range(3):
            try:
                kkLogger_log("开始处理图片")
                image_util.collage(f'{snapImgPath}', save_original=save_original_pic)

                kkLogger_log(f'开始上传')

                upload_kk_img_to_oss(f'{snapImgPath}')

                kkLogger_log(f'上传结束 pic_url_arrays:{pic_url_arrays}')
                return get_return_taskEvent("step_img_upload", EventStatus.SUCCESS,"图片上传完成")
            except Exception as e:
                kkLogger_log(f"图片上传异常 error,{e}")
                sleep(2)
                continue

    return get_return_taskEvent("step_img_upload", EventStatus.FAILURE,"图片上传失败")
###########################################################
def safe_step_meta_upload(is_skip):
    for i in range(3):
        try:
            return step_meta_upload(is_skip)
        except Exception as e:
            kkLogger_log(f"safe_step_meta_upload error,{e}")
            sleep(2)
            continue

    return get_return_taskEvent("step_meta_upload", EventStatus.FAILURE,"录号数据提交异常")
#################################################################
def step_meta_upload(step_meta_upload):
    global product_meta
    if step_meta_upload == 0:
        kkLogger_log(f"product_meta_json:{product_meta}")
        kkLogger_log(f"pic_url_arrays:{pic_url_arrays}")
        portal_client.sync_product_info(luhao_task['productSn'], configs.image_server_url + get_head_pic(), pic_url_arrays, product_meta)

    global end_time
    end_time = datetime.datetime.now()
    total_time = end_time - start_time
    minutes = total_time.total_seconds() // 60
    kkLogger_log(f"录号执行了 {minutes} 分钟。")

    return get_return_taskEvent("step_meta_upload", EventStatus.SUCCESS,f"录号数据提交成功，共执行 {minutes} 分钟")
#########################################################################
def step_exit_game(is_skip):
    if is_skip == 0:
        kktouch2(1261,20,2,"关闭游戏")
        kktouch2(1261 , 20 , 2, "关闭游戏")
        kktouch2(1154+10,391+37,1,"确认")
    return get_return_taskEvent("step_meta_upload", EventStatus.SUCCESS,"录号数据提交成功")
#########################################################################
def step_restart_game(is_skip):
    global width
    global height
    global snapImgPath
    global projectPath
    projectPath = "D:\\kkzhw\\airtest_log"
    snapImgPath = f"{projectPath}"

    if is_skip == 0:
        connect_device("windows:///")
        kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")

        print(f"连接桌面:{device().get_current_resolution()}")
        # touch(device().get_current_resolution())
        sys_tool.restart_nshm()
        for i in range(300):
            sleep(3)
            try:
                connect_device("Windows:///?title_re=逆水寒手游-MuMu模拟器")
                (width,height) = device().get_current_resolution()
                print(f"尝试逆水寒手游-MuMu模拟器:{(width,height)}")
                kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
                if width == 1600:
                    wait_for_ocr_txt("启动游戏",5,280,304,1106,650)
                    touch((640,546))
                    sleep(3)
                    for i in range(120):
                        sleep(2)
                        try:
                            connect_device("Windows:///?title_re=MuMu模拟器12")
                            (width2, height2) = device().get_current_resolution()
                            print(f"尝试连接MuMu模拟器12:{(width2, height2)}")
                            kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")

                            if width2 == 1288:
                                print(f"连接MuMu模拟器12成功:{(width2, height2)}")
                                G.DEVICE.move((0, 0))
                                (width, height) = (width2, height2)
                                break
                            elif width2 == 60 :
                                print(f"发现登录弹窗:{(width2, height2)}")
                                connect_device("Windows:///?title_re=登录")
                                print(f"登录窗口：{device().get_current_resolution()}")
                                kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
                                kktouch2(340,22,1,"关闭登录弹窗")
                                continue
                            else:
                                continue
                        except Exception as e:
                            print(e)
                            continue
                    break
                else:
                    continue
            except Exception as e:
                print(e)
                continue
    return get_return_taskEvent("step_restart_game", EventStatus.SUCCESS,"重启游戏成功")
#####################################################
def step_mumu_logout():
    kkLogger_log("开始退登mumu模拟器云逆水寒手游")
    connect_device("Windows:///?title_re=MuMu模拟器12")
    sleep(1)
    connect_device("Android://127.0.0.1:5037/127.0.0.1:7555?ori_method=adbori")
    for i in range(3):
        # set_current(0)
        # 登录状态则退出
        target = ocr_text_target_coords("退出", 872, 20, 1270, 214)
        if target is not None:
            kktouch2(target[0], target[1] - 10, 1.2, "右上退出")
            kktouch2(746, 461 - 33, 1.1, "弹窗退出")

        target = ocr_text_target_coords("其他账号登录", 330, 417, 920, 626)
        if target is not None:
            kktouch2(target[0], target[1], 1.2, "其他账号登录")

        target = ocr_text_target_coords("详细", 399, 456, 910, 634)
        if target is not None:
            sync_current_snapshot("云逆水寒退登成功")
            return True
        else:
            sleep(1)
            continue
    return True

#######################################
def step_logout(is_skip):
    if is_skip == 0:
        connect_device("windows:///?title_re=MuMu模拟器12")
        check_and_try_game_home()
        kktouch2(1227+10, 35+37,1,"菜单")
        kktouch2(1248, 466,2,"右侧设置")
        kktouch2(73, 163,2,"左侧系统")
        kktouch2(585, 316,2,"设置中切换账号")
        kktouch2(582, 393,2,"切换账号.")
        sleep(4)
        kktouch2(801,160,1,"关闭登录窗口X")

        global end_time
        end_time = datetime.datetime.now()
        total_time = end_time - start_time
        minutes = total_time.total_seconds() // 60
        kkLogger_log(f"录号执行了 {minutes} 分钟。")

        coords = local_ocr_text_target_coords("选择服务器",471, 473,811, 565)
        if coords is not None:
            kkLogger_log("逆水寒模拟器退登成功")
            step_mumu_logout()
            return get_return_taskEvent("step_logout", EventStatus.SUCCESS,f"录号完成，退登成功，共录制了{minutes}分钟")
        else:
            kkLogger_log("逆水寒模拟器退登失败")
            step_mumu_logout()
            return get_return_taskEvent("step_logout", EventStatus.MANUAL_REQUIRED,f"异常，需要人工介入回到登录首页,共录制了{minutes}分钟")

#
if __name__ == '__main__':
    # 记录程序开始执行的时间
    # accounts = ["***********","<EMAIL>","<EMAIL>","<EMAIL>"]
    accounts = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
    # accounts = ["***********"]
    # password = ["wodejia","Jun135790","Jun135790.","Jun135790."]
    password = ["Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790.","Jun135790."]

    i = 0

    for item in accounts:
        luhao_task = {
            #     'gameAccount':'***********',
            'gameAccount': item,
            # 'gamePassword': "Jun135790",
            'gamePassword': password[i],
            "id": 540,
            "status": "IN_PROGRESS",
            # "loginType":"QR_CODE",
            "loginType":"PASSWORD",
            # "loginType":"SMS_CODE",
            "productSn": "TESTNSH20228850",
            "product_meta": requestGameProductCategoryMeta(75),
            "product_info": {
                "product": {
                    "productCategoryId": 75,
                    "productSn": "TESTNSH20228850",
                    "productCategoryName": "逆水寒手游",
                    "updateTime": "2024-09-29T06:36:15.000+00:00",
                    "createTime": "2024-09-28T22:49:57.000+00:00",
                    "gameActiveFlag": 0,
                    "gameAccountQufu": "踏北尘|紫禁之巅"
                }
            }
        }
        i = i +1

        start_time = datetime.datetime.now()
        # step_restart_game(0)


        # init_local_nsh(luhao_task)

        # dev2 = connect_device("windows:///?title_re=雷电模拟器")
        # G.DEVICE.move((416, 228))


        init_nsh(luhao_task)
        # step_login(0)

        # kktouch2(1244, 84, 2, "账号")
        # kktouch2(579, 397, 2, "切换账号")
        # kktouch2(799, 162, 2, "关闭")
        #
        #
        # step_mumu_logout()
        # step_qufu(0)

        # safe_step_gui_task(0)

        safe_step_gameset(0)
        # safe_step_fuben_in(0)
        #
        safe_step_juese(0)
        safe_step_jueji(0)
        safe_step_neigong(0)
        safe_step_dazao(0)
        safe_step_waiguan(0)
        safe_step_kaifang_shijie(0)
        safe_step_qunxia(0)
        safe_step_zhuangyuan(0)
        safe_step_lingcong(0)

        step_chongzhi(0)
        # safe_step_fuut(0)
        #
        # step_img_upload(0)赠礼额度
        # step_meta_upload(0)

        # step_logout(0)
        kkLogger_log(f"第 {i}个===================================================结束")
        sleep(5)

        # connect_device("windows:///?title_re=MuMu模拟器12")
        # kkLogger_log(f"============当前连接设备：{G.DEVICE_LIST}")
        #


        # android = Android()
        # android.disconnect()
        # kkLogger_log(f"=====断开安卓连接后设备=======当前连接设备：{G.DEVICE_LIST}")
        # step_logout(0)

        # step_exit_game(0)


        end_time = datetime.datetime.now()
        total_time = end_time - start_time
        minutes = total_time.total_seconds() / 60
        print(f"录号执行了 {minutes} 分钟。")


        # break


     


















