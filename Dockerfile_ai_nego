# 使用官方 Python 镜像作为基础镜像
FROM python:3.9-slim

# 设置时区为 Asia/Shanghai
ENV TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /app

# 复制 requirements.txt 文件
COPY ./ai_nego/requirements.txt .

# 安装项目依赖
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

# 复制 common 和 ai_nego 目录
COPY ./common /app/common
COPY ./ai_nego /app/ai_nego
COPY configs.py /app

# 设置 PYTHONPATH
ENV PYTHONPATH=/app

# 运行脚本
CMD ["python", "ai_nego/ai_nego_scheduler.py"]