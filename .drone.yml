kind: pipeline
type: docker
name: build-and-deploy-bm

steps:
  # 🏗️ 构建并推送镜像
  - name: build-push-bm
    image: plugins/docker
    settings:
      registry: *************:5000
      repo: *************:5000/kkzhw/bm
      tags: latest
      dockerfile: Dockerfile_bm
      context: .
      insecure: true
      username:
        from_secret: docker_user
      password:
        from_secret: docker_pass

  # 🚀 部署到远程服务器
  - name: deploy-to-*************
    image: appleboy/drone-ssh
    depends_on:
      - build-push-bm
    settings:
      host: *************
      port: 22
      username: root
      password: 12345
      script:
        - docker pull *************:5000/kkzhw/bm:latest
        - docker stop bm || true
        - docker rm bm || true
        - docker run -d --restart=always -e ENV=prod --name bm *************:5000/kkzhw/bm:latest
    when:
      status:
        - success

  # ✅ 成功通知
  - name: notify-success
    image: curlimages/curl
    depends_on:
      - deploy-to-*************
    when:
      status:
        - success
    commands:
      - |
        curl -X POST https://api.day.app/Whsvw4oo4AFPcUZoQCWedH/ \
          -H "Content-Type: application/json" \
          -d "{
            \"title\": \"BM 部署成功 ✅\",
            \"body\": \"仓库：${DRONE_REPO_NAME}\\n分支：${DRONE_COMMIT_BRANCH}\\n提交：${DRONE_COMMIT_SHA:0:7} by ${DRONE_COMMIT_AUTHOR}\\n信息：${DRONE_COMMIT_MESSAGE}\\n👉 查看详情：${DRONE_BUILD_LINK}\"
          }"

  # ❌ 失败通知
  - name: notify-failure
    image: curlimages/curl
    when:
      status:
        - failure
    commands:
      - |
        curl -X POST https://api.day.app/Whsvw4oo4AFPcUZoQCWedH/ \
          -H "Content-Type: application/json" \
          -d "{
            \"title\": \"BM 部署失败 ❌\",
            \"body\": \"仓库：${DRONE_REPO_NAME}\\n分支：${DRONE_COMMIT_BRANCH}\\n提交：${DRONE_COMMIT_SHA:0:7} by ${DRONE_COMMIT_AUTHOR}\\n信息：${DRONE_COMMIT_MESSAGE}\\n⚠️ 请检查构建日志：${DRONE_BUILD_LINK}\"
          }"

trigger:
  branch:
    - dev
  event:
    - push
